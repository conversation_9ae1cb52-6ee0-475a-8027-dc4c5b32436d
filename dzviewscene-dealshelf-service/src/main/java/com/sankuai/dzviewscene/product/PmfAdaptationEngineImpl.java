package com.sankuai.dzviewscene.product;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.pmf.PmfActivityEngine;
import com.sankuai.dzviewscene.client.LaunchClient;
import com.sankuai.dzviewscene.client.LaunchRequest;
import com.sankuai.dzviewscene.client.LaunchResult;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityCtxBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.framework.monitor.TraceElement;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * 混合新旧引擎的执行引擎，用户不需关心是用新引擎还是旧引擎执行，返回结果
 * 执行期间可自由切换新旧引擎执行，并支持结果对比，报警
 * created by zhangzhiyuan04 in 2021/8/27
 */
@Component
public class PmfAdaptationEngineImpl implements PmfAdaptationEngine {

    @Resource
    private PmfActivityEngine pmfActivityEngine;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.mixEngine.engineSwitchConfig")
    private EngineSwitchConfig config;

    private ExecutorService diffExecutorService = ExecutorServices.forThreadPoolExecutor("diffPmfResponseExecutor");

    private Map<String, String> space2ActivityCode = buildSpace2ActivityCode();


    private Map<String, String> buildSpace2ActivityCode() {
        Map<String, String> result = Maps.newHashMap();
        result.put(DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY, DealShelfActivity.CODE);
        result.put(UnifiedShelfActivityCtxBuilder.UNIFIED_SHELF_SPACE_KEY, UnifiedShelfActivity.CODE);
        result.put(ProductShelfActivityCtxBuilder.PRODUCT_SHELF_SPACE_KEY, ProductShelfActivity.CODE);
        result.put(DealDetailActivityCtxBuilder.DEAL_DETAIL_SPACE_KEY, DealDetailActivity.CODE);
        result.put(DealDetailActivityCtxBuilder.DEAL_DETAIL_STANDARD_SERVICE_SPACE_KEY, DealDetailActivity.CODE);
        result.put(DealDetailActivityCtxBuilder.HEALTH_EXAMINATION_DEAL_DETAIL_ITEM_DETAIL_SPACE_KEY, DealDetailActivity.CODE);
        result.put(DealShelfActivityCtxBuilder.IM_DEAL_SHELF_SPACE_KEY,DealShelfActivity.CODE);
        result.put(DealShelfActivityCtxBuilder.SEARCH_DEAL_SHELF,DealShelfActivity.CODE);
        return result;
    }

    @Override
    public boolean shouldDiff(String sceneCode) {
        if (config == null || CollectionUtils.isEmpty(config.getDiffSceneCodes())) {
            return false;
        }
        return config.getDiffSceneCodes().contains(sceneCode);
    }

    @Override
    public boolean usePmfEngine(ActivityContext activityContext) {
        if (config == null || CollectionUtils.isEmpty(config.getPmfSceneCodes())) {
            return false;
        }
        String spaceKey = activityContext.getParam("spaceKey");
        if (StringUtils.isBlank(spaceKey)) {
            return false;
        }
        LaunchRequest launchRequest = new LaunchRequest();
        launchRequest.setSpaceKey(spaceKey);
        launchRequest.setFeatureParams(buildFeatureParams(activityContext));
        LaunchResult launch = LaunchClient.getLaunch(launchRequest);
        if (launch == null || !config.getPmfSceneCodes().contains(launch.getSceneCode())) {
            return false;
        }
        return true;
    }

    private Map<String, Object> buildFeatureParams(ActivityContext activityContext) {
        String spaceKey = activityContext.getParam("spaceKey");
        Map<String, Object> parameters = activityContext.getParameters();
        if (StringUtils.isBlank(spaceKey)) {
            return parameters;
        }
        if (DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY.equals(spaceKey)) {
            ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
            if (shopM != null) {
                parameters.put("shopType", shopM.getShopType());
                parameters.put("category", shopM.getCategory());
            }
        }
        if (UnifiedShelfActivityCtxBuilder.UNIFIED_SHELF_SPACE_KEY.equals(spaceKey)) {
            ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
            if (shopM != null) {
                parameters.put("shopType", shopM.getShopType());
                parameters.put("category", shopM.getCategory());
            }
        }

        if (DealDetailActivityCtxBuilder.DEAL_DETAIL_SPACE_KEY.equals(spaceKey)) {
            int dealCategoryId = activityContext.getParam(ProductDetailActivityConstants.Params.dealCategoryId);
            if (dealCategoryId != 0) {
                parameters.put("dealCategory", dealCategoryId);
            }
            String serviceType = activityContext.getParam(ProductDetailActivityConstants.Params.serviceType);
            if(StringUtils.isNotEmpty(serviceType)) {
                parameters.put("serviceType", serviceType);
            }
            String serviceTypeLeafId = activityContext.getParam(ProductDetailActivityConstants.Params.serviceTypeLeafId);
            if(StringUtils.isNotEmpty(serviceTypeLeafId)) {
                parameters.put("serviceTypeLeafId", serviceTypeLeafId);
            }
            String standardDealGroup = activityContext.getParam(ProductDetailActivityConstants.Params.standardDealGroup);
            if(StringUtils.isNotEmpty(standardDealGroup)) {
                parameters.put("standardDealGroup", standardDealGroup);
            }
            String sceneCode = activityContext.getParam(ProductDetailActivityConstants.Params.sceneCode);
            if (StringUtils.isNotEmpty(sceneCode)) {
                parameters.put("sceneCode", sceneCode);
            }
        }

        if (DealDetailActivityCtxBuilder.DEAL_DETAIL_STANDARD_SERVICE_SPACE_KEY.equals(spaceKey)) {
            int dealCategoryId = activityContext.getParam(ProductDetailActivityConstants.Params.dealCategoryId);
            if (dealCategoryId != 0) {
                parameters.put("dealCategory", dealCategoryId);
            }
        }

        if (DealDetailActivityCtxBuilder.HEALTH_EXAMINATION_DEAL_DETAIL_ITEM_DETAIL_SPACE_KEY.equals(spaceKey)) {
            int dealCategoryId = activityContext.getParam(ProductDetailActivityConstants.Params.dealCategoryId);
            if (dealCategoryId != 0) {
                parameters.put("dealCategory", dealCategoryId);
            }
        }

        if (ProductShelfActivityCtxBuilder.PRODUCT_SHELF_SPACE_KEY.equals(spaceKey)) {
            ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
            if (shopM != null) {
                parameters.put("shopType", shopM.getShopType());
                parameters.put("category", shopM.getCategory());
            }
        }

        if (DealShelfActivityCtxBuilder.SEARCH_DEAL_SHELF.equals(spaceKey)) {
            ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
            if (shopM != null) {
                parameters.put("shopType", shopM.getShopType());
                parameters.put("category", shopM.getCategory());
            }
        }
        return parameters;
    }

    @Override
    public <T> ActivityResponse<T> executeWithPmfEngine(ActivityContext activityContext) {
        com.sankuai.athena.viewscene.framework.ActivityRequest request = new com.sankuai.athena.viewscene.framework.ActivityRequest();
        String spaceKey = activityContext.getParam("spaceKey");
        String activityCode = space2ActivityCode.get(spaceKey);
        Cat.logEvent("PmfEngine", activityCode);
        request.setActivityCode(activityCode);
        request.setParams(activityContext.getParameters());
        request.setStartTime(activityContext.getStartTime());
        com.sankuai.athena.viewscene.framework.ActivityResponse<T> execute = pmfActivityEngine.execute(request);
        ActivityResponse<T> activityResponse = new ActivityResponse<>();
        activityResponse.setResult(execute.getResult());
        activityResponse.setTraceElements(convertTraceElements(execute.getTraceElements()));
        activityResponse.setExecuteError(execute.getExecuteError());
        return activityResponse;
    }

    private List<TraceElement> convertTraceElements(List elements) {
        List<TraceElement> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(elements)) {
            return result;
        }
        int size = elements.size();
        for(int i = 0 ; i < size; i ++){
            Object element = elements.get(i);
            if (element instanceof TraceElement) {
                result.add((TraceElement) element);
            }
            if (element instanceof com.sankuai.athena.viewscene.framework.monitor.TraceElement) {
                com.sankuai.athena.viewscene.framework.monitor.TraceElement item = (com.sankuai.athena.viewscene.framework.monitor.TraceElement) element;
                TraceElement traceElement = new TraceElement();
                traceElement.setType(item.getType());
                traceElement.setName(item.getName());
                traceElement.setParam(item.getParam());
                traceElement.setResult(item.getResult());
                traceElement.setLatency(item.getLatency());
                traceElement.setThrowable(item.getThrowable());
                result.add(traceElement);
            }
        }
        return result;
    }

    @Override
    public void diffWithPmfEngine(ActivityRequest activityRequest, ActivityResponse response) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.PmfAdaptationEngineImpl.diffWithPmfEngine(com.sankuai.dzviewscene.shelf.framework.ActivityRequest,com.sankuai.dzviewscene.shelf.framework.ActivityResponse)");
        try {
            diffExecutorService.submit(() -> {
                com.sankuai.athena.viewscene.framework.ActivityResponse execute = pmfActivityEngine.execute(convertRequest(activityRequest));
                diffResult(execute, response);
            });
        } catch (Exception e) {}
    }

    private void diffResult(com.sankuai.athena.viewscene.framework.ActivityResponse pmfEngineResponse, ActivityResponse activityResponse) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.PmfAdaptationEngineImpl.diffResult(com.sankuai.athena.viewscene.framework.ActivityResponse,com.sankuai.dzviewscene.shelf.framework.ActivityResponse)");
        if (config == null || !config.getDiffSceneCodes().contains(pmfEngineResponse.getSceneCode())) {
            return;
        }
        String sceneCode = pmfEngineResponse.getSceneCode();
        Object pmfResult = pmfEngineResponse.getResult().join();
        Object result = activityResponse.getResult().join();
        String pmfResultJson = JsonCodec.encode(pmfResult);
        String resultJson = JsonCodec.encode(result);
        if (pmfResultJson.equals(resultJson)) {
            return;
        }
        String message = String.format("[raw:%s] , [pmf:%s]", resultJson, pmfResultJson);
        Cat.logErrorWithCategory("Diff : " + sceneCode, message, new RuntimeException());
    }

    private com.sankuai.athena.viewscene.framework.ActivityRequest convertRequest(ActivityRequest activityRequest) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.PmfAdaptationEngineImpl.convertRequest(com.sankuai.dzviewscene.shelf.framework.ActivityRequest)");
        com.sankuai.athena.viewscene.framework.ActivityRequest request = new com.sankuai.athena.viewscene.framework.ActivityRequest();
        BeanUtils.copyProperties(activityRequest, request);
        Map<String, Object> params = request.getParams();
        if (MapUtils.isEmpty(params)) {
            return request;
        }
        String spaceKey = (String) params.get("spaceKey");
        String activityCode = space2ActivityCode.get(spaceKey);
        request.setActivityCode(activityCode);
        return request;
    }

    public static class EngineSwitchConfig {
        List<String> pmfSceneCodes = Lists.newArrayList();

        List<String> diffSceneCodes = Lists.newArrayList();

        public List<String> getPmfSceneCodes() {
            return pmfSceneCodes;
        }

        public void setPmfSceneCodes(List<String> pmfSceneCodes) {
            this.pmfSceneCodes = pmfSceneCodes;
        }

        public List<String> getDiffSceneCodes() {
            return diffSceneCodes;
        }

        public void setDiffSceneCodes(List<String> diffSceneCodes) {
            this.diffSceneCodes = diffSceneCodes;
        }
    }

}
