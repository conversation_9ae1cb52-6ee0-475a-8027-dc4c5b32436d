package com.sankuai.dzviewscene.product.shelf.ability.builder.filter;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.*;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.common.OldEngineAdaptCfg;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.OldEngineAdaptUtil;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
@Ability(code = FilterBuilder.CODE,
        name = "VO-筛选栏构造能力",
        description = "筛选栏构造能力（前置）。构造 Map<String, FilterComponentVO>",
        activities = {DealShelfActivity.CODE},
        dependency = {ShelfMainDataAssembler.CODE}
)
public class FilterBuilder extends PmfAbility<Map<String, FilterComponentVO>, FilterBuilder.Request, FilterBuilder.Config> {

    public static final String CODE = "FilterBuilder";

    @Resource
    private ComponentFinder componentFinder;

    @Override
    public CompletableFuture<Map<String, FilterComponentVO>> build(ActivityCxt ctx, Request request, Config config) {
        CompletableFuture<Map<String, FilterComponentVO>> oldEngineRes = getOldEngineRes(ctx, config);
        if (oldEngineRes != null) {
            return oldEngineRes;
        }
        return CompletableFuture.completedFuture(getNewEngineRes(ctx, request, config));
    }

    /**
     * 获取旧框架的构造结果，若不走旧框架则返回 null
     *
     * @param ctx
     * @param config
     * @return
     */
    private CompletableFuture<Map<String, FilterComponentVO>> getOldEngineRes(ActivityCxt ctx, Config config) {
        if (config.getOldEngineCfg() == null) {
            return null;
        }
        OldEngineAdaptUtil.paddingAbilityToCtx(ctx, com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilder.ABILITY_FILTER_GEN_CODE, config.getOldEngineCfg());
        OldEngineAdaptUtil.paddingExtPointToCtx(ctx, FilterBuilderExt.EXT_POINT_FILTER_COMPONENT_CODE, config.getOldEngineCfg());

        ActivityContext oldContext = ActivityCtxtUtils.toActivityContext(ctx);
        return componentFinder.findAbility(oldContext, com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilder.ABILITY_FILTER_GEN_CODE).build(oldContext);
    }


    private Map<String, FilterComponentVO> getNewEngineRes(ActivityCxt ctx, Request request, Config config) {
        ShelfGroupM shelfGroupM = ctx.getSource(ShelfMainDataAssembler.CODE);
        if (shelfGroupM == null) {
            return Maps.newHashMap();
        }
        if ((ModelUtils.hasNoProducts(shelfGroupM)) || MapUtils.isEmpty(shelfGroupM.getFilterMs())) {
            return Maps.newHashMap();
        }
        // 多层货架则生成多个FilterComponentVO
        return shelfGroupM.getFilterMs()
                .entrySet()
                .stream()
                .collect(HashMap::new,
                        (map, filterEntry) -> map.put(filterEntry.getKey(), buildFilterComponentVO(ctx, request, config, filterEntry.getValue(), shelfGroupM.getProductGroupMs().get(filterEntry.getKey()))),
                        HashMap::putAll);
    }

    private FilterComponentVO buildFilterComponentVO(ActivityCxt ctx, Request request, Config config, FilterM filterM, ProductGroupM productGroupM) {
        FilterComponentVO filterComponentVO = new FilterComponentVO();
        // 1. 根据扩展点设置minShowNum
        filterComponentVO.setMinShowNum(config.getMinShowNum());
        // 2. 根据扩展点设置showType
        filterComponentVO.setShowType(getFilterShowType(ctx, filterM, config, ctx.getSource(ShelfDouHuFetcher.CODE)));
        // 3. 设置筛选
        filterComponentVO.setFilterBtns(buildFilterButtons(ctx, request, config, filterM, productGroupM));
        filterComponentVO.setHideFilter(fillHideFilter(ctx, productGroupM));
        return filterComponentVO;
    }

    private int getFilterShowType(ActivityCxt ctx, FilterM filterM, Config config, List<DouHuM> douHuMS) {
        try {
            FilterShowTypeVP<?> filterShowTypeVP = findVPoint(ctx, FilterShowTypeVP.CODE);
            if (filterShowTypeVP == null) {
                // 兼容以前的逻辑
                return getFilterShowTypeByBuildConfig(config, douHuMS);
            }
            Integer showType = filterShowTypeVP.execute(ctx,
                    FilterShowTypeVP.Param.builder().filterM(filterM).douHuMS(douHuMS).build());
            if (showType == null) {
                // 兼容以前的逻辑
                return getFilterShowTypeByBuildConfig(config, douHuMS);
            }
            return showType;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
        }
        // 兼容以前的逻辑
        return getFilterShowTypeByBuildConfig(config, douHuMS);
    }

    private int getFilterShowTypeByBuildConfig(Config config, List<DouHuM> douHuMS) {
        if (MapUtils.isEmpty(config.getDouhuToShowTypeMap())) {
            return config.getShowType();
        }
        for (DouHuM douHuM : douHuMS) {
            Integer showType = config.getDouhuToShowTypeMap().get(douHuM.getSk());
            if (showType != null) {
                return showType;
            }
        }
        return config.getShowType();
    }


    private boolean fillHideFilter(ActivityCxt ctx, ProductGroupM productGroupM) {
        try {
            FilterBtnHideVP<?> filterBtnHideVP = findVPoint(ctx, FilterBtnHideVP.CODE);
            return filterBtnHideVP.execute(ctx,
                    FilterBtnHideVP.Param.builder().productNum(Objects.nonNull(productGroupM) ? productGroupM.getTotal():0).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
            return false;
        }
    }

    private List<FilterButtonVO> buildFilterButtons(ActivityCxt activityCxt, Request request, Config config, FilterM filterM, ProductGroupM productGroupM) {
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return Lists.newArrayList();
        }
        int layer = 1;
        AtomicInteger index = new AtomicInteger(1);
        List<FilterBtnM> allFilter = com.google.common.collect.Lists.newArrayList(filterM.getFilters());
        return filterM.getFilters()
                .stream()
                .map(filterButtonM -> builderFilterButtonVO(activityCxt, request, config, filterButtonM, layer, index.getAndIncrement(), productGroupM, allFilter))
                .collect(Collectors.toList());

    }

    private FilterButtonVO builderFilterButtonVO(ActivityCxt activityCxt, Request request, Config config, FilterBtnM filterBtnM, int layer, int index, ProductGroupM productGroupM, List<FilterBtnM> allFilter) {
        FilterButtonVO filterButtonVO = new FilterButtonVO();
        try {
            List<ProductActivityM> activities = activityCxt.getSource(ProductActivitiesFetcher.CODE);
            // 1. 标准字段, 直接设置
            filterButtonVO.setFilterBtnId(filterBtnM.getFilterId());
            filterButtonVO.setTag(filterBtnM.getTag());

            // 2. 非标字段, 通过扩展点设置
            filterButtonVO.setTitle(title(activityCxt, filterBtnM, layer, index, activities));
            filterButtonVO.setImage(image(activityCxt, filterBtnM, activities, allFilter));
            filterButtonVO.setIcon(icon(activityCxt, filterBtnM, activities, allFilter));
            filterButtonVO.setSelectedTitle(selectedTitle(activityCxt, filterBtnM, layer, index));
            filterButtonVO.setChildrenMinShowNum(config.getChildrenMinShowNum());
            filterButtonVO.setLabs(labs(activityCxt, request, filterBtnM, index));
            filterButtonVO.setChildren(buildChildren(activityCxt, request, config, filterBtnM.getChildren(), layer + 1, productGroupM, allFilter));
            filterButtonVO.setFilterTipsBar(tipsBar(activityCxt, filterBtnM.getFilterId()));
            filterButtonVO.setExtra(buildExtra(activityCxt, productGroupM, activityCxt.getSource(ShelfDouHuFetcher.CODE), filterBtnM));
            filterButtonVO.setSubTitle(subTitle(activityCxt, filterBtnM, layer, index));

            // 3. 兼容
            filterButtonVO.setSelected(filterBtnM.isSelected());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return filterButtonVO;
    }

    private String buildExtra(ActivityCxt activityCxt, ProductGroupM productGroupM, List<DouHuM> douHuMS, FilterBtnM filterBtnM) {
        try {
            FilterExtraVP<?> filterExtraVP = findVPoint(activityCxt, FilterExtraVP.CODE);
            return filterExtraVP.execute(activityCxt,
                    FilterExtraVP.Param.builder().productGroupM(productGroupM).douHuList(douHuMS).filterBtnM(filterBtnM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private List<FilterButtonVO> buildChildren(ActivityCxt activityCxt, Request request, Config config, List<FilterBtnM> children, int layer, ProductGroupM productGroupM, List<FilterBtnM> allFilter) {
        if (CollectionUtils.isEmpty(children)) {
            return Lists.newArrayList();
        }
        AtomicInteger index = new AtomicInteger(1);
        return children.stream()
                .map(filterButtonM -> builderFilterButtonVO(activityCxt, request, config, filterButtonM, layer, index.getAndIncrement(), productGroupM, allFilter))
                .collect(Collectors.toList());
    }

    private DzPictureComponentVO image(ActivityCxt activityCxt, FilterBtnM filterBtnM, List<ProductActivityM> activities, List<FilterBtnM> allFilter) {

        try {
            FilterBtnImageVP<?> filterBtnImageVP = findVPoint(activityCxt, FilterBtnImageVP.CODE);
            return filterBtnImageVP.execute(activityCxt,
                    FilterBtnImageVP.Param.builder().filterBtnM(filterBtnM).allFilter(allFilter).activities(activities).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private DzPictureComponentVO icon(ActivityCxt activityCxt, FilterBtnM filterBtnM, List<ProductActivityM> activities, List<FilterBtnM> allFilter) {

        try {
            FilterBtnIconVP<?> filterBtnIconVP = findVPoint(activityCxt, FilterBtnIconVP.CODE);
            return filterBtnIconVP.execute(activityCxt,
                    FilterBtnIconVP.Param.builder().filterBtnM(filterBtnM).allFilter(allFilter).activities(activities).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private RichLabelVO title(ActivityCxt activityCxt, FilterBtnM filterBtnM, int layer, int index, List<ProductActivityM> activities) {
        try {
            FilterTitleVP<?> filterTitleVP = findVPoint(activityCxt, FilterTitleVP.CODE);
            return filterTitleVP.execute(activityCxt,
                    FilterTitleVP.Param.builder()
                            .filterBtnM(filterBtnM)
                            .layer(layer)
                            .index(index)
                            .activities(activities)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private RichLabelVO subTitle(ActivityCxt activityCxt, FilterBtnM filterBtnM, int layer, int index) {
        try {
            FilterSubTitleVP<?> filterTitleVP = findVPoint(activityCxt, FilterSubTitleVP.CODE);
            return filterTitleVP.execute(activityCxt,
                    FilterSubTitleVP.Param.builder()
                            .filterBtnM(filterBtnM)
                            .layer(layer)
                            .index(index)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private RichLabelVO selectedTitle(ActivityCxt activityCxt, FilterBtnM filterBtnM, int layer, int index) {
        try {
            FilterSelectedTitleVP<?> filterSelectedTitleVP = findVPoint(activityCxt, FilterSelectedTitleVP.CODE);
            return filterSelectedTitleVP.execute(activityCxt,
                    FilterSelectedTitleVP.Param.builder()
                            .filterBtnM(filterBtnM)
                            .layer(layer)
                            .index(index)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private String labs(ActivityCxt activityCxt, Request request, FilterBtnM filterBtnM, int index) {
        try {
            FilterBtnLabsVP<?> filterBtnLabsVP = findVPoint(activityCxt, FilterBtnLabsVP.CODE);
            return filterBtnLabsVP.execute(activityCxt,
                    FilterBtnLabsVP.Param.builder()
                            .btn(filterBtnM).dpPoiId(PoiIdUtil.getDpPoiIdL(request)).mtPoiId(PoiIdUtil.getMtPoiIdL(request))
                            .platform(request.getPlatform()).shop(request.getCtxShop()).index(index)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private TipsBarVO tipsBar(ActivityCxt activityCxt, long filterId) {
        try {
            FilterTipsBarVP<?> filterTipsBarVP = findVPoint(activityCxt, FilterTipsBarVP.CODE);
            return filterTipsBarVP.execute(activityCxt,
                    FilterTipsBarVP.Param.builder().filterId(filterId).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 旧框架的适配配置
         */
        private OldEngineAdaptCfg oldEngineCfg;
        /**
         * 最少展示个数
         */
        private int minShowNum;
        /**
         * 子标签最少展示个数
         */
        private int childrenMinShowNum;
        /**
         * 显示样式，1：第一层下划线，第二层圆角；2：全圆角
         * com.sankuai.dzviewscene.productshelf.vo.enums.FilterShowTypeEnums
         */
        private int showType;

        private Map<String, Integer> douhuToShowTypeMap;
    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * {@link ShelfActivityConstants.Params#dpPoiId}
         */
        private int dpPoiId;
        private long dpPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#mtPoiId}
         */
        private int mtPoiId;
        private long mtPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#platform}
         */
        private int platform;

        /**
         * {@link ShelfActivityConstants.Ctx#ctxShop}
         */
        private ShopM ctxShop;
    }
}