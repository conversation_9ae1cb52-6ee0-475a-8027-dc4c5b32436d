package com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@VPoint(name = "筛选-icon", description = "筛选-icon", code = FilterBtnIconVP.CODE, ability = FilterBuilder.CODE)
public abstract class FilterBtnIconVP<T> extends PmfVPoint<DzPictureComponentVO, FilterBtnIconVP.Param, T> {

    public static final String CODE = "FilterIConVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 筛选按钮
         */
        private FilterBtnM filterBtnM;

        private List<ProductActivityM> activities;

        private List<FilterBtnM> allFilter;
    }
}
