package com.sankuai.dzviewscene.product.shelf.options.builder.filter.image;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterBtnImageVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import lombok.Data;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@VPointOption(name = "指定筛选Id映射图片按钮",
        description = "",
        code = "FilterIdMapFilterBtnImageOpt")
public class FilterIdMapFilterBtnImageOpt extends FilterBtnImageVP<FilterIdMapFilterBtnImageOpt.Config> {

    private static final int DEFAULT_ACTIVITY_HEIGHT = 16;

    @Override
    public DzPictureComponentVO compute(ActivityCxt activityCxt, Param param, Config config) {
        Map<Long, DzPictureComponentVO> filterId2ImageCfg = config.getFilterId2ImageCfg() == null ? Maps.newHashMap() : config.getFilterId2ImageCfg();
        // 根据id判断
        DzPictureComponentVO componentVO = filterId2ImageCfg.get(param.getFilterBtnM().getFilterId());
        if (Objects.nonNull(componentVO)) {
            return componentVO;
        }
        // 根据时间和id判断
        DzPictureComponentVO componentVObyTime = getFilterImageByTime(param, config);
        if (Objects.nonNull(componentVObyTime)) {
            return componentVObyTime;
        }
        //tab活动样式
        DzPictureComponentVO activityStyle = getActivityStyle(param.getFilterBtnM(), getActivityPicHeight(config));
        if (Objects.nonNull(activityStyle)) {
            return activityStyle;
        }
        return null;
    }

    private int getActivityPicHeight(Config config) {
        if(config.getActivityPicHeight() <= 0){
            return DEFAULT_ACTIVITY_HEIGHT;
        }
        return config.getActivityPicHeight();
    }

    /**
     * 根据时间和id判断筛选项图片
     *
     * @param param
     * @param config
     * @return
     */
    private DzPictureComponentVO getFilterImageByTime(Param param, Config config) {
        try {
            if (!config.isEnableId2UrlByTime()) {
                return null;
            }
            id2UrlByTimeConfigDTO timeConfig = config.getId2UrlByTimeCfg().get(param.getFilterBtnM().getFilterId());
            if (Objects.isNull(timeConfig)) {
                return null;
            }
            return inTimeRange(convert(timeConfig.getBeginTime()), convert(timeConfig.getEndTime())) ? timeConfig.getComponentVO() : null;
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }


    /**
     * 是否在时间区间里
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    private boolean inTimeRange(LocalTime beginTime, LocalTime endTime) {
        LocalTime md = LocalTime.now();
        return md.isBefore(endTime) || md.isAfter(beginTime);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * key-filterId
         */
        private Map<Long, DzPictureComponentVO> filterId2ImageCfg;
        /**
         * 根据时间替换图片开关
         */
        private boolean enableId2UrlByTime = false;
        private Map<Long, id2UrlByTimeConfigDTO> id2UrlByTimeCfg;

        /**
         * 营销活动tab图片高度
         */
        private int activityPicHeight;
    }

    /**
     * 根据时间替换图片 内部类
     */
    @Data
    private static class id2UrlByTimeConfigDTO {
        private String beginTime = "22:59:59";
        private String endTime = "06:00:00";
        private DzPictureComponentVO componentVO;
    }

    private LocalTime convert(String timeStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        return LocalTime.parse(timeStr, formatter);
    }
}
