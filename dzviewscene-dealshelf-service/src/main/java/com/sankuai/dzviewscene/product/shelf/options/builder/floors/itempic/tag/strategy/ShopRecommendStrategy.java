package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.google.common.collect.ImmutableMap;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 商家推荐
 *
 * <AUTHOR>
 * @date 2023/3/28
 */
@Component
public class ShopRecommendStrategy extends AbstractFloatTagBuildStrategy {

    private final static Map<Integer, String> SHELF_SHOW_TYPE_TO_PIC_KEY = new ImmutableMap.Builder<Integer, String>()
            .put(ShelfShowTypeEnum.SINGLE_BIG_PIC_SHELF.getType(), "https://p0.meituan.net/ingee/c48814c47298d0833a9d14e2e98868f44708.png")
            .put(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType(), "https://p0.meituan.net/ingee/3481ab63ef3b7784ad12dbe85e4e395d4644.png")
            .put(ShelfShowTypeEnum.DOUBLE_BIG_PIC_SHELF.getType(), "https://p0.meituan.net/ingee/4769194b38ad672570aa032e007691064932.png")
            .put(ShelfShowTypeEnum.SINGLE_NEW_BIG_PIC_SHELF.getType(), "https://p0.meituan.net/travelcube/a2875f850c5f504d7aff5d4c4354d2b57239.png")
            .put(ShelfShowTypeEnum.DOUBLE_NEW_BIG_PIC_SHELF.getType(), "https://p0.meituan.net/travelcube/a2875f850c5f504d7aff5d4c4354d2b57239.png")
            .build();

    @Override
    public String getName() {
        return ShopRecommendStrategy.class.getSimpleName();
    }

    @Override
    public boolean isMatch(FloatTagBuildReq param, FloatTagBuildCfg config) {
        return ProductMAttrUtils.isShopRecommend(param.getProductM(), param.getFilterId()) || isMaterialRecommended(param);
    }

    @Override
    public FloatTagVO buildTag(FloatTagBuildReq param, FloatTagBuildCfg config) {
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        String url = SHELF_SHOW_TYPE_TO_PIC_KEY.get(param.getShelfShowType());
        url = StringUtils.isNotEmpty(url) ? url : "https://p0.meituan.net/ingee/c48814c47298d0833a9d14e2e98868f44708.png";
        floatTagPic.setPicUrl(url);
        floatTagPic.setAspectRadio(getAspectRadio(param.getShelfShowType()));
        if (SHELF_SHOW_TYPE_TO_PIC_HEIGHT.get(param.getShelfShowType()) != null) {
            floatTagPic.setPicHeight(SHELF_SHOW_TYPE_TO_PIC_HEIGHT.get(param.getShelfShowType()));
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(getPriceTagPosition(param));
        return floatTagVO;
    }

    private int getPriceTagPosition(FloatTagBuildReq param) {
        return FloatTagPositionEnums.LEFT_TOP.getPosition();
    }

    private boolean isMaterialRecommended(FloatTagBuildReq param) {
        List<DealProductMaterialM> materialList = param.getProductM().getMaterialList();
        if (CollectionUtils.isEmpty(materialList)) {
            return false;
        }
        return materialList.stream().filter(t ->
                StringUtils.isNotBlank(t.getName()) && StringUtils.isNotBlank(t.getPic()))
                .anyMatch(dealProductMaterialM -> dealProductMaterialM.getRecommended() == 1);
    }
}
