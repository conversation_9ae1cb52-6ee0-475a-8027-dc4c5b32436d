package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.DzTagStyleWrapUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/18 17:01
 */
@VPointOption(name = "足疗优惠感知",
        description = "足疗优惠感知",
        code = "MassageItemPriceBottomTagsV2Opt"
)
public class MassageItemPriceBottomTagsV2Opt extends ItemPriceBottomTagsVP<MassageItemPriceBottomTagsV2Opt.Config> {

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        List<DzTagVO> result = Lists.newArrayList();
        if (Objects.isNull(config)) return result;
        // 优惠标签
        DzTagVO promoTag = fillPromoTag(param, config);
        CollectionUtils.addIgnoreNull(result, promoTag);
        // 买贵必赔/比价标签/价保标签
        DzTagVO priceTag = fillPriceTag(context, param, config);
        CollectionUtils.addIgnoreNull(result, priceTag);
        return result;
    }

    private DzTagVO fillPromoTag(Param param, Config config) {
        // 生成优惠标签
        DzTagVO promoDzTagVO = buildPriceBottomTag(param, config);
        DzTagStyleWrapUtils.overridePromoStyle(promoDzTagVO);
        return promoDzTagVO;
    }

    // 判断和构建「比价标签」（包括全网低价标签、时间比价标签(可选)）
    private DzTagVO fillPriceTag(ActivityCxt context, Param param, Config config) {
        // 判断是否展示比价标签
        if (isHidePriceTag(param, config)) return null;
        // 生成比价标签
        return convertStyle(PriceDisplayUtils.buildPriceTagContainsProtectionAndGuarantee(
                        param.getProductM(), param.getPlatform(), true, true, config.isNeedPriceProtectionTag(), isNeedPriceGuaranteeTag(context, config)),
                param.getPlatform());
    }

    /**
     * 结合实验，判断是否展示买贵必赔
     *
     * @param context
     * @param config
     * @return
     */
    public boolean isNeedPriceGuaranteeTag(ActivityCxt context, Config config) {
        // 若不指定实验，则默认展示买贵必赔标签（这是足疗专用Opt）
        if (CollectionUtils.isEmpty(config.getNeedPriceGuaranteeTagBySkList())) {
            return true;
        }
        // 根据实验判断是否展示比价标签
        List<DouHuM> douHuMList = context.getSource(ShelfDouHuFetcher.CODE);
        return !CollectionUtils.isNotEmpty(douHuMList) ||
                !Collections.disjoint(config.getNeedPriceGuaranteeTagBySkList(), douHuMList.stream().map(DouHuM::getSk).collect(Collectors.toList()));
    }

    /**
     * 样式更换
     *
     * @return
     */
    private DzTagVO convertStyle(DzTagVO tagVO, int platform) {
        if (Objects.isNull(tagVO)) {
            return null;
        }
        if (Objects.nonNull(tagVO.getName()) && tagVO.getName().equals("买贵必赔")) {
            return tagVO;
        }
        tagVO.setHasBorder(true);
        tagVO.setBackground(null);
        tagVO.setBorderRadius(3);
        if (PlatformUtil.isMT(platform)) {
            tagVO.setBorderColor(ColorUtils.colorFF4B10);
            tagVO.setTextColor(ColorUtils.colorFF4B10);
        } else {
            tagVO.setBorderColor(ColorUtils.colorFF6633);
            tagVO.setTextColor(ColorUtils.colorFF6633);
        }
        return tagVO;
    }

    /**
     * 是否隐藏比价标签，默认为 false
     *
     * @param param
     * @param config
     * @return
     */
    private boolean isHidePriceTag(Param param, Config config) {
        // 根据开关，选择是否启用"有秒杀就不展示比价标签"逻辑
        return Objects.nonNull(config) && config.enableSecKillHidePriceTag && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM());
    }

    private DzTagVO buildPriceBottomTag(Param param, Config config) {
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(param.getProductM().getPromoPrices());
        if (CollectionUtils.isEmpty(param.getProductM().getPromoPrices()) || MapUtils.isEmpty(promoPriceMMap)) {
            return null;
        }

        // 预售优先级最高
        DzTagVO preSaleDzTag = buildPreSaleTag(param, promoPriceMMap, config);
        if (Objects.nonNull(preSaleDzTag)) {
            return preSaleDzTag;
        }
        //包含商家会员优惠
        boolean hasMerchantMemberPromo = MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(param.getProductM().getPromoPrices());
        if(MapUtils.isNotEmpty(promoPriceMMap) && hasMerchantMemberPromo){
            // 这里已经确保 会员比其他标签便宜，返回 商家会员标签
            MerchantMemberProductPromoData merchantMemberPromo = MerchantMemberPromoUtils.getMerchantMemberPromo(param.getProductM());
            if(merchantMemberPromo.getProductPromoPrice() != null){
                return ProductMPromoInfoUtils.buildMerchantMemberPromoTag(param.getProductM(), param.getPlatform(), param.getSalePrice(), merchantMemberPromo, config.getPopType());
            }
        }
        // 会员
        ProductPromoPriceM memberPromoM = ProductMPromoInfoUtils.getMemberPromoPriceM(param.getProductM(), param.getCardM());
        // 非会员标签
        ProductPromoPriceM noMemberPromoM = this.getNoMemberPromoM(param);

        if (Objects.nonNull(memberPromoM) && (Objects.isNull(noMemberPromoM) || memberPromoM.getPromoPrice().compareTo(noMemberPromoM.getPromoPrice()) < 0)) {
            // 会员比其他标签便宜，返回 会员标签
            return buildMemberTag(param.getPlatform(), memberPromoM, config);
        } else if (Objects.nonNull(noMemberPromoM) && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
            // 有秒杀，返回秒杀标签
            return buildSecKillTag(param, noMemberPromoM, config);
        } else if (Objects.nonNull(noMemberPromoM)) {
            // 非会员、非秒杀
            return buildNoMemberTag(param.getPlatform(), noMemberPromoM, config);
        }
        return null;
    }

    /**
     * 判断并构建秒杀标签
     *
     * @param param
     * @param noMemberPromoM
     * @param config
     * @return
     */
    private DzTagVO buildSecKillTag(Param param, ProductPromoPriceM noMemberPromoM, Config config) {
        // 秒杀单独实现
        DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(Lists.newArrayList(noMemberPromoM), param.getProductM().getMarketPrice(), param.getSalePrice(), param.getPlatform(), config.getPopType());
        if (Objects.nonNull(secKillDzTag)) {
            secKillDzTag.setPrePic(new DzPictureComponentVO(
                    PlatformUtil.isMT(param.getPlatform()) ? ProductMPromoInfoUtils.MT_SEC_KILL_TAG_URL : ProductMPromoInfoUtils.DP_SEC_KILL_TAG_URL, 3.25));
            secKillDzTag.setAfterPic(this.buildAfterPic(param.getPlatform(), false));
            secKillDzTag.setBorderRadius(PlatformUtil.isMT(param.getPlatform()) ? 3 : 1);
            return secKillDzTag;
        }
        return null;
    }

    /**
     * 判断并构建预售标签
     *
     * @param param
     * @param promoPriceMMap
     * @param config
     * @return
     */
    private DzTagVO buildPreSaleTag(Param param, Map<Integer, ProductPromoPriceM> promoPriceMMap, Config config) {
        // 预售优先级最高
        ProductPromoPriceM preSalePromoPrice = null;
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_Member.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_Member.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_NewUser.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_NewUser.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale.getCode())) {
            preSalePromoPrice = promoPriceMMap.get((PromoTagTypeEnum.PreSale.getCode()));
        }
        if (Objects.nonNull(preSalePromoPrice)) {
            return this.buildNoMemberTag(param.getPlatform(), preSalePromoPrice, config);
        }
        return null;
    }

    private ProductPromoPriceM getNoMemberPromoM(Param param) {
        List<ProductPromoPriceM> noMemberPriceM = param.getProductM().getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                // 过滤掉 使用了会员优惠的Promo
                .filter(a -> !CardPromoUtils.CARD_PROMOS.contains(a.getPromoType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }

    private DzTagVO buildNoMemberTag(int platform, ProductPromoPriceM productPromoPriceM, Config config) {
        DzTagVO dzTagVO = this.buildPromoTagVo(platform, productPromoPriceM);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        dzTagVO.setPrePic(new DzPictureComponentVO(productPromoPriceM.getIcon(), 3.25));
        dzTagVO.setAfterPic(this.buildAfterPic(platform, false));
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(productPromoPriceM, config.getPopType()));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return dzTagVO;
    }

    private DzTagVO buildMemberTag(int platform, ProductPromoPriceM memberPromoM, Config config) {
        DzTagVO memberDzTagVO = this.buildPromoTagVo(platform, memberPromoM);
        memberDzTagVO.setPrePic(new DzPictureComponentVO(memberPromoM.getIcon(), 3.25));
        memberDzTagVO.setAfterPic(this.buildAfterPic(platform, true));
        memberDzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(memberPromoM, config.getPopType()));
        memberDzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return memberDzTagVO;
    }

    /**
     * 构建 AfterPic
     *
     * @param platform
     */
    private DzPictureComponentVO buildAfterPic(int platform, boolean isMember) {
        if (isMember) {
            return new DzPictureComponentVO("https://p0.meituan.net/travelcube/b5ef0237b2cb19d166956ce499b6f0bb495.png", 0.875);
        }
        if (PlatformUtil.isMT(platform)) {
            return new DzPictureComponentVO("https://p0.meituan.net/travelcube/ac0b1b7e016f85fcf9b8784d34d1bc14439.png", 1);
        } else {
            return new DzPictureComponentVO("https://p1.meituan.net/travelcube/4b5a342d1d0c2ae89ea51d8a2a6f7d75459.png", 1);
        }
    }

    /**
     * 构建优惠感知版本的priceBottomTag，有特别的样式逻辑
     *
     * @return
     */
    private DzTagVO buildPromoTagVo(int platform, ProductPromoPriceM productPromoPriceM) {
        DzTagVO basicTagVo;
        if (Objects.nonNull(productPromoPriceM.getPromoPriceTag()) && productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())) {
            // 会员样式不一样
            basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColor(platform, productPromoPriceM.getPromoTag(),
                    ColorUtils.color8C5819, null, null, "#8E3C12", "#FFEDDE");
        } else {
            basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColor(platform, productPromoPriceM.getPromoTag(),
                    ColorUtils.colorFF4B10, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, ColorUtils.colorFFF2EE);
        }
        // 点评侧的「新客特惠」、「特惠促销」样式不一样
        if (Objects.nonNull(basicTagVo) && !PlatformUtil.isMT(platform) && Objects.nonNull(productPromoPriceM.getPromoTagType()) &&
                (productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.NewUser.getCode()) || productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Other.getCode()))) {
            basicTagVo.setHasBorder(true);
            basicTagVo.setBorderColor(ColorUtils.colorFFCFBF);
            basicTagVo.setBackground(null);
        }

        return basicTagVo;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 弹窗样式
         */
        private int popType = 3;
        /**
         * 有秒杀时，是否不展示比价标签
         */
        private boolean enableSecKillHidePriceTag = false;
        /**
         * 是否展示价保标签
         */
        private boolean needPriceProtectionTag;
        /**
         * 根据实验是否展示买贵必赔标签
         */
        private List<String> needPriceGuaranteeTagBySkList;
    }
}
