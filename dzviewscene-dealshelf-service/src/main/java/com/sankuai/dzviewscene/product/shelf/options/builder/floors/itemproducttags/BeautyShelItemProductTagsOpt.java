package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.config.BeautyHairTagConfig;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/15
 */
@VPointOption(name = "丽人三美-副标题构造",
        description = "",
        code = "BeautyShelItemProductTagsOpt")
public class BeautyShelItemProductTagsOpt extends ItemProductTagsVP<BeautyShelItemProductTagsOpt.Config> {

    /**
     * 美发团购类目id
     */
    private static final int BEAUTY_HAIR_DEAL_CATEGORY = 501;

    /**
     * 美甲美睫团购类目id
     */
    private static final int BEAUTY_MEIJIAMEIJIE_DEAL_CATEGORY = 502;
    /**
     * 美容SPA 类目ID
     */
    private static final int SPA_DEAL_CATEGORY = 503;

    private static final String ATTR_VALID_PRODUCT_TAG_ID = "attr_validProductTagIds";

    private static final String ATTR_DEAL_STRUCT_DETAIL = "attr_deal_struct_detail";

    private static final String SERVICE_EFFECT = "serviceeffect";

    private static final long TAG_ID = 100069568;

    private static final int FIVE = 5;

    private static final int TWO = 2;

    private static final int DEPILATION_DEAL_CATEGORY = 514;

    private static final String SERVICE_ITEM_CATEGORY = "service_item_category";

    private static final String DEPILATION = "脱毛";

    private static final String SKIN_MANAGEMENT = "皮肤管理";

    private static final String DURATION = "duration";

    private static final String SERVICE_STEP = "servicestep";

    private static final String CATEGORY2 = "category2";

    private static final String SUB_STEP_NAME = "subStepName";

    private static final String BODY_NAME = "bodyname";

    private static final String SUITABLE_BODY_PARTS = "suitable_body_parts";

    private static final String SERVICE_EFFECT_ATTR = "service_effect";

    /**
     * 美甲-服务项目-跳色 id
     */
    private static final int MEI_JIA_STRUCT_TIAO_SE_CATEGORY = 4043;

    /**
     * 美甲-服务项目-甲片
     */
    private static final int ME_JIA_STRUCT_JIA_PIAN_CATEGORY = 4041;

    /**
     * 美甲-服务项目-延长甲
     */
    private static final int ME_JIA_STRUCT_YAN_CHANG_JIA_CATEGORY = 4042;

    /**
     * 美睫-适用部位
     */
    private static final String EYELASH_SUIT_PART = "eyelash_suit_part";

    private static final String  PURE_COLOR_TEXT = "纯色";


    @Override
    public List<String> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        //美发团单
        if (productM.getCategoryId() == BEAUTY_HAIR_DEAL_CATEGORY) {
            return getBeautyHairTagsNew(productM, param.getFilterId(), config.getHairConfig());
        }
        //美甲美睫团单
        if (productM.getCategoryId() == BEAUTY_MEIJIAMEIJIE_DEAL_CATEGORY) {
            return getMeiJiaMeiJieTags(productM);
        }
        //老逻辑
        if (hitOldProductTagsAB(config, param, productM.getCategoryId(), context.getSceneCode())) {
            return productM.getProductTags();
        }
        //美容SPA标签列表
        return getBeautySpaTags(productM, config);
    }

    private boolean hitOldProductTagsAB(Config config, Param param, int categoryId, String sceneCode) {
        //商品类目为空或未命中商品类目白名单，展示老逻辑
        if (CollectionUtils.isEmpty(config.getProductCategoryIds()) || !config.getProductCategoryIds().contains(categoryId)) {
            return true;
        }
        //非美容SPA场景直接展示新的标签逻辑
        if (!"beauty_spa_deal_shelf".equals(sceneCode)) {
            return false;
        }
        //美容SPA场景需要命中相关斗斛SK才可以展示老的标签逻辑
        return DouHuUtils.hitAnySk(param.getDouHuMList(), config.douHuSkList);
    }

    /**
     * 丽人通用的标签列表
     * @param productM
     * @return
     */
    private List<String> getBeautySpaTags(ProductM productM, Config config) {
        List<SkuItemDto> optionalSkuList = DealStructUtils.getOptionalSkuList(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL));
        List<SkuItemDto> mustSkuList = DealStructUtils.getMustSkuList(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL));
        //命中标签ID=新皮管 100069568 且 serviceType 为 【皮肤管理】
        if (hitSpecialTagId(productM.getAttr(ATTR_VALID_PRODUCT_TAG_ID)) && StringUtils.equals(ProductMAttrUtils.getServiceType(productM), SKIN_MANAGEMENT)) {
            return getNewSkinTags(mustSkuList, productM);
        }
        if (CollectionUtils.isNotEmpty(optionalSkuList)) {
            //服务项目存在N选1的情况，仅展示【功效】这1个副标题，做多展示5个功效值（支持展示商家自定义的功效值）
            return getFirstServiceEffect(DealStructUtils.getTotalSkuItem(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL)));
        }
        if (CollectionUtils.isEmpty(mustSkuList)) {
            return Lists.newArrayList();
        }
        // 脱毛返回副标题
        if (productM.getCategoryId() == SPA_DEAL_CATEGORY
                && DEPILATION.equals(ProductMAttrUtils.getServiceType(productM))
                && Objects.equals(mustSkuList.get(0).getProductCategory(), 2104709L)
                && CollectionUtils.isNotEmpty(productM.getProductTags())) {
            if(CollectionUtils.isNotEmpty(productM.getProductTags())){
                return productM.getProductTags().stream().map(item->item.replaceAll("、"," ")).collect(Collectors.toList());
            }
            return productM.getProductTags();
        }
        //命中脱毛团单规则，不展示副标题
        if (hitDepilation(productM)) {
            return Lists.newArrayList();
        }
        //兜底副标题列表
        return getDefaultBeautyCommonTags(mustSkuList, config);
    }

    private List<String> getFirstServiceEffect(List<SkuItemDto> skuItems) {
        List<String> serviceEffects = getAttrValuesByLimit(skuItems, SERVICE_EFFECT, FIVE);
        if (CollectionUtils.isEmpty(serviceEffects)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(joinNames(serviceEffects));
    }

    private List<String> getNewSkinTags(List<SkuItemDto> skuItems, ProductM productM) {
        //功效信息, 翻单前通过团单属性【service_effect】获取，翻单后通过货属性【serviceeffect】获取
        String serviceEffectTag = getProductAttrValuesByStr(getTagFromSkuValueIfAttrValueIsNull(skuItems, productM, SERVICE_EFFECT, SERVICE_EFFECT_ATTR));
        //核心步骤文案
        String serviceStep = getServiceStep(skuItems);
        //时长
        String duration = DealStructUtils.getFirstAttrValueByAttrKey(skuItems, DURATION);
        //部位，翻单前通过团单属性【suitable_body_parts】获取，翻单后通过货属性【bodyname】获取
        String suitableBodyPart = getProductAttrValuesByStr(getTagFromSkuValueIfAttrValueIsNull(skuItems, productM, BODY_NAME, SUITABLE_BODY_PARTS));
        List<String> tags = new ArrayList<>();
        addTagIfNoNull(tags, serviceEffectTag);
        addTagIfNoNull(tags, serviceStep);
        addTagIfNoNull(tags, duration);
        addTagIfNoNull(tags, suitableBodyPart);
        return tags;
    }

    /**
     * @param skuAttrKey 货属性key
     * @param attrName 团单属性key
     * @return
     */
    private String getTagFromSkuValueIfAttrValueIsNull(List<SkuItemDto> skuItems, ProductM productM, String skuAttrKey, String attrName) {
        String attr = productM.getAttr(attrName);
        if(StringUtils.isNotEmpty(attr)){
            return attr;
        }
        return DealStructUtils.getFirstAttrValueByAttrKey(skuItems, skuAttrKey);
    }

    private String joinNames(List<String> names) {
        return StringUtils.join(names, " ");
    }

    private String getProductAttrValuesByStr(String attrValueStr) {
        if (StringUtils.isEmpty(attrValueStr)) {
            return null;
        }
        return attrValueStr.replaceAll(",", " ").replaceAll("、", " ");
    }

    private List<String> getDefaultBeautyCommonTags(List<SkuItemDto> skuItems, Config config) {
        //功效信息
        List<String> tags = new ArrayList<>();
        addTagIfNoNull(tags, getAllServiceEffectByLimit(skuItems, TWO));
        if (skuItems.size() >= TWO) {
            return tags;
        }
        //时长
        SkuAttrItemDto durationAttr = DealStructUtils.getFirstMatchSkuAttrInListByAttrName(skuItems, DURATION);
        if (durationAttr != null && StringUtils.isNotEmpty(durationAttr.getRawAttrValue())) {
            addTagIfNoNull(tags, String.format("%s分钟", durationAttr.getRawAttrValue()));
        }
        //部位
        String bodyNames = getNamesByWhiteList(getAttrValuesByLimit(skuItems, BODY_NAME, 0), config.tagNameList);
        addTagIfNoNull(tags, bodyNames);
        return tags;
    }


    private String getNamesByWhiteList(List<String> tags, List<String> whiteNameList) {
        if (CollectionUtils.isEmpty(tags) || CollectionUtils.isEmpty(whiteNameList)) {
            return joinNames(tags);
        }
        List<String> currentTags = tags.stream().filter(whiteNameList::contains).collect(Collectors.toList());
        return joinNames(currentTags);
    }

    private String getServiceStep(List<SkuItemDto> skuItems) {
        List<Map<String, String>> serviceSteps = JsonCodec.decode(DealStructUtils.getFirstAttrValueByAttrKey(skuItems, SERVICE_STEP), new TypeReference<List<Map<String, String>>>() {});
        String category2 = DealStructUtils.getFirstAttrValueByAttrKey(skuItems, CATEGORY2);
        if (CollectionUtils.isEmpty(serviceSteps) || StringUtils.isEmpty(category2)) {
            return null;
        }
        //当团单货属性“二级分类”=“超微小气泡”，或“服务步骤-二级步骤名称”中包含“小气泡清洁”时，副标题展示文案：“含小气泡”
        if ("超微小气泡".equals(category2) || subStepNameContainSpecialName(serviceSteps, "小气泡清洁")) {
            return "含小气泡";
        }
        //当团单货属性“二级分类”=“大气泡”时，副标题展示文案：“含大气泡”
        if ("大气泡".equals(category2)) {
            return "含大气泡";
        }
        //当团单货属性“二级分类”=“海菲秀”，或“服务步骤-二级步骤名称”中包含“海菲秀”时，副标题展示文案“含海菲秀”
        if ("海菲秀".equals(category2) || subStepNameContainSpecialName(serviceSteps, "海菲秀")) {
            return "含海菲秀";
        }
        //当团单货属性“二级分类”=“无针水光”，或“服务步骤-二级步骤名称”中包含“无针水光”时，副标题展示文案“含无针水光”
        if ("无针水光".equals(category2) || subStepNameContainSpecialName(serviceSteps, "无针水光")) {
            return "含无针水光";
        }
        return null;
    }

    private boolean subStepNameContainSpecialName(List<Map<String, String>> serviceSteps, String name) {
        return serviceSteps.stream()
                .filter(map -> MapUtils.isNotEmpty(map) && StringUtils.isNotEmpty(map.get(SUB_STEP_NAME)) && map.get(SUB_STEP_NAME).contains(name))
                .findFirst()
                .map(map -> true)
                .orElse(false);
    }

    private boolean hitDepilation(ProductM productM) {
        //团单二级分类=514 或 团单service_type=“脱毛” 或  团单service_item_category =“脱毛”
        return productM.getCategoryId() == DEPILATION_DEAL_CATEGORY
                || DEPILATION.equals(ProductMAttrUtils.getServiceType(productM))
                || DEPILATION.equals(productM.getAttr(SERVICE_ITEM_CATEGORY));
    }

    private boolean hitSpecialTagId(String tagIdStr) {
        if (StringUtils.isEmpty(tagIdStr)) {
            return false;
        }
        List<Long> tagIds = JsonCodec.decode(tagIdStr, new TypeReference<List<Long>>() {
        });
        return CollectionUtils.isNotEmpty(tagIds) && tagIds.contains(TAG_ID);
    }

    private List<String> getAttrValuesByLimit(List<SkuItemDto> skuItems, String attrKey, int countLimit) {
        String firstSkuItemAttrValueStr = DealStructUtils.getFirstAttrValueByAttrKey(skuItems, attrKey);
        if (StringUtils.isEmpty(firstSkuItemAttrValueStr)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(firstSkuItemAttrValueStr.split("、", countLimit));
    }

    private String getAllServiceEffectByLimit(List<SkuItemDto> skuItems, int countLimit) {
        List<String> serviceEffectNames = DealStructUtils.getAllAttrValueByAttrKey(skuItems, SERVICE_EFFECT);
        if (CollectionUtils.isEmpty(serviceEffectNames)) {
            return null;
        }
        List<String> tags = serviceEffectNames.stream()
                .map(serviceEffectName -> serviceEffectName.split("、"))
                .flatMap(Arrays::stream)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        if (tags.size() <= countLimit) {
            return joinNames(tags);
        }
        return joinNames(tags.subList(0, countLimit));
    }

    /************** 美发·501  **************/

    /**
     * @param skuItemDtoList
     * @return 漂色标签
     */
    private String getTangRanPiaoSeTag(List<SkuItemDto> skuItemDtoList) {
        for (SkuItemDto skuItemDto : skuItemDtoList) {
            if (skuItemDto.getProductCategory() == 3112) {
                SkuAttrItemDto category1 = DealStructUtils.getSkuAttrByAttrName(skuItemDto, "category1");
                SkuAttrItemDto containsCnt = DealStructUtils.getSkuAttrByAttrName(skuItemDto, "containsCount");
                if (category1 == null || !Objects.equals(category1.getAttrValue(), "漂色")) {
                    continue;
                }
                if (containsCnt != null && StringUtils.isNotEmpty(containsCnt.getAttrValue())) {
                    return String.format("含%s漂色", containsCnt.getAttrValue());
                }
                return "含漂色";
            }
        }
        return null;
    }

    /************** 美甲美睫·502  **************/

    private List<String> getMeiJiaMeiJieTags(ProductM productM) {
        if(StringUtils.equals(ProductMAttrUtils.getServiceType(productM), "美睫")){
            return buildMeiJieTags(productM);
        }
        return getMeiJiaTagsNew(productM);
    }

    private List<String> buildMeiJieTags(ProductM productM){
        List<String> tags = new ArrayList<>(productM.getProductTags());
        //美睫适用部位
        addTagIfNoNull(tags, productM.getAttr(EYELASH_SUIT_PART));
        //含卸睫
        addTagIfNoNull(tags, getMeiJieXieJieTag(productM));
        return tags;
    }
    private List<String> getMeiJiaTagsNew(ProductM productM) {
        String tagIdStr = ProductMAttrUtils.getProductTagIdStr(productM);
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL));
        List<String> resultTags = new ArrayList<>();
        if (StringUtils.isEmpty(tagIdStr)) {
            return resultTags;
        }
        //纯色/跳色美甲团单：“适用部位”“可选颜色”“含x指跳色”“含卸甲”四类副标题
        if (tagIdStr.contains("100075023")) {
            //若命中“纯色美甲”（标签id：100075023），则展示文案“纯色
            addTagIfNoNull(resultTags, PURE_COLOR_TEXT);
            //适用部位
            addTagIfNoNull(resultTags, productM.getAttr("nail_suit_part"));
            //可选颜色
            addTagIfNoNull(resultTags, getMeiJiaChooseColorTag(allSkus));
            //含x指跳色
            addTagIfNoNull(resultTags, getMeiJiaTiaoSeTag(allSkus));
            //含卸甲
            addTagIfNoNull(resultTags, getMeiJiaXieJiaTag(allSkus));
            return resultTags;
        }
        //款式美甲团单：存在“x种款式供参考”“含延长/甲片”“含卸甲”三类副标题
        if (tagIdStr.contains("100075024")) {
            //存在“x种款式供参考”
            if (CollectionUtils.isNotEmpty(productM.getProductTags())) {
                resultTags.addAll(productM.getProductTags());
            }
            boolean hasYanChangJia = allSkus.stream().anyMatch(o->o.getProductCategory() == ME_JIA_STRUCT_YAN_CHANG_JIA_CATEGORY);
            boolean hasJiaPian = allSkus.stream().anyMatch(o->o.getProductCategory() == ME_JIA_STRUCT_JIA_PIAN_CATEGORY);
            //含延长
            if (hasYanChangJia && !hasJiaPian) {
                resultTags.add("含延长");
            }
            //含甲片
            if (hasJiaPian && !hasYanChangJia) {
                resultTags.add("含甲片");
            }
            //含卸甲
            addTagIfNoNull(resultTags, getMeiJiaXieJiaTag(allSkus));
            return resultTags;
        }
        //其他团单没有标签
        return resultTags;
    }

    /**
     * 含x指跳色
     * 展示规则：
     * 1、美甲团单
     * 2、团单含有“涂色”服务项目且“涂色”下二级分类为“纯色/跳色”且“纯色/跳色”下服务类型“是否含跳色”的值为“是”
     * 3、x为“纯色/跳色”下跳色指数的值
     *
     * @param skus
     * @return
     */
    private String getMeiJiaTiaoSeTag(List<SkuItemDto> skus) {
        for (SkuItemDto sku : skus) {
            if (sku.getProductCategory() == MEI_JIA_STRUCT_TIAO_SE_CATEGORY) {
                //二级分类需为 "纯色/跳色"
                SkuAttrItemDto category2Attr = DealStructUtils.getSkuAttrByAttrId(sku, 1038);
                if (category2Attr == null || !Objects.equals(category2Attr.getAttrValue(), "纯色/跳色")) {
                    continue;
                }
                //跳色指数>0
                SkuAttrItemDto toningIndexAttr = DealStructUtils.getSkuAttrByAttrId(sku, 2551);
                if (toningIndexAttr == null || StringUtils.isEmpty(toningIndexAttr.getAttrValue())) {
                    continue;
                }
                int toningIndex = NumberUtils.toInt(toningIndexAttr.getRawAttrValue());
                if (toningIndex > 0) {
                    return String.format("含%d指跳色", toningIndex);
                }
            }
        }
        return "";
    }

    /**
     * 含卸甲 Tag
     * 展示规则：
     * 1、美甲团单
     * 2、团单有多个服务项目，且包含本次到店卸甲
     * @param skus
     * @return
     */
    private String getMeiJiaXieJiaTag(List<SkuItemDto> skus) {
        boolean isXieJia = skus.stream().anyMatch(sku-> {
            SkuAttrItemDto category2Attr = DealStructUtils.getSkuAttrByAttrId(sku, 1038);
            if (category2Attr == null) {
                return false;
            }
            return Objects.equals(category2Attr.getAttrValue(), "本次到店卸甲/卸睫二选一") || Objects.equals(category2Attr.getAttrValue(), "本次到店卸甲");
        });
        return isXieJia ? "含卸甲": null;
    }

    private String getMeiJieXieJieTag(ProductM productM) {
        String dealStructJson = productM.getAttr(ATTR_DEAL_STRUCT_DETAIL);
        if (StringUtils.isEmpty(dealStructJson)) {
            return null;
        }
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(dealStructJson);
        if (CollectionUtils.isEmpty(allSkus)) {
            return null;
        }
        //只有一个服务项目不展示"含卸睫"
        if(allSkus.size() <= 1){
            return null;
        }
        boolean isXieJie = allSkus
                .stream()
                .anyMatch(skuItemDto -> StringUtils.equals(skuItemDto.getName(), "本次到店卸睫毛"));
        return isXieJie ? "含卸睫" : null;
    }

    /**
     * 可选颜色
     * 展示规则：
     * 当纯色/跳色美甲团单的“涂色”的服务项目下属性“可选颜色”有值时，副标题展示“可选颜色”，展示逻辑如下：
     * ①可选颜色为“全店颜色任选（且保障店内至少有200色可选）”，副标题展示“全店颜色任选”
     * ②可选颜色为“至少150色可选”，副标题展示“150+色可选”
     * ③可选颜色为“至少100色可选”，副标题展示“100+色可选”
     * ④可选颜色为“至少50色可选”，副标题展示“50+色可选”
     * @param skus
     * @return
     */
    private String getMeiJiaChooseColorTag(List<SkuItemDto> skus) {
        SkuAttrItemDto skuAttrItemDto = DealStructUtils.getFirstMatchSkuAttrInListByAttrName(skus, "colorSelectableRange");
        if (skuAttrItemDto == null) {
            return null;
        }
        String rawShowValue = skuAttrItemDto.getAttrValue();
        if (rawShowValue.contains("全店颜色任选")) {
            return "全店颜色任选";
        }
        if (Objects.equals(rawShowValue, "至少150色可选")) {
            return "150+色可选";
        }
        if (Objects.equals(rawShowValue, "至少100色可选")) {
            return "100+色可选";
        }
        if (Objects.equals(rawShowValue, "至少50色可选")) {
            return "50+色可选";
        }
        return null;
    }

    private List<String> getBeautyHairTagsNew(ProductM productM, long filterId, BeautyHairTagConfig hairConfig) {
        if (hairConfig == null) {
            return new ArrayList<>();
        }
        String tagIdStr = productM.getAttr(ATTR_VALID_PRODUCT_TAG_ID);
        List<Long> tagIds = JSON.parseArray(tagIdStr, Long.class);
        List<String> tags = new ArrayList<>();
        //丽人主标签
        addTagIfNoNull(tags, hairConfig.doGetHotFilterBeautyMainTag(productM, filterId));
        //服务分类
        addTagIfNoNull(tags, hairConfig.doGetCategoryTag(tagIds, filterId));
        //药水品牌
        addTagIfNoNull(tags, hairConfig.doGetPharmaceuticsBrandTag(productM, filterId));
        //发型师等级
        addTagIfNoNull(tags, hairConfig.doGetBarberTitleTag(tagIds, productM));
        //所含服务项目
        addTagIfNoNull(tags, getTangRanServiceProjectTagNew(productM));
        return tags;
    }

    /**
     * @param productM
     * @return 烫染团单返回服务项目，优先级为 含X次漂发、含护理、含剪发
     */
    private String getTangRanServiceProjectTagNew(ProductM productM) {
        if (!Objects.equals(ProductMAttrUtils.getServiceType(productM), "烫染")) {
            return null;
        }
        List<SkuItemDto> skuItemDtoList = DealStructUtils.getTotalSkuItem(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL));
        List<String> tags = new ArrayList<>();
        //漂色
        tags.add(getTangRanPiaoSeTag(skuItemDtoList));
        //护理（有服务项目 且团单标题有护理）
        tags.add(getTangRanServiceHuLiTag(skuItemDtoList, productM.getTitle()));
        //洗剪吹
        tags.add(skuItemDtoList.stream().anyMatch(o -> o.getProductCategory() == 3111) ? "含剪发" : null);
        for (String tag : tags) {
            if (StringUtils.isNotEmpty(tag)) {
                return tag;
            }
        }
        return null;
    }

    private String getTangRanServiceHuLiTag(List<SkuItemDto> skuItemDtoList, String productTitle) {
        if (CollectionUtils.isEmpty(skuItemDtoList) || StringUtils.isEmpty(productTitle)) {
            return null;
        }
        boolean matchCategory = skuItemDtoList.stream().anyMatch(o -> o.getProductCategory() == 3114);
        boolean matchTitle = productTitle.contains("+护理") || productTitle.contains("含护理");
        return matchCategory && matchTitle ? "含护理" : null;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 药水品牌
         * key-tagId
         * value-展示的标签
         */
        private LinkedHashMap<Long, String> pharmaceuticsBrandTagMap;

        /**
         * 发型师等级
         */
        private LinkedHashMap<Long, String> barberTitleTagMap;

        /**
         * 烫染全部时展示服务分类
         */
        private LinkedHashMap<Long, String> tangRanCategoryTagMap;

        /**
         * 展示详细烫染分类id的筛选项id
         */
        private List<Long> tangRanCategoryFilterIds;

        /**
         * 染发全部时展示服务分类
         */
        private LinkedHashMap<Long, String> ranFaCategoryTagMap;

        /**
         * 展示详细染发分类id的筛选id
         */
        private List<Long> ranFaCategoryFilterIds;

        /**
         * 商品列表ID列表
         */
        private List<Integer> productCategoryIds;

        /**
         * 斗斛策略列表
         */
        private List<String> douHuSkList;

        /**
         * 标签名称列表
         */
        private List<String> tagNameList;

        private BeautyHairTagConfig hairConfig;
    }
}
