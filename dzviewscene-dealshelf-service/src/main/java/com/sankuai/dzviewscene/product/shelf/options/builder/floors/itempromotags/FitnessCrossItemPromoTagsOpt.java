package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.options.FitnessCrossDataFetchOpt;
import com.sankuai.dzviewscene.product.enums.FitnessCrossIdentityEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

@VPointOption(name = "健身通价格后标签",
        description = "",
        code = "FitnessCrossItemPromoTagsOpt")
public class FitnessCrossItemPromoTagsOpt extends ItemPromoTagsVP<FitnessCrossItemPromoTagsOpt.Config> {

    /**
     * 默认展示的文案
     */
    private static final String DEFAULT_TEXT = "（购买后可兑）";

    @Override
    public List<DzPromoVO> compute(ActivityCxt context, Param param, Config config) {
        FitnessCrossDataFetchOpt.Context dataFetchContext = ParamsUtil.getObjSafely(context, "FitnessCrossDataFetchOpt", FitnessCrossDataFetchOpt.Context.class);
        // 是否展示(新客、游客)
        boolean available = dataFetchContext == null || dataFetchContext.getIdentityEnum() == null || !FitnessCrossIdentityEnum.HAS_PAY.equals(dataFetchContext.getIdentityEnum());

        // 新客、游客返回配置的文案。老客不展示
        String name = available ? ((config != null && !StringUtils.isEmpty(config.getText())) ? config.getText() : DEFAULT_TEXT) : null;

        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(name);

        return Collections.singletonList(dzPromoVO);
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 文案
         */
        private String text = "（购买后可兑）";

    }

}
