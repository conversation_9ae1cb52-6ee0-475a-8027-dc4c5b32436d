package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemwarmup;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemWarmUpVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.WarmUpVO;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@VPointOption(
        name = "空变化点-返回Null",
        description = "空变化点-返回Null",
        code = "NullItemWarmUpOpt",
        isDefault = true)
public class NullItemWarmUpOpt extends ItemWarmUpVP<Void> {

    @Override
    public WarmUpVO compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
