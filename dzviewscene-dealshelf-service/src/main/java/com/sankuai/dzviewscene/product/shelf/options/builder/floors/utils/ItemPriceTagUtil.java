package com.sankuai.dzviewscene.product.shelf.options.builder.floors.utils;

import com.dianping.vc.sdk.lang.DateUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 跨VP 优惠标签 util
 *
 * <AUTHOR>
 * @since 2023/10/19 15:42
 */
public class ItemPriceTagUtil {

    private static final double DISCOUNT_LIMIT = 9.0;

    // 预售优惠类型
    private static final List<Integer> PRE_SALE_TAG_TYPE_LIST = Lists.newArrayList
            (PromoTagTypeEnum.PreSale_Member.getCode(), PromoTagTypeEnum.PreSale_NewUser.getCode(), PromoTagTypeEnum.PreSale.getCode());

    public static boolean hashPromoTagWithMember(ProductM productM, CardM cardM) {
        return hasPreSaleTag(productM) || hasRainBowSecKillTag(productM) || hasNoMemberPromoM(productM) || hasMemberPromo(productM, cardM);
    }

    /**
     * 是否包含会员优惠
     *
     * @param productM
     * @param cardM
     * @return
     */
    private static boolean hasMemberPromo(ProductM productM, CardM cardM) {
        ProductPromoPriceM userHoldCardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
        if (Objects.isNull(userHoldCardPromo)) {
            return false;
        }
        return CollectionUtils.isNotEmpty(productM.getPromoPrices())
                && productM.getPromoPrices().stream().anyMatch(a -> a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode()));
    }

    /**
     * 是否包含预售标签
     *
     * @param productM
     * @return
     */
    private static boolean hasPreSaleTag(ProductM productM) {
        return Objects.nonNull(productM) && CollectionUtils.isNotEmpty(productM.getPromoPrices())
                && productM.getPromoPrices().stream().anyMatch(a -> Objects.nonNull(a.getPromoTagType()) && PRE_SALE_TAG_TYPE_LIST.contains(a.getPromoTagType()));
    }

    /**
     * 是否包含秒杀标签
     *
     * @param productM
     * @return
     */
    private static boolean hasRainBowSecKillTag(ProductM productM) {
        return hasNoMemberPromoM(productM) && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(productM);
    }

    /**
     * 获取非会员优惠信息
     *
     * @param productM
     * @return
     */
    private static boolean hasNoMemberPromoM(ProductM productM) {
        if (Objects.isNull(productM) || CollectionUtils.isEmpty(productM.getPromoPrices())) {
            return false;
        }
        List<ProductPromoPriceM> noMemberPriceM = productM.getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                // 过滤掉 使用了会员优惠的Promo
                .filter(a -> !CardPromoUtils.CARD_PROMOS.contains(a.getPromoType()))
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(noMemberPriceM);
    }

    public static boolean isShowIdlePromo(ProductM productM) {
        if (productM == null) {
            return false;
        }
        ProductPromoPriceM promoPriceM = productM.getPromo(PromoTypeEnum.IDLE_PROMO.getType());
        if (promoPriceM == null || StringUtils.isEmpty(promoPriceM.getAvailableTime())) {
            return false;
        }
        return promoPriceM.getDiscount().compareTo(BigDecimal.valueOf(DISCOUNT_LIMIT)) <= 0;
    }
}
