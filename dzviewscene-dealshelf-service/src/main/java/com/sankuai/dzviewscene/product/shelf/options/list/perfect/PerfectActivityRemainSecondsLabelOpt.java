package com.sankuai.dzviewscene.product.shelf.options.list.perfect;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.ActivityRemainSecondsLabelVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

/**
 * created by zhang<PERSON>yuan04 in 2021/8/31
 */
@VPointOption(name = "玩美季活动剩余时间标签", description = "玩美季活动剩余时间标签", code = PerfectActivityRemainSecondsLabelOpt.CODE, isDefault = true)
public class PerfectActivityRemainSecondsLabelOpt extends ActivityRemainSecondsLabelVP<Void> {

    public static final String CODE = "PerfectActivityRemainSecondsLabelOpt";

    @Override
    public RichLabelVO compute(ActivityCxt context, Param param, Void aVoid) {
        ProductM productM = param.getProductM();
        RichLabelVO richLabelVO = PerfectActivityBuildUtils.buildPerfectActivityRemainSecondsLabel(productM);
        return richLabelVO;
    }

}
