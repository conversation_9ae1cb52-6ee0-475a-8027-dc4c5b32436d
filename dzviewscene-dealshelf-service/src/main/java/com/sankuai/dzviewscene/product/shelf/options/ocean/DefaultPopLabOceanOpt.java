package com.sankuai.dzviewscene.product.shelf.options.ocean;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.ocean.vpoints.PopLabOceanVP;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * created by zhang<PERSON>yuan04 in 2021/8/26
 */
@VPointOption(name = "默认弹窗打点", description = "货架打点信息, 支持可配置", code = DefaultPopLabOceanOpt.CODE, isDefault = true)
public class DefaultPopLabOceanOpt extends PopLabOceanVP<DefaultPopLabOceanOpt.Config> {

    public static final String CODE = "DefaultPopLabOceanOpt";

    @Override
    public Map<String, String> compute(ActivityCxt context, Param param, Config config) {
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("cat_id", param.getCategoryId());
        String labs = JsonCodec.encode(oceanMap);
        return buildFiledMap(config.getFields(), labs);
    }


    private Map<String, String> buildFiledMap(List<String> fields, String labs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.shelf.options.ocean.DefaultPopLabOceanOpt.buildFiledMap(java.util.List,java.lang.String)");
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(fields)) {
            return result;
        }
        for (String field : fields) {
            result.put(field, labs);
        }
        return result;
    }


    @Data
    @VPointCfg
    public static class Config {
        // 个性化打点位置
        private List<String> fields;
    }
}
