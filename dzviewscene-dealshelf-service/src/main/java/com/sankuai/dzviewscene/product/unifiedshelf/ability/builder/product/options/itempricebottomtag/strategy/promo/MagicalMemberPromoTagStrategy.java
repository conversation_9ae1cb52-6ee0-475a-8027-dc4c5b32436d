package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils.*;


@Component
public class MagicalMemberPromoTagStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

    private static final double DP_PRE_ASPECT_RADIO = 2.46875;
    public static final double MT_PRE_ASPECT_RADIO = 2.5;
    public static final int DP_PRE_PIC_HEIGHT = 14;
    public static final int MT_PRE_PIC_HEIGHT = 16;

    public static final double DP_AFTER_ASPECT_RADIO = 1.0;
    public static final int DP_AFTER_PIC_HEIGHT = 10;
    public static final String DP_BACKGROUND_COLOR = "#FEEFF4";
    public static final int DP_LIMIT_SHELF_VERSION = 111;
    // 点评侧神券图标
    public static String DP_MAGICAL_MEMBER_ICON = "https://p0.meituan.net/travelcube/3fd732bf6371bef67379c1fc50da4a035112.png";
    // 美团侧神券图标
    public static String MT_MAGICAL_MEMBER_ICON = "https://p0.meituan.net/ingee/82081249d39757ec88f4a744a16938e26122.png";
    // 点评侧箭头
    public static String DP_MAGICAL_MEMBER_AFTER_ICON = "https://p0.meituan.net/travelcube/8a92b7d7d5e8d6b480e7b45002f458b7465.png";

    public static final String DP_TEXT_COLOR = "#FF4433";

    private static final String OFFLINE_CODE_KEY = "offlinecode";

    @Override
    public String getName() {
        return "神会员";
    }

    @Override
    public ShelfTagVO buildTag(PriceBottomTagBuildReq req) {
        ProductPromoPriceM promoPriceM = PriceUtils.getUserHasPromoPrice(req.getProductM(), req.getCardM());
        //优惠组合无神券
        if (!DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)) {
            return null;
        }

        if (MagicalMemberTagUtils.hitMagicalMemberNewStyle(req.getContext()) && !req.isHitSimplifyPromoTag()) {
            // 神券新样式，带膨胀信息
            return buildNewStyleMagicalMemberTag(promoPriceM, req);
        } else {
            // 普通样式
            return buildNormalStyleMagicalMemberTag(req, promoPriceM);
        }
    }

    /**
     * 构建普通样式的神会员标签
     */
    private ShelfTagVO buildNormalStyleMagicalMemberTag(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        ShelfTagVO shelfTagVO = initializeTag(req, promoPriceM);
        shelfTagVO.setPrePic(buildPrePic(req, promoPriceM));
        shelfTagVO.setText(buildNormalStyleText(req, promoPriceM));
        addAfterPic(req, shelfTagVO, getCommonAfterPic(req.getPlatform()), promoPriceM);
        return shelfTagVO;
    }

    private RichLabelModel buildNormalStyleText(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        if (!shouldShowInflatePic(req, promoPriceM)) {
            return buildNormalTextTag(req, promoPriceM);
        }

        List<String> inflateText = getInflateTextFromPromo(req, promoPriceM);
        if (CollectionUtils.isEmpty(inflateText)) {
            return buildNormalTextTag(req, promoPriceM);
        }

        return PlatformUtil.isMT(req.getPlatform()) ?
                buildMultiText(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType(), inflateText.get(0), inflateText, ColorUtils.colorFF2D19, ColorUtils.colorFFF1F0) :
                buildMultiText(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType(), inflateText.get(0), inflateText, DP_TEXT_COLOR, DP_BACKGROUND_COLOR);
    }

    private RichLabelModel buildNormalTextTag(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        String tagText = buildPromoTag(req, promoPriceM);
        RichLabelModel tag = super.buildCommonTagText(tagText, null, false);
        if (tag == null) {
            return null;
        }
        return isUseMtStyle(req) ? buildMtStyleTag(tag) : buildDpStyleTag(tag);
    }

    private RichLabelModel buildMtStyleTag(RichLabelModel tag) {
        tag.setStyle(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType());
        tag.setTextColor(ColorUtils.colorFF2D19);
        tag.setBackgroundColor(ColorUtils.colorFFF1F0);
        return tag;
    }

    private RichLabelModel buildDpStyleTag(RichLabelModel tag) {
        tag.setStyle(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType());
        tag.setTextColor(DP_TEXT_COLOR);
        tag.setBackgroundColor(ColorUtils.colorFFFFFF);
        return tag;
    }

    private void addAfterPic(PriceBottomTagBuildReq req, ShelfTagVO shelfTagVO, String commonAfterPic, ProductPromoPriceM promoPriceM) {
        if (shouldAddInflateAfterPic(req, promoPriceM)) {
            shelfTagVO.setAfterPic(buildPicModel(MagicalMemberTagUtils.DP_NORMAL_MAGICAL_MEMBER_AFTER_ICON,
                    MagicalMemberTagUtils.MAGICAL_AFTER_PIC_RADIO,
                    MagicalMemberTagUtils.DP_MAGICAL_AFTER_PIC_HEIGHT));
        }
        super.addAfterPic(shelfTagVO, commonAfterPic);
        if (isUseMtStyle(req) || shelfTagVO.getAfterPic() == null) {
            return;
        }
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(DP_MAGICAL_MEMBER_AFTER_ICON);
        pictureModel.setAspectRadio(DP_AFTER_ASPECT_RADIO);
        pictureModel.setPicHeight(DP_AFTER_PIC_HEIGHT);
        shelfTagVO.setAfterPic(pictureModel);
    }

    private boolean shouldAddInflateAfterPic(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        if (!req.isHitSimplifyPromoTag()) {
            return false;
        }
        List<String> inflateText = getInflateTextFromPromo(req, promoPriceM);
        return shouldShowInflatePic(req, promoPriceM) && CollectionUtils.isNotEmpty(inflateText);
    }

    private PictureModel buildPrePic(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        boolean shouldShowInflatePic = shouldShowInflatePic(req, promoPriceM);
        boolean isMtStyle = isUseMtStyle(req);
        return buildPictureModel(shouldShowInflatePic, isMtStyle);
    }

    private PictureModel buildPictureModel(boolean shouldShowInflatePic, boolean isMtStyle) {
        PictureModel pictureModel = new PictureModel();
        if (isMtStyle) {
            pictureModel.setPicHeight(MAGICAL_PIC_HEIGHT);
            return buildMtPictureModel(shouldShowInflatePic, pictureModel);
        }
        pictureModel.setPicHeight(DP_MAGICAL_PIC_HEIGHT_NEW);
        return buildDpPictureModel(shouldShowInflatePic, pictureModel);
    }

    private PictureModel buildMtPictureModel(boolean shouldShowInflatePic, PictureModel pictureModel) {
        pictureModel.setPicUrl(shouldShowInflatePic ? INFLATE_MAGICAL_PIC_WITH_BG : NORMAL_MAGICAL_PIC_WITH_BG);
        pictureModel.setAspectRadio(shouldShowInflatePic ? INFLATE_MAGICAL_PIC_WITH_BG_RADIO : NORMAL_MAGICAL_PIC_WITH_BG_RADIO);
        return pictureModel;
    }

    private PictureModel buildDpPictureModel(boolean shouldShowInflatePic, PictureModel pictureModel) {
        pictureModel.setPicUrl(shouldShowInflatePic ? DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_WHITE_BORDER : DP_NORMAL_MAGICAL_PIC_WITH_BG);
        pictureModel.setAspectRadio(shouldShowInflatePic ? DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_WHITE_BORDER_RADIO : DP_NORMAL_MAGICAL_PIC_WITH_BG_RADIO);
        return pictureModel;
    }

    private List<String> getInflateTextFromPromo(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        PromoItemM magicalItemM = DzPromoUtils.getMagicalMemberPromoItem(promoPriceM);
        return MagicalMemberTagUtils.getInflateText(req.getContext(), magicalItemM);
    }

    private boolean shouldShowInflatePic(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        PromoItemM magicalItemM = DzPromoUtils.getMagicalMemberPromoItem(promoPriceM);
        Map<String, String> extraInfo = Optional.ofNullable(magicalItemM.getPromotionOtherInfoMap())
                .orElse(Maps.newHashMap());

        if (BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()))) {
            return true;
        }

        ShelfTagVO shelfTagVO = initializeTag(req, promoPriceM);
        boolean canInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
        boolean hasInflateInfo = hasInflateInfo(shelfTagVO);

        Map<String, String> extendDisplayInfo = DzPromoUtils.getExtendDisplayInfo(promoPriceM);
        boolean canInflateAndCanInflateMore = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(req.getContext(), extendDisplayInfo);

        return hasInflateInfo && canInflate && canInflateAndCanInflateMore;
    }

    private ShelfTagVO buildNewStyleMagicalMemberTag(ProductPromoPriceM promoPriceM, PriceBottomTagBuildReq req) {
        ShelfTagVO shelfTagVO = initializeTag(req, promoPriceM);
        PromoItemM magicalItemM = DzPromoUtils.getMagicalMemberPromoItem(promoPriceM);
        Map<String, String> extraInfo = Optional.ofNullable(magicalItemM.getPromotionOtherInfoMap()).orElse(Maps.newHashMap());
        boolean canInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
        boolean afterInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
        boolean hasInflateInfo = hasInflateInfo(shelfTagVO);
        boolean isMt = PlatformUtil.isMT(req.getPlatform());
        //普通样式神券，无膨胀信息
        if (useNormalMagicalTag(req)) {
            return isMt ? buildMtNormalShelfMagicalMemberTag(shelfTagVO, promoPriceM, req, afterInflate || (hasInflateInfo && canInflate))
                    : buildDpNormalShelfMagicalMemberTag(shelfTagVO, promoPriceM, req, afterInflate || (hasInflateInfo && canInflate));
        }
        // 神券还可膨胀，但膨胀后能不能形成更优算价组合
        Map<String, String> extendDisplayInfo = DzPromoUtils.getExtendDisplayInfo(promoPriceM);
        boolean canInflateAndCanInflateMore = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(req.getContext(),extendDisplayInfo);
        if (hasInflateInfo && canInflate && canInflateAndCanInflateMore) {
            //可膨胀
            List<String> inflateText = MagicalMemberTagUtils.getInflateText(req.getContext(), magicalItemM);
            if (CollectionUtils.isNotEmpty(inflateText)) {
                return isMt ? buildMtCanInflateMagicalMemberTag(shelfTagVO, inflateText)
                        : buildDpCanInflateMagicalMemberTag(shelfTagVO, inflateText);
            }
        }
        return isMt ? buildMtCantInflateMagicalMemberTag(shelfTagVO, promoPriceM, req, magicalItemM, afterInflate)
                : buildDpCantInflateMagicalMemberTag(shelfTagVO, promoPriceM, req, magicalItemM, afterInflate);
    }

    private ShelfTagVO buildMtCanInflateMagicalMemberTag(ShelfTagVO shelfTagVO, List<String> inflateText) {
        shelfTagVO.setText(buildMultiText(RichLabelStyleEnum.MAGICAL_MEMBER_GRADIENT_STYLE.getType(), inflateText.get(0), inflateText, ColorUtils.colorFFFFFF, null));
        shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.INFLATE_MAGICAL_PIC, MagicalMemberTagUtils.INFLATE_MAGICAL_PIC_RADIO, MagicalMemberTagUtils.MAGICAL_PIC_HEIGHT));
        shelfTagVO.setAfterPic(buildPicModel(MagicalMemberTagUtils.INFLATE_MAGICAL_MEMBER_AFTER_ICON, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_RADIO, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_HEIGHT));
        return shelfTagVO;
    }

    private ShelfTagVO buildDpCanInflateMagicalMemberTag(ShelfTagVO shelfTagVO, List<String> inflateText) {
        shelfTagVO.setText(buildMultiText(RichLabelStyleEnum.MAGICAL_MEMBER_NORMAL_STYLE.getType(), inflateText.get(0), inflateText, DP_TEXT_COLOR, DP_BACKGROUND_COLOR));
        shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.DP_INFLATE_MAGICAL_PIC, MagicalMemberTagUtils.DP_INFLATE_MAGICAL_PIC_RADIO, MagicalMemberTagUtils.DP_MAGICAL_PIC_HEIGHT));
        shelfTagVO.setAfterPic(buildPicModel(MagicalMemberTagUtils.DP_NORMAL_MAGICAL_MEMBER_AFTER_ICON, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_RADIO, MagicalMemberTagUtils.DP_MAGICAL_AFTER_PIC_HEIGHT));
        return shelfTagVO;
    }

    private ShelfTagVO buildMtCantInflateMagicalMemberTag(ShelfTagVO shelfTagVO, ProductPromoPriceM promoPriceM, PriceBottomTagBuildReq req, PromoItemM magicalItemM, boolean afterInflate) {
        String promoTag = buildPromoTag(req, promoPriceM);
        String cantInflateText = MagicalMemberTagUtils.getCantInflateText(magicalItemM);
        shelfTagVO.setText(buildNormalText(RichLabelStyleEnum.ROUND_CORNER.getType(), promoTag, ColorUtils.colorFF2727, ColorUtils.colorFFF1EC));
        shelfTagVO.setPreText(buildNormalText(RichLabelStyleEnum.MAGICAL_MEMBER_GRADIENT_STYLE.getType(), cantInflateText, ColorUtils.colorFFFFFF, null));
        if (afterInflate) {
            shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.INFLATE_MAGICAL_PIC, MagicalMemberTagUtils.INFLATE_MAGICAL_PIC_RADIO, MagicalMemberTagUtils.MAGICAL_PIC_HEIGHT));
        } else {
            shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.NORMAL_MAGICAL_PIC, MagicalMemberTagUtils.NORMAL_MAGICAL_PIC_RADIO, MagicalMemberTagUtils.MAGICAL_PIC_HEIGHT));
        }
        shelfTagVO.setAfterPic(buildPicModel(MagicalMemberTagUtils.NORMAL_MAGICAL_MEMBER_AFTER_ICON, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_RADIO, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_HEIGHT));
        return shelfTagVO;
    }

    private ShelfTagVO buildDpCantInflateMagicalMemberTag(ShelfTagVO shelfTagVO, ProductPromoPriceM promoPriceM, PriceBottomTagBuildReq req, PromoItemM magicalItemM, boolean afterInflate) {
        String promoTag = buildPromoTag(req, promoPriceM);
        String cantInflateText = MagicalMemberTagUtils.getCantInflateText(magicalItemM);
        shelfTagVO.setText(buildNormalText(RichLabelStyleEnum.ROUND_CORNER.getType(), promoTag, DP_TEXT_COLOR, ColorUtils.colorFFFFFF));
        shelfTagVO.setPreText(buildNormalText(RichLabelStyleEnum.MAGICAL_MEMBER_NORMAL_STYLE.getType(), cantInflateText, DP_TEXT_COLOR, DP_BACKGROUND_COLOR));
        if (afterInflate) {
            shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.DP_INFLATE_MAGICAL_PIC, MagicalMemberTagUtils.DP_INFLATE_MAGICAL_PIC_RADIO, MagicalMemberTagUtils.DP_MAGICAL_PIC_HEIGHT));
        } else {
            shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.DP_NORMAL_MAGICAL_PIC, MagicalMemberTagUtils.DP_NORMAL_MAGICAL_PIC_RADIO, MagicalMemberTagUtils.DP_MAGICAL_PIC_HEIGHT));
        }
        shelfTagVO.setAfterPic(buildPicModel(MagicalMemberTagUtils.DP_NORMAL_MAGICAL_MEMBER_AFTER_ICON, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_RADIO, MagicalMemberTagUtils.DP_MAGICAL_AFTER_PIC_HEIGHT));
        return shelfTagVO;
    }

    private ShelfTagVO buildMtNormalShelfMagicalMemberTag(ShelfTagVO shelfTagVO, ProductPromoPriceM promoPriceM, PriceBottomTagBuildReq req, boolean showInflate) {
        String promoTag = buildPromoTag(req, promoPriceM);
        shelfTagVO.setText(buildNormalText(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType(), promoTag, ColorUtils.colorFF2727, ColorUtils.colorFFF1EC));
        if (showInflate) {
            shelfTagVO.setPrePic(buildPicModel(INFLATE_MAGICAL_PIC_WITH_BG, MagicalMemberTagUtils.INFLATE_MAGICAL_PIC_WITH_BG_RADIO, MagicalMemberTagUtils.MAGICAL_PIC_HEIGHT));
        } else {
            shelfTagVO.setPrePic(buildPicModel(NORMAL_MAGICAL_PIC_WITH_BG, MagicalMemberTagUtils.NORMAL_MAGICAL_PIC_WITH_BG_RADIO, MagicalMemberTagUtils.MAGICAL_PIC_HEIGHT));
        }
        shelfTagVO.setAfterPic(buildPicModel(MagicalMemberTagUtils.NORMAL_MAGICAL_MEMBER_AFTER_ICON, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_RADIO, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_HEIGHT));
        return shelfTagVO;
    }

    private ShelfTagVO buildDpNormalShelfMagicalMemberTag(ShelfTagVO shelfTagVO, ProductPromoPriceM promoPriceM, PriceBottomTagBuildReq req, boolean showInflate) {
        String promoTag = buildPromoTag(req, promoPriceM);
        shelfTagVO.setText(buildNormalText(RichLabelStyleEnum.MAGICAL_MEMBER_NORMAL_STYLE.getType(), promoTag, DP_TEXT_COLOR, ColorUtils.colorFFFFFF));
        if (showInflate) {
            shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_BORDER, MagicalMemberTagUtils.DP_INFLATE_MAGICAL_PIC_WITH_BG_RADIO, MagicalMemberTagUtils.MAGICAL_PIC_HEIGHT));
        } else {
            shelfTagVO.setPrePic(buildPicModel(MagicalMemberTagUtils.DP_NORMAL_MAGICAL_PIC_WITH_BG_NO_BORDER, MagicalMemberTagUtils.DP_NORMAL_MAGICAL_PIC_WITH_BG_RADIO, MagicalMemberTagUtils.MAGICAL_PIC_HEIGHT));
        }
        shelfTagVO.setAfterPic(buildPicModel(MagicalMemberTagUtils.DP_NORMAL_MAGICAL_MEMBER_AFTER_ICON, MagicalMemberTagUtils.MAGICAL_AFTER_PIC_RADIO, MagicalMemberTagUtils.DP_MAGICAL_AFTER_PIC_HEIGHT));
        return shelfTagVO;
    }

    private boolean hasInflateInfo(ShelfTagVO shelfTagVO) {
        CouponPromoItemVO couponPromoItemVO = shelfTagVO.getCouponPromoItemVO();
        if (couponPromoItemVO != null) {
            return true;
        }
        ShelfItemPromoDetail dzPromoDetailVO = shelfTagVO.getPromoDetail();
        if (dzPromoDetailVO == null || CollectionUtils.isEmpty(dzPromoDetailVO.getPromoItems())) {
            return false;
        }
        return dzPromoDetailVO.getPromoItems().stream().anyMatch(v -> v.getCouponPromoItem() != null);
    }

    private ShelfTagVO initializeTag(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setName(UnifiedShelfPromoUtils.MAGICAL_MEMBER_TAG_NAME);
        shelfTagVO.setLabs(buildOceanLabs());
        if (!req.isHitSimplifyPromoTag()) {
            shelfTagVO.setPromoDetail(buildPromoDetail(req.getContext(), promoPriceM));
        } else {
            shelfTagVO.setCouponPromoItemVO(buildCouponPromoItemVO(req, promoPriceM));
        }
        return shelfTagVO;
    }

    private CouponPromoItemVO buildCouponPromoItemVO(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        int shelfVersion = ParamsUtil.getIntSafely(req.getContext().getParameters(), ShelfActivityConstants.Params.shelfVersion);
        long productId = promoPriceM.getProductId();
        int platform = req.getPlatform();
        return promoPriceM.getPromoItemList().stream().filter(Objects::nonNull)
                .map(item -> UnifiedShelfPromoUtils.buildMagicMemberCoupon(platform, shelfVersion, item, productId))
                .filter(Objects::nonNull).findFirst().orElse(null);
    }

    private boolean useNormalMagicalTag(PriceBottomTagBuildReq req) {
        int doubleColumnShelf = ParamsUtil.getIntSafely(req.getContext(), ShelfActivityConstants.Params.doubleColumnShelf);
        //双列
        if (doubleColumnShelf == 1) {
            return true;
        }
        if (req.getCfg() != null && req.getCfg().isUseNormalMagicalTag()) {
            return true;
        }
        //F型
        if (ParamsUtil.getBooleanSafely(req.getContext().getParameters(), ShelfActivityConstants.Params.isFShelf)) {
            return true;
        }
        return false;
    }

    public RichLabelModel buildCommonTagText(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
        String tagText = buildPromoTag(req, promoPriceM);
        RichLabelModel tag = super.buildCommonTagText(tagText, null, false);
        if (tag == null) {
            return null;
        }
        if (isUseMtStyle(req)) {
            tag.setStyle(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType());
            return tag;
        }
        tag.setStyle(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType());
        tag.setTextColor(DP_TEXT_COLOR);
        tag.setBackgroundColor(DP_BACKGROUND_COLOR);
        return tag;
    }

    private boolean isUseMtStyle(PriceBottomTagBuildReq req) {
        int shelfVersion = ParamsUtil.getIntSafely(req.getContext().getParameters(), ShelfActivityConstants.Params.shelfVersion);
        return PlatformUtil.isMT(req.getPlatform()) || shelfVersion < DP_LIMIT_SHELF_VERSION;
    }

    private ShelfItemPromoDetail buildPromoDetail(ActivityCxt context, ProductPromoPriceM couponPromo) {
        int platform = getPlatform(context);
        int shelfVersion = ParamsUtil.getIntSafely(context.getParameters(), ShelfActivityConstants.Params.shelfVersion);

        ShelfItemPromoDetail dzPromoDetailVO = UnifiedShelfPromoUtils.buildPromoDetail(platform, shelfVersion, couponPromo);
        if (dzPromoDetailVO == null || CollectionUtils.isEmpty(dzPromoDetailVO.getPromoItems())) {
            return dzPromoDetailVO;
        }
        String offlineCode = ContextParamBuildUtils.getParamFromExtraMap(context, OFFLINE_CODE_KEY, "");
        String channelType = ParamsUtil.getStringSafely(context.getParameters(), ShelfActivityConstants.Params.channelType);
        dzPromoDetailVO.getPromoItems().forEach(v -> {
            if (v.getCouponPromoItem() == null) {
                return;
            }
            v.getCouponPromoItem().setPosition(ParamsUtil.getStringSafely(context.getParameters(), ShelfActivityConstants.Params.position));
            v.getCouponPromoItem().setCouponChannel(channelType);
            v.getCouponPromoItem().setOfflineCode(offlineCode);
        });
        return dzPromoDetailVO;
    }

    private int getPlatform(ActivityCxt context) {
        int platform = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform);
        if (platform < 10) {
            // platform = 1 或者 2，使用userAgent
            int userAgent = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.userAgent);
            if (userAgent > 0) {
                return userAgent;
            }
        }
        return platform;
    }

    private String buildPromoTag(PriceBottomTagBuildReq req, ProductPromoPriceM productPromoPriceM) {
        if(req.isHitSimplifyPromoTag()){
            PromoItemM magicalItemM = DzPromoUtils.getMagicalMemberPromoItem(productPromoPriceM);
            return MagicalMemberTagUtils.getCantInflateText(magicalItemM);
        }
        //计算优惠金额
        BigDecimal promoPrice;
        if (StringUtils.isNotEmpty(req.getProductM().getMarketPrice()) && StringUtils.isNotEmpty(req.getSalePrice())) {
            promoPrice = new BigDecimal(req.getProductM().getMarketPrice()).subtract(new BigDecimal(req.getSalePrice()));
        } else {
            promoPrice = productPromoPriceM.getTotalPromoPrice();
        }
        return "共省" + promoPrice.stripTrailingZeros().toPlainString();
    }

    public String buildOceanLabs() {
        Map<String, Object> oceanMap = Maps.newHashMap();
        oceanMap.put("type", "1");
        return JsonCodec.encode(oceanMap);
    }
}
