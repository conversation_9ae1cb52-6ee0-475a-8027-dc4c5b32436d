package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreTextVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "商品区-F型货架更多展示文案",
        description = "单列货架默认展示「更多X个团购」，F型货架可以定制格式",
        code = "FShelfProductAreaMoreTextOpt")
public class FShelfProductAreaMoreTextOpt extends ProductAreaMoreTextVP<FShelfProductAreaMoreTextOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        int totalNum = param.getItemAreaItemCnt();
        if (totalNum <= param.getDefaultShowNum()) {
            return StringUtils.EMPTY;
        }
        if (ParamsUtil.getBooleanSafely(context.getParameters(), ShelfActivityConstants.Params.isFShelf)) {
            return config.isUseTotal() ? getTotalText(totalNum, config.getFShelfTextFormat()) : getRemainText(totalNum, param.getDefaultShowNum(), config.getFShelfTextFormat());
        }
        return config.isUseTotal() ? getTotalText(totalNum, config.getTextFormat()) : getRemainText(totalNum, param.getDefaultShowNum(), config.getTextFormat());
    }

    @VPointCfg
    @Data
    public static class Config {

        private String textFormat;

        /**
         * F型货架更多文案格式
         */
        private String FShelfTextFormat = "查看更多%d个";

        /**
         * 是否使用全部数
         */
        private boolean useTotal;
    }
}
