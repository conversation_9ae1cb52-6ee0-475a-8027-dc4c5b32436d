package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemActivityVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "团单活动结束信息", description = "item-activity", code = UnifiedShelfItemActivityVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class UnifiedShelfItemActivityVP<T> extends PmfVPoint<ShelfItemActivityVO, UnifiedShelfItemActivityVP.Param, T> {
    public static final String CODE = "UnifiedShelfItemActivityVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private List<DouHuM> douHuList;
    }
}
