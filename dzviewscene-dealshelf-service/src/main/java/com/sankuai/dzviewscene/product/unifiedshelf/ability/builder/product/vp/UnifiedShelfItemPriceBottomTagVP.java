package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "商品价格下方标签", description = "商品价格下方标签", code = UnifiedShelfItemPriceBottomTagVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class UnifiedShelfItemPriceBottomTagVP<T> extends PmfVPoint<List<ShelfTagVO>, UnifiedShelfItemPriceBottomTagVP.Param, T> {

    public static final String CODE = "UnifiedShelfItemPriceBottomTagVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ProductM productM;

        private CardM cardM;

        private int platform;

        private String salePrice;
    }
}