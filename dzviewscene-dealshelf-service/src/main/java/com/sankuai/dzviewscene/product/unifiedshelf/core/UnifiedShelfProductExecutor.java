package com.sankuai.dzviewscene.product.unifiedshelf.core;

import com.dianping.cat.Cat;
import com.dianping.cat.util.Pair;
import com.dianping.gateway.client.debug.DEBUG;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberMonitorUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.RequestTypeEnum;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.faulttolerance.utils.TriggerFaultTolerantUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.nibpt.unionlogger.UnionLoggerContext;
import com.sankuai.nibpt.unionlogger.constant.BusinessTypeEnum;
import com.sankuai.nibscp.common.flow.identify.anno.FlowDyeAnnotation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

@Component
public class UnifiedShelfProductExecutor implements Executor<ActivityContextRequest, ShelfFilterProductAreaVO> {

    @Resource
    private UnifiedShelfActivityEngine activityEngine;

    @Override
    @FlowDyeAnnotation
    public ShelfFilterProductAreaVO execute(ActivityContextRequest request) {
        try {
            //全链路日志埋入userId
            UnionLoggerContext.logUserId(BusinessTypeEnum.GENERAL_POI_SHELF, String.valueOf(request.getUserId()));
            //1. tracer开关打开
            ExceptionTracer.start();
            long startTime = System.currentTimeMillis();
            Pair<ShelfFilterProductAreaVO, String> pair = getExecuteResult(request);
            ShelfFilterProductAreaVO filterBtnIdAndProAreasVO = pair.getKey();
            String sceneCode = pair.getValue();
            //3.根据场景，过滤未登录用户
            antiCrawler(filterBtnIdAndProAreasVO, sceneCode, request.getUserId(),request.isMiniProgramLoginStatus());
            MagicalMemberMonitorUtils.monitorTrace(sceneCode, startTime);
            return filterBtnIdAndProAreasVO;
        } finally {
            //4. 清理tracer上下文
            ExceptionTracer.end();
            //清理日志埋点
            UnionLoggerContext.clear();
            MagicalMemberMonitorUtils.clear();
        }
    }

    private void antiCrawler(ShelfFilterProductAreaVO filterBtnIdAndProAreasVO, String sceneCode, long userId,boolean loginStatus) {
        try {
            if (filterBtnIdAndProAreasVO == null || CollectionUtils.isEmpty(filterBtnIdAndProAreasVO.getProductAreas())) {
                return;
            }
            // 强制登录
            AntiCrawlerUtils.clearUnifiedShelf(filterBtnIdAndProAreasVO, userId, sceneCode,loginStatus);
        } catch (Exception e) {
            Cat.logError("AntiCrawlerError", e);
        }
    }

    private Pair<ShelfFilterProductAreaVO, String> getExecuteResult(ActivityContextRequest request) {
        ActivityRequest activityRequest = buildActivityRequest(request);
        return executeByActivityEngine(activityRequest);
    }

    private ActivityRequest buildActivityRequest(ActivityContextRequest request) {
        ActivityRequest activityRequest = UnifiedShelfExecutor.buildActivityRequest(request);
        activityRequest.addParam(ShelfActivityConstants.Params.selectedFilterId, request.getFilterBtnId());
        activityRequest.addParam(ShelfActivityConstants.Params.selectedFilterParams, request.getFilterParams());
        activityRequest.addParam(ShelfActivityConstants.Params.selectedOriginFilterId, request.getFilterBtnId());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfListForTab);
        activityRequest.addParam(ShelfActivityConstants.Params.requestType, RequestTypeEnum.API_UNIFIED_SHELF_DEAL.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.refreshTag, request.getRefreshTag());
        return activityRequest;
    }

    private Pair<ShelfFilterProductAreaVO, String> executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<UnifiedShelfResponse> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return Pair.from(null, null);
        }
        // 2. 框架执行trace信息加入debug
        addTrace2DEBUG(activityResponse);
        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            //4. 执行过程中如果发生需要关注异常，则会触发容错异常
            TriggerFaultTolerantUtils.triggerFaultTolerantByCareExecuteError(activityResponse.getExecuteError());
            return Pair.from(null, null);
        }
        // 5. 解析返回结果
        Optional<UnifiedShelfResponse> response = Optional.ofNullable(activityResponse.getResult().join());
        ShelfFilterProductAreaVO filterBtnIdAndProAreasVO = response
                .map(UnifiedShelfResponse::getFilterIdAndProductAreas)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(null);
        String sceneCode = response.map(UnifiedShelfResponse::getSceneCode).orElse(null);
        // 6. 返回结果不符合预期会触发容错
        TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(activityResponse.getExecuteError(), filterBtnIdAndProAreasVO);
        return Pair.from(filterBtnIdAndProAreasVO, sceneCode);
    }

    private void addTrace2DEBUG(ActivityResponse<UnifiedShelfResponse> activityResponse) {
        DEBUG.log("traces", activityResponse.getTraceElements());
    }

}
