package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.data.Converters;
import com.google.common.collect.Lists;
import com.sankuai.dztheme.deal.res.DealCardDTO;
import com.sankuai.dztheme.deal.res.DealMarketingInfoDTO;
import com.sankuai.dztheme.deal.res.DealThemeDTO;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.nr.assemble.res.NavTabRes;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankerEnum;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankingRequest;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankingService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DealThemeSortService {

    @Resource
    private ConfigUtils configUtils;

    @Autowired
    private BatchRankingService batchRankingService;

    public Map<String, List<RankingItem>> batchRanking(List<NavTabRes> navTabResList, int mainCategoryId, Map<String, Object> batchRankParam, boolean recommendSortReplace) {
        if (recommendSortReplace) {
            //斗斛命中时，推荐排序替换销量和创建时间排序
            return recommendSortReplace(navTabResList, mainCategoryId, batchRankParam);
        }
        // 足疗原有的排序
        doPreSort(navTabResList, mainCategoryId, false);
        // 再做新逻辑排序：外部推荐排序
        BatchRankingRequest batchRankingRequest = buildBatchRankingRequest(navTabResList, batchRankParam, Lists.newArrayList(BatchRankerEnum.Recommend.toString()));
        return batchRankingService.batchRanking(batchRankingRequest);
    }

    private BatchRankingRequest buildBatchRankingRequest(List<NavTabRes> navTabResList, Map<String, Object> batchRankParam, List<String> batchRanker) {
        BatchRankingRequest rankingRequest = new BatchRankingRequest();
        rankingRequest.setSource(buildBatchRankingItem(navTabResList));
        rankingRequest.setBatchRankers(batchRanker);
        rankingRequest.getBatchRankParams().putAll(batchRankParam);
        rankingRequest.setSceneCode("DealThemeSortService");
        return rankingRequest;
    }

    /**
     * @return 推荐排序替换销量和创建时间排序
     */
    private Map<String, List<RankingItem>> recommendSortReplace(List<NavTabRes> navTabResList, int mainCategoryId, Map<String, Object> batchRankParam) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.productshelf.vu.biz.utils.DealThemeSortService.recommendSortReplace(java.util.List,int,java.util.Map)");
        //默认推荐排序
        BatchRankingRequest batchRankingRequest = buildBatchRankingRequest(navTabResList, batchRankParam, Lists.newArrayList(BatchRankerEnum.PureRecommend.toString()));
        Map<String, List<RankingItem>> recommendRankingMap = batchRankingService.batchRanking(batchRankingRequest);
        for (NavTabRes navTabRes : navTabResList) {
            String groupKey = String.valueOf(navTabRes.getNavTagId());
            if (MapUtils.isEmpty(recommendRankingMap) || CollectionUtils.isEmpty(recommendRankingMap.get(groupKey))) {
                continue;
            }
            List<Integer> afterSortDealIds = recommendRankingMap.get(groupKey).stream().map(o->((ProductM)o).getProductId()).collect(Collectors.toList());;
            navTabRes.getDealThemes().sort(Comparator.comparingInt(o -> afterSortDealIds.indexOf(o.getDealId())));
        }
        //足疗业务排序（除兜底的销量和创建时间外）
        doPreSort(navTabResList, mainCategoryId, true);
        //可解释性置顶排序（本地的）
        BatchRankingRequest searchTopRequest = buildBatchRankingRequest(navTabResList, batchRankParam, Lists.newArrayList(BatchRankerEnum.SearchProductTop.toString()));
        return batchRankingService.batchRanking(searchTopRequest);
    }

    /**
     * 先做足疗原有的排序逻辑：大促、销量等
     * @param navTabResList
     * @param mainCategoryId
     */
    private void doPreSort(List<NavTabRes> navTabResList, int mainCategoryId, boolean recommendSortReplace) {
        for (NavTabRes navTabRes : navTabResList) {
            if (CollectionUtils.isEmpty(navTabRes.getDealThemes())) {
                continue;
            }
            sortDealTheme(navTabRes.getDealThemes(), navTabRes.getNavTagId(), mainCategoryId, recommendSortReplace);
        }
    }

    /**
     * 构造排序对象
     * @param navTabResList
     * @return
     */
    private Map<String, List<RankingItem>> buildBatchRankingItem(List<NavTabRes> navTabResList) {
        Map<String, List<RankingItem>> source = new HashMap<>(navTabResList.size());
        for (NavTabRes navTabRes : navTabResList) {
            if (CollectionUtils.isEmpty(navTabRes.getDealThemes())) {
                continue;
            }
            List<RankingItem> rankingItems = navTabRes.getDealThemes().stream()
                    .map(this::buildSortProductM)
                    .collect(Collectors.toList());
            source.put(String.valueOf(navTabRes.getNavTagId()), rankingItems);
        }
        return source;
    }

    private ProductM buildSortProductM(DealThemeDTO dealThemeDTO) {
        ProductM productM = new ProductM();
        productM.setProductId(dealThemeDTO.getDealId());
        return productM;
    }

    public List<DealThemeDTO> sortDealTheme(List<DealThemeDTO> dealThemes, List<RankingItem> rankingItems) {
        if (CollectionUtils.isEmpty(dealThemes) || CollectionUtils.isEmpty(rankingItems)) {
            return dealThemes;
        }
        List<Integer> dealIds = rankingItems.stream().map(o->((ProductM)o).getProductId()).collect(Collectors.toList());
        return dealThemes.stream().sorted(Comparator.comparingInt(o -> dealIds.indexOf(o.getDealId()))).collect(Collectors.toList());
    }

    public void sortDealTheme(List<DealThemeDTO> dealThemes, long filterId, int mainCategoryId, boolean recommendSortReplace) {
        Map<Integer, DealThemeDTO> preDealGroupIdThemeMap = buildPreDealGroupIdThemeMap(dealThemes);
        List<Integer> shopRecommendThemeIds = buildShopRecommendThemeIds(dealThemes, filterId, mainCategoryId);
        dealThemes.sort((o1, o2) -> {
            if (ConstantUtils.ACTIVITY_NAV_ID == filterId || ConstantUtils.DEFALT_SELECTED_TAG_ID == filterId) {
                //大促排序 大促在最上面 大促内部按折扣排序
                int activityDiscount = compareActivityDiscount(o1, o2);
                if (activityDiscount != 0) {
                    return activityDiscount;
                }
            }
            int beginDate = sortByTime(o1, o2, shopRecommendThemeIds, filterId, preDealGroupIdThemeMap);
            if (beginDate != 0) {
                return beginDate;
            }
            if (recommendSortReplace) {
                return 0;
            }
            //销量
            int saleCount = compareSaleCount(o1, o2);
            if (saleCount != 0) {
                return saleCount;
            }
            //创建时间排序
            return compareTime(o1, o2);
        });
    }

    /**
     * 按团单创建时间排序
     */
    private int compareTime(DealThemeDTO a, DealThemeDTO b) {
        long beginTime1 = a == null ? 0 : a.getBeginDate();
        long beginTime2 = b == null ? 0 : b.getBeginDate();

        long diff = beginTime2 - beginTime1;
        if (diff > 0) {
            return 1;
        }

        if (diff < 0) {
            return -1;
        }
        return 0;
    }

    private int sortByTime(DealThemeDTO a, DealThemeDTO b, List<Integer> shopRecommendThemeIds, long filterId, Map<Integer, DealThemeDTO> preDealGroupIdThemeMap) {
        //商家推荐，不包括会员日
        int recommend = compareRecommend(a, b, shopRecommendThemeIds, filterId);
        if (recommend != 0) {
            return recommend;
        }

        //预售
        int pre = comparePre(a, b, preDealGroupIdThemeMap);
        if (pre != 0) {
            return pre;
        }

        //会员日
        int memberDay = compareMemberDay(a, b);
        if (memberDay != 0) {
            return memberDay;
        }
        return 0;
    }

    public List<Integer> buildShopRecommendThemeIds(List<DealThemeDTO> dealThemes, long filterId, int mainCategoryId) {
        List<DealThemeDTO> shopRecommendThemes = dealThemes.stream().filter(dealTheme -> dealTheme.getDealRecommend() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopRecommendThemes)) {
            return Lists.newArrayList();
        }

        long topTagId = configUtils.getTopTagId(filterId);
        if (!ConstantUtils.CATEGORY_IDS.contains(mainCategoryId)) {
            return Converters.newPropertyExtractorConverter("dealId").convert(shopRecommendThemes);
        }

        Map<Long, List<DealThemeDTO>> navTagIdThemeMap = buildNavTagIdThemeMap(shopRecommendThemes);
        if (MapUtils.isEmpty(navTagIdThemeMap) || CollectionUtils.isEmpty(navTagIdThemeMap.get(topTagId))) {
            return Collections.EMPTY_LIST;
        }

        return Converters.newPropertyExtractorConverter("dealId").convert(navTagIdThemeMap.get(topTagId));
    }

    private Map<Long, List<DealThemeDTO>> buildNavTagIdThemeMap(List<DealThemeDTO> shopRecommendThemes) {
        Map<Long, List<DealThemeDTO>> navTagIdThemeMap = new HashMap<>();
        for (DealThemeDTO shopRecommendTheme : shopRecommendThemes) {
            long navTagId = shopRecommendTheme.getDealRecommend().getNavTagId();
            List<DealThemeDTO> dealThemes = navTagIdThemeMap.get(navTagId);
            if (CollectionUtils.isEmpty(dealThemes)) {
                dealThemes = new ArrayList<>();
            }
            dealThemes.add(shopRecommendTheme);

            navTagIdThemeMap.put(navTagId, dealThemes);
        }
        return navTagIdThemeMap;
    }

    private static Map<Integer, DealThemeDTO> buildPreDealGroupIdThemeMap(List<DealThemeDTO> dealThemes) {
        List<DealThemeDTO> preDealThemes = dealThemes.stream().filter(dealTheme -> dealTheme.isPreSale()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(preDealThemes)) {
            return new HashMap<>();
        }

        return Converters.newMapByPropertyNameConverter("dealId", preDealThemes);
    }

    /**
     * 按销量排序(倒序）
     */
    private static int compareSaleCount(DealThemeDTO a, DealThemeDTO b) {
        int aDisplaySale = a.getDealShopSale() == null ? 0 : a.getDealShopSale().getSaleNum();
        int bDisplaySale = b.getDealShopSale() == null ? 0 : b.getDealShopSale().getSaleNum();
        return bDisplaySale - aDisplaySale;
    }
    
    /**
     * 会员日排序，会员日团单需要排在前面
     */
    private static int compareMemberDay(DealThemeDTO a, DealThemeDTO b) {
        boolean aIsMemberDay = isMemberDayProduct(a.getDealCard());
        boolean bIsMemberDay = isMemberDayProduct(b.getDealCard());
        if (allHit(aIsMemberDay, bIsMemberDay) || allNoHit(aIsMemberDay, bIsMemberDay)) {
            return 0;
        }

        if (aIsMemberDay) {
            return -1;
        }
        return 1;
    }

    private static boolean isMemberDayProduct(DealCardDTO dealCard) {
        //当天会员日团单
        if (dealCard == null) {
            return false;
        }

        return dealCard.getPromoTypeEnum().getType() == PromoTypeEnum.MEMBER_DAY.getType();
    }

    /**
     * 预售排序规则，是预售团单则排在前面
     */
    private static int comparePre(DealThemeDTO a, DealThemeDTO b, Map<Integer, DealThemeDTO> preDGDataMap) {
        boolean aIsPre = isPreDG(a, preDGDataMap);
        boolean bIsPre = isPreDG(b, preDGDataMap);
        if (allNoHit(aIsPre, bIsPre) || allHit(aIsPre, bIsPre)) {
            return 0;
        }

        if (aIsPre) {
            return -1;
        }
        return 1;
    }

    private static boolean allNoHit(boolean aHit, boolean bHit) {
        if (!aHit && !bHit) {
            return true;
        }

        return false;
    }

    private static boolean allHit(boolean aHit, boolean bHit) {
        if (aHit && bHit) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否是预售团单
     */
    private static boolean isPreDG(DealThemeDTO dealThemeDTO, Map<Integer, DealThemeDTO> preDGDataMap) {
        if (!preDGDataMap.containsKey(dealThemeDTO.getDealId())) {
            return false;
        }
        DealThemeDTO preDealTheme = preDGDataMap.get(dealThemeDTO.getDealId());
        return preDealTheme.isPreSale() && !dealThemeDTO.getPreSaleDTO().isSoldOut();
    }

    /**
     * 推荐排序
     */
    private static int compareRecommend(DealThemeDTO a, DealThemeDTO b, List<Integer> recommendProductIds, long filterId) {

        boolean aRecommend = isRecommendDG(a, recommendProductIds, filterId);

        boolean bRecommend = isRecommendDG(b, recommendProductIds, filterId);

        if (allHit(aRecommend, bRecommend) || allNoHit(aRecommend, bRecommend)) {
            return 0;
        }

        if (aRecommend) {
            return -1;
        }

        if (bRecommend) {
            return 1;
        }

        return 0;
    }

    /**
     * 判断是否是推荐团单，会员日团单不算推荐团单
     */
    private static boolean isRecommendDG(DealThemeDTO dealTheme, List<Integer> recommendProductIds, long filterId) {
        if (dealTheme == null) {
            return false;
        }
        if (dealTheme.getDealCard() != null && PromoTypeEnum.MEMBER_DAY.getType() == dealTheme.getDealCard().getPromoTypeEnum().getType()) {
            return false;
        }
        return isShopRecommendProduct(recommendProductIds, dealTheme.getDealId(), filterId);
    }

    private static boolean isShopRecommendProduct(List<Integer> recommendProductIds, int productId, long filterId) {
        if (filterId != ConstantUtils.DEFALT_SELECTED_TAG_ID || !recommendProductIds.contains(productId)) {
            return false;
        }

        return true;
    }

    /**
     * 比较团单大促力度
     */
    private static int compareActivityDiscount(DealThemeDTO a, DealThemeDTO b) {
        boolean aHasActivity = themeHasActivity(a.getDealMarketingTagsDTO());
        boolean bHasActivity = themeHasActivity(b.getDealMarketingTagsDTO());

        //两个团单都没有优惠

        if (allNoHit(aHasActivity, bHasActivity)) {
            return 0;
        }

        //两个团单都有优惠 比较折扣力度
        if (allHit(aHasActivity, bHasActivity)) {
            BigDecimal aDiscount = computeActivityDiscount(a);
            BigDecimal bDiscount = computeActivityDiscount(b);
            return aDiscount.compareTo(bDiscount);
        }

        //哪个团单有优惠，排在前面
        if (themeHasActivity(a.getDealMarketingTagsDTO())) {
            return -1;
        }
        return 1;
    }

    private static boolean themeHasActivity(DealMarketingInfoDTO dealMarketingInfo) {
        if (dealMarketingInfo != null && dealMarketingInfo.getDealActivity() != null && StringUtils.isNotEmpty(dealMarketingInfo.getDealActivity().getUrl())) {
            return true;
        }
        return false;
    }

    /**
     * 计算团单折扣力度
     */
    private static BigDecimal computeActivityDiscount(DealThemeDTO dealTheme) {
        if (dealTheme == null) {
            return BigDecimal.valueOf(1.0);
        }
        BigDecimal marketPrice = dealTheme.getMarketPrice();
        BigDecimal price = dealTheme.getDealGroupPrice();
        if (marketPrice == null || price == null) {
            return BigDecimal.valueOf(1.0);
        }
        BigDecimal divide = price.divide(marketPrice, 10, BigDecimal.ROUND_HALF_UP);
        return divide;
    }

}
