package com.sankuai.dzviewscene.shelf.business.detail.edu.biz;

import com.dianping.beauty.zone.annotation.ItemAttributeMark;
import com.dianping.beauty.zone.annotation.ItemMark;
import com.dianping.beauty.zone.annotation.ItemReferenceMark;
import com.dianping.beauty.zone.annotation.ItemVideoMark;
import com.dianping.beauty.zone.biz.dto.ItemBaseLocalDTO;
import com.dianping.beauty.zone.biz.dto.ItemVideoDTO;
import com.dianping.beauty.zone.biz.enums.ItemAttributeTypeEnum;
import com.dianping.beauty.zone.biz.enums.ItemType;
import com.dianping.beauty.zone.biz.enums.ReferenceEntityType;
import com.dianping.beauty.zone.biz.enums.ReferenceRelationType;
import lombok.Data;

import java.io.Serializable;

/**
 * 新版体验课传输对象
 *
 * <AUTHOR>
 * @date 2020-10-16 11:31
 */
@Data
@ItemMark(itemType= ItemType.EDU_COURSE_CLASS)
public class OnlineCourseDTO extends ItemBaseLocalDTO implements Serializable {

    /**
     * 视频信息
     */
    @ItemVideoMark
    private ItemVideoDTO videoDTO;

    /**
     * 试听课标题
     */
    @ItemAttributeMark(attributeType = ItemAttributeTypeEnum.STRING, attributeKey = "title")
    private String title;

    /**
     * 主讲老师
     */
    @ItemAttributeMark(attributeType = ItemAttributeTypeEnum.STRING, attributeKey = "lecturer")
    private String lecturer;

    /**
     * 关联正式课id
     */
    @ItemReferenceMark(entityType = ReferenceEntityType.COURSE_ID, relationType = ReferenceRelationType.OTHER)
    private Integer courseId;

}
