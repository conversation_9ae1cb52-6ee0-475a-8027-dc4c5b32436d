package com.sankuai.dzviewscene.shelf.business.detail.pet.builder;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.*;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.detail.ability.builder.ProductDetailBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import jodd.util.StringUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/22 11:28 上午
 */
@ExtPointInstance(name = "宠物-疫苗-团单详情页扩展点实现")
public class PetVaccineDealDetailBuilderExt implements ProductDetailBuilderExt {

    private static final String DEAL_STRUCT_CONTENTT_ATTR_NAME = "dealStructContent";

    public static final String DETAIL_INFO = "detailInfo";

    private static final String PRICE_FORMAT = "%s元";

    private static final String PRODUCT_DESC_CHN_NAME = "产品描述";

    private static final String VACCINE_TYPE_CHN_NAME = "疫苗类型";

    private static final String VACCINE_BRAND_CHN_NAME = "疫苗品牌";

    private static final String INJECTION_NUM_CHN_NAME = "疫苗针数";

    private static final String VACCINE_INOCULATION_PRODUCT_CATEGORU_NAME = "疫苗接种";

    private static final String OTHER_VACCINE_PRODUCT_CATEGORY_NAME = "其他疫苗";

    private static final String OPTIONAL_GROUP_DESC_FORMAT = "以下服务项目%s选%s";

    private static final String MUST_GROUP_DESC_FORMAT = "以下服务全部可享";

    private static final String PRODUCT_PRICE_FORMAT = "%s元";

    private static final String PRODUCT_COPIES_FORMAT = "%s次";

    private static String HAIR_LENGTH_PET_DOG = "hair_length_pet_dog";

    private static String HAIR_LENGTH_PET_CAT = "hair_length_pet_cat";

    private static String PET_HAIR_ATTRIBUTE_NAME = "适用毛长";

    private static String BODY_TYPE_PET_DOG = "body_type_pet_dog";

    private static String WEIGHT_PET_CAT = "wegith_pet_cat";

    private static String PET_WEIGHT_ATTRIBUTE_NAME = "适用体重";

    private static final int DEAL_DETAIL_STRING_STRUCT_ATTRS_STYLE = 0;

    private static final int DEAL_DETAIL_TAG_STRUCT_ATTRS_STYLE = 1;

    private static final String ATTR_VALUE_SEPERATOR = ",";

    private static final String DOG_PREFIX_STR = "狗狗：";

    private static final String CAT_PREFIX_STR = "猫咪：";

    private static final String ATTR_VALUE_FORMAT = "%s；%s%s";

    private static final String VACCINE_BRAND_VACCINE_TYPE_SKU_TITLE_FORMAT = "%s%s";

    private static final String VACCINE_BRAND_VACCINE_TYPE_IJECTION_NUM_SKU_TITLE_FORMAT = "%s%s%s";

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.pet.deal.detail.attributeKeys")
    private List<String> attributeKeys;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.pet.vaccine.specifization.config")
    private PetDealDetailSpecification petDealDetailSpecification;

    @Override
    public Object buildDetail(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDetail(ActivityContext,ProductM)");
        if (productM == null) {
            return null;
        }
        return buildDealDetailStructModuleVO(productM, activityContext);
    }

    private DealDetailStructModuleVO buildDealDetailStructModuleVO(ProductM productM, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDealDetailStructModuleVO(ProductM,ActivityContext)");
        if (productM == null) {
            return null;
        }
        DealDetailStructModuleVO dealDetailStructModuleVO = new DealDetailStructModuleVO();
        //市场价
        dealDetailStructModuleVO.setMarketPrice(String.format(PRICE_FORMAT, productM.getMarketPrice()));
        //售价
        if (productM.getBasePrice() != null) {
            dealDetailStructModuleVO.setPrice(String.format(PRICE_FORMAT, productM.getBasePrice().stripTrailingZeros().toPlainString()));
        }
        //货项目模块列表
        List<OptionalDealSkuStructInfoVO> dealSkuStructInfoVOList = getDealSkuStructInfoVOList(productM);
        if (CollectionUtils.isNotEmpty(dealSkuStructInfoVOList)) {
            dealDetailStructModuleVO.setOptionalGroups(dealSkuStructInfoVOList);
        }
        //结构化信息
        List<DealDetailStructAttrVO> dealDetailStructAttrVOS = buildDealDetailStructAttrVOs(extractStructAttrs(productM.getExtAttrs()));
        if (CollectionUtils.isNotEmpty(dealDetailStructAttrVOS)) {
            dealDetailStructModuleVO.setStructAttrs(dealDetailStructAttrVOS);
        }
        //补充信息
        dealDetailStructModuleVO.setDesc(getDesc(productM));
        AntiCrawlerUtils.hideProductKeyInfo(dealDetailStructModuleVO, activityContext);
        return dealDetailStructModuleVO;
    }

    private List<AttrM> extractStructAttrs(List<AttrM> attrMs) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.extractStructAttrs(java.util.List)");
        if (CollectionUtils.isEmpty(attrMs) || CollectionUtils.isEmpty(attributeKeys)) {
            return null;
        }
        return attrMs.stream().filter(attr -> attributeKeys.contains(attr.getName())).collect(Collectors.toList());
    }

    private List<DealDetailStructAttrVO> buildDealDetailStructAttrVOs(List<AttrM> attrMs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDealDetailStructAttrVOs(java.util.List)");
        if (CollectionUtils.isEmpty(attrMs)) {
            return null;
        }
        List<DealDetailStructAttrVO> petDealGroupStructAttributeDos = Lists.newArrayList();
        for (AttrM attr : attrMs) {
            if (attr == null) {
                continue;
            }
            addSingleAttributeInfo(petDealGroupStructAttributeDos, attr);
        }
        return petDealGroupStructAttributeDos;
    }

    private void addSingleAttributeInfo(List<DealDetailStructAttrVO> petDealGroupStructAttributeDos, AttrM attr) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.addSingleAttributeInfo(java.util.List,com.sankuai.dzviewscene.shelf.platform.common.model.AttrM)");
        DealDetailStructAttrVO petDealGroupStructAttributeDo = new DealDetailStructAttrVO();
        //洗澡、美容针对狗和猫特殊属性处理，合并狗和猫的适用体重和适用毛长
        boolean isProcessedWeight = addDogCatSpecialAttributeInfo(petDealGroupStructAttributeDos, attr, BODY_TYPE_PET_DOG, WEIGHT_PET_CAT, PET_WEIGHT_ATTRIBUTE_NAME);
        boolean isProcessedHair = addDogCatSpecialAttributeInfo(petDealGroupStructAttributeDos, attr, HAIR_LENGTH_PET_DOG, HAIR_LENGTH_PET_CAT, PET_HAIR_ATTRIBUTE_NAME);
        if (isProcessedWeight || isProcessedHair) {
            //如果是适用体重或者是适用毛长，合并处理，后续不再处理，直接返回
            return;
        }
        petDealGroupStructAttributeDo.setAttrName(getSkuAttrName(attr.getName()));
        petDealGroupStructAttributeDo.setAttrValues(getAttrValues(attr.getValue()));
        int style = CollectionUtils.size(getAttrValues(attr.getValue())) > 1 ? DEAL_DETAIL_TAG_STRUCT_ATTRS_STYLE : DEAL_DETAIL_STRING_STRUCT_ATTRS_STYLE;
        petDealGroupStructAttributeDo.setStyle(style);
        petDealGroupStructAttributeDos.add(petDealGroupStructAttributeDo);
    }

    private String getSkuAttrName(String attrName) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getSkuAttrName(java.lang.String)");
        if (StringUtils.isEmpty(attrName) || petDealDetailSpecification == null || CollectionUtils.isEmpty(petDealDetailSpecification.getPetStructInfosModels())) {
            return null;
        }
        PetStructInfosModel petStructInfosModel = petDealDetailSpecification.getPetStructInfosModels().stream().filter(model -> attrName.equals(model.getAttrName())).findFirst().orElse(null);
        if (petStructInfosModel == null) {
            return null;
        }
        return petStructInfosModel.getStructAttrTitle();
    }

    private List<String> getAttrValues(String attrValuesStr) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getAttrValues(java.lang.String)");
        if (StringUtils.isEmpty(attrValuesStr)) {
            return null;
        }
        return Lists.newArrayList(attrValuesStr.split(ATTR_VALUE_SEPERATOR));
    }

    private boolean addDogCatSpecialAttributeInfo(List<DealDetailStructAttrVO> petDealGroupStructAttributeDos, AttrM attr, String attributeDogKey, String attributeCatKey, String resultAttributeName) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.addDogCatSpecialAttributeInfo(List,AttrM,String,String,String)");
        if (!attributeDogKey.equals(attr.getName()) && !attributeCatKey.equals(attr.getName())) {
            return false;
        }
        Optional<DealDetailStructAttrVO> attributeDoOptional = petDealGroupStructAttributeDos.stream()
                .filter(attributeDO -> resultAttributeName.equals(attributeDO.getAttrName())).findAny();
        DealDetailStructAttrVO specialAttributeDo;
        if (attributeDoOptional.isPresent()) {
            specialAttributeDo = attributeDoOptional.get();
        } else {
            specialAttributeDo = new DealDetailStructAttrVO();
            petDealGroupStructAttributeDos.add(specialAttributeDo);
        }
        String specialPrefix = attributeDogKey.equals(attr.getName()) ? DOG_PREFIX_STR : attributeCatKey.equals(attr.getName()) ? CAT_PREFIX_STR : StringUtils.EMPTY;
        String specialContent = CollectionUtils.isEmpty(getAttrValues(attr.getValue())) ? StringUtils.EMPTY : getAttrValues(attr.getValue()).get(0);
        specialAttributeDo.setAttrName(resultAttributeName);
        List<String> attrValues = specialAttributeDo.getAttrValues() == null ? Lists.newArrayList() : specialAttributeDo.getAttrValues();
        String attrValue;
        if (CollectionUtils.isNotEmpty(attrValues)) {
            attrValue = String.format(ATTR_VALUE_FORMAT, attrValues.get(0).trim(), specialPrefix, specialContent.trim());
            attrValues.set(0, attrValue);
        } else {
            attrValue = specialPrefix + specialContent;
            attrValues.add(attrValue);
        }
        specialAttributeDo.setAttrValues(attrValues);
        specialAttributeDo.setStyle(0);
        return true;
    }

    private String getDesc(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getDesc(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if (productM == null) {
            return null;
        }
        String dealDetailText = productM.getAttr(DETAIL_INFO);
        if (StringUtils.isEmpty(dealDetailText)) {
            return null;
        }
        UniformStructModel uniformStruct = JsonCodec.decode(dealDetailText, UniformStructModel.class);
        if (uniformStruct == null) {
            return null;
        }
        return getRichText(uniformStruct.getContent());
    }

    private String getRichText(List<UniformStructContentModel> structContentModels) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getRichText(java.util.List)");
        if (CollectionUtils.isEmpty(structContentModels)) {
            return null;
        }
        Object struct = structContentModels.stream().filter(model -> StringUtils.isNotEmpty(model.getType()) && model.getType().equals("richtext")).findFirst().orElse(null);
        if (struct == null) {
            return null;
        }
        if (!(struct instanceof UniformStructContentModel)) {
            return null;
        }
        UniformStructContentModel richTextModel = (UniformStructContentModel) struct;
        if (richTextModel == null || richTextModel.getData() == null) {
            return null;
        }
        return richTextModel.getData().toString();
    }

    private List<OptionalDealSkuStructInfoVO> getDealSkuStructInfoVOList(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getDealSkuStructInfoVOList(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if (productM == null || StringUtil.isEmpty(productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME))) {
            return null;
        }
        List<OptionalDealSkuStructInfoVO> optionalDealSkuStructInfoVOS = new ArrayList<>();
        List<OptionalDealSkuStructInfoVO> dealSkuStructInfoVOSFromMustGroups = buildDealSkuStructInfoVOsByMustGroups(productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME));
        if (CollectionUtils.isNotEmpty(dealSkuStructInfoVOSFromMustGroups)) {
            optionalDealSkuStructInfoVOS.addAll(dealSkuStructInfoVOSFromMustGroups);
        }
        List<OptionalDealSkuStructInfoVO> dealSkuStructInfoVOSFromOptionalGroups = buildDealSkuStructInfoVOsByOptionalGroups(productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME));
        if (CollectionUtils.isNotEmpty(dealSkuStructInfoVOSFromOptionalGroups)) {
            optionalDealSkuStructInfoVOS.addAll(dealSkuStructInfoVOSFromOptionalGroups);
        }
        return getNormalizedDealSkuStructInfoVOS(optionalDealSkuStructInfoVOS);
    }

    private List<OptionalDealSkuStructInfoVO> getNormalizedDealSkuStructInfoVOS(List<OptionalDealSkuStructInfoVO> optionalDealSkuStructInfoVOS) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getNormalizedDealSkuStructInfoVOS(java.util.List)");
        if (CollectionUtils.isEmpty(optionalDealSkuStructInfoVOS)) {
            return null;
        }
        //1 "疫苗接种"模块置顶
        List<OptionalDealSkuStructInfoVO> sortedGroups = optionalDealSkuStructInfoVOS.stream().sorted((o1, o2) -> {
            if (hasVaccineInoculationSkuList(o1) && hasVaccineInoculationSkuList(o2)) {
                return 0;
            }
            if (hasVaccineInoculationSkuList(o1)) {
                return -1;
            }
            if (hasVaccineInoculationSkuList(o2)) {
                return 1;
            }
            return 0;
        }).collect(Collectors.toList());
        //2 全部可享模块不在第一位时要加分割线
        OptionalDealSkuStructInfoVO firstGroup = CollectUtils.firstValue(sortedGroups);
        if (firstGroup != null && firstGroup.getDesc() == null) {
            return sortedGroups;
        }
        for (OptionalDealSkuStructInfoVO group : sortedGroups) {
            if (group != null && StringUtil.isEmpty(group.getDesc())) {
                group.setDesc(MUST_GROUP_DESC_FORMAT);
            }
        }
        return sortedGroups;
    }

    private boolean hasVaccineInoculationSkuList(OptionalDealSkuStructInfoVO skuStructInfoVO) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.hasVaccineInoculationSkuList(OptionalDealSkuStructInfoVO)");
        if (skuStructInfoVO == null || CollectionUtils.isEmpty(skuStructInfoVO.getDealStructInfoGroupList())) {
            return false;
        }
        DealSkuStructInfoVOGroup sku = skuStructInfoVO.getDealStructInfoGroupList().stream().filter(skusGroup -> skusGroup != null && VACCINE_INOCULATION_PRODUCT_CATEGORU_NAME.equals(skusGroup.getName())).findFirst().orElse(null);
        return sku != null;
    }

    private List<OptionalDealSkuStructInfoVO> buildDealSkuStructInfoVOsByMustGroups(String dealDetail) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDealSkuStructInfoVOsByMustGroups(java.lang.String)");
        List<ProductSkuCategoryModel> productSkuCategoryModels = getProductSkuCategoryModels(dealDetail);
        List<MustSkuItemsGroupDto> mustSkuItemsGroupDtos = getMustSkuItemsGroupDtoList(dealDetail);
        return mustSkuItemsGroupDtos.stream().map(mustSkuItemsGroupDto -> {
            if (mustSkuItemsGroupDto == null) {
                return null;
            }
            return convertSkuItems2DealSkuStructInfoVOGroups(mustSkuItemsGroupDto.getSkuItems(), productSkuCategoryModels);
        }).filter(skuList -> CollectionUtils.isNotEmpty(skuList)).map(skuList -> buildOptionalDealSkuStructInfoVO(null, skuList)).collect(Collectors.toList());
    }

    private List<OptionalDealSkuStructInfoVO> buildDealSkuStructInfoVOsByOptionalGroups(String dealDetail) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDealSkuStructInfoVOsByOptionalGroups(java.lang.String)");
        List<ProductSkuCategoryModel> productSkuCategoryModels = getProductSkuCategoryModels(dealDetail);
        List<OptionalSkuItemsGroupDto> optionalSkuItemsGroupDtos = getOptionalSkuItemsGroupDtoList(dealDetail);
        if (CollectionUtils.isEmpty(optionalSkuItemsGroupDtos)) {
            return null;
        }
        Map<List<DealSkuStructInfoVOGroup>, Integer> optionalSkuItemsGroupDtos2OptionalNumMap = optionalSkuItemsGroupDtos.stream().collect(HashMap::new, (map, optionalSkuItemsGroupDto) -> {
            if (optionalSkuItemsGroupDto != null) {
                List<DealSkuStructInfoVOGroup> dealSkuStructInfoVOGroups = convertSkuItems2DealSkuStructInfoVOGroups(optionalSkuItemsGroupDto.getSkuItems(), productSkuCategoryModels);
                map.put(dealSkuStructInfoVOGroups, optionalSkuItemsGroupDto.getOptionalCount());
            }
        }, HashMap::putAll);
        if (MapUtils.isEmpty(optionalSkuItemsGroupDtos2OptionalNumMap)) {
            return null;
        }
        return optionalSkuItemsGroupDtos2OptionalNumMap.entrySet().stream()
                .map(entry -> buildOptionalDealSkuStructInfoVO(getDesc(entry.getKey(), entry.getValue()), entry.getKey())).collect(Collectors.toList());
    }

    private String getDesc(List<DealSkuStructInfoVOGroup> skuList, int optionalNum) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getDesc(java.util.List,int)");
        if (CollectionUtils.isEmpty(skuList) || optionalNum == 0) {
            return null;
        }
        int skuNum = 0;
        for (DealSkuStructInfoVOGroup sku : skuList) {
            if (sku != null && CollectionUtils.isNotEmpty(sku.getDealStructInfoList())) {
                skuNum += sku.getDealStructInfoList().size();
            }
        }
        return String.format(OPTIONAL_GROUP_DESC_FORMAT, skuNum, optionalNum);
    }

    private OptionalDealSkuStructInfoVO buildOptionalDealSkuStructInfoVO(String desc, List<DealSkuStructInfoVOGroup> dealStructInfoGroupList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildOptionalDealSkuStructInfoVO(java.lang.String,java.util.List)");
        OptionalDealSkuStructInfoVO optionalDealSkuStructInfoVO = new OptionalDealSkuStructInfoVO();
        optionalDealSkuStructInfoVO.setDesc(desc);
        optionalDealSkuStructInfoVO.setDealStructInfoGroupList(dealStructInfoGroupList);
        return optionalDealSkuStructInfoVO;
    }

    private List<DealSkuStructInfoVOGroup> convertSkuItems2DealSkuStructInfoVOGroups(List<SkuItemDto> skuItems, List<ProductSkuCategoryModel> productSkuCategoryModels) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.convertSkuItems2DealSkuStructInfoVOGroups(java.util.List,java.util.List)");
        if (CollectionUtils.isEmpty(skuItems)) {
            return null;
        }
        Map<String, List<DealSkuStructInfoVO>> category2DealSkuStructInfoVOsMap = skuItems.stream().collect(LinkedHashMap::new, (map, skuItemDto) -> {
            if (skuItemDto == null) {
                return;
            }
            //获取sku的分类
            String categoryName = getProductCategoryName(skuItemDto.getProductCategory(), productSkuCategoryModels);
            //通过sku信息构造sku结构化model
            DealSkuStructInfoVO dealSkuStructInfoVO = convertSkuItemDto2DealSkuStructInfoVO(skuItemDto, productSkuCategoryModels, categoryName);
            //categoryName为"其他疫苗"的组内元素要放在"疫苗接种"组里
            if (OTHER_VACCINE_PRODUCT_CATEGORY_NAME.equals(categoryName)) {
                categoryName = VACCINE_INOCULATION_PRODUCT_CATEGORU_NAME;
            }
            //按照sku分类名对sku列表进行分组
            if (map.get(categoryName) != null) {
                List<DealSkuStructInfoVO> skusExisted = JsonCodec.decode(JsonCodec.encode(map.get(categoryName)), new TypeReference<List<DealSkuStructInfoVO>>() {
                });
                List<DealSkuStructInfoVO> allSkuList = new ArrayList<>();
                allSkuList.addAll(skusExisted);
                allSkuList.add(dealSkuStructInfoVO);
                map.put(categoryName, allSkuList);
                return;
            }
            List<DealSkuStructInfoVO> dealSkuStructList = new ArrayList<>();
            dealSkuStructList.add(dealSkuStructInfoVO);
            map.put(categoryName, dealSkuStructList);
        }, LinkedHashMap::putAll);
        category2DealSkuStructInfoVOsMap.entrySet().stream().collect(HashMap::new, (map, entry) -> {
            DealSkuStructInfoVOGroup dealSkuStructInfoVOGroup = buildDealSkuStructInfoVOGroup(entry.getKey(), entry.getValue());
            if (dealSkuStructInfoVOGroup == null) {
                return;
            }
            map.put(dealSkuStructInfoVOGroup, 0);
        }, HashMap::putAll);
        //用分组后的sku列表构造DealSkuStructInfoVOGroup列表并按：疫苗接种 > 挂号建档 > 基础体检 > 体内驱虫 > 体外驱虫 > 体内外通用驱虫 排序
        return category2DealSkuStructInfoVOsMap.entrySet().stream().
                map(entry -> buildDealSkuStructInfoVOGroup(entry.getKey(), entry.getValue())).
                sorted(Comparator.comparingInt(this::getSkusGroupSequence)).collect(Collectors.toList());
    }

    private int getSkusGroupSequence(DealSkuStructInfoVOGroup skuGroup) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getSkusGroupSequence(com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.DealSkuStructInfoVOGroup)");
        if (skuGroup == null || StringUtil.isEmpty(skuGroup.getName())) {
            return Integer.MAX_VALUE;
        }
        PetDetailSkusGroupSequenceModel sequenceModel = petDealDetailSpecification.getPetDetailSkusGroupSequenceModels().stream().
                filter(model -> skuGroup.getName().equals(model.getSkusGroupName())).findFirst().orElse(null);
        if (sequenceModel == null) {
            return Integer.MAX_VALUE;
        }
        return sequenceModel.getSequenceId();
    }

    private DealSkuStructInfoVOGroup buildDealSkuStructInfoVOGroup(String groupName, List<DealSkuStructInfoVO> dealStructInfoList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDealSkuStructInfoVOGroup(java.lang.String,java.util.List)");
        DealSkuStructInfoVOGroup dealSkuStructInfoVOGroup = new DealSkuStructInfoVOGroup();
        dealSkuStructInfoVOGroup.setName(groupName);
        dealSkuStructInfoVOGroup.setDealStructInfoList(dealStructInfoList);
        return dealSkuStructInfoVOGroup;
    }

    private DealSkuStructInfoVO convertSkuItemDto2DealSkuStructInfoVO(SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productSkuCategoryModels, String categoryName) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.convertSkuItemDto2DealSkuStructInfoVO(SkuItemDto,List,String)");
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        String productDesc = getProductAttrValueByProductChnName(skuItemDto.getAttrItems(), PRODUCT_DESC_CHN_NAME);
        String vaccineType = getProductAttrValueByProductChnName(skuItemDto.getAttrItems(), VACCINE_TYPE_CHN_NAME);
        String vaccineBrand = getProductAttrValueByProductChnName(skuItemDto.getAttrItems(), VACCINE_BRAND_CHN_NAME);
        String injectionNum = getProductAttrValueByProductChnName(skuItemDto.getAttrItems(), INJECTION_NUM_CHN_NAME);
        List<DealStructVO> items = buildDealStructVOList(vaccineType);
        String title = getSkuTitle(vaccineType, productDesc, vaccineBrand, categoryName, injectionNum);
        return buildDealSkuStructInfoVO(skuItemDto.getMarketPrice(), skuItemDto.getCopies(), title, items);
    }

    private String getSkuTitle(String vaccineType, String productDesc, String vaccineBrand, String categoryName, String injectionNum) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getSkuTitle(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)");
        if (!VACCINE_INOCULATION_PRODUCT_CATEGORU_NAME.equals(categoryName)) {
            return productDesc;
        }
        if (StringUtil.isEmpty(vaccineBrand) || StringUtil.isEmpty(vaccineType)) {
            return productDesc;
        }
        if (StringUtil.isEmpty(injectionNum)) {
            return String.format(VACCINE_BRAND_VACCINE_TYPE_SKU_TITLE_FORMAT, vaccineBrand, vaccineType);
        }
        return String.format(VACCINE_BRAND_VACCINE_TYPE_IJECTION_NUM_SKU_TITLE_FORMAT, vaccineBrand, vaccineType, injectionNum);
    }

    private List<DealStructVO> buildDealStructVOList(String vaccineType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDealStructVOList(java.lang.String)");
        if (StringUtil.isEmpty(vaccineType) || petDealDetailSpecification == null || CollectionUtils.isEmpty(petDealDetailSpecification.getVaccineSpecificationModels())) {
            return null;
        }
        VaccineSpecificationModel specificationModel = petDealDetailSpecification.getVaccineSpecificationModels().stream().filter(
                specification -> vaccineType.equals(specification.getVaccineType())).findFirst().orElse(null);
        if (specificationModel == null) {
            return null;
        }
        if (MapUtils.isEmpty(specificationModel.getSpecificationContentMap())) {
            return null;
        }
        return specificationModel.getSpecificationContentMap().entrySet().stream().map(entry -> {
            if (entry == null) {
                return null;
            }
            DealStructVO dealStructVO = new DealStructVO();
            dealStructVO.setName(entry.getKey());
            dealStructVO.setValue(entry.getValue());
            return dealStructVO;
        }).filter(dealStructVO -> dealStructVO != null).collect(Collectors.toList());
    }

    private DealSkuStructInfoVO buildDealSkuStructInfoVO(BigDecimal price, int copies, String title, List<DealStructVO> items) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.buildDealSkuStructInfoVO(java.math.BigDecimal,int,java.lang.String,java.util.List)");
        DealSkuStructInfoVO dealSkuStructInfoVO = new DealSkuStructInfoVO();
        if (price != null) {
            dealSkuStructInfoVO.setPrice(String.format(PRODUCT_PRICE_FORMAT, price.stripTrailingZeros().toPlainString()));
        }
        dealSkuStructInfoVO.setCopies(String.format(PRODUCT_COPIES_FORMAT, String.valueOf(copies)));
        dealSkuStructInfoVO.setTitle(title);
        dealSkuStructInfoVO.setItems(items);
        return dealSkuStructInfoVO;
    }

    private String getProductAttrValueByProductChnName(List<SkuAttrItemDto> attrItems, String chnName) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getProductAttrValueByProductChnName(java.util.List,java.lang.String)");
        if (CollectionUtils.isEmpty(attrItems) || StringUtil.isEmpty(chnName)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = attrItems.stream().filter(attr -> attr != null && chnName.equals(attr.getChnName())).findFirst().orElse(null);
        if (skuAttrItemDto == null) {
            return null;
        }
        return skuAttrItemDto.getAttrValue();
    }

    private String getProductCategoryName(long productCategoryId, List<ProductSkuCategoryModel> productSkuCategoryModels) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getProductCategoryName(long,java.util.List)");
        if (CollectionUtils.isEmpty(productSkuCategoryModels)) {
            return null;
        }
        ProductSkuCategoryModel skuCategoryModel = productSkuCategoryModels.stream().filter(model -> {
            if (model == null || model.getProductCategoryId() == null) {
                return false;
            }
            return model.getProductCategoryId() == productCategoryId;
        }).findFirst().orElse(null);
        if (skuCategoryModel == null) {
            return null;
        }
        return skuCategoryModel.getCnName();
    }

    private List<ProductSkuCategoryModel> getProductSkuCategoryModels(String dealDetail) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getProductSkuCategoryModels(java.lang.String)");
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || CollectionUtils.isEmpty(dealStructModel.getProductCategories())) {
            return null;
        }
        return dealStructModel.getProductCategories();
    }

    //获取团单必选项目组列表
    private List<MustSkuItemsGroupDto> getMustSkuItemsGroupDtoList(String dealDetail) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getMustSkuItemsGroupDtoList(java.lang.String)");
        DealDetailModel dealDetailModel = getDealDetailModel(dealDetail);
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailModel.getSkuUniStructuredDto().getMustGroups();
    }

    //获取团单可选项目组列表
    private List<OptionalSkuItemsGroupDto> getOptionalSkuItemsGroupDtoList(String dealDetail) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getOptionalSkuItemsGroupDtoList(java.lang.String)");
        DealDetailModel dealDetailModel = getDealDetailModel(dealDetail);
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailModel.getSkuUniStructuredDto().getOptionalGroups();
    }

    //获取结构化团详model
    private DealDetailModel getDealDetailModel(String dealDetail) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getDealDetailModel(java.lang.String)");
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        DealDetailModel dealDetailModel = JsonCodec.decode(dealStructModel.getStract(), DealDetailModel.class);
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailModel;
    }

    //获取团单结构化信息
    private DealStructModel getDealStructModel(String dealDetail) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.pet.builder.PetVaccineDealDetailBuilderExt.getDealStructModel(java.lang.String)");
        if (StringUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    @Data
    private static class PetDealDetailSpecification {
        private List<VaccineSpecificationModel> vaccineSpecificationModels;
        private List<PetStructInfosModel> petStructInfosModels;
        private List<PetDetailSkusGroupSequenceModel> petDetailSkusGroupSequenceModels;
        private List<String> structInfoAttrList;
    }

    @Data
    private static class VaccineSpecificationModel {
        private String vaccineType;
        private Map<String, String> specificationContentMap;
    }

    @Data
    private static class PetStructInfosModel {
        private String attrName;
        private String structAttrTitle;
    }

    @Data
    private static class PetDetailSkusGroupSequenceModel {
        private int sequenceId;
        private String SkusGroupName;
    }

}
