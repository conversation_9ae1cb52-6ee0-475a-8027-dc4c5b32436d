package com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPoolScenesListProductBuilder;
import com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPoolScenesListProductListBuilder;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist.ProductListBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.flow.MergeQueryFilterProductsFlow;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Title: BackroomPoolScenesTemplate
 * Description: 密室在拼场次列表
 *
 * <AUTHOR>
 * @date 2021-10-16
 */
@ActivityTemplate(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, sceneCode = "backroom_pool_scenes_list", name = "密室在拼场次列表")
public class BackroomPoolScenesListTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.BackroomPoolScenesListTemplate.flow()");
        return MergeQueryFilterProductsFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.BackroomPoolScenesListTemplate.extParams(java.util.Map)");
        // 1. 设置召回参数, 代表只召回一组商品, 商品是密室
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.backroom.name));
        // 2. 设置第一组商品参数,
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.backroom.name, new HashMap<String, Object>() {{
                // 2.1 配置召回策略
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.shopGeneralProductsQuery.name());
                put(QueryFetcher.Params.productType, 4);
                put(QueryFetcher.Params.spuType, 522L);
                // 2.2 配置填充策略
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.generalProductPadding.name());
                // 2.3 设置第一组商品填充参数
                put(PaddingFetcher.Params.planId, "10100168");
                put(MergeQueryFetcher.Params.rankId, "10500168");
                put(QueryFetcher.Params.filterInvalid, false);
            }});
        }});
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.BackroomPoolScenesListTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.BackroomPoolScenesListTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.BackroomPoolScenesListTemplate.extAbilities(java.util.Map)");
        /*召回能力*/
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        /*填充能力*/
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        /*融合召回填充能力*/
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.BackroomPoolScenesListTemplate.extPoints(java.util.Map)");
        extPoints.put(ProductBuilderVoExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, BackroomPoolScenesListProductBuilder.class);
        extPoints.put(ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE, BackroomPoolScenesListProductListBuilder.class);
    }
}
