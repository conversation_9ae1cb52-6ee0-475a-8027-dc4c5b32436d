package com.sankuai.dzviewscene.shelf.business.filterlist.collect;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.filterlist.collect.build.MyCollectBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.IAbility;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityValidator;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.framework.core.IExtPoint;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.flow.FilterListOnlyProductsFlow;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ActivityTemplate(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, sceneCode = "my_collect_list", name = "我的收藏列表")
public class MyCollectListTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.collect.MyCollectListTemplate.flow()");
        return FilterListOnlyProductsFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.collect.MyCollectListTemplate.extParams(java.util.Map)");
        // 1. 设置召回参数, 代表只召回一组商品, 商品是化妆品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.joy_inactive_role_play_spu.name));

        // 2. 设置第一组商品参数,
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.joy_inactive_role_play_spu.name, new HashMap<String, Object>() {{
                // 2.1 配置召回策略
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.paramGeneralProductsQuery.name());
                // 2.2 配置填充策略
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.resourceSpuPadding.name());
                // 2.3 设置第一组商品填充参数。供应链剧本杀资源类型是25
                put(PaddingFetcher.Params.QUERY_RESOURCE_TYPE, 25);
            }});
        }});
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.collect.MyCollectListTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.collect.MyCollectListTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.collect.MyCollectListTemplate.extAbilities(java.util.Map)");
        /*召回能力*/
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        /*填充能力*/
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.collect.MyCollectListTemplate.extPoints(java.util.Map)");
        extPoints.put(ProductBuilderVoExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, MyCollectBuilderExt.class);

    }
}
