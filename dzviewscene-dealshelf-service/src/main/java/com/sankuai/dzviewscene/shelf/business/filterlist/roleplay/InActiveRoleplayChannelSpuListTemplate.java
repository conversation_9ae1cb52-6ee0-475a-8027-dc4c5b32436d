package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.InActiveRolePlayChannelSpuBuilderExt;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.InactiveRolePlayChannelSpuListProductListBuilderExt;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.validator.FilterListProductTypeValidator;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.validator.RolePlaySpuChannelListValidator;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.IAbility;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityValidator;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.framework.core.IExtPoint;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuResourcePaddingHandler;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist.ProductListBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.flow.FilterListOnlyProductsFlow;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * Description: 剧本杀频道页-热门剧本列表https://km.sankuai.com/page/891841018
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
@ActivityTemplate(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, sceneCode = "roleplay_channel_standardproduct_list", name = "剧本杀频道页-热门剧本列表")
public class InActiveRoleplayChannelSpuListTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.InActiveRoleplayChannelSpuListTemplate.flow()");
        return FilterListOnlyProductsFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.InActiveRoleplayChannelSpuListTemplate.extParams(java.util.Map)");

        // 1. 设置召回参数, 代表只召回一组商品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.joy_inactive_role_play_spu.name));

        // 2. 设置第一组商品参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.joy_inactive_role_play_spu.name, new HashMap<String, Object>() {{
                // 2.1 配置召回策略
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.joyPinProductRecommendQuery.name());
                put(QueryFetcher.Params.recommendBizId, 114);
                put(QueryFetcher.Params.recommendBizParam, buildBizParams());
                put(QueryFetcher.Params.maxPageSize, 10);
                put(QueryFetcher.Params.minQueryNum, 5);
                // 2.2 配置填充策略
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.resourceSpuPadding.name());
                // 2.3 设置第一组商品填充参数
                put(PaddingFetcher.Params.QUERY_RESOURCE_TYPE, 25);

            }});
        }});
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.InActiveRoleplayChannelSpuListTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.InActiveRoleplayChannelSpuListTemplate.extValidators(java.util.List)");
        validators.add(RolePlaySpuChannelListValidator.class);
    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.InActiveRoleplayChannelSpuListTemplate.extAbilities(java.util.Map)");
        /*召回能力*/
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        /*填充能力*/
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        /*融合召回填充能力*/
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.InActiveRoleplayChannelSpuListTemplate.extPoints(java.util.Map)");

        // 1. 商品VO构造扩展点实现
        extPoints.put(ProductBuilderVoExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, InActiveRolePlayChannelSpuBuilderExt.class);
        // 2. 商品列表样式构造拓展点
        extPoints.put(ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE, InactiveRolePlayChannelSpuListProductListBuilderExt.class);
    }

    private Map<String, Object> buildBizParams(){
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.InActiveRoleplayChannelSpuListTemplate.buildBizParams()");
        Map<String, Object> bizParams = Maps.newHashMap();

        bizParams.put("sortType", "AI");
        return bizParams;
    }
}
