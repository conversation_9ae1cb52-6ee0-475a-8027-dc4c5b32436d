package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayCityPinProductBuilderExt;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayCityPinProductListBuilderExt;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayVirtualDPUserIdContext;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.fetcher.RolePlayCityPinListFetcherExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist.ProductListBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.MultiShowFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.filterlist.flow.ParallelHandleFilterAndProductsFlow;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import org.apache.commons.collections.MapUtils;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ActivityTemplate(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE,
        sceneCode = "active_roleplay_city_pin_list_filter",
        name = "落地页-实景剧本杀拼场承接列表首屏接口")
public class ActiveRolePlayCityPinListFilterTemplate implements IActivityTemplate {
    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.flow()");
        return ParallelHandleFilterAndProductsFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.extParams(java.util.Map)");
        Map<String, Object> extraMap = loadExtraMap(exParams);
        DateTime queryPoolDate = loadQueryPoolDate(extraMap);
        DateTime queryPoolEndDate = queryPoolDate.plusDays(1).minusSeconds(1);

        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.joy_active_role_play.name));
        exParams.put(PaddingFetcher.Params.POOL_NEED_PRICE_INFO, true);
        exParams.put(PaddingFetcher.Params.POOL_NEED_USER_INFO, false);
        exParams.put(QueryFetcher.Params.QUERY_PIN_POOL_START_DATE, queryPoolDate.toDate());
        exParams.put(QueryFetcher.Params.QUERY_PIN_POOL_END_DATE, queryPoolEndDate.toDate());
        exParams.put(PaddingFetcher.Params.QUERY_PRICE_START_TIME, new DateTime().withTimeAtStartOfDay().toDate());
        exParams.put(PaddingFetcher.Params.CAL_PRICE_DAYS, 7);
        exParams.put(PaddingFetcher.Params.QUERY_PRICE_SCENE, RequestSceneEnum.NO_PROMO_SHELF_LIST.getScene());

        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.joy_active_role_play.name, new HashMap<String, Object>() {
                {
                    put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.joyPinProductRecommendQuery.name());
                    put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.generalProductPadding.name());
                    put(QueryFetcher.Params.recommendBizParam, loadExtraMap(exParams));
                    put(QueryFetcher.Params.recommendBizId, 144);
                    put(QueryFetcher.Params.maxPageSize, 5);
                    put(PaddingFetcher.Params.planId, "10100161");
                }
            });
        }});
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.extContexts(java.util.List)");
        extContexts.add(RolePlayVirtualDPUserIdContext.class);
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.extAbilities(java.util.Map)");
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, MultiShowFilterFetcher.class);
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.extPoints(java.util.Map)");
        extPoints.put(PaddingFetcherExt.EXT_POINT_PADDING_FETCHER_CODE, RolePlayCityPinListFetcherExt.class);
        extPoints.put(ProductBuilderVoExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, ActiveRolePlayCityPinProductBuilderExt.class);
        extPoints.put(ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE, ActiveRolePlayCityPinProductListBuilderExt.class);

    }


    private Map<String, Object> loadExtraMap(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.loadExtraMap(java.util.Map)");
        Map<String, Object> extraMap = Optional.ofNullable(JsonCodec.decode((String) exParams.get(FilterListActivityConstants.Params.extra), new TypeReference<Map<String, Object>>() {
        })).orElse(Maps.newHashMap());
        if (!extraMap.containsKey("poolBeginDate")) {
            extraMap.put("poolBeginDate", new DateTime().toString("MMdd"));
        }
        if (!extraMap.containsKey("sortType")) {
            extraMap.put("sortType", "AI");
        }
        return extraMap;
    }

    private DateTime loadQueryPoolDate(Map<String, Object> extraMap) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.template.pinlist.ActiveRolePlayCityPinListFilterTemplate.loadQueryPoolDate(java.util.Map)");
        if (MapUtils.isEmpty(extraMap) || extraMap.get("timestamp") == null) {
            return new DateTime().withTimeAtStartOfDay();
        }
        long timeStamp = NumberUtils.toLong((String) extraMap.get("timestamp"));
        return new DateTime(timeStamp);
    }
}
