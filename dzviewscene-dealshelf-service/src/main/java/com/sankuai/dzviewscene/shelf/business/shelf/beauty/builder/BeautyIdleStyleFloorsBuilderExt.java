package com.sankuai.dzviewscene.shelf.business.shelf.beauty.builder;

import com.dianping.appkit.utils.VersionUtil;
import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.dianping.vc.sdk.lang.DateUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.JuHuaSuanUtils;
import com.sankuai.dzviewscene.product.utils.DzItemVOStyleUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.VipPriceEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.query.HasIdlePromoSimplexQuery;
import com.sankuai.dzviewscene.shelf.business.utils.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.AbstractDefaultFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhangsuping on 2021/3/1.
 */
@ExtPointInstance(name = "丽人-闲时样式团购货架楼层模块构造扩展点")
public class BeautyIdleStyleFloorsBuilderExt extends AbstractDefaultFloorsBuilderExt {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.constant.config", defaultValue = "{}")
    private FloorConstant floorConstant;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.hair.detail.jumpurl.switch.config", defaultValue = "{}")
    private SwitchConfig switchConfig;

    public static final String DEAL_DETAIL_URL_WITH_VERSION_CHANGED = "dealDetailUrlWithVersionChanged";

    private static final String DOUHU_AB_TEST_RESULT_DP = "exp000375_b";

    private static final String DOUHU_AB_TEST_RESULT_MT = "exp000376_b";

    private static final List<String> ZH_WEEK_LIST = Lists.newArrayList("周日", "周六");

    @Override
    public String itemComponentRichLabelsTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId){
        return ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(activityContext,
                itemComponentTitle(activityContext, groupName, productM, filterId),
                ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD);
    }

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        return floorConstant.defaultShowProductCount;
    }

    @Override
    public FloatTagVO itemComponentPreTitleTag(ActivityContext activityContext, String groupName, ProductM productM) {
        if(StringUtils.isNotEmpty(productM.getPicUrl())) {
            return null;
        }
        return CollectUtils.firstValue(PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM));
    }

    @Override
    public String jumpUrl(ActivityContext activityContext, String groupName, ProductM productM) {
        if (productM == null) {
            return null;
        }
        if (switchConfig != null && switchConfig.isDouhuSwitchOpen() && dealCategoryMeet(productM.getCategoryId())) {
            String version = activityContext.getParam(ShelfActivityConstants.Params.appVersion);
            int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
            if (appVersionMeet(version, platform) && isDouhuHit(activityContext)) {
                return productM.getAttr(DEAL_DETAIL_URL_WITH_VERSION_CHANGED);
            }
        }
        return productM.getJumpUrl();
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, DzItemAreaComponentVO itemAreaComponentVO, long filterId) {
        if (itemAreaComponentVO.getProductItems().size() <= 3) {
            return null;
        }
        return floorConstant.getMorePrefix() + (itemAreaComponentVO.getProductItems().size() - itemAreaComponentVO.getDefaultShowNum()) + floorConstant.getMoreSuffix();
    }

    @Override
    public String itemComponentTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        if (CollectionUtils.isEmpty(floorConstant.getTitleFormatFilterIds()) || !floorConstant.getTitleFormatFilterIds().contains(filterId)) {
            return productM.getTitle();
        }
        return productM.getTitle();
    }

    @Override
    public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
        if(PerfectActivityBuildUtils.isDuringPerfectActivityPreheatTime(productM)) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(productM.getPurchase());
        return richLabelVO;
    }

    @Override
    public int itemShowType(ActivityContext activityContext, String groupName) {
        return 4;
    }

    @Override
    public DzSimpleButtonVO itemComponentButton(ActivityContext activityContext, String groupName, ProductM productM) {
        DzSimpleButtonVO buttonVO = new DzSimpleButtonVO();
        buttonVO.setName("抢购");
        buttonVO.setJumpUrl(productM.getJumpUrl());
        return buttonVO;
    }

    @Override
    public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        return calculateSalePrice(productM);
    }

    private String calculateSalePrice(ProductM productM) {
        String perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPrice(productM);
        if (PerfectActivityBuildUtils.isDuringPerfectActivity(productM) && org.apache.commons.lang3.StringUtils.isNotEmpty(perfectActivityPrice)) {
            return perfectActivityPrice;
        }
        // 会员卡
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.JOY_CARD.getType());
        if (productPromoPriceM != null) {
            return productPromoPriceM.getPromoPriceTag();
        }
        // 普通优惠
        productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM != null) {
            return productPromoPriceM.getPromoPriceTag();
        }
        return productM.getBasePriceTag();
    }

    @Override
    public List<DzTagVO> itemComponentPriceBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        try {
            List<DzTagVO> tags = PromoPerceptionUtils.buildPromoPerceptionPriceBottomTags(productM, calculateSalePrice(productM), activityContext.getParam(ShelfActivityConstants.Params.platform));
            return tags;
        } catch (Exception e) {
            Cat.logErrorWithCategory(String.format("DocBuilderUtils#itemComponentPriceBottomTags#buildExpressionDoc:%s", productM), e);
            return null;
        }
    }


    @Override
    public RichLabelVO activityRemainSecondsLabel(ActivityContext activityContext, String groupName, ProductM productM) {
        return PerfectActivityBuildUtils.buildActivityRemainSecondsLabel(productM);
    }

    @Override
    public List<RichLabelVO> itemComponentBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        if (hasIdlePromo(activityContext, productM)) {
            return buildCouponBottomTag(productM.getCoupons());
        }
        RichLabelVO card = buildRichLabel(productM.getCardPrice());
        RichLabelVO pin = buildRichLabel(productM.getPinPrice());
        return Lists.newArrayList(card, pin).stream().filter(richLabel -> richLabel!= null).collect(Collectors.toList());
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        if (StringUtils.isEmpty(productM.getPicUrl())) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(buildPictureComponent(PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), floorConstant.getPicWidth(), floorConstant.getPicHeight(), PictureURLBuilders.ScaleType.Cut), floorConstant.getHeaderPicAspectRadio()));
        if(PerfectActivityBuildUtils.isShowPerfectActivity(productM)) {
            picAreaVO.setFloatTags(PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM));
            return picAreaVO;
        }
        //不展示玩美季活动但是有玩美季活动时
        if(PerfectActivityBuildUtils.hasPerfectActivity(productM)) {
            return picAreaVO;
        }
        picAreaVO.setFloatTags(getFloatTags(activityContext, productM));
        return picAreaVO;
    }

    @Override
    public String itemComponentSale(ActivityContext activityContext, String groupName, ProductM productM) {
        if (productM.getSale() == null || productM.getSale().getSale() <= 0) {
            return Strings.EMPTY;
        }
        return productM.getSale().getSaleTag();
    }

    @Override
    public String itemComponentMarketPrice(ActivityContext activityContext, String groupName, ProductM productM) {
        return productM.getMarketPrice();
    }

    @Override
    public VipPriceVO itemComponentVipPrice(ActivityContext activityContext, String groupName, ProductM productM) {
        if (!hasIdlePromo(activityContext, productM) || isWeekend() || PerfectActivityBuildUtils.isShowPerfectActivity(productM)) {
            return null;
        }
        VipPriceVO vipPriceVO = new VipPriceVO();
        vipPriceVO.setPrice(productM.getPromo(PromoTypeEnum.IDLE_PROMO.getType()).getPromoPriceTag());
        vipPriceVO.setType(VipPriceEnums.IDLE_PROMO.getType());
        vipPriceVO.setTagImg(buildPictureComponent(floorConstant.idlePromPriceDescPic, floorConstant.idlePromPriceDescPicAspectRadio));
        return vipPriceVO;
    }

    @Override
    public List<DzPromoVO> itemComponentPromoTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        try {
            return DiscountItemPromoTagsUtil.buildDiscountItemPromoTag(productM, calculateSalePrice(productM));
        } catch (Exception e) {
            Cat.logErrorWithCategory(String.format("swimdealfloorsbuilderext#itemComponentPromoTags#productid:%s", productM.getProductId()), e);
            return null;
        }
    }

    @Override
    public List<String> itemComponentProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        return null;
    }

    private DzPictureComponentVO buildPictureComponent(String picUrl, double aspectRadio) {
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setAspectRadio(aspectRadio);
        pic.setPicUrl(picUrl);
        return pic;
    }

    private List<FloatTagVO> getFloatTags(ActivityContext ctx, ProductM productM) {
        //活动
        List<FloatTagVO> floatTagVOS = buildFloatTag(productM.getActivities(), productM.getAttr("beautyCheapHot"));
        if (CollectionUtils.isNotEmpty(floatTagVOS) ) {
            return floatTagVOS;
        }
        FloatTagVO juHuSuanPicTag = JuHuaSuanUtils.getJuHuSuanPicTag(productM);
        if (juHuSuanPicTag != null) {
            return Lists.newArrayList(juHuSuanPicTag);
        }
        //商家推荐
        if (ProductMAttrUtils.isShopRecommend(productM)) {
            return Lists.newArrayList(DzItemVOStyleUtils.getShopRecommendFloatTag(productM));
        }
        //工作日
        if (hasIdlePromo(ctx, productM) && !isWeekend()) {
            return buildFloatTags(floorConstant.idlePromHeadPic, floorConstant.idlePromHeadPicAspectRadio, FloatTagPositionEnums.LEFT_BOTTOM.getPosition());
        }
        return new ArrayList<>();
    }

    private RichLabelVO buildRichLabel(ProductPriceM productPriceM) {
        if (productPriceM == null) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        List<RichLabel> richLabelList = Lists.newArrayList(new RichLabel(productPriceM.getPriceDesc(), floorConstant.color777777, floorConstant.front11), new RichLabel(productPriceM.getPriceTag(), floorConstant.colorFF6633, floorConstant.front11));
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return richLabelVO;
    }

    /**
     * 大促（普通大促和美团周四半价）的优先级高于底价爆款
     *
     * @param activities
     * @param cheapHot
     * @return
     */
    private List<FloatTagVO> buildFloatTag(List<ProductActivityM> activities, String cheapHot) {
        List<FloatTagVO> activityFloatTags = buildActivityFloatTags(activities);
        if (CollectionUtils.isNotEmpty(activityFloatTags)) return activityFloatTags;
        return buildCheapHotFloatTags(cheapHot);
    }

    private List<FloatTagVO> buildCheapHotFloatTags(String cheapHot) {
        if (StringUtils.isEmpty(cheapHot) || "false".equals(cheapHot)) {
            return new ArrayList<>();
        }
        return buildFloatTags(floorConstant.cheapHotPic, floorConstant.cheapHotPicAspectRadio, FloatTagPositionEnums.LEFT_TOP.getPosition());
    }

    private List<FloatTagVO> buildActivityFloatTags(List<ProductActivityM> activities) {
        if (CollectionUtils.isEmpty(activities) || activities.get(0) == null || StringUtils.isEmpty(activities.get(0).getUrl())) {
            return new ArrayList<>();
        }
        return buildFloatTags(activities.get(0).getUrl(), floorConstant.activityPicAspectRadio, FloatTagPositionEnums.LEFT_TOP.getPosition());
    }

    private List<RichLabelVO> buildCouponBottomTag(List<ProductCouponM> coupons) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.builder.BeautyIdleStyleFloorsBuilderExt.buildCouponBottomTag(java.util.List)");
        List<RichLabel> richLabelList = buildCouponTag(coupons);
        if (CollectionUtils.isEmpty(richLabelList)) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return Lists.newArrayList(richLabelVO);
    }

    private List<FloatTagVO> buildFloatTags(String url, double aspectRadio, int position) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.builder.BeautyIdleStyleFloorsBuilderExt.buildFloatTags(java.lang.String,double,int)");
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(buildPictureComponent(url, aspectRadio));
        floatTagVO.setPosition(position);
        return Lists.newArrayList(floatTagVO);
    }

    private boolean hasIdlePromo(ActivityContext activityContext, ProductM productM) {
        long shopId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        if (platform == VCPlatformEnum.MT.getType()) {
            shopId = PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return new HasIdlePromoSimplexQuery(shopId, platform).match(null, productM);
    }

    private List<RichLabel> buildCouponTag(List<ProductCouponM> coupons) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.builder.BeautyIdleStyleFloorsBuilderExt.buildCouponTag(java.util.List)");
        if (CollectionUtils.isEmpty(coupons) || coupons.get(0) == null) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(new RichLabel(coupons.get(0).getCouponTag(), floorConstant.colorFF6200, floorConstant.front10));
    }

    private boolean isDouhuHit(ActivityContext activityContext) {
        return DouHuUtils.hitExpectSk(activityContext, DOUHU_AB_TEST_RESULT_MT) || DouHuUtils.hitExpectSk(activityContext, DOUHU_AB_TEST_RESULT_DP);
    }

    private boolean appVersionMeet(String version, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return VersionUtil.compare(version, switchConfig.getMinVersionMt()) >= 0;
        }
        return VersionUtil.compare(version, switchConfig.getMinVersionDp()) >= 0;
    }

    private boolean dealCategoryMeet(int categoryId) {
        return CollectionUtils.isNotEmpty(switchConfig.getDealCategoryIdList()) && switchConfig.getDealCategoryIdList().contains(categoryId);
    }

    private boolean isWeekend() {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.builder.BeautyIdleStyleFloorsBuilderExt.isWeekend()");
        if (floorConstant.idleWeekendSwitch) {
            return true;
        }
        return ZH_WEEK_LIST.contains(DateUtils.getZhouJiOfWeek(new Date()));
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class RichLabel {
        private String text;
        private String textcolor;
        private int textsize;
    }

    @Data
    private static class FloorConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
        private int front11;
        private String color777777;
        private String colorFF6633;
        private String titleFormat;
        private String cheapHotPic;
        private double cheapHotPicAspectRadio;
        private double activityPicAspectRadio;
        private double headerPicAspectRadio;
        private int defaultShowProductCount;
        private List<Long> titleFormatFilterIds;
        private String morePrefix;
        private String moreSuffix;
        private int picWidth;
        private int picHeight;
        private String dpIdleStyleSk;
        private String mtIdleStyleSk;
        private int front10;
        private String colorFF6200;
        private String idlePromHeadPic;
        private double idlePromHeadPicAspectRadio;
        private String idlePromPriceDescPic;
        private double idlePromPriceDescPicAspectRadio;
        private boolean idleWeekendSwitch;
    }

    @Data
    private static class SwitchConfig {
        private boolean douhuSwitchOpen;
        private String minVersionDp;
        private String minVersionMt;
        private List<Integer> dealCategoryIdList;
    }

}
