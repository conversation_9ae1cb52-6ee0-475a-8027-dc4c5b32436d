package com.sankuai.dzviewscene.shelf.business.shelf.car.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/4/14 8:52 下午
 */
@ExtPointInstance(name = "爱车团购货架主标题模块构造扩展点")
public class CarDealShelfMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.car.deal.shelf.constant.config", defaultValue = "")
    public ConstantConfig constantConfig;

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealShelfMainTitleBuilderExt.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return constantConfig.getTitleName();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealShelfMainTitleBuilderExt.icon(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if(VCPlatformEnum.MT.getType() == platform) {
            return constantConfig.getIconUrlMt();
        }
        return constantConfig.getIconUrlDp();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealShelfMainTitleBuilderExt.tags(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return new ArrayList<IconRichLabelVO>() {{
            add(buildIconRichLabelVO(constantConfig.getTagIconUrl(), constantConfig.getReturnAnytimeTag()));
            add(buildIconRichLabelVO(constantConfig.getTagIconUrl(), constantConfig.getReturnOverdueTag()));
        }};
    }

    private IconRichLabelVO buildIconRichLabelVO(String url, String text) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealShelfMainTitleBuilderExt.buildIconRichLabelVO(java.lang.String,java.lang.String)");
        IconRichLabelVO iconRichLabelVO = new IconRichLabelVO();
        iconRichLabelVO.setIcon(url);
        iconRichLabelVO.setText(new RichLabelVO(text));
        return iconRichLabelVO;
    }

    @Data
    private static class ConstantConfig {
        private String titleName;
        private String iconUrlDp;
        private String iconUrlMt;
        private String tagIconUrl;
        private String returnAnytimeTag;
        private String returnOverdueTag;
    }
}
