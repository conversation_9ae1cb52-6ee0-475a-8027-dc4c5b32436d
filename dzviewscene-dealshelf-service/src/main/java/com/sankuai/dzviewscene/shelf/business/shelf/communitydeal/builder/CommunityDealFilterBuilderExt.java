package com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;


/**
 * Created by zhaizhui on 2020/9/9
 */
@ExtPointInstance(name = "生活服务小区货架货架筛选栏构造扩展点")
public class CommunityDealFilterBuilderExt extends FilterBuilderExtAdapter {


    @Override
    public int showType(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFilterBuilderExt.showType(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 2;
    }

    @Override
    public int minShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFilterBuilderExt.minShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 3;
    }

    @Override
    public int childrenMinShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFilterBuilderExt.childrenMinShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 3;
    }

    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFilterBuilderExt.titleButton(ActivityContext,String,FilterBtnM)");
        return new RichLabelVO(13, "#111111", filterBtnM.getTitle());
    }
}
