package com.sankuai.dzviewscene.shelf.business.shelf.edu.fetcher;

import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.Query;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.AbstractConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultipleConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import joptsimple.internal.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 自习室团购货架筛选栏
 * @auther: liweilong06
 * @date: 2021/1/12 4:03 下午
 */
@ExtPointInstance(name = "自习室团购货架筛选栏(多路召回融合版)")
public class EduStudyRoomFilterFetcherExt extends AbstractConfigFilterFetcherExt {

    //////////////////////////////////////常量定义//////////////////////////////////////////
    public static final FilterBtnM TOTAL_FILTER = buildFilterBtn(1L, "全部");
    public static final FilterBtnM HOUR_CARD_FILTER = buildFilterBtn(2L, "小时卡");
    public static final FilterBtnM DAY_CARD_FILTER = buildFilterBtn(3L, "天卡");
    public static final FilterBtnM WEEK_CARD_FILTER = buildFilterBtn(4L, "周卡");
    public static final FilterBtnM MONTH_CARD_FILTER = buildFilterBtn(5L, "月卡");
    public static final FilterBtnM SEASON_CARD_FILTER = buildFilterBtn(6L, "季卡年卡");
    public static final FilterBtnM OTHER_FILTER = buildFilterBtn(7L, "其他");

    /**
     * 货模板Id
     */
    public static final String PRODUCT_CATEGORY_ID_ATTR_KEY = "attr_totalProductCategoryIds";
    /**
     * 商家推荐顺序的属性key
     */
    private static final String SHOP_RECOMMEND_INDEX_ATTR_NAME = "attr_shopRecommendIndex";
    /**
     * 商家推荐的属性key
     */
    private static final String SHOP_RECOMMEND_ATTR_NAME = "attr_shopRecommend";
    /**
     * 是商家推荐时的商家推荐属性值
     */
    private static final String SHOP_RECOMMEND_ATTR_VALUE = "true";

    private static final String PERFECT_FILTER_TITLE = "玩美季";

    public static final Long PRODUCT_CATEGORY_ID_DAY_CARD = 70041L;
    public static final Long PRODUCT_CATEGORY_ID_HOUR_CARD = 70028L;
    public static final Long PRODUCT_CATEGORY_ID_SERVICE = 70029L;
    public static final Long PRODUCT_CATEGORY_ID_WEEK_CARD = 70063L;
    public static final Long PRODUCT_CATEGORY_ID_MONTH_CARD = 70064L;
    public static final Long PRODUCT_CATEGORY_ID_SEASON_CARD = 70065L;

    @Override
    public Map<Long, Query> loadQuery(ActivityContext activityContext, String groupName, FilterM filter) {
        Map<Long, Query> filterToQuery = new HashMap<>();
        filter.getFilters().forEach(filterBtnM -> {
            if (PERFECT_FILTER_TITLE.equals(filterBtnM.getTitle())) {
                filterToQuery.put(filterBtnM.getFilterId(), perfectActivityQuery(activityContext));
            }
        });
        filterToQuery.put(TOTAL_FILTER.getFilterId(), totalQuery());
        filterToQuery.put(HOUR_CARD_FILTER.getFilterId(), hourCardQuery(activityContext));
        filterToQuery.put(DAY_CARD_FILTER.getFilterId(), dayCardQuery(activityContext));
        filterToQuery.put(WEEK_CARD_FILTER.getFilterId(), weekCardQuery(activityContext));
        filterToQuery.put(MONTH_CARD_FILTER.getFilterId(), monthCardQuery(activityContext));
        filterToQuery.put(SEASON_CARD_FILTER.getFilterId(),seasonCardQuery(activityContext));
        filterToQuery.put(OTHER_FILTER.getFilterId(), otherQuery());
        return filterToQuery;
    }

    private SimplexQuery perfectActivityQuery(ActivityContext activityContext) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                String activityTag = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
                return StringUtils.isNotEmpty(activityTag) && PerfectActivityBuildUtils.isShowPerfectActivity(product);
            }
        };
    }

    private String getActivityTabTitle(CompletableFuture<List<DealActivityDTO>> activityDTOsCompletableFuture) {
        if (activityDTOsCompletableFuture == null) {
            return Strings.EMPTY;
        }
        List<DealActivityDTO> activityDTOS = activityDTOsCompletableFuture.join();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(activityDTOS) && activityDTOS.get(0) != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(activityDTOS.get(0).getTitles())) {
            return activityDTOS.get(0).getTitles().get(0);
        }
        return Strings.EMPTY;
    }


    @Override
    public void endIntercept(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        /*取玩美季标签名作为tab名*/
        if (PERFECT_FILTER_TITLE.equals(filterBtnM.getTitle()) && org.apache.commons.collections.CollectionUtils.isNotEmpty(filterBtnM.getProducts())) {
            String activityFilterName = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
            if (StringUtils.isEmpty(activityFilterName)) {
                return;
            }
            filterBtnM.setTitle(activityFilterName);
        }
    }

    /**
     * 命中任意一个skuProductIds返回true
     * @param productM
     * @param skuProductIds
     * @return
     */
    private boolean containsProductCategoryId(ProductM productM, List<Long> skuProductIds) {
        if (StringUtils.isEmpty(productM.getAttr(PRODUCT_CATEGORY_ID_ATTR_KEY))) {
            return false;
        }
        List<Long> productCategoryIds = JsonCodec.decode(productM.getAttr(PRODUCT_CATEGORY_ID_ATTR_KEY), new TypeReference<List<Long>>() {});
        if (CollectionUtils.isEmpty(productCategoryIds)) {
            return false;
        }
        for (Long skuProductId : skuProductIds) {
            if (productCategoryIds.contains(skuProductId)) {
                return true;
            }
        }
        return false;
    }

    @NotNull
    private Comparator<ProductM> getComparatorForAll() {
        // 活动>商家推荐>新模板>销量>Id倒序
        return new Comparator<ProductM>() {
            @Override
            public int compare(ProductM o1, ProductM o2) {
                List<Integer> compareResult = Lists.newArrayList();
                // 运营活动
                compareResult.add(compareActive(o1, o2));
                // 商家推荐
                compareResult.add(compareShopRecommend(o1, o2));
                // 新货模板
                compareResult.add(compareProductCategoryId(o1, o2));
                // 销量
                compareResult.add(compareSaleNum(o1, o2));
                // 商品Id倒序
                compareResult.add(compareProductId(o1, o2));
                // 按照排序优先级，获取第一个不为0的结果
                return compareResult.stream().filter(perCompare -> perCompare != 0).findFirst().orElse(0);
            }

        };
    }

    @NotNull
    private Comparator<ProductM> getComparator() {
        // 活动>商家推荐>销量>Id倒序
        return new Comparator<ProductM>() {
            @Override
            public int compare(ProductM o1, ProductM o2) {
                List<Integer> compareResult = Lists.newArrayList();
                // 运营活动
                compareResult.add(compareActive(o1, o2));
                // 商家推荐
                compareResult.add(compareShopRecommend(o1, o2));
                // 销量
                compareResult.add(compareSaleNum(o1, o2));
                // 商品Id倒序
                compareResult.add(compareProductId(o1, o2));
                // 按照排序优先级，获取第一个不为0的结果
                return compareResult.stream().filter(perCompare -> perCompare != 0).findFirst().orElse(0);
            }

        };
    }

    private Integer compareProductId(ProductM o1, ProductM o2) {
        // 商品Id倒序
        return Long.valueOf(o2.getProductId()).compareTo(Long.valueOf(o1.getProductId()));
    }

    private Integer compareSaleNum(ProductM o1, ProductM o2) {
        Integer displaySale1 = o1.getSale() == null ? 0 : o1.getSale().getSale();
        Integer displaySale2 = o2.getSale() == null ? 0 : o2.getSale().getSale();
        // 按照销量倒序排序
        return displaySale2.compareTo(displaySale1);
    }

    private Integer compareProductCategoryId(ProductM o1, ProductM o2) {
        boolean useFormatCategory1 = containsProductCategoryId(o1,
                Lists.newArrayList(PRODUCT_CATEGORY_ID_DAY_CARD, PRODUCT_CATEGORY_ID_HOUR_CARD, PRODUCT_CATEGORY_ID_SERVICE));
        boolean useFormatCategory2 = containsProductCategoryId(o2,
                Lists.newArrayList(PRODUCT_CATEGORY_ID_DAY_CARD, PRODUCT_CATEGORY_ID_HOUR_CARD, PRODUCT_CATEGORY_ID_SERVICE));
        if (useFormatCategory1 == useFormatCategory2) {
            return 0;
        }
        // 使用新模板的优先
        return useFormatCategory1 ? -1 : 1;
    }

    private Integer compareShopRecommend(ProductM o1, ProductM o2) {
        // 先比较有没有商家推荐标记
        int result = compareShopRecommendTag(o1, o2);
        if (result != 0) {
            return result;
        }
        // 如果相同，则比较商家推荐的顺序
        Integer shoRecommendIndex1 = NumberUtils.objToInt(o1.getAttr(SHOP_RECOMMEND_INDEX_ATTR_NAME));
        Integer shoRecommendIndex2 = NumberUtils.objToInt(o2.getAttr(SHOP_RECOMMEND_INDEX_ATTR_NAME));
        return shoRecommendIndex1.compareTo(shoRecommendIndex2);
    }

    private Integer compareShopRecommendTag(ProductM o1, ProductM o2) {
        boolean shopRecommend1 = isExpectedAttr(o1, SHOP_RECOMMEND_ATTR_NAME, SHOP_RECOMMEND_ATTR_VALUE);
        boolean shopRecommend2 = isExpectedAttr(o2, SHOP_RECOMMEND_ATTR_NAME, SHOP_RECOMMEND_ATTR_VALUE);
        if (shopRecommend1 == shopRecommend2) {
            return 0;
        }
        // 商家推荐优先
        return shopRecommend1 ? -1 : 1;
    }

    private Integer compareActive(ProductM o1, ProductM o2) {
        if (CollectionUtils.isNotEmpty(o1.getActivities()) && CollectionUtils.isNotEmpty(o2.getActivities())) {
            return 0;
        }
        if (CollectionUtils.isEmpty(o1.getActivities()) && CollectionUtils.isEmpty(o2.getActivities())) {
            return 0;
        }
        if (CollectionUtils.isNotEmpty(o1.getActivities())) {
            return -1;
        }
        return 1;
    }

    private boolean isExpectedAttr(ProductM productM, String attrKey, String expectedValue) {
        if (StringUtils.equals(productM.getAttr(attrKey), expectedValue)) {
            return true;
        }
        return false;
    }

    protected static FilterBtnM buildFilterBtn(long filterId, String name) {
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(filterId);
        filterBtnM.setTitle(name);
        return filterBtnM;
    }

    private SimplexQuery totalQuery() {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return true;
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return getComparatorForAll();
            }
        };

    }



    protected SimplexQuery hourCardQuery(ActivityContext activityContext) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return containsProductCategoryId(product, Lists.newArrayList(PRODUCT_CATEGORY_ID_HOUR_CARD));
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return getComparator();
            }
        };
    }

    protected SimplexQuery dayCardQuery(ActivityContext activityContext) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return containsProductCategoryId(product, Lists.newArrayList(PRODUCT_CATEGORY_ID_DAY_CARD));
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return getComparator();
            }
        };
    }

    protected SimplexQuery weekCardQuery(ActivityContext activityContext) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return containsProductCategoryId(product, Lists.newArrayList(PRODUCT_CATEGORY_ID_WEEK_CARD));
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return getComparator();
            }
        };
    }

    protected SimplexQuery monthCardQuery(ActivityContext activityContext) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return containsProductCategoryId(product, Lists.newArrayList(PRODUCT_CATEGORY_ID_MONTH_CARD));
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return getComparator();
            }
        };
    }

    protected SimplexQuery seasonCardQuery(ActivityContext activityContext) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return containsProductCategoryId(product, Lists.newArrayList(PRODUCT_CATEGORY_ID_SEASON_CARD));
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return getComparator();
            }
        };
    }

    private SimplexQuery otherQuery() {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return !containsProductCategoryId(product,
                        Lists.newArrayList(PRODUCT_CATEGORY_ID_DAY_CARD, PRODUCT_CATEGORY_ID_HOUR_CARD, PRODUCT_CATEGORY_ID_SERVICE, PRODUCT_CATEGORY_ID_WEEK_CARD, PRODUCT_CATEGORY_ID_MONTH_CARD, PRODUCT_CATEGORY_ID_SEASON_CARD));
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return getComparator();
            }
        };
    }

}
