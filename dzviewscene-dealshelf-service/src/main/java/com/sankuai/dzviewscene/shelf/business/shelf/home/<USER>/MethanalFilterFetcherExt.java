package com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher;

import com.dianping.cat.Cat;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.utils.ProductMCompareUtils;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.Query;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.AbstractConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultipleConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/1 8:18 下午
 */
@ExtPointInstance(name = "甲醛团购货架筛选构造扩展点")
public class MethanalFilterFetcherExt extends AbstractConfigFilterFetcherExt {

    private static final List<String> FILTER_ATTR_NAME_LIST = Lists.newArrayList("scene_decorate", "sys_deal_universal_type");

    private static final List<String> CHILD_FILTER_ATTR_NAME_LIST = Lists.newArrayList("scene_decorate_house", "decorate_public_place", "decorate_car");

    private static final List<String> FILTER_TAB_ATTR_NAME_LIST = Lists.newArrayList("scene_decorate", "sence_formaldehyde_governance", "sence_formaldehyde_detection");

    private static final String VOUCHER_ATTR_NAME = "sys_deal_universal_type";

    private static final String VOUCHER_VALUE = "2";

    private static final int DEAL_MAX_NUM_RETURNED = 200;

    private static final String FILTER_ALL_NAME = "全部";

    private static final String FILTER_VOUTHER_NAME = "代金券";

    private static final String RECOMMEND_ATTR_NAME = "shopRecommendAttr";

    private static final String TRUE = "true";

    private static final String PRODUCT_SKU_CATEGORY_ATTR_NAME = "skuProductCategories";

    private static final String PRODUCT_CONTENT_ATTR_NAME = "formaldehyde_content";

    private static final String METHANAL_DEAL_SKU_CATEGORY_NAME = "甲醛治理";

    private static final String METHANAL_DETECTION_SKU_CATEGORY_NAME = "甲醛检测";

    private static final String AIR_DETECTION_SKU_CATEGORY_NAME = "空气检测";

    private static final String PRIVATE_HOUSE_SCENE_ATTR_NAME = "私人住宅";

    private static final String HOUSE_SCENE_ATTR_NAME = "住宅";

    private static final String PUBLIC_PLACE_SCENE_ATTR_NAME = "公共场所";

    private static final String PUBLIC_SPACE_SCENE_ATTR_NAME = "公共空间";

    private static final String NAV_TAB_OPTIMIZED_SWITCH_LION_CONFIG = "com.sankuai.dzviewscene.productshelf.methanal.deal.shelf.nav.tab.optimize";

    private static final String PERFECT_FILTER_TITLE = "玩美季";

    @Resource
    private MultipleConfigFilterFetcherExt multipleConfigFilterFetcherExt;

    @Override
    public long selected(ActivityContext activityContext, String groupName, FilterM filterM) {
        return multipleConfigFilterFetcherExt.selected(activityContext, groupName, filterM);
    }

    @Override
    public Map<Long, Query> loadQuery(ActivityContext activityContext, String groupName, FilterM filter) {
        if(isNavTabSwitchOpen()) {
            return buildNavTabsOptimizedQuery(filter, activityContext);
        }
        return buildQuery(filter, activityContext);
    }

    private boolean isNavTabSwitchOpen() {
        return Lion.getBooleanValue(NAV_TAB_OPTIMIZED_SWITCH_LION_CONFIG, false);
    }

    private Map<Long, Query> buildQuery(FilterM filter, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher.MethanalFilterFetcherExt.buildQuery(FilterM,ActivityContext)");
        Map<Long, Query> filterIdQueryMap = new HashMap<>();
        if (filter == null || CollectionUtils.isEmpty(filter.getFilters())) {
            return filterIdQueryMap;
        }
        filter.getFilters().forEach(filterBtnM -> {
            if(PERFECT_FILTER_TITLE.equals(filterBtnM.getTitle())) {
                filterIdQueryMap.put(filterBtnM.getFilterId(), perfectActivityQuery(activityContext));
                return;
            }
            filterIdQueryMap.put(filterBtnM.getFilterId(), buildProductsQuery(filterBtnM));
            if(CollectionUtils.isEmpty(filterBtnM.getChildren())) {
                return;
            }
            filterIdQueryMap.putAll(buildChildFilterId2ProductsQueryMap(filterBtnM.getChildren(), filterBtnM));
        });
        return filterIdQueryMap;
    }

    protected SimplexQuery perfectActivityQuery(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher.MethanalFilterFetcherExt.perfectActivityQuery(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                String activityTag = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
                return StringUtils.isNotEmpty(activityTag) && PerfectActivityBuildUtils.isShowPerfectActivity(product);
            }
        };
    }

    @Override
    public void endIntercept(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        /*取玩美季标签名作为tab名*/
        if (PERFECT_FILTER_TITLE.equals(filterBtnM.getTitle()) && CollectionUtils.isNotEmpty(filterBtnM.getProducts())) {
            String activityFilterName = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
            if (StringUtils.isEmpty(activityFilterName)) {
                return;
            }
            filterBtnM.setTitle(activityFilterName);
        }
    }

    private String getActivityTabTitle(CompletableFuture<List<DealActivityDTO>> activityDTOsCompletableFuture) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher.MethanalFilterFetcherExt.getActivityTabTitle(java.util.concurrent.CompletableFuture)");
        if (activityDTOsCompletableFuture == null) {
            return Strings.EMPTY;
        }
        List<DealActivityDTO> activityDTOS = activityDTOsCompletableFuture.join();
        if (CollectionUtils.isNotEmpty(activityDTOS) && activityDTOS.get(0) != null && CollectionUtils.isNotEmpty(activityDTOS.get(0).getTitles())) {
            return activityDTOS.get(0).getTitles().get(0);
        }
        return Strings.EMPTY;
    }

    private Map<Long, Query> buildNavTabsOptimizedQuery(FilterM filter, ActivityContext activityContext) {
        Map<Long, Query> filterIdQueryMap = new HashMap<>();
        if (filter == null || CollectionUtils.isEmpty(filter.getFilters())) {
            return filterIdQueryMap;
        }
        filter.getFilters().forEach(filterBtnM -> {
            if(PERFECT_FILTER_TITLE.equals(filterBtnM.getTitle())) {
                filterIdQueryMap.put(filterBtnM.getFilterId(), perfectActivityQuery(activityContext));
                return;
            }
            filterIdQueryMap.put(filterBtnM.getFilterId(), buildNavOptimizedFatherTadProductsQuery(filterBtnM));
            if(CollectionUtils.isEmpty(filterBtnM.getChildren())) {
                return;
            }
            filterIdQueryMap.putAll(buildNavOptimizedChildFilterId2ProductsQueryMap(filterBtnM));
        });
        return filterIdQueryMap;
    }

    private Map<Long, Query> buildNavOptimizedChildFilterId2ProductsQueryMap(FilterBtnM filterBtnM) {
        Map<Long, Query> childFilterId2QueryMap = new HashMap<>();
        if(filterBtnM == null || CollectionUtils.isEmpty(filterBtnM.getChildren())) {
            return childFilterId2QueryMap;
        }
        filterBtnM.getChildren().forEach(childFilter -> {
            if(childFilter == null || StringUtils.isEmpty(childFilter.getTitle())) {
                return;
            }
            if(childFilter.getTitle().equals(FILTER_ALL_NAME)) {
                childFilterId2QueryMap.put(childFilter.getFilterId(), buildNavOptimizedFatherTadProductsQuery(filterBtnM));
                return;
            }
            childFilterId2QueryMap.put(childFilter.getFilterId(), buildChildrenTadProductsQuery(childFilter, filterBtnM));
        });
        return childFilterId2QueryMap;
    }

    private SimplexQuery buildChildrenTadProductsQuery(FilterBtnM chilldFilterBtnM, FilterBtnM fatherFilterBtnM) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                if(chilldFilterBtnM == null || StringUtils.isEmpty(chilldFilterBtnM.getTitle()) || StringUtils.isEmpty(fatherFilterBtnM.getTitle())) {
                    return false;
                }
                if(fatherFilterBtnM.getTitle().equals(FILTER_ALL_NAME)) {
                    return chilldFilterBtnM.getTitle().equals(getDealChildFilterTabName(product));
                }
                return chilldFilterBtnM.getTitle().equals(getDealChildFilterTabName(product)) && fatherFilterBtnM.getTitle().equals(getDealFatherFilterTabName(product));
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (o1, o2) -> {
                    if(o1.getAttr(RECOMMEND_ATTR_NAME).equals(TRUE) || o2.getAttr(RECOMMEND_ATTR_NAME).equals(TRUE)) {
                        return 0;
                    }
                    if(getFilterTitleBelongTo(o1).equals(FILTER_VOUTHER_NAME) && getFilterTitleBelongTo(o2).equals(FILTER_VOUTHER_NAME)) {
                        return 0;
                    }
                    if(getFilterTitleBelongTo(o1).equals(FILTER_VOUTHER_NAME)) {
                        return -1;
                    }
                    if(getFilterTitleBelongTo(o2).equals(FILTER_VOUTHER_NAME)) {
                        return 1;
                    }
                    return ProductMCompareUtils.saleFirst(o1, o2);
                };
            }

            @Override
            public int topN(QueryContext context) {
                return DEAL_MAX_NUM_RETURNED;
            }
        };
    }

    private String getDealChildFilterTabName(ProductM productM) {
        if(productM == null) {
            return null;
        }
        String tabName = getChildFilterTitle(FILTER_TAB_ATTR_NAME_LIST, productM.getExtAttrs());
        return getNormalizedTabName(tabName);
    }

    private String getNormalizedTabName(String tabName) {
        if(StringUtils.isEmpty(tabName)) {
            return tabName;
        }
        if(tabName.equals(PRIVATE_HOUSE_SCENE_ATTR_NAME)) {
            return HOUSE_SCENE_ATTR_NAME;
        }
        if(tabName.equals(PUBLIC_PLACE_SCENE_ATTR_NAME)) {
            return PUBLIC_SPACE_SCENE_ATTR_NAME;
        }
        return tabName;
    }

    private SimplexQuery buildNavOptimizedFatherTadProductsQuery(FilterBtnM filterBtnM) {
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                if(filterBtnM == null || StringUtils.isEmpty(filterBtnM.getTitle())) {
                    return false;
                }
                //标签类型为"全部"时，返回true
                if(filterBtnM.getTitle().equals(FILTER_ALL_NAME)) {
                    return true;
                }
                return filterBtnM.getTitle().equals(getDealFatherFilterTabName(product));
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (o1, o2) -> {
                    if(Objects.equals(o1.getAttr(RECOMMEND_ATTR_NAME), TRUE) || Objects.equals(o2.getAttr(RECOMMEND_ATTR_NAME),TRUE)) {
                        return 0;
                    }
                    if(getFilterTitleBelongTo(o1).equals(FILTER_VOUTHER_NAME) && getFilterTitleBelongTo(o2).equals(FILTER_VOUTHER_NAME)) {
                        return 0;
                    }
                    if(getFilterTitleBelongTo(o1).equals(FILTER_VOUTHER_NAME)) {
                        return -1;
                    }
                    if(getFilterTitleBelongTo(o2).equals(FILTER_VOUTHER_NAME)) {
                        return 1;
                    }
                    return getSale(o2) - getSale(o1);
                };
            }

            @Override
            public int topN(QueryContext context) {
                return DEAL_MAX_NUM_RETURNED;
            }
        };
    }

    private int getSale(ProductM productM) {
        if (productM == null || productM.getSale() == null) {
            return -1;
        }
        return productM.getSale().getSale();
    }

    private Map<Long, Query> buildChildFilterId2ProductsQueryMap(List<FilterBtnM> childFilterBtnMs, FilterBtnM parentFilterBtnM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher.MethanalFilterFetcherExt.buildChildFilterId2ProductsQueryMap(java.util.List,com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM)");
        if(CollectionUtils.isEmpty(childFilterBtnMs) || parentFilterBtnM == null) {
            return null;
        }
        Map<Long, Query> childFilterId2QueryMap = new HashMap<>();
        childFilterBtnMs.forEach(childFilterBtnM -> {
            //如果子标签类型为"全部"，则设置为父标签类型去判断，因为商品属性中不包含"全部"这一属性
            if(childFilterBtnM.getTitle().equals(FILTER_ALL_NAME)) {
                childFilterId2QueryMap.put(childFilterBtnM.getFilterId(), buildProductsQuery(parentFilterBtnM));
                return;
            }
            childFilterId2QueryMap.put(childFilterBtnM.getFilterId(), buildProductsQuery(childFilterBtnM));
        });
        return childFilterId2QueryMap;
    }

    private SimplexQuery buildProductsQuery(FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher.MethanalFilterFetcherExt.buildProductsQuery(com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                //标签类型为"全部"时，返回true
                if(filterBtnM.getTitle().equals(FILTER_ALL_NAME)) {
                    return true;
                }
                String filterTitle = getFilterTitleBelongTo(product);
                if(StringUtils.isEmpty(filterTitle)) {
                    return false;
                }
                if(filterTitle.equals(filterBtnM.getTitle())) {
                    return true;
                }
                if(CollectionUtils.isEmpty(filterBtnM.getChildren())) {
                    return false;
                }
                List<FilterBtnM> filterBtnMS = filterBtnM.getChildren().stream().filter(filter -> filterTitle.equals(filter.getTitle())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(filterBtnMS)) {
                    return false;
                }
                return true;
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return new Comparator<ProductM>() {
                    @Override
                    public int compare(ProductM o1, ProductM o2) {
                        if(o1.getAttr(RECOMMEND_ATTR_NAME).equals(TRUE) || o2.getAttr(RECOMMEND_ATTR_NAME).equals(TRUE)) {
                            return 0;
                        }
                        if(getFilterTitleBelongTo(o1).equals(FILTER_VOUTHER_NAME) && getFilterTitleBelongTo(o2).equals(FILTER_VOUTHER_NAME)) {
                            return 0;
                        }
                        if(getFilterTitleBelongTo(o1).equals(FILTER_VOUTHER_NAME)) {
                            return -1;
                        }
                        if(getFilterTitleBelongTo(o2).equals(FILTER_VOUTHER_NAME)) {
                            return 1;
                        }
                        return o2.getSale().getSale() - o1.getSale().getSale();
                    }
                };
            }

            @Override
            public int topN(QueryContext context) {
                return DEAL_MAX_NUM_RETURNED;
            }
        };
    }

    private String getDealFatherFilterTabName(ProductM productM) {
        if(productM == null) {
            return null;
        }
        String tabName = getFatherFilterTabNamByContentAttr(productM);
        if(StringUtils.isNotEmpty(tabName)) {
            return tabName;
        }
        return getFatherFilterTabNamBySkuCategory(productM);
    }

    private String getFatherFilterTabNamByContentAttr(ProductM productM) {
        if(productM == null) {
            return null;
        }
        String dealContentName = productM.getAttr(PRODUCT_CONTENT_ATTR_NAME);
        if(StringUtils.isEmpty(dealContentName)) {
            return null;
        }
        return dealContentName.equals(METHANAL_DETECTION_SKU_CATEGORY_NAME) ? AIR_DETECTION_SKU_CATEGORY_NAME : dealContentName;
    }

    private String getFatherFilterTabNamBySkuCategory(ProductM productM) {
        String dealProductCategorys = productM.getAttr(PRODUCT_SKU_CATEGORY_ATTR_NAME);
        if(StringUtils.isEmpty(dealProductCategorys)) {
            return null;
        }
        List<String> dealProductCategoryList = Lists.newArrayList(dealProductCategorys.split(","));
        //下述判断顺序不可调换！产品规则：只含有"空气检测"时为空气检测团单，同时含有"空气检测"和"甲醛治理"或者只含有"空气治理"的为甲醛治理团单
        if(dealProductCategoryList.contains(METHANAL_DEAL_SKU_CATEGORY_NAME)) {
            return METHANAL_DEAL_SKU_CATEGORY_NAME;
        }
        if(dealProductCategoryList.contains(AIR_DETECTION_SKU_CATEGORY_NAME) || dealProductCategoryList.contains(METHANAL_DETECTION_SKU_CATEGORY_NAME)) {
            return AIR_DETECTION_SKU_CATEGORY_NAME;
        }
        return null;
    }

    private String getFilterTitleBelongTo(ProductM product) {
        //先判断子标签类型
        String chileFilterTitle = getChildFilterTitle(CHILD_FILTER_ATTR_NAME_LIST, product.getExtAttrs());
        if(StringUtils.isNotEmpty(chileFilterTitle) && !chileFilterTitle.equals(FILTER_ALL_NAME)) {
            return chileFilterTitle;
        }
        //如果子标签类型不存在或者为"全部"时，判断父标签类型并返回
        if(StringUtils.isNotEmpty(getFilterTitle(FILTER_ATTR_NAME_LIST, product.getExtAttrs()))) {
            return getFilterTitle(FILTER_ATTR_NAME_LIST, product.getExtAttrs());
        }
        //如果父标签类型也不存在，则返回""
        return "";
    }

    private String getChildFilterTitle(List<String> childFilterTypes, List<AttrM> attrbutes) {
        if(CollectionUtils.isEmpty(attrbutes)) {
            return null;
        }
        for(String childFilterType : childFilterTypes) {
            List<AttrM> attributeModels = attrbutes.stream().filter(attribute -> attribute.getName().equals(childFilterType)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(attributeModels)) {
                continue;
            }
            return attributeModels.stream().findFirst().get().getValue();
        }
        return null;
    }

    private String getFilterTitle(List<String> filterTypes, List<AttrM> attrbutes) {
        if(CollectionUtils.isEmpty(attrbutes)) {
            return null;
        }
        for(String filterType : filterTypes) {
            if(filterType.equals(VOUCHER_ATTR_NAME)) {
                List<AttrM> attributeModels = attrbutes.stream().filter(attribute -> attribute.getName().equals(filterType)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(attributeModels) && attributeModels.stream().findFirst().get().getValue().contains(VOUCHER_VALUE)) {
                    return FILTER_VOUTHER_NAME;
                }
            }
            List<AttrM> attributeModels = attrbutes.stream().filter(attribute -> attribute.getName().equals(filterType)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(attributeModels)) {
                return attributeModels.stream().findFirst().get().getValue();
            }
        }
        return null;
    }

    /**
     * 筛选列表按照下属商品数目排序
     *@param filterM 筛选栏
     *@return
     */
    @Override
    public void endIntercept(ActivityContext activityContext, String groupName, FilterM filterM) {
        if(filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return;
        }
        Collections.sort(filterM.getFilters(), (filterFirst, filterSecond) -> {
            if(isContainsAllOrVouther(filterFirst, filterSecond)) {
                return 0;
            }
            return compareProductsNum(filterFirst.getProducts(), filterSecond.getProducts());
        });
        for(FilterBtnM filterBtnM : filterM.getFilters()) {
            if(CollectionUtils.isEmpty(filterBtnM.getChildren())) {
                continue;
            }
            Collections.sort(filterBtnM.getChildren(), (childFilterFirst, childFilterSecond) -> {
                return compareProductsNum(childFilterFirst.getProducts(), childFilterSecond.getProducts());
            });
        }
    }

    /**
     * 比较筛选项下团单的个数
     */
    private int compareProductsNum(List<ProductM> a, List<ProductM> b) {
        if (CollectionUtils.isEmpty(a) && CollectionUtils.isEmpty(b)) {
            return 0;
        }
        if (CollectionUtils.isNotEmpty(a) && CollectionUtils.isNotEmpty(b)) {
            return b.size() - a.size();
        }
        if (CollectionUtils.isNotEmpty(a)) {
            return 1;
        }
        return -1;
    }

    private boolean isContainsAllOrVouther(FilterBtnM filterFirst, FilterBtnM filterSecond) {
        if(filterFirst.getTitle().equals(FILTER_VOUTHER_NAME) || filterFirst.getTitle().equals(FILTER_ALL_NAME) || filterFirst.getTitle().equals(PERFECT_FILTER_TITLE)) {
            return true;
        }
        if(filterSecond.getTitle().equals(FILTER_VOUTHER_NAME) || filterSecond.getTitle().equals(FILTER_ALL_NAME) || filterSecond.getTitle().equals(PERFECT_FILTER_TITLE)) {
            return true;
        }
        return false;
    }

}
