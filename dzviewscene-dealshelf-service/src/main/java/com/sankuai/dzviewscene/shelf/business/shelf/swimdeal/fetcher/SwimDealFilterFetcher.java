package com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.fetcher;

import com.dianping.cat.Cat;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.product.shelf.common.dto.NavTag;
import com.sankuai.dzviewscene.shelf.business.shelf.bathdeal.BathContext;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.SwimContext;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import joptsimple.internal.Strings;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 游泳团单货架筛选能力
 * @auther: liweilong06
 * @date: 2020/10/10 12:22 下午
 */
@AbilityInstance(name = "游泳团单货架筛选能力")
public class SwimDealFilterFetcher extends FilterFetcher {

    /**
     * 游泳卡筛选栏排序
     */
    private static final List<String> SWIM_CARD_TABLE = Lists.newArrayList("单次票", "多次卡", "月卡", "年卡");

    /**
     * 培训课筛选栏排序规则
     */
    private static final List<String> SWIM_COURSE_TABLE = Lists.newArrayList("婴儿", "青少年", "成人", "女子", "亲子/家庭");

    private static final long PERFECT_FILTER_ID = 19L;

    /**
     * 构造对象
     *
     * @param ctx
     * @return
     */
    @Override
    public CompletableFuture<Map<String, FilterM>> build(ActivityContext ctx) {
        // 1. 获取召回对象
        CompletableFuture<Map<String, ProductGroupM>> floorsFuture = ctx.getAttachment(Attachments.productGroups);
        // 2. 转换为商品组合导航映射
        CompletableFuture<Map<String, FilterM>> filterMsCompletableFuture = floorsFuture.thenApply(
                floorTitle2ProductGroup -> buildProductGroup2Filter(ctx, floorTitle2ProductGroup));
        // 3. 设置选中状态
        return filterMsCompletableFuture.thenApply(filterMs ->  buildSelectedFilter(ctx, filterMs));
    }

    private Map<String, FilterM> buildProductGroup2Filter(ActivityContext ctx, Map<String, ProductGroupM> floorTitle2ProductGroup) {
        Map<String, FilterM> returnValue = new HashMap<>();
        if (MapUtils.isEmpty(floorTitle2ProductGroup)) {
            return returnValue;
        }
        floorTitle2ProductGroup.forEach((groupName, productM) -> {
            FilterM filterM = buildFilterM(ctx, groupName, productM);
            if (filterM != null) {
                returnValue.put(groupName, filterM);
            }
        });
        return returnValue;
    }

    private FilterM buildFilterM(ActivityContext ctx, String groupName, ProductGroupM productM) {
        List<String> queryGroupNames = ctx.getParam(QueryFetcher.Params.groupNames);
        if (notCreateFilter(groupName, productM, queryGroupNames)) {
            return null;
        }
        if (SwimContext.SWIM_CARD_FLOOR.equals(groupName)) {
            return buildSwimCardFilter(productM.getProducts(), ctx);
        }
        if (SwimContext.SWIM_COURSE_FLOOR.equals(groupName)) {
            return buildSwimCourseFilter(productM.getProducts(), ctx);
        }
        return null;
    }

    private FilterM buildSwimCourseFilter(List<ProductM> products, ActivityContext ctx) {
        // 1、获取所有的适用人群
        List<String> suitePeoples = assembleSuitePeople(products);
        // 2、根据排序规则生成筛选
        return buildSwimCourseFilterBySuitePeople(suitePeoples, ctx, products);
    }

    private boolean notCreateFilter(String groupName, ProductGroupM productM, List<String> queryGroupNames) {
        return !queryGroupNames.contains(groupName) || productM == null || CollectionUtils.isEmpty(productM.getProducts());
    }

    private FilterM buildSwimCardFilter(List<ProductM> products, ActivityContext ctx) {
        // 1、获取所有的serviceType
        List<String> serviceTypes = assembleServiceType(products);
        // 2、根据排序规则生成筛选
        return buildSwimCardFilterByServiceType(serviceTypes, ctx, products);
    }

    private FilterM buildSwimCardFilterByServiceType(List<String> serviceTypes, ActivityContext ctx, List<ProductM> products) {
        List<FilterBtnM> filters = new ArrayList<>();
        long index = BathContext.START_FILTER_ID;
        // 添加全部
        filters.add(buildFilter(index++, "全部"));
        // 添加实际分类
        for (String currentTableName : SWIM_CARD_TABLE) {
            if (serviceTypes.contains(currentTableName)) {
                filters.add(buildFilter(index++, currentTableName));
            }
        }
        FilterBtnM perfectActivityFilterBtnM = buildPerfectActivityFilterBtnM(ctx, products);
        if(perfectActivityFilterBtnM != null) {
            filters.add(1, perfectActivityFilterBtnM);
        }
        FilterM returnValue = new FilterM();
        returnValue.setFilters(filters);
        return returnValue;
    }

    private FilterBtnM buildPerfectActivityFilterBtnM(ActivityContext ctx, List<ProductM> products) {
        String activityTag = getActivityTabTitle(ctx.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
        if(StringUtils.isEmpty(activityTag)) {
            return null;
        }
        List<ProductM> activityProducts = products.stream().filter(product -> PerfectActivityBuildUtils.isShowPerfectActivity(product)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(activityProducts)) {
            return null;
        }
        return buildFilter(PERFECT_FILTER_ID, activityTag);
    }

    private String getActivityTabTitle(CompletableFuture<List<DealActivityDTO>> activityDTOsCompletableFuture) {
        if (activityDTOsCompletableFuture == null) {
            return Strings.EMPTY;
        }
        List<DealActivityDTO> activityDTOS = activityDTOsCompletableFuture.join();
        if (CollectionUtils.isNotEmpty(activityDTOS) && activityDTOS.get(0) != null && CollectionUtils.isNotEmpty(activityDTOS.get(0).getTitles())) {
            return activityDTOS.get(0).getTitles().get(0);
        }
        return Strings.EMPTY;
    }

    private FilterM buildSwimCourseFilterBySuitePeople(List<String> suitePeoples, ActivityContext ctx, List<ProductM> products) {
        List<FilterBtnM> filters = new ArrayList<>();
        long index = BathContext.START_FILTER_ID;
        // 添加全部
        filters.add(buildFilter(index++, "全部"));
        // 添加实际分类
        for (String currentSuitePeople : SWIM_COURSE_TABLE) {
            if (suitePeoples.contains(currentSuitePeople)) {
                filters.add(buildFilter(index++, currentSuitePeople));
            }
        }
        FilterBtnM perfectActivityFilterBtnM = buildPerfectActivityFilterBtnM(ctx, products);
        if(perfectActivityFilterBtnM != null) {
            //玩美季筛选位于第二位
            filters.add(1, perfectActivityFilterBtnM);
        }
        FilterM returnValue = new FilterM();
        returnValue.setFilters(filters);
        return returnValue;
    }

    private FilterBtnM buildFilter(long filterId, String title) {
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(filterId);
        filterBtnM.setTitle(title);
        return filterBtnM;
    }

    /**
     * 获取所有的ServiceType
     * @param products
     * @return
     */
    private List<String> assembleServiceType(List<ProductM> products) {
        Set<String> serviceTypes = new HashSet();
        for (ProductM product : products) {
            String serviceType = getServiceType(product);
            if (StringUtils.isEmpty(serviceType)) {
                continue;
            }
            serviceTypes.add(serviceType);
        }
        return new ArrayList<>(serviceTypes);
    }

    /**
     * 获取所有的适用人群
     * @param products
     * @return
     */
    private List<String> assembleSuitePeople(List<ProductM> products) {
        Set<String> suitePeoples = new HashSet();
        for (ProductM product : products) {
            String suitePeople = getSuitePeople(product);
            if (StringUtils.isEmpty(suitePeople)) {
                continue;
            }
            suitePeoples.add(suitePeople);
        }
        return new ArrayList<>(suitePeoples);
    }

    private String getServiceType(ProductM productM) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        List<AttrM> filterList = productM.getExtAttrs().stream().filter(attrM -> SwimContext.ONLINE_DEAL_ATTR_SERVICE_TYPE.equals(attrM.getName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return null;
        }
        return filterList.get(0).getValue();
    }

    private String getSuitePeople(ProductM productM) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        List<AttrM> suitePeopleList = productM.getExtAttrs().stream().filter(attrM -> SwimContext.ONLINE_DEAL_ATTR_SUITE_PEOPLE.equals(attrM.getName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(suitePeopleList)) {
            return null;
        }
        return suitePeopleList.get(0).getValue();
    }

    private Map<String, FilterM> buildSelectedFilter(ActivityContext ctx, Map<String, FilterM> filterMs) {
        if (MapUtils.isEmpty(filterMs)) {
            return Maps.newHashMap();
        }
        filterMs.entrySet()
                .stream()
                .forEach(filterEntry -> {
                    // 1. 计算选中标签
                    long selectedTagId = getSelected(filterEntry.getValue());
                    // 2. 设置选中状态
                    setFilterSelectedStatus(selectedTagId, null, filterEntry.getValue().getFilters());
                    // 3. 设置选中标签参数, 非多组商品的情况下, 才会重新设置选中标签
                    if (filterMs.size() <= 1) {
                        ctx.addParam(ShelfActivityConstants.Params.selectedFilterId, selectedTagId);
                    }
                });

        return filterMs;
    }

    /**
     * 选中第一个
     * @param filterM
     * @return
     */
    private long getSelected(FilterM filterM) {
        return filterM.getFilters().get(0).getFilterId();
    }

    // 当前只设置叶子节点为选中状态
    private void setFilterSelectedStatus(long selectedTagId, FilterBtnM parentFilterBtnM, List<FilterBtnM> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return;
        }
        // 1. 根据入参设置选中状态
        if (!setSelectedBySelectedTagId(selectedTagId, parentFilterBtnM, filters)) {
            // 2、如果设置失败, 默认选中第一个
            filters.get(0).setSelected(true);
            if (CollectionUtils.isNotEmpty(filters.get(0).getChildren())) {
                filters.get(0).getChildren().get(0).setSelected(true);
            }
        }
    }

    private boolean setSelectedBySelectedTagId(long selectedTagId, FilterBtnM parentFilterBtnM, List<FilterBtnM> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return false;
        }
        for (FilterBtnM filterBtnM : filters) {
            if (CollectionUtils.isNotEmpty(filterBtnM.getChildren())) {
                return setSelectedBySelectedTagId(selectedTagId, filterBtnM, filterBtnM.getChildren());
            }
            if (selectedTagId == filterBtnM.getFilterId()) {
                filterBtnM.setSelected(true);
                if (parentFilterBtnM != null) {
                    parentFilterBtnM.setSelected(true);
                }
                return true;
            }
        }
        return false;
    }

    private List<FilterBtnM> buildFilterButtonMListFromNavTags(List<NavTag> navTags) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.fetcher.SwimDealFilterFetcher.buildFilterButtonMListFromNavTags(java.util.List)");
        if (CollectionUtils.isEmpty(navTags)) {
            return Lists.newArrayList();
        }
        return navTags
                .stream()
                .map(navTag -> buildFilterButtonM(navTag))
                .collect(Collectors.toList());
    }

    private FilterBtnM buildFilterButtonM(NavTag navTag) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.fetcher.SwimDealFilterFetcher.buildFilterButtonM(com.dianping.product.shelf.common.dto.NavTag)");
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(navTag.getId());
        filterBtnM.setTitle(navTag.getName());
        filterBtnM.setTotalCount(navTag.getTotalCount());
        filterBtnM.setChildren(buildFilterButtonMListFromNavTags(navTag.getChildNavTag()));
        return filterBtnM;
    }

}
