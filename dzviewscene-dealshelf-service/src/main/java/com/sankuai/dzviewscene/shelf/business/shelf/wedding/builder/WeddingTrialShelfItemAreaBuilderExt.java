package com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.business.utils.ProductTitleHighLightUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * 结婚-预约试纱货架楼层VO构造扩展点实现
 *
 * <AUTHOR>
 */
@ExtPointInstance(name = "结婚-预约试纱货架楼层VO构造扩展点实现")
public class WeddingTrialShelfItemAreaBuilderExt extends FloorsBuilderExtAdapter {

    private static final String TITLE_TRIAL_NUM_TEMPLATE = "试纱%s件";
    private static final String ATTR_KEY_TRIAL_NUM = "setcount";

    private static final String TITLE_FREE_SERVICE = "赠送";
    private static final int TITLE_FREE_SERVICE_COUNT_MIN = 2;
    private static final String ATTR_KEY_FREE_SERVICE = "serviceTag";

    @Override
    public String itemComponentRichLabelsTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId){
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingTrialShelfItemAreaBuilderExt.itemComponentRichLabelsTitle(ActivityContext,String,ProductM,long)");
        return ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(activityContext, productM.getTitle(), ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD);
    }

    @Override
    public List<String> itemComponentProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingTrialShelfItemAreaBuilderExt.itemComponentProductTags(ActivityContext,String,ProductM,long)");
        List<String> tags = new ArrayList<>();
        String trialNum = productM.getAttr(ATTR_KEY_TRIAL_NUM);
        String serviceTag = getFreeServiceTag(productM.getTotalAttr(ATTR_KEY_FREE_SERVICE));
        if (StringUtils.isNotEmpty(trialNum)) {
            tags.add(String.format(TITLE_TRIAL_NUM_TEMPLATE, trialNum));
        }
        if (StringUtils.isNotEmpty(serviceTag)) {
            tags.add(serviceTag);
        }
        return tags;
    }

    private String getFreeServiceTag(List<String> serviceTags) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingTrialShelfItemAreaBuilderExt.getFreeServiceTag(java.util.List)");
        if (CollectionUtils.isEmpty(serviceTags) || StringUtils.isEmpty(serviceTags.get(0))) {
            return StringUtils.EMPTY;
        }
        StringBuffer freeServiceTag = new StringBuffer(TITLE_FREE_SERVICE);
        freeServiceTag.append(serviceTags.get(0));
        if (serviceTags.size() >= TITLE_FREE_SERVICE_COUNT_MIN) {
            freeServiceTag.append("等");
        }
        return freeServiceTag.toString();
    }

}
