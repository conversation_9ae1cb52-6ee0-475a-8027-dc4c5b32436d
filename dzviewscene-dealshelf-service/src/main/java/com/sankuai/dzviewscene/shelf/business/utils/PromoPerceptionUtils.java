package com.sankuai.dzviewscene.shelf.business.utils;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.DzTagStyleWrapUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.life.builder.LifeDealFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * template专用，优惠感知版本，构建优惠标签
 *
 * <AUTHOR>
 * @since 2023/4/24 20:59
 */
public class PromoPerceptionUtils {
    
    /**
     * 构建优惠感知版本的优惠标签，针对于单列
     *
     * @param productM
     * @param salePrice
     * @param platform
     * @return
     */
    public static List<DzTagVO> buildPromoPerceptionPriceBottomTags(ProductM productM, String salePrice, int platform) {
        List<DzTagVO> result = new ArrayList<>();
        // 判断和构建「优惠标签」（优惠感知的优惠标签）
        DzTagVO promoPriceTag = PromoPerceptionUtils.buildPromoPerceptionTag(productM, salePrice, platform);
        DzTagStyleWrapUtils.overridePromoStyle(promoPriceTag);
        if (Objects.nonNull(promoPriceTag)) {
            result.add(promoPriceTag);
        }
        // 判断和构建「比价标签」（包括全网低价标签、时间比价标签(可选)）
        DzTagVO priceDzTagVo = PriceDisplayUtils.buildPriceTag(productM, platform, false);
        if (Objects.nonNull(priceDzTagVo)) {
            result.add(priceDzTagVo);
        }
        return result;
    }

    /**
     * 构建优惠感知版本的优惠标签，针对于单列
     *
     * @param productM
     * @param salePrice
     * @param platform
     * @param needTimePriceTag
     * @return
     */
    public static List<DzTagVO> buildPromoPerceptionPriceBottomTags(ProductM productM, String salePrice, int platform,
                                                                    boolean needSpacePriceTag, boolean needTimePriceTag, boolean needPriceProtectionTag) {
        List<DzTagVO> result = new ArrayList<>();
        // 判断和构建「优惠标签」（优惠感知的优惠标签）
        DzTagVO promoPriceTag = PromoPerceptionUtils.buildPromoPerceptionTag(productM, salePrice, platform);
        DzTagStyleWrapUtils.overridePromoStyle(promoPriceTag);
        if (Objects.nonNull(promoPriceTag)) {
            result.add(promoPriceTag);
        }
        // 判断和构建「比价标签」（包括全网低价标签、时间比价标签(可选)）
        DzTagVO priceDzTagVo = PriceDisplayUtils.buildPriceTag(productM, platform,needSpacePriceTag, needTimePriceTag, needPriceProtectionTag);
        if (Objects.nonNull(priceDzTagVo)) {
            result.add(priceDzTagVo);
        }
        return result;
    }

    /**
     * 构建优惠感知版本的优惠标签，针对 基础策略
     *
     * @param productM
     * @param salePrice
     * @param platform
     * @return
     */
    public static List<DzTagVO> buildPromoPerceptionPriceBottomTagsBasic(ProductM productM, String salePrice, int platform) {
        return buildTabList(productM, salePrice, platform);
    }

    /**
     * 预售 > 秒杀 > 美团补贴 > 新客 > 其他
     * 这里无法处理会员逻辑（足疗独有）
     *
     * @param productM
     * @param platform
     * @return
     */
    private static DzTagVO buildPromoPerceptionTag(ProductM productM, String salePrice, int platform) {
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(productM.getPromoPrices());
        if (CollectionUtils.isEmpty(productM.getPromoPrices()) || MapUtils.isEmpty(promoPriceMMap)) {
            return null;
        }
        ProductPromoPriceM preSalePromoPrice = null;
        DzTagVO dzTagVO = null;
        // 预售优先级最高
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_Member.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_Member.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_NewUser.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_NewUser.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale.getCode())) {
            preSalePromoPrice = promoPriceMMap.get((PromoTagTypeEnum.PreSale.getCode()));
        }

        // 秒杀其次（非商品侧处理）
        if (Objects.isNull(preSalePromoPrice) && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(productM)) {
            DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(productM.getPromoPrices(), productM.getMarketPrice(), salePrice, platform, 3);
            if (Objects.nonNull(secKillDzTag)) {
                secKillDzTag.setPrePic(new DzPictureComponentVO(
                        PlatformUtil.isMT(platform) ? ProductMPromoInfoUtils.MT_SEC_KILL_TAG_URL : ProductMPromoInfoUtils.DP_SEC_KILL_TAG_URL,
                        3.25));
                secKillDzTag.setAfterPic(PromoPerceptionUtils.buildAfterPic(platform));
                secKillDzTag.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
                return secKillDzTag;
            }
        }
        ProductPromoPriceM otherPromoPrice = PromoPerceptionUtils.getNoMemberPromoM(productM);
        if (Objects.isNull(otherPromoPrice)) {
            return null;
        }
        ProductPromoPriceM promoPriceM = Objects.nonNull(preSalePromoPrice) ? preSalePromoPrice : otherPromoPrice;
        dzTagVO = PromoPerceptionUtils.buildPromoTagVo(platform, promoPriceM);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        dzTagVO.setPrePic(new DzPictureComponentVO(promoPriceM.getIcon(),
                PlatformUtil.isMT(platform) ? 3.25 : 3.25));
        dzTagVO.setAfterPic(PromoPerceptionUtils.buildAfterPic(platform));
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(promoPriceM, 3));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return dzTagVO;
    }

    /**
     * 获取非会员的标签
     *
     * @param productM
     * @return
     */
    private static ProductPromoPriceM getNoMemberPromoM(ProductM productM) {
        List<ProductPromoPriceM> noMemberPriceM = productM.getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉无效值
                        && !a.getPromoTagType().equals(0)
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }

    /**
     * 构建优惠感知版本的priceBottomTag，有特别的样式逻辑
     *
     * @return
     */
    private static DzTagVO buildPromoTagVo(int platform, ProductPromoPriceM productPromoPriceM) {
        DzTagVO basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColor(platform, productPromoPriceM.getPromoTag(),
                ColorUtils.colorFF5500, ColorUtils.colorFFD6BE, null, ColorUtils.colorFF6633, ColorUtils.colorFFF2EE);
        // 点评侧的「新客特惠」、「特惠促销」样式不一样
        if (Objects.nonNull(basicTagVo) && !PlatformUtil.isMT(platform) && Objects.nonNull(productPromoPriceM.getPromoTagType()) &&
                (productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.NewUser.getCode()) || productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Other.getCode()))) {
            basicTagVo.setHasBorder(true);
            basicTagVo.setBorderColor(ColorUtils.colorFFCFBF);
            basicTagVo.setBackground(null);
        }
        return basicTagVo;
    }

    /**
     * 构建 AfterPic
     *
     * @param platform
     */
    private static DzPictureComponentVO buildAfterPic(int platform) {
        if (PlatformUtil.isMT(platform)) {
            return new DzPictureComponentVO("https://p0.meituan.net/travelcube/ac0b1b7e016f85fcf9b8784d34d1bc14439.png", 1);
        } else {
            return new DzPictureComponentVO("https://p1.meituan.net/travelcube/4b5a342d1d0c2ae89ea51d8a2a6f7d75459.png", 1);
        }
    }

    public static List<DzTagVO> buildTabList(ProductM productM, String salePrice, int platform) {
        List<DzTagVO> tags = new ArrayList<>();
        List<DzTagVO> directPromoTag = buildPromoTag(productM, salePrice, platform);
        if (CollectionUtils.isNotEmpty(directPromoTag)) {
            tags.addAll(directPromoTag);
        }
        List<DzTagVO> couponTag = buildDefCouponTags(productM);
        if (CollectionUtils.isNotEmpty(couponTag)) {
            tags.addAll(couponTag);
        }
        return tags;
    }

    private static List<DzTagVO> buildPromoTag(ProductM productM, String salePrice, int platform) {
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(productM.getPromoPrices());
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return Lists.newArrayList();
        }
        DzTagVO dzTagVO = null;
        BigDecimal promoPrice = new BigDecimal(productM.getMarketPrice()).subtract(getSalePrice(salePrice));
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.NewUser.getCode())) {
            dzTagVO = DzPromoUtils.buildBasicDzTagVOWithColor(platform, "商家新客共省¥" + promoPrice,
                    ColorUtils.colorFF4B10, null, null, ColorUtils.colorFF6633, null);
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.Default.getCode())) {
            //当有立减、券等除“门市价-团购价”以外优惠时才展示优惠标签
            return Lists.newArrayList();
        } else {
            dzTagVO = DzPromoUtils.buildBasicDzTagVOWithColor(platform, "特惠促销共省¥" + promoPrice,
                    ColorUtils.colorFF4B10, null, null, ColorUtils.colorFF6633, null);
        }
        return Lists.newArrayList(dzTagVO);
    }

    public static BigDecimal getSalePrice(String salePrice) {
        if (org.apache.commons.lang.StringUtils.isEmpty(salePrice)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(salePrice);
    }

    /**
     * 默认价格下方显示返券信息
     * 适用于大部分货架场景，不包括医美预付货架
     *
     * @param productM
     * @return
     */
    protected static List<DzTagVO> buildDefCouponTags(ProductM productM) {
        if (CollectionUtils.isEmpty(productM.getCoupons())) {
            return null;
        }
        return productM.getCoupons().stream()
                .filter(productCouponM -> productCouponM != null && org.apache.commons.lang.StringUtils.isNotEmpty(productCouponM.getCouponTag()))
                .map(productCouponM -> new DzTagVO("#FF6633", false, productCouponM.getCouponTag()))
                .collect(Collectors.toList());
    }


    /**
     * 双列货架-加上多买多减的bottomTags逻辑，template-家居家装用（优惠信息放在了第三行，因此第四行无需）
     * 多买多减 > 次卡拼团信息*
     *
     * @return
     */
    public static List<RichLabelVO> buildHomeDoubleShelfBottomTagsWithPerception(ActivityContext activityContext, ProductM productM, String salePrice) {

        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        List<DzTagVO> homeDoubleShelfPromoInfo = buildTabList(productM, salePrice, platform);
        RichLabelVO discount = ProductMPromoInfoUtils.getDiscountByNum(productM, platform);
        RichLabelVO card = buildBottomTagsRichLabel(productM.getCardPrice());
        RichLabelVO pin = buildBottomTagsRichLabel(productM.getPinPrice());
        if (CollectionUtils.isNotEmpty(homeDoubleShelfPromoInfo)) {
            //当有优惠信息时，优先用priceBottomTags展示
            return null;
        }
        if (discount != null) {
            return Lists.newArrayList(discount);
        }
        if (card != null) {
            return Lists.newArrayList(card);
        }
        if (pin != null) {
            return Lists.newArrayList(pin);
        }
        return new ArrayList<>();
    }

    public static RichLabelVO buildBottomTagsRichLabel(ProductPriceM productPriceM) {
        if (productPriceM == null || StringUtils.isEmpty(productPriceM.getPriceTag())) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        String[] priceDesc = productPriceM.getPriceTag().split("/");
        if (priceDesc.length < 2) {
            List<LifeDealFloorsBuilderExt.RichLabel> richLabelList = Lists.newArrayList(
                    new LifeDealFloorsBuilderExt.RichLabel(productPriceM.getPriceDesc(), ColorUtils.color777777, FrontSizeUtils.front11),
                    new LifeDealFloorsBuilderExt.RichLabel(productPriceM.getPriceTag(), ColorUtils.colorFF6633, FrontSizeUtils.front11));
            richLabelVO.setText(JsonCodec.encode(richLabelList));
            return richLabelVO;
        }
        List<LifeDealFloorsBuilderExt.RichLabel> richLabelList = Lists.newArrayList(
                new LifeDealFloorsBuilderExt.RichLabel(productPriceM.getPriceDesc(), ColorUtils.color777777, FrontSizeUtils.front11),
                new LifeDealFloorsBuilderExt.RichLabel(priceDesc[0], ColorUtils.colorFF6633, FrontSizeUtils.front11),
                new LifeDealFloorsBuilderExt.RichLabel("/" + priceDesc[1], ColorUtils.colorFF6633, FrontSizeUtils.front11));
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return richLabelVO;
    }


}
