package com.sankuai.dzviewscene.shelf.framework.monitor;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * 楼层商品水位监控
 */
@Component
public class FloorItemsMonitor {

    public void doMonitor(ActivityCxt activityCxt, DzItemVO dzItemVO, ProductM productM) {
        monitorMagicalMember(activityCxt, dzItemVO);
    }

    public void doMonitor(ActivityCxt activityCxt, DzProductVO dzProductVO) {
        monitorMagicalMember(activityCxt, dzProductVO);
    }

    public void doMonitor(ActivityCxt activityCxt, ShelfItemVO shelfItemVO) {
        monitorMagicalMember(activityCxt, shelfItemVO);
    }

    /**
     * 神会员监控
     * @param activityCxt
     * @param dzItemVO
     * @param dzItemVO
     */
    private void monitorMagicalMember(ActivityCxt activityCxt, DzItemVO dzItemVO) {
        Cat.logMetricForCount("ProductMagicalMemberTag",  buildMetricTags(activityCxt, dzItemVO));
    }

    private void monitorMagicalMember(ActivityCxt activityCxt, DzProductVO dzProductVO) {
        Cat.logMetricForCount("ProductMagicalMemberTag",  buildMetricTags(activityCxt, dzProductVO));
    }

    private void monitorMagicalMember(ActivityCxt activityCxt, ShelfItemVO shelfItemVO) {
        Cat.logMetricForCount("ProductMagicalMemberTag",  buildMetricTags(activityCxt, shelfItemVO));
    }

    private Map<String, String> buildMetricTags(ActivityCxt activityCxt, DzItemVO dzItemVO) {
        Map<String, String> tags = new HashMap<>();
        tags.put("sceneCode", activityCxt.getSceneCode());
        tags.put("showMagicalMember", Boolean.toString(ModelUtils.hasMagicalMemberPromo(dzItemVO)));
        int clientType = ParamsUtil.getIntSafely(activityCxt.getParameters(), OtherActivityConstants.Params.userAgent);
        String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
        if(StringUtils.isNotEmpty(clientTypeMsg)){
            tags.put("clientType", clientTypeMsg);
        }
        return tags;
    }

    private Map<String, String> buildMetricTags(ActivityCxt activityCxt, DzProductVO dzProductVO) {
        Map<String, String> tags = new HashMap<>();
        tags.put("sceneCode", activityCxt.getSceneCode());
        tags.put("showMagicalMember", Boolean.toString(ModelUtils.hasMagicalMemberPromo(dzProductVO)));
        int clientType = ParamsUtil.getIntSafely(activityCxt.getParameters(), OtherActivityConstants.Params.userAgent);
        String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
        if(StringUtils.isNotEmpty(clientTypeMsg)){
            tags.put("clientType", clientTypeMsg);
        }
        return tags;
    }

    private Map<String, String> buildMetricTags(ActivityCxt activityCxt, ShelfItemVO shelfItemVO) {
        Map<String, String> tags = new HashMap<>();
        tags.put("sceneCode", activityCxt.getSceneCode());
        tags.put("showMagicalMember", Boolean.toString(ModelUtils.hasMagicalMemberPromo(shelfItemVO)));
        int clientType = ParamsUtil.getIntSafely(activityCxt.getParameters(), OtherActivityConstants.Params.userAgent);
        String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
        if(StringUtils.isNotEmpty(clientTypeMsg)){
            tags.put("clientType", clientTypeMsg);
        }
        return tags;
    }
}
