package com.sankuai.dzviewscene.shelf.gateways.rpc;

import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mtrace.Tracer;
import com.sankuai.athena.viewscene.framework.pmf.monitor.error.ExecuteError;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.dealshelf.req.DealShelfRequest;
import com.sankuai.dzviewscene.dealshelf.res.DealShelfResponse;
import com.sankuai.dzviewscene.dealshelf.service.ProductShelfQueryService;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;

/**
 * 团购货架RPC接口实现
 * Created by zhangsuping on 2023/3/30.
 */
@Service(url = "com.sankuai.dzviewscene.dealshelf.service.ProductShelfQueryRPCService")
public class ProductShelfQueryRPCService implements ProductShelfQueryService {

    @Resource
    private ActivityEngine activityEngine;

    @Override
    public DealShelfResponse getDealShelfForFirstLoad(DealShelfRequest request) {
        Tracer.putContext(PoiIdUtil.POIID_CRYPTO_ENABLE, "false");
        return getDealShelfByChannel(request, ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
    }

    @Override
    public DealShelfResponse getShelfDeals(DealShelfRequest request) {
        return getDealShelfByChannel(request, ShelfActivityConstants.ChannelType.dealShelfListForTab);
    }

    /**
     * 根据 channel 获取对应团购货架
     * 把 channel 语义给内部消化掉，业务方只要关心具体场景即可。后续能力丰富了再把 channel 抽象对外
     *
     * @param request
     * @param channel
     * @return
     */
    private DealShelfResponse getDealShelfByChannel(DealShelfRequest request, String channel) {
        //0. 校验入参
        String checkRequestRes = checkRequest(request);
        if (StringUtils.isNotEmpty(checkRequestRes)) {
            return new DealShelfResponse(null, checkRequestRes);
        }
        // 1. 活动框架
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(buildActivityRequest(request, channel));
        if (activityResponse == null) {
            return null;
        }
        // 2. 构造返回结果
        return buildResponse(activityResponse);
    }

    /**
     * 构造返回结果
     *
     * @param activityResponse
     * @return
     */
    private DealShelfResponse buildResponse(ActivityResponse<DzShelfResponseVO> activityResponse) {
        if (activityResponse.getResult() == null) {
            return new DealShelfResponse();
        }
        DzShelfResponseVO shelfResponseVO = activityResponse.getResult().join();
        if (shelfResponseVO == null) {
            return new DealShelfResponse();
        }
        //清空打点数据
        shelfResponseVO.setOcean(null);
        return new DealShelfResponse(shelfResponseVO, buildErrorMessage(activityResponse.getExecuteError()));
    }

    private String buildErrorMessage(ExecuteError executeError) {
        if (executeError == null) {
            return StringUtils.EMPTY;
        }
        if (executeError.getActivityError() == null && CollectionUtils.isEmpty(executeError.getProcedureError())) {
            return StringUtils.EMPTY;
        }
        return JsonCodec.encodeWithUTF8(executeError);
    }

    /**
     * 校验接口入参
     *
     * @param request
     * @return
     */
    private String checkRequest(DealShelfRequest request) {
        if (request == null) {
            return "request is empty";
        }
        if (request.getShopId() < 0) {
            return "shopId can not be negative";
        }
        if (request.getCityId() < 0) {
            return "cityId can not be negative";
        }
        return "";
    }

    private ActivityRequest buildActivityRequest(DealShelfRequest request, String channel) {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, request.getSceneCode());
        activityRequest.addParam(ShelfActivityConstants.Params.pageNo, request.getPageNo());
        activityRequest.addParam(ShelfActivityConstants.Params.pageSize, request.getPageSize());
        activityRequest.addParam(ShelfActivityConstants.Params.keyword, request.getSearchKeyword());
        activityRequest.addParam(ShelfActivityConstants.Params.extra, request.getExtra());
        activityRequest.addParam(ShelfActivityConstants.Params.selectedFilterId, request.getFilterBtnId());
        activityRequest.addParam(ShelfActivityConstants.Params.shopUuid, request.getShopUuid());
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, request.getPlatform());
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, request.getUnionId());
        activityRequest.addParam(ShelfActivityConstants.Params.deviceId, request.getDeviceId());
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, request.getClient());
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, request.getVersion());
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, channel);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(request.getPlatform()) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(request.getPlatform())) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, request.getCityId());
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, Long.valueOf(request.getShopId()).intValue()); // poiMigrate
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiIdL, request.getShopId());
            activityRequest.addParam(ShelfActivityConstants.Params.mtUserId, request.getUserId());
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, request.getCityId());
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, Long.valueOf(request.getShopId()).intValue()); // poiMigrate
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiIdL, request.getShopId());
            activityRequest.addParam(ShelfActivityConstants.Params.dpUserId, request.getUserId());
        }
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        activityRequest.addParam(ShelfActivityConstants.Params.searchKeyword, request.getSearchKeyword());
        activityRequest.addParam(ShelfActivityConstants.Params.searchterm, request.getSearchKeyword());
        activityRequest.addParam(ShelfActivityConstants.Params.spaceKey, getSpaceKey(request));
        return activityRequest;
    }

    private String getSpaceKey(DealShelfRequest request){
        if (StringUtils.isNotBlank(request.getSpaceKey())){
            return request.getSpaceKey();
        }
        return DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY;
    }
}
