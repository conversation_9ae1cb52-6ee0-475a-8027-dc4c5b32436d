package com.sankuai.dzviewscene.shelf.gateways.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import static org.apache.commons.lang.CharEncoding.UTF_8;


/**
 * @desc:
 * @author: yudongping
 * @date: 2021/8/18 11:07 上午
 */
@Slf4j
public class CompressUtils {

    public static byte[] gzipCompress(String str) {
        if (str == null || str.length() == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip;
        try {
            gzip = new GZIPOutputStream(out);
            gzip.write(str.getBytes(UTF_8));
            gzip.close();
        } catch (Exception e) {
            log.error("[CompressUtils.gzipCompress]",e);
        }
        return out.toByteArray();
    }

    /**
     * 使用gzip进行解压缩
     */
    public static String uncompress(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(bytes);
        try {
            GZIPInputStream ungzip = new GZIPInputStream(in);
            byte[] buffer = new byte[256];
            int n;
            while ((n = ungzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
            return out.toString(UTF_8);
        } catch (Exception e) {
            log.error("[CompressUtils.uncompress]",e);
        }
        return null;
    }

    public static Map<String,String> parseHeader(Header[] headers){
        if (headers == null){
            return Collections.emptyMap();
        }
        Map<String,String> headerMap = new HashMap<>();
        for (Header header : headers){
            if (header == null){
                continue;
            }
            headerMap.put(header.getName(),header.getValue());
        }
        return headerMap;
    }
}
