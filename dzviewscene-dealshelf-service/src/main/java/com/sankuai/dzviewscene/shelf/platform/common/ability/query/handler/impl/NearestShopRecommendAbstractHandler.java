package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2024-07-08
 * @description:
 */
@Component
public abstract class NearestShopRecommendAbstractHandler {
    @Resource
    private AtomFacadeService atomFacadeService;

    private static final String PRODUCT_IDS_PARAM = "productIds";
    private static final String RELATED_POI_IDS_PARAM = "relatedPois";
    private static int MAX_QUERY_SIZE = 15;

    protected void addNearestShopForProduct(ActivityContext context, Integer bizId, List<ProductM> products) {
        // 如果当前召回选择从推荐召回商户，需要将context中的商户信息置空
        cleanShopIdFromContext(context);
        if (CollectionUtils.isEmpty(products) || bizId == null || bizId <= 0) {
            return;
        }
        Map<Long, Long> product2ShopIdMap = queryProduct2ShopIdMap(context, bizId, products);
        if (MapUtils.isEmpty(product2ShopIdMap)) {
            return;
        }
        for (ProductM product : products) {
            if (product == null || !product2ShopIdMap.containsKey((long) product.getProductId())) {
                continue;
            }
            Long shopId = product2ShopIdMap.get((long) product.getProductId());
            product.setShopIds(Lists.newArrayList(shopId.intValue()));
            product.setShopLongIds(Lists.newArrayList(shopId));
        }
    }

    protected Map<Long, Long> queryProduct2ShopIdMap(ActivityContext context, Integer bizId, List<ProductM> products) {
        if (CollectionUtils.isEmpty(products) || bizId == null || bizId <= 0) {
            return Maps.newHashMap();
        }
        List<Long> productIds = products.stream().filter(Objects::nonNull).map(ProductM::getProductId).map(Long::valueOf).collect(Collectors.toList());
        // 请求商品最近商户，一次最多请求15个商品
        List<List<Long>> productsParts = Lists.partition(productIds, MAX_QUERY_SIZE);
        List<CompletableFuture<RecommendResult<RecommendDTO>>> responseFutures = Lists.newArrayList();
        for (List<Long> productsPart : productsParts) {
            RecommendParameters recommendParameters = buildCalShopRecommendParams(context, productsPart, bizId);
            responseFutures.add(atomFacadeService.queryRecommendProductIds(recommendParameters, RecommendDTO.class));
        }
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(responseFutures.toArray(new CompletableFuture[0]));
        CompletableFuture<List<RecommendDTO>> recommendFuture = allFutures.thenApply(v -> {
            List<RecommendDTO> resultList = Lists.newArrayList();
            for (CompletableFuture<RecommendResult<RecommendDTO>> future : responseFutures) {
                RecommendResult<RecommendDTO> recommendResult = future.join();
                if (recommendResult != null && CollectionUtils.isNotEmpty(recommendResult.getSortedResult())) {
                    resultList.addAll(recommendResult.getSortedResult());
                }
            }
            return resultList;
        });
        List<RecommendDTO> recommendList = recommendFuture.join();
        return convertProduct2ShopIdMap(recommendList);
    }

    private Map<Long, Long> convertProduct2ShopIdMap(List<RecommendDTO> recommendList) {
        if (CollectionUtils.isEmpty(recommendList)) {
            return Maps.newHashMap();
        }
        Map<Long, Long> resultMap = Maps.newHashMap();
        for (RecommendDTO recommendDTO : recommendList) {
            if (recommendDTO == null || StringUtils.isEmpty(recommendDTO.getItem()) || MapUtils.isEmpty(recommendDTO.getBizData())) {
                continue;
            }
            Map<String, Object> bizData = recommendDTO.getBizData();
            if (!bizData.containsKey(RELATED_POI_IDS_PARAM)) {
                continue;
            }
            String relatedShopIdsStr = (String) bizData.get(RELATED_POI_IDS_PARAM);
            List<String> relatedShopIds = convertStr2List(relatedShopIdsStr, ",");
            if (CollectionUtils.isEmpty(relatedShopIds) || StringUtils.isEmpty(relatedShopIds.get(0))) {
                continue;
            }
            resultMap.put(Long.parseLong(recommendDTO.getItem()), Long.parseLong(relatedShopIds.get(0)));
        }
        return resultMap;
    }

    private RecommendParameters buildCalShopRecommendParams(ActivityContext context, List<Long> productIds, int bizId) {
        RecommendParameters recommendParameters = new RecommendParameters();
        int platform = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (PlatformUtil.isMT(platform)) {
            recommendParameters.setPlatformEnum(PlatformEnum.MT);
            recommendParameters.setCityId(Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.mtCityId)).orElse(0));
            recommendParameters.setUuid(context.getParam(ShelfActivityConstants.Params.deviceId));
            recommendParameters.setOriginUserId(Optional.ofNullable((Long) context.getParam(ShelfActivityConstants.Params.mtUserId)).map(String::valueOf).orElse(null));
        } else {
            recommendParameters.setPlatformEnum(PlatformEnum.DP);
            recommendParameters.setCityId(Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0));
            recommendParameters.setDpid(context.getParam(ShelfActivityConstants.Params.deviceId));
            recommendParameters.setOriginUserId(Optional.ofNullable((Long) context.getParam(ShelfActivityConstants.Params.dpUserId)).map(String::valueOf).orElse(null));
        }
        recommendParameters.setLat(context.getParam(ShelfActivityConstants.Params.lat));
        recommendParameters.setLng(context.getParam(ShelfActivityConstants.Params.lng));
        recommendParameters.setBizId(bizId);
        recommendParameters.setPageNumber(1);
        recommendParameters.setPageSize(productIds.size());
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put(PRODUCT_IDS_PARAM, convertList2StrWithDelimiter(productIds, ","));
        recommendParameters.setBizParams(bizParams);
        return recommendParameters;
    }

    private void cleanShopIdFromContext(ActivityContext context) {
        context.addParam(ShelfActivityConstants.Params.dpPoiId, 0);
        context.addParam(ShelfActivityConstants.Params.mtPoiId, 0);
        context.addParam(ShelfActivityConstants.Params.dpPoiIdL, 0);
        context.addParam(ShelfActivityConstants.Params.mtPoiIdL, 0);
    }

    private String convertList2StrWithDelimiter(List<?> list, String delimiter) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return StringUtils.join(list, delimiter);
    }

    private List<String> convertStr2List(String str, String delimiter) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(str.split(delimiter))
                .map(String::trim).collect(Collectors.toList());
    }
}
