package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dztheme.shop.enums.ClientType;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeResponse;
import com.sankuai.swan.udqs.api.PageInfo;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;


@Component
public class SupplyCommandMatchDealHandler implements GroupQueryHandler {

    @Resource
    private CompositeAtomService compositeAtomService;
    private static final String DP_SHOP_THEME_PLANID = "10500565";

    private static final String MT_SHOP_THEME_PLANID = "10500564";

    private static final String SHOP_TYPE_VALUE_HAVE = "有相关供给但价格需优化";

    public static final String SHOP_TYPE_VALUE_HAVE_KEY = "SHOP_TYPE_VALUE_HAVE_LIMIT_BUY";

    //直辖市backCityId转换
    private static final Map<Integer, Integer> transBackCityId = Maps.newHashMap();

    static {
        transBackCityId.put(310100, 310000);
        transBackCityId.put(110100, 110000);
        transBackCityId.put(120100, 120000);
        transBackCityId.put(500100, 500000);
    }

    @Override

    public CompletableFuture<ProductGroupM> query(ActivityContext activityContext, String groupName, Map<String, Object> params) {

        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.userAgent);

        CompletableFuture<Integer> backCityIdFuture = queryBackCiyId(activityContext, platform);

        CompletableFuture<Result<QueryData>> tenantIdRespDTOCompletableFuture =
                backCityIdFuture.thenCompose(backCityId -> {
                    Integer newBackCityId = transBackCityId.getOrDefault(backCityId, backCityId);
                    return compositeAtomService.batchQuerySupplyCommandMatchDeals(newBackCityId,
                            ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.pageNo),
                            ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.pageSize));
                });

        CompletableFuture<ProductGroupM> productGroupMCompletableFuture = tenantIdRespDTOCompletableFuture
                .thenApply(paginateMemberGrouponRespDTO -> buildProductGroupM(paginateMemberGrouponRespDTO, platform));


        return productGroupMCompletableFuture.thenCompose(productGroupM -> buildProductGroupMShopM(productGroupM, activityContext));


    }

    CompletableFuture<Integer> queryBackCiyId(ActivityContext activityContext, int platform) {
        boolean isMT = com.dianping.vc.enums.VCClientTypeEnum.isMtClientTypeByCode(platform);
        int sourceCityId = isMT ? ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.mtCityId)
                : ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.dpCityId);
        CompletableFuture<TransformCityTypeResponse> future = compositeAtomService.queryBackCityId(String.valueOf(sourceCityId), isMT);
        return future.thenApply(transformCityTypeResponse ->
                NumberUtils.toInt(transformCityTypeResponse.getMt_back_info().getMt_back_city_id())
        );
    }


    private CompletionStage<ProductGroupM> buildProductGroupMShopM(ProductGroupM productGroupM, ActivityContext activityContext) {
        List<Long> shopIds = Lists.newArrayList();
        for (ProductM product : productGroupM.getProducts()) {
            if (CollectionUtils.isNotEmpty(PoiIdUtil.getShopIdsL(product))) {
                //product.getShopIds().size == 1 只会等于1
                shopIds.addAll(PoiIdUtil.getShopIdsL(product));
            }
        }
        ShopThemePlanRequest shopThemePlanRequest = buildShopThemePlanRequest(shopIds, activityContext);
        CompletableFuture<List<ShopM>> shopListCompletableFuture = compositeAtomService.multiShopMListNew(shopThemePlanRequest);
        return shopListCompletableFuture.thenApply(shopDTOS -> fillShop(shopDTOS, productGroupM));
    }


    private ShopThemePlanRequest buildShopThemePlanRequest(List<Long> shopIds, ActivityContext ctx) {
        shopIds = shopIds.stream().distinct().collect(Collectors.toList());
        int platform = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.platform);
        int cityId = PlatformUtil.isMT(platform) ? ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.mtCityId) : ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.dpCityId);
        String deviceId = ParamsUtil.getStringSafely(ctx, FilterListActivityConstants.Params.deviceId);
        long userId = PlatformUtil.isMT(platform) ? ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.mtUserId) : ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.dpUserId);
        double lat = ParamsUtil.getDoubleSafely(ctx, FilterListActivityConstants.Params.lat);
        double lng = ParamsUtil.getDoubleSafely(ctx, FilterListActivityConstants.Params.lng);
        String version = ParamsUtil.getStringSafely(ctx, FilterListActivityConstants.Params.appVersion);
        ShopThemePlanRequest shopThemePlanRequest = new ShopThemePlanRequest();
        String planId = PlatformUtil.isMT(platform) ? MT_SHOP_THEME_PLANID : DP_SHOP_THEME_PLANID;
        shopThemePlanRequest.setPlanId(planId);
        shopThemePlanRequest.setPlatform(PlatformUtil.isMT(platform) ? 2 : 1);
        shopThemePlanRequest.setShopIds(shopIds.stream().map(Long::intValue).collect(Collectors.toList()));
        shopThemePlanRequest.setLongShopIds(shopIds);
        shopThemePlanRequest.setNativeClient(true);
        shopThemePlanRequest.setClientType(ClientType.APP.getType());
        shopThemePlanRequest.setCityId(cityId);
        shopThemePlanRequest.setDeviceId(deviceId);
        shopThemePlanRequest.setUserId(userId);
        shopThemePlanRequest.setUserLat(lat);
        shopThemePlanRequest.setUserLng(lng);
        shopThemePlanRequest.setClientVersion(version);
        return shopThemePlanRequest;
    }

    private ProductGroupM fillShop(List<ShopM> shopDTOS, ProductGroupM productGroupM) {

        Map<Long, ShopM> shopId2ShopM = shopDTOS.stream().collect(Collectors.toMap(PoiIdUtil::getShopIdL, (p) -> p));

        productGroupM.getProducts().forEach(e -> e.setShopMs(Lists.newArrayList(transFormShopM(shopId2ShopM.get(PoiIdUtil.getShopIdsL(e).get(0))))));
        //进行排序
        sortByDistance(productGroupM);
        return productGroupM;
    }

    private void sortByDistance(ProductGroupM productGroupM) {

        List<ProductM> sortedAsc = productGroupM.getProducts().stream()
                .sorted((o1, o2) -> (int) (o1.getShopMs().get(0).getDistanceNum() - o2.getShopMs().get(0).getDistanceNum())).collect(Collectors.toList());
        productGroupM.setProducts(sortedAsc);

    }

    private ShopM transFormShopM(ShopM shopDTO) {
        ShopM shopM = new ShopM();
        shopM.setShopId(shopDTO.getShopId());
        shopM.setLongShopId(shopDTO.getLongShopId());
        shopM.setShopName(shopDTO.getShopName());
        shopM.setScoreTag(shopDTO.getScoreTag());
        shopM.setMainRegionName(shopDTO.getMainRegionName());
        shopM.setDistance(shopDTO.getDistance());
        shopM.setDistanceNum(shopDTO.getDistanceNum());
        return shopM;
    }


    private ProductGroupM buildProductGroupM(Result<QueryData> queryDataResult, int platform) {
        if (queryDataResult == null || queryDataResult.getData() == null || CollectionUtils.isEmpty(queryDataResult.getData().getResultSet())) {
            return null;
        }
        PageInfo pageInfo = queryDataResult.getData().getPageInfo();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setHasNext(pageInfo.getPageNo() < pageInfo.getTotalPage());
        ArrayList<ProductM> productMS = Lists.newArrayList();
        if (com.dianping.vc.enums.VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            for (Map<String, Object> map : queryDataResult.getData().getResultSet()) {
                if (!map.containsKey("mt_productgroup_id") || StringUtils.isBlank(map.get("mt_productgroup_id").toString())) {
                    continue;
                }
                ProductM productM = new ProductM();
                productM.setProductId(Integer.parseInt(map.get("mt_productgroup_id").toString()));
                if (map.containsKey("mt_shop_id")) {
                    productM.setShopIds(Lists.newArrayList(Integer.parseInt(map.get("mt_shop_id").toString())));
                    productM.setShopLongIds(Lists.newArrayList(Long.parseLong(map.get("mt_shop_id").toString())));
                }

                List<AttrM> attrMList = Lists.newArrayList();
                if (map.containsKey("shop_type") && SHOP_TYPE_VALUE_HAVE.equals(map.get("shop_type"))) {
                    AttrM attrM = new AttrM();
                    attrM.setName(SHOP_TYPE_VALUE_HAVE_KEY);
                    attrM.setValue(SHOP_TYPE_VALUE_HAVE);
                    attrMList.add(attrM);
                }

                productM.setExtAttrs(attrMList);
                productMS.add(productM);
            }
        } else {
            for (Map<String, Object> map : queryDataResult.getData().getResultSet()) {
                if (!map.containsKey("dp_productgroup_id") || StringUtils.isBlank(map.get("dp_productgroup_id").toString())) {
                    continue;
                }
                ProductM productM = new ProductM();
                productM.setProductId(Integer.parseInt(map.get("dp_productgroup_id").toString()));
                if (map.containsKey("dp_shop_id")) {
                    productM.setShopIds(Lists.newArrayList(Integer.parseInt(map.get("dp_shop_id").toString())));
                    productM.setShopLongIds(Lists.newArrayList(Long.parseLong(map.get("dp_shop_id").toString())));
                }

                List<AttrM> attrMList = Lists.newArrayList();
                if (map.containsKey("shop_type") && SHOP_TYPE_VALUE_HAVE.equals(map.get("shop_type"))) {
                    AttrM attrM = new AttrM();
                    attrM.setName(SHOP_TYPE_VALUE_HAVE_KEY);
                    attrM.setValue(SHOP_TYPE_VALUE_HAVE);
                    attrMList.add(attrM);
                }

                productM.setExtAttrs(attrMList);
                productMS.add(productM);
            }
        }
        productGroupM.setTotal(productMS.size());
        productGroupM.setProducts(productMS);
        return productGroupM;
    }


}
