package com.sankuai.dzviewscene.shelf.platform.common.ranking.function.product;

import cn.hutool.core.bean.BeanUtil;
import com.dianping.zebra.util.StringUtils;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON>zhui on 2020/12/18
 */
public class ShopFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        ProductM productM = (ProductM) env.get("item");
        List<ShopM> shopMS = productM.getShopMs();
        if (CollectionUtils.isEmpty(shopMS)) {
            return AviatorRuntimeJavaType.valueOf(BigDecimal.valueOf(10L));
        }
        ShopM shopM = shopMS.get(0);
        if (shopM == null) {
            return AviatorRuntimeJavaType.valueOf(BigDecimal.valueOf(10L));
        }
        String fieldName = FunctionUtils.getStringValue(arg1, env);
        if (StringUtils.isBlank(fieldName)) {
            return AviatorRuntimeJavaType.valueOf(BigDecimal.valueOf(10L));
        }
        Object value = BeanUtil.getProperty(shopM, fieldName);
        if (value == null) {
            return AviatorRuntimeJavaType.valueOf(BigDecimal.valueOf(10L));
        }
        return AviatorRuntimeJavaType.valueOf(value);
    }

    @Override
    public String getName() {
        return "shop";
    }
}
