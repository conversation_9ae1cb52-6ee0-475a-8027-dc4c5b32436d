package com.sankuai.dzviewscene.shelf.platform.detail;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.shopremote.remote.dto.NormPhone;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.SimpleShopDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetProductNearestShopReq;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextBuilder;
import com.sankuai.dzviewscene.shelf.framework.core.IContextBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.CategoryM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.*;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.DpPoiUuidRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.ap.internal.util.Collections;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Created by liweilong06 on 20209/09/27.
 */
@ContextBuilder(activityCode = ProductDetailActivity.ACTIVITY_PRODUCT_DETAIL_CODE, name = "商品详情活动上下文构造器")
public class ProductDetailActivityContextBuilder implements IContextBuilder {

    private static final String SERVICE_TYPE_ATTR_NAME = "service_type";
    //是否标准团单
    private static final String STANDARD_SERVICE_ATTR_NAME = "standardDealGroup";
    //团单四级分类
    private static final String SERVICE_TYPE_LEAD_ID = "service_type_leaf_id";

    private static final String SKU_ATTR_PRE = "sku_attr_";

    private static final String TAG_UNIFY_PRODUCT = "tag_unifyProduct";

    private static final List<String> ALL_DEAL_ATTR_NAMES = Lists.newArrayList(SERVICE_TYPE_ATTR_NAME, STANDARD_SERVICE_ATTR_NAME, SERVICE_TYPE_LEAD_ID);

    //团详场景识别所需的团单属性Map，该map的key是团单属性标识，该map的value是团单属性名
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.deal.detail.scene.identifier.required.deal.attrs.config", defaultValue = "{}")
    private Map<String, String> dealDetailSceneIdentifierRequiredDealAttrMap;

    /**
     * 可获取服务项目id二级分类列表
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.deal.productcategoryid.categorylist", defaultValue = "[]")
    private static List<Integer> categorylist;

    /**
     * 太极项目二级分类
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.deal.productcategoryid.taijicategorylist", defaultValue = "[]")
    private static List<Integer> taiJiCategoryList;

    /**
     * 需要读取sku属性的团单分类
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.deal.detial.dealcategoryids.needskuattrs", defaultValue = "[1202, 1203, 1206, 1211, 1213, 1217, 1220, 1224]")
    private List<Integer> dealCategoryIdsForNeedSkuAttr;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public ActivityContext build(ActivityRequest activityRequest) {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(activityRequest.getParams());

        addPlatformParam(activityContext);
        return activityContext;
    }

    /**
     * 获取团详场景识别所需要的团单属性名列表
     *
     * @param
     * @return
     */
    private List<String> getDealDetailSceneIdentifierRequiredDealAttrNames() {
        List<String> dealAttrNameList = new ArrayList<>();
        dealAttrNameList.addAll(ALL_DEAL_ATTR_NAMES);
        if (MapUtils.isEmpty(dealDetailSceneIdentifierRequiredDealAttrMap)) {
            return dealAttrNameList;
        }
        dealAttrNameList.addAll(dealDetailSceneIdentifierRequiredDealAttrMap.values());
        return dealAttrNameList;
    }

    private void addPlatformParam(ActivityContext activityContext) {
        int platform = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform);
        // 点评平台
        if (!PlatformUtil.isMT(platform)) {
            activityContext.addParam(ProductDetailActivityConstants.Params.dpCityId, ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.dpCityId));
            int dealId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.productId);
            CategoryM category = getDpDealCategory(dealId);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(category).map(CategoryM::getCategoryId).orElse(0));
            activityContext.addParam(ProductDetailActivityConstants.Params.productCategoryList, Optional.ofNullable(category).map(CategoryM::getProductCategoryList).orElse(Lists.newArrayList()));

            addDealAttrParams(activityContext, dealId, platform);
            addPoiInfo(activityContext);
            addDealInfo(category, dealId, platform, activityContext);
            return;
        }
        // 美团平台
        CompletableFuture<Integer> dpCityIdFuture = compositeAtomService.getDpCityIdByMt(ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.mtCityId));
        dpCityIdFuture.thenAccept(dpCityId -> {
            activityContext.addParam(ProductDetailActivityConstants.Params.mtCityId, ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.mtCityId));
            activityContext.addParam(ProductDetailActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
            int dealId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.productId);
            CategoryM category = getMtDealCategory(dealId);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(category).map(CategoryM::getCategoryId).orElse(0));
            activityContext.addParam(ProductDetailActivityConstants.Params.productCategoryList, Optional.ofNullable(category).map(CategoryM::getProductCategoryList).orElse(Lists.newArrayList()));
            addDealAttrParams(activityContext, dealId, platform);
            addPoiInfo(activityContext);
            addDealInfo(category, dealId, platform, activityContext);
        }).join();
    }

    private void addDealInfo(CategoryM category, int dealId, int platform, ActivityContext activityContext) {
        if (Objects.isNull(category) || ((CollectionUtils.isEmpty(taiJiCategoryList) || !taiJiCategoryList.contains(category.getCategoryId())) &&
                !isNeedSkuAttrs(category.getCategoryId()))) {
            return;
        }
        IdTypeEnum idTypeEnum = PlatformUtil.isMT(platform) ? IdTypeEnum.MT : IdTypeEnum.DP;
        long dealIdLong = NumberUtils.objToLong(dealId);
        QueryByDealGroupIdRequestBuilder builder = (QueryByDealGroupIdRequestBuilder)QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dealIdLong), idTypeEnum)
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, Sets.newHashSet(TAG_UNIFY_PRODUCT));
        if (isNeedSkuAttrs(category.getCategoryId())) {
            builder.serviceProject(ServiceProjectBuilder.builder().all());
        }
        QueryByDealGroupIdRequest request = builder.build();
        compositeAtomService.batchGetServiceProjectByDealIds(request).thenAccept(dealGroupDTOS -> {
            if (ObjectUtils.isEmpty(dealGroupDTOS)) {
                return;
            }
            DealGroupDTO dealGroup = dealGroupDTOS.stream().filter(dealGroupDTO -> {
                long dealGroupId = idTypeEnum == IdTypeEnum.DP ? dealGroupDTO.getDpDealGroupId() : dealGroupDTO.getMtDealGroupId();
                return Objects.equals(dealIdLong, dealGroupId);
            }).findFirst().orElse(null);
            List<String> unifyProductList = Optional.ofNullable(dealGroupDTOS.get(0)).map(DealGroupDTO::getAttrs).orElse(Lists.newArrayList())
                    .stream().filter(attr -> !ObjectUtils.isEmpty(attr) && Objects.equals(TAG_UNIFY_PRODUCT, attr.getName()))
                    .map(AttrDTO::getValue)
                    .findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(unifyProductList)) {
                activityContext.addParam(ProductDetailActivityConstants.Params.isTaiJi, true);
            }
            StandardServiceProjectDTO standardServiceProjectDTO = Optional.ofNullable(dealGroup).map(DealGroupDTO::getStandardServiceProject).orElse(null);
            DealGroupServiceProjectDTO serviceProject = Optional.ofNullable(dealGroup).map(DealGroupDTO::getServiceProject).orElse(null);
            if (Objects.nonNull(standardServiceProjectDTO)) {
                activityContext.addParam(ProductDetailActivityConstants.Params.isTaiJi, true);
                addDealSkuAttrParams(activityContext, standardServiceProjectDTO);
            }
            if (Objects.nonNull(serviceProject)) {
                addDealSkuAttrParams(activityContext, serviceProject);
            }
        }).join();
    }

    private boolean isNeedSkuAttrs(Integer categoryId) {
        return categoryId != null && CollectionUtils.isNotEmpty(dealCategoryIdsForNeedSkuAttr)
                && dealCategoryIdsForNeedSkuAttr.contains(categoryId);
    }

    private CategoryM getDpDealCategory(int dealId) {
        if (dealId == 0) {
            return null;
        }
        CompletableFuture<Map<Integer, Integer>> dealIdCategoryIdMapFuture = compositeAtomService.batchGetDealIdCategoryIdMap(Lists.newArrayList(dealId));
        return dealIdCategoryIdMapFuture.thenCompose(dealIdCategoryIdMap -> {
            if (MapUtils.isEmpty(dealIdCategoryIdMap)) {
                return CompletableFuture.completedFuture(null);
            }
            Integer categoryId = dealIdCategoryIdMap.get(dealId);

            if (!categorylist.contains(categoryId)) {
                CategoryM categoryM = new CategoryM();
                categoryM.setCategoryId(categoryId);
                return CompletableFuture.completedFuture(categoryM);
            }

            return compositeAtomService.batchQueryDealDetailInfo(Lists.newArrayList(dealId)).thenApply(res -> {
                CategoryM categoryM = new CategoryM();
                categoryM.setCategoryId(categoryId);
                categoryM.setProductCategoryList(getProductCategoryList(res, dealId));
                return categoryM;
            });
        }).join();
    }

    private void addDealAttrParams(ActivityContext activityContext, int dealId, int platform) {
        if (dealId <= 0) {
            return;
        }
        CompletableFuture<DealGroupAttributeDTO> dealIdAttrDTOMapFuture = PlatformUtil.isMT(platform) ? getMtDealAttrValues(dealId) : getDealAttrValuesFuture(dealId);
        if (dealIdAttrDTOMapFuture == null) {
            return;
        }
        dealIdAttrDTOMapFuture.thenAccept(dealIdAttrDTO -> {
            if (dealIdAttrDTO != null) {
                String serviceType = getAttrValue(dealIdAttrDTO, SERVICE_TYPE_ATTR_NAME);
                String standardDealGroup = getAttrValue(dealIdAttrDTO, STANDARD_SERVICE_ATTR_NAME);
                String serviceTypeLeafId = getAttrValue(dealIdAttrDTO, SERVICE_TYPE_LEAD_ID);
                activityContext.addParam(ProductDetailActivityConstants.Params.serviceType, Optional.ofNullable(serviceType).orElse(""));
                activityContext.addParam(ProductDetailActivityConstants.Params.standardDealGroup, Optional.ofNullable(standardDealGroup).orElse(""));
                activityContext.addParam(ProductDetailActivityConstants.Params.serviceTypeLeafId, Optional.ofNullable(serviceTypeLeafId).orElse(""));
                addSceneIdentifierRequiredDealAttrs(activityContext, dealIdAttrDTO);
            }
            return;
        }).join();
    }

    private void addDealSkuAttrParams(ActivityContext activityContext, DealGroupServiceProjectDTO serviceProject) {
        if (serviceProject == null) {
            return;
        }
        List<ServiceProjectDTO> totalItems = getTotalServiceItem(serviceProject);
        if (CollectionUtils.isEmpty(totalItems)) {
            return;
        }
        for (ServiceProjectDTO item : totalItems) {
            if (CollectionUtils.isEmpty(item.getAttrs())) {
                continue;
            }
            addAllSkuAttrs(activityContext, item.getAttrs());
        }
    }

    private List<ServiceProjectDTO> getTotalServiceItem(DealGroupServiceProjectDTO serviceProject) {
        List<ServiceProjectDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceProject.getMustGroups())) {
            for (MustServiceProjectGroupDTO mustGroup : serviceProject.getMustGroups()) {
                if (CollectionUtils.isEmpty(mustGroup.getGroups())) {
                    continue;
                }
                result.addAll(mustGroup.getGroups());
            }
        }
        if (CollectionUtils.isNotEmpty(serviceProject.getOptionGroups())) {
            for (OptionalServiceProjectGroupDTO options : serviceProject.getOptionGroups()) {
                if (CollectionUtils.isEmpty(options.getGroups())) {
                    continue;
                }
                result.addAll(options.getGroups());
            }
        }
        return result;
    }

    private void addAllSkuAttrs(ActivityContext activityContext, List<ServiceProjectAttrDTO> attrs) {
        for (ServiceProjectAttrDTO attr : attrs) {
            if (activityContext.getParam(SKU_ATTR_PRE + attr.getAttrName()) != null || StringUtils.isBlank(attr.getAttrValue())) {
                // 防止覆盖已有属性
                continue;
            }
            activityContext.addParam(SKU_ATTR_PRE + attr.getAttrName(), attr.getAttrValue());
        }
    }

    private void addDealSkuAttrParams(ActivityContext activityContext, StandardServiceProjectDTO standardServiceProjectDTO) {
        if (standardServiceProjectDTO == null) {
            return;
        }
        List<StandardServiceProjectItemDTO> totalItems = new ArrayList<>();
        addStandardSkuItems(standardServiceProjectDTO.getMustGroups(), totalItems);
        addStandardSkuItems(standardServiceProjectDTO.getOptionalGroups(), totalItems);
        if (CollectionUtils.isEmpty(totalItems)) {
            return;
        }
        for (StandardServiceProjectItemDTO item : totalItems) {
            if (item.getStandardAttribute() == null ||CollectionUtils.isEmpty(item.getStandardAttribute().getAttrs())) {
                continue;
            }
            addAllStandardSkuAttrs(activityContext, item.getStandardAttribute().getAttrs());
        }
    }

    private static void addStandardSkuItems(List<StandardServiceProjectGroupDTO> groups, List<StandardServiceProjectItemDTO> totalItems) {
        if (CollectionUtils.isNotEmpty(groups)) {
            List<StandardServiceProjectItemDTO> items = groups.stream().map(StandardServiceProjectGroupDTO::getServiceProjectItems).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(items)) {
                totalItems.addAll(items);
            }
        }
    }

    private void addAllStandardSkuAttrs(ActivityContext activityContext, List<StandardAttributeItemDTO> attrs) {
        for (StandardAttributeItemDTO attr : attrs) {
            if (activityContext.getParam(SKU_ATTR_PRE + attr.getAttrName()) != null || !hasAttrValue(attr)) {
                // 防止覆盖已有属性
                continue;
            }
            activityContext.addParam(SKU_ATTR_PRE + attr.getAttrName(), attr.getAttrValues().get(0).getSimpleValues().get(0));
        }
    }

    private boolean hasAttrValue(StandardAttributeItemDTO attr) {
        return CollectionUtils.isNotEmpty(attr.getAttrValues()) && attr.getAttrValues().get(0) != null
                && CollectionUtils.isNotEmpty(attr.getAttrValues().get(0).getSimpleValues());
    }

    /**
     * 在上下文中加入属性名配置在lion（key为com.sankuai.dzviewscene.dealshelf.deal.detail.scene.identifier.required.deal.attrs.config）上的团详场景识别所需要的团单属性
     *
     * @param
     * @return
     */
    private void addSceneIdentifierRequiredDealAttrs(ActivityContext activityContext, DealGroupAttributeDTO dealGroupAttributeDTO) {
        if (MapUtils.isEmpty(dealDetailSceneIdentifierRequiredDealAttrMap)) {
            return;
        }
        for (Map.Entry<String, String> entry : dealDetailSceneIdentifierRequiredDealAttrMap.entrySet()) {
            String dealAttrValue = getAttrValue(dealGroupAttributeDTO, entry.getValue());
            activityContext.addParam(entry.getKey(), Optional.ofNullable(dealAttrValue).orElse(""));
        }
    }

    private String getAttrValue(DealGroupAttributeDTO dealGroupAttributeDTO, String attrKey) {
        if (dealGroupAttributeDTO == null || CollectionUtils.isEmpty(dealGroupAttributeDTO.getAttributes())) {
            return null;
        }
        AttributeDTO attributeDTO = dealGroupAttributeDTO.getAttributes().stream().filter(attr -> attrKey.equals(attr.getName())).findFirst().orElse(null);
        if (attributeDTO == null) {
            return null;
        }
        return CollectUtils.firstValue(attributeDTO.getValue());
    }

    private CompletableFuture<DealGroupAttributeDTO> getDealAttrValuesFuture(int dealId) {
        List<Integer> dealIds = Lists.newArrayList(dealId);
        List<String> allDealAttrNames = getDealDetailSceneIdentifierRequiredDealAttrNames();
        return compositeAtomService.batchGetDealAttribute(dealIds, allDealAttrNames).thenApply(dealAttrValues -> {
            if (MapUtils.isEmpty(dealAttrValues)) {
                return null;
            }
            return CollectUtils.firstValue(dealAttrValues);
        });
    }

    private CompletableFuture<DealGroupAttributeDTO> getMtDealAttrValues(int dealId) {
        List<Integer> dealIds = Lists.newArrayList(dealId);
        CompletableFuture<List<IdMapper>> idMappersCompletableFuture = compositeAtomService.batchGetDealIdByMtId(dealIds);
        return idMappersCompletableFuture.thenCompose(idMappers -> {
            if (CollectionUtils.isEmpty(idMappers)) {
                return CompletableFuture.completedFuture(null);
            }
            return getMtAttrValuesFuture(idMappers);
        });
    }

    private CompletableFuture<DealGroupAttributeDTO> getMtAttrValuesFuture(List<IdMapper> idMappers) {
        if (CollectionUtils.isEmpty(idMappers)) {
            return CompletableFuture.completedFuture(null);
        }
        List<Integer> dpDealIds = idMappers.stream().map(idMapper -> idMapper.getDpDealGroupID()).collect(Collectors.toList());
        List<String> allDealAttrNames = getDealDetailSceneIdentifierRequiredDealAttrNames();
        CompletableFuture<Map<Integer, DealGroupAttributeDTO>> dealIdAttrDTOMapFuture = compositeAtomService.batchGetDealAttribute(dpDealIds, allDealAttrNames);
        if (dealIdAttrDTOMapFuture == null) {
            return CompletableFuture.completedFuture(null);
        }
        return dealIdAttrDTOMapFuture.thenCompose(dealIdAttrDTOMap -> {
            if (MapUtils.isEmpty(dealIdAttrDTOMap)) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(CollectUtils.firstValue(dealIdAttrDTOMap));
        });
    }

    private CategoryM getMtDealCategory(int dealId) {
        if (dealId == 0) {
            return null;
        }
        CompletableFuture<List<IdMapper>> idMappersCompletableFuture = compositeAtomService.batchGetDealIdByMtId(Lists.newArrayList(dealId));

        return idMappersCompletableFuture.thenCompose(idMappers -> {
            if (CollectionUtils.isEmpty(idMappers)) {
                return CompletableFuture.completedFuture(null);
            }

            List<Integer> dpDealIds = idMappers.stream().map(idMapper -> idMapper.getDpDealGroupID()).collect(Collectors.toList());

            return compositeAtomService.batchGetDealIdCategoryIdMap(dpDealIds).thenCompose(r1 -> {
                Integer categoryId = CollectUtils.firstValue(r1);
                if (!categorylist.contains(categoryId)) {
                    CategoryM categoryM = new CategoryM();
                    categoryM.setCategoryId(categoryId);
                    return CompletableFuture.completedFuture(categoryM);
                }
                return compositeAtomService.batchQueryDealDetailInfo(dpDealIds).thenApply(res -> {
                    CategoryM categoryM = new CategoryM();
                    categoryM.setCategoryId(categoryId);
                    categoryM.setProductCategoryList(getProductCategoryList(res, dpDealIds.get(0)));
                    return categoryM;
                });
            });
        }).join();
    }

    private List<Long> getProductCategoryList(Map<Integer, DealDetailDto> dealDetailDtoMap, Integer dealId) {
        if (dealDetailDtoMap == null) {
            return null;
        }
        List<Long> result = Lists.newArrayList();
        DealDetailDto dealDetailDto = dealDetailDtoMap.get(dealId);
        if (dealDetailDto != null && dealDetailDto.getSkuUniStructuredDto() != null) {
            DealDetailSkuUniStructuredDto skuUniStructuredDto = dealDetailDto.getSkuUniStructuredDto();
            if (CollectionUtils.isNotEmpty(skuUniStructuredDto.getMustGroups())) {
                result.addAll(skuUniStructuredDto.getMustGroups().stream().flatMap(aa -> aa.getSkuItems().stream()).map(SkuItemDto::getProductCategory).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(skuUniStructuredDto.getOptionalGroups())) {
                result.addAll(skuUniStructuredDto.getOptionalGroups().stream().flatMap(aa -> aa.getSkuItems().stream()).map(SkuItemDto::getProductCategory).collect(Collectors.toList()));
            }
        }
        return result;
    }

    private void addPoiInfo(ActivityContext activityContext) {
        // poiMigrate
        if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.DealFilterListActivityCtxBuilder.getScene())) {
            if (hasPoiId(activityContext)) {
                addPoiInfoAsPoiIdPoiMigrate(activityContext);
            } else if (StringUtils.isNotBlank(activityContext.getParam(ProductDetailActivityConstants.Params.shopUuid))) {
                addPoiInfoAsShopUuidPoiMigrate(activityContext);
            }
            if (activityContext.getParam(ProductDetailActivityConstants.Ctx.ctxShop) == null) {
                // 没有获取到商户，则读取最近适用商户
                addPoiInfoAsNearestShopPoiMigrate(activityContext);
            }
        } else {
            // 原逻辑
            if (hasPoiId(activityContext)) {
                addPoiInfoAsPoiId(activityContext);
            } else if (StringUtils.isNotBlank(activityContext.getParam(ProductDetailActivityConstants.Params.shopUuid))) {
                addPoiInfoAsShopUuid(activityContext);
            }
            if (activityContext.getParam(ProductDetailActivityConstants.Ctx.ctxShop) == null) {
                // 没有获取到商户，则读取最近适用商户
                addPoiInfoAsNearestShop(activityContext);
            }
        }
    }

    private void addPoiInfoAsShopUuid(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.addPoiInfoAsShopUuid(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        String shopUuid = activityContext.getParam(ProductDetailActivityConstants.Params.shopUuid);
        compositeAtomService.loadShop(shopUuid).thenAccept(shopDTO -> {
            if (shopDTO == null) return;
            ShopM shopM = convertToShopM(shopDTO);
            if (shopM == null) {
                return;
            }
            activityContext.addParam(ProductDetailActivityConstants.Ctx.ctxShop, shopM);
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiId, shopM.getShopId());
            if (PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform))) {
                addMtShopId(activityContext, shopM.getShopId());
            }
        }).join();
    }

    private void addPoiInfoAsShopUuidPoiMigrate(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.addPoiInfoAsShopUuidPoiMigrate(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        String shopUuid = activityContext.getParam(ProductDetailActivityConstants.Params.shopUuid);
        compositeAtomService.findShopsByUuids(buildDpPoiUuidRequest(Lists.newArrayList(shopUuid))).thenAccept(dpPoiDTOList -> {
            if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                return;
            }
            ShopM shopM = convertToShopM(Collections.first(dpPoiDTOList));
            activityContext.addParam(ProductDetailActivityConstants.Ctx.ctxShop, shopM);
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiId, PoiIdUtil.getShopIdL(shopM).intValue());
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiIdL, PoiIdUtil.getShopIdL(shopM));
            if (PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform))) {
                addMtShopIdPoiMigrate(activityContext, PoiIdUtil.getShopIdL(shopM));
            }
        }).join();
    }

    private DpPoiUuidRequest buildDpPoiUuidRequest(List<String> uuids) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.buildDpPoiUuidRequest(java.util.List)");
        DpPoiUuidRequest dpPoiUuidRequest = new DpPoiUuidRequest();
        dpPoiUuidRequest.setUuids(uuids);
        dpPoiUuidRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiUuidRequest;
    }

    private void addMtShopId(ActivityContext activityContext, Integer dpShopId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.addMtShopId(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.Integer)");
        List<Integer> mtShopIds = compositeAtomService.batchGetMtShopIdByDp(dpShopId).join();
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return;
        }
        activityContext.addParam(ProductDetailActivityConstants.Params.mtPoiId, mtShopIds.get(0));
    }

    private void addMtShopIdPoiMigrate(ActivityContext activityContext, Long dpShopId) {
        List<Long> mtShopIds = compositeAtomService.batchGetMtShopIdByDpL(dpShopId).join();
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return;
        }
        activityContext.addParam(ProductDetailActivityConstants.Params.mtPoiId, mtShopIds.get(0).intValue());
        activityContext.addParam(ProductDetailActivityConstants.Params.mtPoiIdL, mtShopIds.get(0));
    }

    /**
     * 添加最近适用商户
     *
     * @param activityContext
     */
    private void addPoiInfoAsNearestShop(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.addPoiInfoAsNearestShop(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        Double lng = Optional
                .ofNullable((Double) activityContext.getParam(ProductDetailActivityConstants.Params.lng))
                .orElse(0.0);
        Double lat = Optional
                .ofNullable((Double) activityContext.getParam(ProductDetailActivityConstants.Params.lat))
                .orElse(0.0);
        int productId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.productId);
        GetProductNearestShopReq nearestShopReq = new GetProductNearestShopReq();
        nearestShopReq.setLng(lng);
        nearestShopReq.setLat(lat);
        nearestShopReq.setProductIds(Lists.newArrayList(productId));
        compositeAtomService.batchGetProductNearestShop(nearestShopReq).thenAccept(product2ShopIdMap -> {
            if (MapUtils.isEmpty(product2ShopIdMap) || product2ShopIdMap.get(productId) == null) {
                return;
            }
            SimpleShopDTO simpleShopDTO = product2ShopIdMap.get(productId);
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiId, simpleShopDTO.getDpShopId());
            addPoiInfoAsDpShopId(activityContext);
            addMtShopId(activityContext, simpleShopDTO.getDpShopId());
        }).join();
    }

    private void addPoiInfoAsNearestShopPoiMigrate(ActivityContext activityContext) {
        Double lng = Optional
                .ofNullable((Double) activityContext.getParam(ProductDetailActivityConstants.Params.lng))
                .orElse(0.0);
        Double lat = Optional
                .ofNullable((Double) activityContext.getParam(ProductDetailActivityConstants.Params.lat))
                .orElse(0.0);
        int productId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.productId);
        GetProductNearestShopReq nearestShopReq = new GetProductNearestShopReq();
        nearestShopReq.setLng(lng);
        nearestShopReq.setLat(lat);
        nearestShopReq.setProductIds(Lists.newArrayList(productId));
        compositeAtomService.batchGetProductNearestShop(nearestShopReq).thenAccept(product2ShopIdMap -> {
            if (MapUtils.isEmpty(product2ShopIdMap) || product2ShopIdMap.get(productId) == null) {
                return;
            }
            SimpleShopDTO simpleShopDTO = product2ShopIdMap.get(productId);
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiId, PoiIdUtil.getDpShopIdL(simpleShopDTO).intValue());
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiIdL, PoiIdUtil.getDpShopIdL(simpleShopDTO));
            addPoiInfoAsDpShopIdPoiMigrate(activityContext);
            addMtShopIdPoiMigrate(activityContext, PoiIdUtil.getDpShopIdL(simpleShopDTO));
        }).join();
    }

    private void addPoiInfoAsPoiId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.addPoiInfoAsPoiId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform);
        if (!PlatformUtil.isMT(platform)) {
            addPoiInfoAsDpShopId(activityContext);
            return;
        }
        // 美团侧需要转换ShopId为点评
        CompletableFuture<List<Integer>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtId(ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.mtPoiId));
        dpShopIdsFuture.thenAccept(dpShopIds -> {
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds));
            addPoiInfoAsDpShopId(activityContext);
        }).join();
    }

    private void addPoiInfoAsPoiIdPoiMigrate(ActivityContext activityContext) {
        int platform = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform);
        if (!PlatformUtil.isMT(platform)) {
            addPoiInfoAsDpShopIdPoiMigrate(activityContext);
            return;
        }
        // 美团侧需要转换ShopId为点评
        CompletableFuture<List<Long>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtIdL(PoiIdUtil.getMtPoiIdL(activityContext, ProductDetailActivityConstants.Params.mtPoiIdL, ProductDetailActivityConstants.Params.mtPoiId));
        dpShopIdsFuture.thenAccept(dpShopIds -> {
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds).intValue());
            activityContext.addParam(ProductDetailActivityConstants.Params.dpPoiIdL, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds));
            addPoiInfoAsDpShopIdPoiMigrate(activityContext);
        }).join();
    }

    private void addPoiInfoAsDpShopId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.addPoiInfoAsDpShopId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        ShopM shopM = loadShop(ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.dpPoiId));
        activityContext.addParam(ProductDetailActivityConstants.Ctx.ctxShop, shopM);
        if (shopM == null) {
            return;
        }
        activityContext.addParam(ProductDetailActivityConstants.Params.shopUuid, shopM.getShopUuid());
    }

    private void addPoiInfoAsDpShopIdPoiMigrate(ActivityContext activityContext) {
        ShopM shopM = loadShopPoiMigrate(PoiIdUtil.getDpPoiIdL(activityContext, ProductDetailActivityConstants.Params.dpPoiIdL, ProductDetailActivityConstants.Params.dpPoiId));
        activityContext.addParam(ProductDetailActivityConstants.Ctx.ctxShop, shopM);
        if (shopM == null) {
            return;
        }
        activityContext.addParam(ProductDetailActivityConstants.Params.shopUuid, shopM.getShopUuid());
    }

    private boolean hasPoiId(ActivityContext activityContext) {
        return dpHasPoiId(activityContext) || mtHasPoiId(activityContext);
    }

    private boolean mtHasPoiId(ActivityContext activityContext) {
        int platform = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform);
        long mtPoiId = PoiIdUtil.getMtPoiIdL(activityContext, ProductDetailActivityConstants.Params.mtPoiIdL, ProductDetailActivityConstants.Params.mtPoiId);
        return PlatformUtil.isMT(platform) && mtPoiId > 0;
    }

    private boolean dpHasPoiId(ActivityContext activityContext) {
        int platform = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform);
        long dpPoiId = PoiIdUtil.getDpPoiIdL(activityContext, ProductDetailActivityConstants.Params.dpPoiIdL, ProductDetailActivityConstants.Params.dpPoiId);
        return !PlatformUtil.isMT(platform) && dpPoiId > 0;
    }

    private ShopM loadShop(int dpShopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.loadShop(int)");
        return compositeAtomService.loadShop(dpShopId).thenApply(shopDTO -> {
            if (shopDTO == null) {
                return null;
            }
            return convertToShopM(shopDTO);
        }).join();
    }

    private ShopM loadShopPoiMigrate(long dpShopId) {
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(Lists.newArrayList(dpShopId))).thenApply(dpPoiDTOList -> {
            if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                return null;
            }
            return convertToShopM(Collections.first(dpPoiDTOList));
        }).join();
    }

    private DpPoiRequest buildDpPoiRequest(List<Long> dpPoiIds) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }

    private ShopM convertToShopM(ShopDTO shopDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.convertToShopM(com.dianping.shopremote.remote.dto.ShopDTO)");
        ShopM shopM = new ShopM();
        shopM.setShopId(shopDTO.getShopId());
        shopM.setShopName(shopDTO.getShopName());
        shopM.setBranchName(shopDTO.getBranchName());
        shopM.setShopType(shopDTO.getShopType());
        shopM.setCategory(shopDTO.getMainCategoryId());
        shopM.setCategoryName(shopDTO.getMainCategoryName());
        shopM.setLat(Optional.ofNullable(shopDTO.getGlat()).orElse(0d));
        shopM.setLng(Optional.ofNullable(shopDTO.getGlng()).orElse(0d));
        shopM.setCityId(shopDTO.getCityId() == null ? 0 : shopDTO.getCityId());
        shopM.setShopUuid(shopDTO.getShopUuid());
        shopM.setPhoneNo(buildShopPhone(shopDTO));
        shopM.setShopPower(shopDTO.getShopPower() == null ? 0 : shopDTO.getShopPower());
        shopM.setAddress(shopDTO.getAddress());
        shopM.setMainRegionName(shopDTO.getMainRegionName());
        return shopM;
    }

    private ShopM convertToShopM(DpPoiDTO dpPoiDTO) {
        ShopM shopM = new ShopM();
        shopM.setShopId(dpPoiDTO.getShopId().intValue());
        shopM.setLongShopId(dpPoiDTO.getShopId());
        shopM.setShopName(dpPoiDTO.getShopName());
        shopM.setBranchName(dpPoiDTO.getBranchName());
        shopM.setShopType(dpPoiDTO.getShopType());
        shopM.setCategory(dpPoiDTO.getMainCategoryId());
        shopM.setCategoryName(dpPoiDTO.getMainCategoryName());
        shopM.setLat(Optional.ofNullable(dpPoiDTO.getLat()).orElse(0d));
        shopM.setLng(Optional.ofNullable(dpPoiDTO.getLng()).orElse(0d));
        shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
        shopM.setShopUuid(dpPoiDTO.getUuid());
        shopM.setPhoneNo(buildShopPhonePoiMigrate(dpPoiDTO));
        shopM.setShopPower(dpPoiDTO.getShopPower() == null ? 0 : dpPoiDTO.getShopPower().shortValue());
        shopM.setAddress(dpPoiDTO.getAddress());
        shopM.setMainRegionName(dpPoiDTO.getMainRegionName());
        return shopM;
    }

    private String buildShopPhone(ShopDTO shopDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityContextBuilder.buildShopPhone(com.dianping.shopremote.remote.dto.ShopDTO)");
        if (CollectionUtils.isEmpty(shopDTO.getNormPhones())) {
            return shopDTO.getPhoneNo();
        }
        NormPhone normPhone = shopDTO.getNormPhones().get(0);
        StringBuffer result = new StringBuffer();
        if (StringUtils.isNotEmpty(normPhone.getAreaCode())) {
            result.append(normPhone.getAreaCode()).append("-");
        }
        result.append(normPhone.getEntity());
        if (StringUtils.isNotEmpty(normPhone.getBranch())) {
            result.append(",,").append(normPhone.getBranch());
        }
        return result.toString();
    }

    private String buildShopPhonePoiMigrate(DpPoiDTO dpPoiDTO) {
        if (CollectionUtils.isEmpty(dpPoiDTO.getNormPhones())) {
            // TODO 新接口把这个字段给删掉了
//            return dpPoiDTO.getPhoneNo();
            return null;
        }
        com.sankuai.sinai.data.api.dto.NormPhone normPhone = dpPoiDTO.getNormPhones().get(0);
        StringBuffer result = new StringBuffer();
        if (StringUtils.isNotEmpty(normPhone.getAreaCode())) {
            result.append(normPhone.getAreaCode()).append("-");
        }
        result.append(normPhone.getEntity());
        if (StringUtils.isNotEmpty(normPhone.getBranch())) {
            result.append(",,").append(normPhone.getBranch());
        }
        return result.toString();
    }

}
