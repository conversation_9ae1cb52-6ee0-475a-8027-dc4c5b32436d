package com.sankuai.dzviewscene.shelf.platform.shelf;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheCompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheGrayUtils;
import com.sankuai.dzviewscene.nr.atom.cache.CacheMethodEnum;
import com.sankuai.dzviewscene.product.shelf.utils.CommonProductTagUtils;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.product.utils.CtxUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextBuilder;
import com.sankuai.dzviewscene.shelf.framework.core.IContextBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopCacheM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.utils.ContextHashMap;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import com.sankuai.swan.udqs.api.SwanParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.ap.internal.util.Collections;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by float.lu on 2020/8/31.
 */
@ContextBuilder(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, name = "货架上下文构造器")
public class ShelfActivityContextBuilder implements IContextBuilder {

    public static final ExecutorService convertIdExecutor = Rhino.newThreadPool("shelfConvertIdExecutor",
            DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(50).withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())).getExecutor();

    // 商户后台类目
    private static final String BACK_CAT_FIELD = "backMainCategoryPath";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource
    private CacheCompositeAtomService cacheCompositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.brandkey.query.categoryid", defaultValue = "[]")
    private List<Integer> brandKeyQueryCategory;
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.zdctag.check.config", defaultValue = "[]")
    private Map<String, Long> category2ZdcTag;

    //参与“春节不打烊”且当天营业ZDC标签
    private static final long ZDC_SPRING_FESTIVAL_NOT_CLOSE = 16160;

    @Override
    public ActivityContext build(ActivityRequest activityRequest) {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(buildContextParameter(activityRequest));
        addPoiPlatformParam(activityContext);
        addDouHuRequest(activityContext);
        addRecommendFlowIdFuture(activityContext);
        addBrandKeyWordQueryFuture(activityContext);
        addZdcTagMatchContext(activityContext);
        addSpringFestivalNoClose(activityContext);
        activityContext.addParam(CtxUtils.SHELF_DEFAULT_CONVERT,
                CompletableFuture.supplyAsync(() -> CtxUtils.addCityIdAndUserIdForMagicMember(activityContext.getParameters()), convertIdExecutor));
        return activityContext;
    }

    private ContextHashMap<String, Object> buildContextParameter(ActivityRequest activityRequest) {
        ContextHashMap<String, Object> parameters = new ContextHashMap<>(64);
        if (MapUtils.isNotEmpty(activityRequest.getParams())) {
            activityRequest.getParams().forEach((k, v) -> parameters.put(k, v));
        }
        return parameters;
    }

    private void addDouHuRequest(ActivityContext ctx) {
        DouHuRequest douHuRequest = new DouHuRequest();
        String deviceId = ctx.getParam(ShelfActivityConstants.Params.deviceId);
        douHuRequest.setUnionId(ctx.getParam(ShelfActivityConstants.Params.unionId));
        if (PlatformUtil.isMT(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform))) {
            douHuRequest.setUuid(deviceId);
        } else {
            douHuRequest.setDpid(deviceId);
        }
        ctx.addParam(ShelfActivityConstants.Params.douHuRequest, douHuRequest);
    }

    private void addPoiPlatformParam(ActivityContext activityContext) {
        ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM != null) {
            return;
        }
        //货架请求上下文已填充ctxShop及相关参数，兜底下其他未填充的场景，比如rpc调用
        Cat.logEvent("RpcMonitor", "ShelfActivityContextBuilder.addPlatformParamPoiMigrate");
        addPlatformParamPoiMigrate(activityContext);
        LogUtils.addDiffShopIdLog(activityContext);
    }

    private void addPlatformParamPoiMigrate(ActivityContext activityContext) {
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform))) {
            long dpPoiId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, (int) dpPoiId);
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, dpPoiId);
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShopModel(dpPoiId));
            return;
        }
        // 美团平台
        long mtPoiId = PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        int mtCityId = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtCityId);

        CompletableFuture<Long> dpShopIdFuture = getDpPoiIdByMt(mtPoiId);
        CompletableFuture<Integer> dpCityIdFuture = getDpCityIdByMt(mtPoiId, mtCityId);

        CompletableFuture.allOf(dpShopIdFuture, dpCityIdFuture).thenAccept(Void -> {
            Long dpShopId = dpShopIdFuture.join();
            Integer dpCityId = dpCityIdFuture.join();
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, dpShopId == null ? 0 : dpShopId.intValue());
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, dpShopId == null ? 0 : dpShopId);
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShopModel(dpShopId));
        }).join();
    }

    private CompletableFuture<Long> getDpPoiIdByMt(long mtShopId) {
        boolean cacheSwitch = CacheGrayUtils.graySwitch(mtShopId, VCPlatformEnum.MT.getType(), CacheMethodEnum.SHOP_ID_MT_2_DP.getCode());
        if (cacheSwitch) {
            return cacheCompositeAtomService.getDpByMtPoiIdL(mtShopId);
        } else {
            return compositeAtomService.getDpByMtPoiIdL(mtShopId);
        }
    }

    private CompletableFuture<Integer> getDpCityIdByMt(long mtShopId, int mtCiyId) {
        boolean cacheSwitch = CacheGrayUtils.graySwitch(mtShopId, VCPlatformEnum.MT.getType(), CacheMethodEnum.CITY_ID_MT_2_DP.getCode());
        if (cacheSwitch) {
            return cacheCompositeAtomService.getDpCityIdByMt(mtCiyId);
        } else {
            return compositeAtomService.getDpCityIdByMt(mtCiyId);
        }
    }

    private ShopM loadShopModel(Long dpShopId) {
        if (dpShopId == null || dpShopId == 0) {
            return null;
        }
        boolean cacheSwitch = CacheGrayUtils.graySwitch(dpShopId, VCPlatformEnum.DP.getType(), CacheMethodEnum.SHOP_INFO_DP.getCode());
        if (cacheSwitch) {
            return cacheCompositeAtomService.findShopsByDpShopId(buildDpPoiRequest(Lists.newArrayList(dpShopId))).thenApply(this::buildShopM).join();
        }
        return loadShopPoiMigrate(dpShopId);
    }

    private ShopM buildShopM(ShopCacheM shopCacheM) {
        if (shopCacheM == null) {
            return null;
        }
        ShopM shopM = new ShopM();
        shopM.setShopId((int) shopCacheM.getShopId());
        shopM.setLongShopId(shopCacheM.getShopId());
        shopM.setShopUuid(shopCacheM.getShopUuid());
        shopM.setShopGroupId(shopCacheM.getShopGroupId());
        shopM.setShopType(shopCacheM.getShopType());
        shopM.setCategory(shopCacheM.getCategory());
        shopM.setBackCategory(shopCacheM.getBackCategory());
        shopM.setUseType(shopCacheM.getUseType());
        shopM.setShopName(shopCacheM.getShopName());
        shopM.setLat(shopCacheM.getLat());
        shopM.setLng(shopCacheM.getLng());
        shopM.setCityId(shopCacheM.getCityId());
        return shopM;
    }

    private ShopM loadShopPoiMigrate(long dpShopId) {
        //shop新增字段请在request上下文构造里添加
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(Lists.newArrayList(dpShopId))).thenApply(dpPoiDTOList -> {
            if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                return null;
            }
            DpPoiDTO dpPoiDTO = Collections.first(dpPoiDTOList);
            ShopM shopM = new ShopM();
            shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId().intValue());
            shopM.setLongShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
            shopM.setShopGroupId(dpPoiDTO.getShopGroupId() == null ? 0 : dpPoiDTO.getShopGroupId());
            shopM.setShopUuid(dpPoiDTO.getUuid() == null ? "" : dpPoiDTO.getUuid());
            shopM.setShopName(dpPoiDTO.getShopName());
            shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
            shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
            shopM.setBackCategory(ModelUtils.extractBackCat(dpPoiDTO));
            shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
            shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
            shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
            return shopM;
        }).join();
    }

    private DpPoiRequest buildDpPoiRequest(List<Long> dpPoiIds) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        dpPoiRequest.getFields().add(BACK_CAT_FIELD);
        return dpPoiRequest;
    }

    private void addRecommendFlowIdFuture(ActivityContext activityContext) {
        //一次性验证开关，上线验证1周后去除
        if (!Lion.getBooleanValue("com.sankuai.dzviewscene.dealshelf.recommend.flowid.switch", false)) {
            return;
        }
        RecommendParameters recommendParameters = buildRecommendRequest(activityContext);
        CompletableFuture<String> flowIdFuture = compositeAtomService.getRecommendFlowId(recommendParameters);
        activityContext.addParam(ShelfActivityConstants.Ctx.ctxRecommendFlowId, flowIdFuture);
    }

    private void addBrandKeyWordQueryFuture(ActivityContext activityContext) {
        String keyWord = activityContext.getParam(ShelfActivityConstants.Params.searchKeyword);
        ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (StringUtils.isEmpty(keyWord) || shopM == null) {
            return;
        }
        if (CollectionUtils.isEmpty(brandKeyQueryCategory)
                || !(brandKeyQueryCategory.contains(shopM.getCategory()) || brandKeyQueryCategory.contains(shopM.getShopType()))) {
            return;
        }
        //判断搜索词是否品牌词
        CompletableFuture<Boolean> brandKeyWordFuture = compositeAtomService.getSwanDataByQueryKey(1042,
                        "realtime_search_dz_brand_querykey", buildSwanParam(keyWord, shopM))
                .thenApply(result -> {
                    if (Objects.isNull(result) || !result.isIfSuccess() || result.getData() == null) {
                        return false;
                    }
                    return CollectionUtils.isNotEmpty(result.getData().getResultSet());
                });
        activityContext.addParam(ShelfActivityConstants.Params.isBrandKeyWord, brandKeyWordFuture);
    }

    private void addZdcTagMatchContext(ActivityContext activityContext) {
        ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM == null || MapUtils.isEmpty(category2ZdcTag) || !(category2ZdcTag.containsKey(String.valueOf(shopM.getCategory())))) {
            return;
        }
        Boolean checkRes = compositeAtomService.checkZdcTagByDpShopId(shopM.getLongShopId(), category2ZdcTag.get(String.valueOf(shopM.getCategory()))).join();
        activityContext.addParam(ShelfActivityConstants.Params.matchZdcTag, BooleanUtils.isTrue(checkRes));
    }

    private void addSpringFestivalNoClose(ActivityContext activityContext) {
        ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM == null) {
            return;
        }
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        boolean inWhite = CommonProductTagUtils.checkSpringUnavailable(shopM.getLongShopId(), platform, null);
        if (inWhite || StringUtils.equals(activityContext.getSceneCode(), CommonProductTagUtils.SHOPPING_MALL_DEAL_SHELF)) {
            //不在时间范围内或在白名单内
            activityContext.addParam(ShelfActivityConstants.Params.springFestivalNoClose, false);
            return;
        }
        Boolean checkRes = compositeAtomService.checkZdcTagByDpShopId(shopM.getLongShopId(), ZDC_SPRING_FESTIVAL_NOT_CLOSE).join();
        activityContext.addParam(ShelfActivityConstants.Params.springFestivalNoClose, BooleanUtils.isTrue(checkRes));
    }

    private SwanParam buildSwanParam(String keyWord, ShopM shopM) {
        List<Map<String, Object>> params = Lists.newArrayList();
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("keyword", keyWord);
        paramMap.put("dpCat0Id", shopM.getShopType());
        params.add(paramMap);

        SwanParam swanParam = new SwanParam();
        swanParam.setRequestParams(params);
        return swanParam;
    }

    private RecommendParameters buildRecommendRequest(ActivityContext activityContext) {
        int platform = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        String deviceId = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.deviceId);
        int cityId = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId);
        String userId = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.dpUserId);
        String shopId = PoiIdUtil.getDpPoiIdStr(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        boolean isMT = PlatformUtil.isMT(platform);
        if (isMT) {
            cityId = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtCityId);
            userId = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.mtUserId);
            shopId = PoiIdUtil.getMtPoiIdStr(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        double lat = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lat);
        double lng = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lng);
        RecommendParameters recommendParameters = new RecommendParameters();
        //商品推荐/团购货架 （biz_id:464）
        recommendParameters.setBizId(464);
        recommendParameters.setPlatformEnum(PlatformUtil.isMT(platform) ? PlatformEnum.MT : PlatformEnum.DP);
        recommendParameters.setLat(lat);
        recommendParameters.setLng(lng);
        recommendParameters.setOriginUserId(userId);
        recommendParameters.setCityId(cityId);
        recommendParameters.setPageNumber(1);
        recommendParameters.setPageSize(100);
        if (isMT) {
            recommendParameters.setUuid(deviceId);
        } else {
            recommendParameters.setDpid(deviceId);
        }

        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("shopId", shopId);
        recommendParameters.setBizParams(bizParams);
        return recommendParameters;
    }
}
