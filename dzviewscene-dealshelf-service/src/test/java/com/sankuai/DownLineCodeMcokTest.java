package com.sankuai;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dztheme.deal.req.DealThemePlanRequest;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.*;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.*;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemtitle.LeHomeMallShelfTitleOpt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.utils.ItemPriceTagUtil;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Lists;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DownLineCodeMcokTest {

    @Test
    public void testShowIdlePromo() {
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoType(PromoTypeEnum.IDLE_PROMO.getType());
        promoPriceM.setAvailableTime("111111");
        promoPriceM.setDiscount(new BigDecimal("0.7"));
        productM.setPromoPrices(Lists.newArrayList(promoPriceM));
        boolean show = ItemPriceTagUtil.isShowIdlePromo(productM);
        promoPriceM.setAvailableTime(null);
        boolean notShow = ItemPriceTagUtil.isShowIdlePromo(productM);
        Assert.isTrue(show);
        Assert.isTrue(!notShow);
    }

    @Test
    public void test(){
        String str = "{\"extParams\":{\"bindExhibitBizType\":0,\"pricePageSource\":7,\"appVersion\":\"12.30.400\",\"cityId\":10,\"deviceId\":\"00000000000006A9190D68FF34723B907E91AB1662C1CA163769520333300208\",\"pageName\":\"DealShelf\",\"mtUserId\":5004221453,\"scene\":400201,\"themeReqSource\":1,\"priceSecretInfo\":\"\",\"mtShopIdForLong\":607001018,\"clientType\":200,\"riskParam\":\"\",\"dealId2ShopId\":{1022448496:607001018,418264301:607001018,417432228:607001018,1022040226:607001018,426386854:607001018,1026899622:607001018,1021028808:607001018,1021234763:607001018,1028663823:607001018,1028115416:607001018,422092552:607001018,1022122957:607001018,418181320:607001018,1021032572:607001018,419690859:607001018,1028172130:607001018,1026891992:607001018,419990246:607001018,418414814:607001018,418116703:607001018,418188501:607001018,418688141:607001018,1038270681:607001018,417447708:607001018,1025667740:607001018,417449694:607001018,418689163:607001018},\"expSks\":[\"EXP2024121200001_j\"],\"displayControlSceneType\":0,\"shopId\":607001018,\"keyword\":\"\",\"pricePosition\":\"2103\",\"lat\":31.2717031,\"localCityId\":10,\"unionId\":\"6a9190d68ff34723b907e91ab1662c1ca163769520333300208\",\"lng\":121.5293995,\"activityQueryScene\":\"2\",\"attributeKeys\":[\"pedicure_core_regions\",\"project_type_massage\",\"project_type_mining_ear\",\"project_type_chinese_medicine\",\"project_type_foot\",\"project_type_women\",\"project_type_recovery\",\"project_type_posture_correct\",\"preSaleTag\",\"service_type\",\"calcName\",\"standardNameDealGroup\",\"topPerformingProduct\",\"single_verification_quantity_desc\",\"service_type_leaf_id\",\"unclassifiedTools\",\"disposableMaterial\"],\"dpShopIdForLong\":607001018,\"shelfNavTagId\":200120548,\"dealId2SkuIdList\":{1022448496:[455974646],418264301:[444386921],417432228:[442982505],1022040226:[455790335],426386854:[452937859],1021028808:[455272024],1026899622:[458013990],1021234763:[455357057],1028115416:[458527845],1028663823:[458769025],422092552:[448985100],1022122957:[455819468],418181320:[444273937],1021032572:[455272936],419690859:[446557457],1028172130:[458552105],1026891992:[458009800],418414814:[444614908],419990246:[446905387],418116703:[444163794],418188501:[444278480],418688141:[445190461],1038270681:[463207957],417447708:[443003581],1025667740:[457463966],418689163:[445191373],417449694:[443009122]},\"mtLocalCityId\":10,\"querySceneStrategy\":3,\"dpUserId\":0,\"platform\":2,\"shopCategory\":141,\"coordType\":\"GCJ02\",\"bpDealGroupTypes\":[8,7,14],\"dealShelfStyleType\":2,\"osType\":2,\"directPromoSceneCode\":400200,\"shopMtCityId\":10,\"shopIdForLong\":607001018,\"dealId2SkuId\":{},\"dpShopId\":607001018,\"promoDetailTemplate\":\"newVersion\",\"mtCityId\":10,\"userId\":5004221453,\"shopDpCityId\":1,\"dealAttributeKeys\":[\"sys_multi_sale_number\"],\"spuSceneTypes\":[20],\"hasProductProbeAttr\":true,\"priceDescType\":4,\"enablePreSalePromoTag\":true,\"mtShopId\":607001018,\"dealId2ShopIdForLong\":{1022448496:607001018,418264301:607001018,417432228:607001018,1022040226:607001018,426386854:607001018,1026899622:607001018,1021028808:607001018,1021234763:607001018,1028663823:607001018,1028115416:607001018,422092552:607001018,1022122957:607001018,418181320:607001018,1021032572:607001018,419690859:607001018,1028172130:607001018,1026891992:607001018,419990246:607001018,418414814:607001018,418116703:607001018,418188501:607001018,418688141:607001018,1038270681:607001018,417447708:607001018,1025667740:607001018,417449694:607001018,418689163:607001018},\"dealIds\":[1022448496,422092552,418116703,418264301,417432228,417449694,1028663823,417447708,418188501,1022040226,1021234763,1038270681,1028115416,1025667740,426386854,419690859,1028172130,1026899622,1026891992,1022122957,1021032572,1021028808,419990246,418689163,418688141,418414814,418181320],\"mtVirtualUserId\":0},\"planId\":\"10002274\",\"productIds\":[1022448496,422092552,418116703,418264301,417432228,417449694,1028663823,417447708,418188501,1022040226,1021234763,1038270681,1028115416,1025667740,426386854,419690859,1028172130,1026899622,1026891992,1022122957,1021032572,1021028808,419990246,418689163,418688141,418414814,418181320]}\n";
        DealThemePlanRequest request = JSONObject.parseObject(str, DealThemePlanRequest.class);
        String s = JacksonUtils.serialize(request);
        System.out.println(s);
    }
}
