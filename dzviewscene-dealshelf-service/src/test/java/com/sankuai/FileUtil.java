package com.sankuai;

import com.dianping.cat.Cat;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class FileUtil {

    public static String file2str(String inputFile) {
        try {
            URL resource = Thread.currentThread().getContextClassLoader().getResource(inputFile);
            if (resource == null) {
                throw new RuntimeException("null resource");
            }
            FileInputStream outputStream = new FileInputStream(resource.getFile());
            BufferedReader in = new BufferedReader(new InputStreamReader(outputStream, StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            String str;
            while ((str = in.readLine()) != null) {
                sb.append(str);
            }
            in.close();
            return sb.toString();
        } catch (Exception e) {
            Cat.logError(e);
            return null;
        }
    }
}
