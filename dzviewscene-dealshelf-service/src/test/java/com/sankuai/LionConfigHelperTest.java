package com.sankuai;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@Ignore("没有可执行的方法")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppContextConfiguration.class)
public class LionConfigHelperTest {
//    @Test
    public void isGrayCodeDownLineTest(){
        ActivityCxt activityCxt = new ActivityCxt();
        boolean test = LionConfigHelper.isGrayCodeDownLine(activityCxt, "test");
        Assert.assertTrue(test);
    }

}
