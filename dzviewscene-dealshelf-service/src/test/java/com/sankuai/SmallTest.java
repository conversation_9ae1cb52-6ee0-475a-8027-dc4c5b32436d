package com.sankuai;

import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityRequest;
import com.dianping.gmkt.activity.api.dto.Version;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.dianping.vc.enums.VCClientTypeEnum;
import org.assertj.core.util.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

/**
 * created by zhangzhiyuan04 in 2021/6/19
 */
@Ignore("没有可执行的方法")
public class SmallTest {

    //@Test
    @DisplayName("工具：查团单活动")
    public void test() {
        BatchQueryDealActivityRequest batchQueryDealActivityRequest = new BatchQueryDealActivityRequest();
        batchQueryDealActivityRequest.setVersion(new Version("10.46.12"));
        batchQueryDealActivityRequest.setUserIdL(0L);
        batchQueryDealActivityRequest.setSource(RequestSource.PoiDealList);
        batchQueryDealActivityRequest.setAppPlatform(AppPlatform.DP);
        batchQueryDealActivityRequest.setDpCity(16);
        batchQueryDealActivityRequest.setDpDealIds(Lists.newArrayList(689927198));
        String serialize = JacksonUtils.serialize(batchQueryDealActivityRequest);
        System.out.println(1);
    }

    //@Test
    @DisplayName("工具：查商户活动")
    public void testShopActivity() {
        BatchQueryShopActivityRequest batchQueryShopActivityRequest = new BatchQueryShopActivityRequest();
        batchQueryShopActivityRequest.setUserIdL(0L);
        batchQueryShopActivityRequest.setSource(RequestSource.PoiDetailShelves_Tab);
        batchQueryShopActivityRequest.setCategory(141);
        batchQueryShopActivityRequest.setAppPlatform(AppPlatform.DP);
        batchQueryShopActivityRequest.setDpShopIds(com.google.common.collect.Lists.newArrayList(1626594091));
        batchQueryShopActivityRequest.setDpCity(16);
        String serialize = JacksonUtils.serialize(batchQueryShopActivityRequest);
        System.out.println(1);
    }

    //@Test
    public void testFormat() {
        String title = "男士SpA精油";
        String keyword = "spA";
        String WITH_DELIMITER = "((?i)(?<=%1$s)|(?=%1$s))";
        String[] splitTitles = title.split(String.format(WITH_DELIMITER, keyword));
        System.out.println(splitTitles);
    }
}
