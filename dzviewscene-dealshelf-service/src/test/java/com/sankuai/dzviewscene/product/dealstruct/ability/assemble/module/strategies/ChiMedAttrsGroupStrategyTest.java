package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ChiMedAttrsGroupStrategyTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assambleParam;

    private ChiMedAddAttrsGroupStrategy chiMedAddAttrsGroupStrategy = new ChiMedAddAttrsGroupStrategy();

    @Test
    public void testBuildModelVOWhenMainAttrMapIsEmpty() throws Throwable {
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setStructAttrModels(Collections.singletonList(new StructAttrItemModel()));
        DealDetailStructAttrModel model = new DealDetailStructAttrModel();
        model.setStructAttrsModels(Collections.singletonList(structAttrsModel));

        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("Applicable_indications");
        attrM.setValue("value");
        dealAttrs.add(attrM);
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealAttrs(dealAttrs);
        List<DealDetailInfoModel> dealDetailInfoModels = new ArrayList<>();
        dealDetailInfoModels.add(dealDetailInfoModel);

        when(activityCxt.getSource(anyString())).thenReturn(dealDetailInfoModels);

        DealDetailModuleVO result = chiMedAddAttrsGroupStrategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNotNull(result);
    }
}
