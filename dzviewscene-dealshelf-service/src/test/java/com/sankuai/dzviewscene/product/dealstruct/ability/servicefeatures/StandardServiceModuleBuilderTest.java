package com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StandardServiceModuleBuilderTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assembleParam;

    @Test
    public void testBuildModelVOWhenNoStandardServiceList() throws Throwable {
        // arrange
        StandardServiceModuleBuilder builder = new StandardServiceModuleBuilder();
        when(activityCxt.getSource(StandardServiceModuleBuilder.CODE)).thenReturn(null);
        // act
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildModelVOWhenStandardServiceListIsEmpty() throws Throwable {
        // arrange
        StandardServiceModuleBuilder builder = new StandardServiceModuleBuilder();
        when(activityCxt.getSource(StandardServiceModuleBuilder.CODE)).thenReturn(Collections.emptyList());
        // act
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildModelVOWhenStandardServiceListIsNotEmpty() throws Throwable {
        // arrange
        StandardServiceModuleBuilder builder = new StandardServiceModuleBuilder();
        StandardServiceVO standardServiceVO = new StandardServiceVO();
        List<StandardServiceVO> standardServiceList = Collections.singletonList(standardServiceVO);
        when(activityCxt.getSource(StandardServiceModuleBuilder.CODE)).thenReturn(standardServiceList);
        // act
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNotNull(result);
    }
}
