package com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleParam;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.when;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class StandardServiceModuleBuilder_BuildTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailVideoModuleParam dealDetailVideoModuleParam;

    @Mock
    private DealDetailVideoModuleCfg dealDetailVideoModuleCfg;

    @Test
    public void testBuildWhenDealDetailInfoModelListIsEmpty() throws Throwable {
        // arrange
        StandardServiceModuleBuilder builder = new StandardServiceModuleBuilder();
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        // act
        CompletableFuture<List<StandardServiceVO>> result = builder.build(activityCxt, dealDetailVideoModuleParam, dealDetailVideoModuleCfg);
        // assert
        assertNull(result.get());
    }

    @Test
    public void testBuildWhenDealDetailInfoModelIsNull() throws Throwable {
        // arrange
        StandardServiceModuleBuilder builder = new StandardServiceModuleBuilder();
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Arrays.asList(null, null));
        // act
        CompletableFuture<List<StandardServiceVO>> result = builder.build(activityCxt, dealDetailVideoModuleParam, dealDetailVideoModuleCfg);
        // assert
        assertNull(result.get().get(0));
        assertNull(result.get().get(1));
    }
}
