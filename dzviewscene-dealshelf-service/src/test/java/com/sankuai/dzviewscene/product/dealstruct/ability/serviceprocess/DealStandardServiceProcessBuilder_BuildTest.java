package com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceProcessVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess.vcpoints.DealStandardServiceProcessVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStandardServiceProcessBuilder_BuildTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private DealStandardServiceProcessParam mockDealStandardServiceProcessParam;

    @Mock
    private DealStandardServiceProcessCfg mockDealStandardServiceProcessCfg;

    @Mock
    private DealStandardServiceProcessVP mockDealStandardServiceProcessVP;

    private DealStandardServiceProcessBuilder builder;

    @Before
    public void setUp() {
        builder = new DealStandardServiceProcessBuilder();
        // Corrected the setup by removing the incorrect stubbing of a non-existent method.
        // Instead, we should prepare the mockActivityCxt to return expected values for methods that exist and are used by the code under test.
    }

    @Test
    public void testBuildWhenDealDetailInfoModelsIsEmpty() throws Throwable {
        when(mockActivityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        CompletableFuture<List<StandardServiceProcessVO>> result = builder.build(mockActivityCxt, mockDealStandardServiceProcessParam, mockDealStandardServiceProcessCfg);
        assertNotNull(result);
        assertTrue(result.isDone());
        assertNull(result.get());
    }
}
