package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaiJiProjectsModuleBuilder_BuildTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private TaijiSkuListsParam taijiSkuListsParam;

    @Mock
    private TaiJiListsCfg taiJiListsCfg;

    @Mock
    private DealDetailAssembleParam assembleParam;

    private TaiJiProjectsModuleBuilder taiJiProjectsModuleBuilder;

    @Before
    public void setUp() {
        taiJiProjectsModuleBuilder = new TaiJiProjectsModuleBuilder();
    }

    // Helper method to create a valid DealDetailInfoModel
    private DealDetailInfoModel createValidDealDetailInfoModel() {
        DealDetailInfoModel model = new DealDetailInfoModel();
        DealDetailDtoModel dtoModel = new DealDetailDtoModel();
        dtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        model.setDealDetailDtoModel(dtoModel);
        return model;
    }

    @Test
    public void testBuildWhenDealDetailInfoModelsIsEmpty() throws Throwable {
        TaiJiProjectsModuleBuilder builder = new TaiJiProjectsModuleBuilder();
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, taijiSkuListsParam, taiJiListsCfg);
        assertEquals(0, result.get().size());
    }

    @Test
    public void testBuildWhenAllDetailModelsAreInvalid() throws Throwable {
        TaiJiProjectsModuleBuilder builder = new TaiJiProjectsModuleBuilder();
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(new DealDetailInfoModel()));
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, taijiSkuListsParam, taiJiListsCfg);
        assertEquals(0, result.get().size());
    }

//    @Test
//    public void testBuildForSetDealSkuGroupModuleVOTitle() throws Throwable {
//        TaiJiProjectsModuleBuilder builder = new TaiJiProjectsModuleBuilder();
//        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
//        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
//        StandardServiceProjectGroupDTO mustGroups = new StandardServiceProjectGroupDTO();
//        StandardServiceProjectGroupDTO optionalGroups = new StandardServiceProjectGroupDTO();
//        mustGroups.setServiceProjectItems(Arrays.asList(new StandardServiceProjectItemDTO()));
//        mustGroups.setOptionalCount(0);
//        optionalGroups.setServiceProjectItems(Arrays.asList(new StandardServiceProjectItemDTO(), new StandardServiceProjectItemDTO()));
//        mustGroups.setOptionalCount(1);
//
//        standardServiceProjectDTO.setMustGroups(Arrays.asList(mustGroups));
//        standardServiceProjectDTO.setOptionalGroups(Arrays.asList(optionalGroups));
//        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
//        dealDetailInfoModel.setDealDetailDtoModel(new DealDetailDtoModel());
//        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));
//        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, taijiSkuListsParam, taiJiListsCfg);
//        assertEquals(0, result.get().size());
//
//    }

    @Test
    public void testBuildModelVODealSkuGroupModuleVoListsIsNull() throws Throwable {
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(null);
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVODealSkuGroupModuleVOSIsNull() throws Throwable {
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(Collections.emptyList());
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVODealDetailInfoModelsIsNull() throws Throwable {
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(null);
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
    }

    @Test
    public void testBuildModelVODealDetailInfoModelIsNull() throws Throwable {
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
    }

    @Test
    public void testBuildModelVODealDetailInfoModelIsNotTimesDeal() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();

        List<StandardServiceProjectGroupDTO> optionalGroups = new ArrayList<>();
        StandardServiceProjectGroupDTO optionalGroup = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> serviceProjectItems = new ArrayList<>();
        serviceProjectItems.add(new StandardServiceProjectItemDTO());
        optionalGroup.setServiceProjectItems(serviceProjectItems);
        optionalGroup.setOptionalCount(1);
        optionalGroups.add(optionalGroup);

        standardServiceProjectDTO.setOptionalGroups(optionalGroups);
        standardServiceProjectDTO.setMustGroups(null);

        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
        assertNull(result.getName());
    }

    @Test
    public void testBuildModelVODealDetailInfoModelIsNotTimesDeal1() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();

        List<StandardServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        StandardServiceProjectGroupDTO mustGroup = new StandardServiceProjectGroupDTO();

        List<StandardServiceProjectItemDTO> serviceProjectItems = new ArrayList<>();
        serviceProjectItems.add(new StandardServiceProjectItemDTO());

        mustGroup.setServiceProjectItems(serviceProjectItems);
        mustGroup.setOptionalCount(1);
        mustGroups.add(mustGroup);

        standardServiceProjectDTO.setMustGroups(mustGroups);

        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
        assertNull(result.getName());
    }

    @Test
    public void testBuildModelVODealDetailInfoModelIsNotTimesDeal2() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();

        List<StandardServiceProjectGroupDTO> optionalGroups = new ArrayList<>();
        StandardServiceProjectGroupDTO optionalGroup = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> serviceProjectItems = new ArrayList<>();
        serviceProjectItems.add(new StandardServiceProjectItemDTO());
        optionalGroup.setServiceProjectItems(serviceProjectItems);
        optionalGroup.setOptionalCount(2);
        optionalGroups.add(optionalGroup);

        standardServiceProjectDTO.setOptionalGroups(optionalGroups);
        standardServiceProjectDTO.setMustGroups(null);

        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
        assertNull(result.getName());
    }

    @Test
    public void testBuildModelVODealDetailInfoModelTitleIsNotNull() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();

        List<StandardServiceProjectGroupDTO> optionalGroups = new ArrayList<>();
        StandardServiceProjectGroupDTO optionalGroup = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> serviceProjectItems = new ArrayList<>();
        serviceProjectItems.add(new StandardServiceProjectItemDTO());
        optionalGroup.setServiceProjectItems(serviceProjectItems);
        optionalGroup.setOptionalCount(2);
        optionalGroups.add(optionalGroup);

        standardServiceProjectDTO.setOptionalGroups(optionalGroups);
        standardServiceProjectDTO.setMustGroups(null);

        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        List<List<DealSkuGroupModuleVO>> dealSkuGroupModuleVoLists = Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO()));
        dealSkuGroupModuleVoLists.get(0).get(0).setTitle("test");
        when(activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE)).thenReturn(dealSkuGroupModuleVoLists);
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));
        DealDetailModuleVO result = taiJiProjectsModuleBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
        assertNull(result.getName());
    }

    @Test
    public void testSetDealSkuGroupModuleVOTitleNull() throws Throwable {
        // 获取类的Class对象
        Class<?> clazz = TaiJiProjectsModuleBuilder.class;

        // 获取buildDealSkuSequenceModel方法
        Method method = clazz.getDeclaredMethod("setDealSkuGroupModuleVOTitle",
                DealSkuGroupModuleVO.class,
                boolean.class,
                int.class,
                int.class,
                boolean.class);

        // 设置方法为可访问
        method.setAccessible(true);
        // 准备参数
        ActivityCxt activityCxt = new ActivityCxt(); // 示例参数
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        boolean isMustGroup = true;
        int optionalNum = 1;
        int optionalSize = 2;
        boolean supportMustAndOptionSwitch = false;
        // 调用方法
        Object result = method.invoke(taiJiProjectsModuleBuilder, dealSkuGroupModuleVO, isMustGroup, optionalNum, optionalSize,supportMustAndOptionSwitch);
        assertNull(dealSkuGroupModuleVO.getTitle());
    }

    @Test
    public void testSetDealSkuGroupModuleVOTitle() throws Throwable {
        // 获取类的Class对象
        Class<?> clazz = TaiJiProjectsModuleBuilder.class;

        // 获取buildDealSkuSequenceModel方法
        Method method = clazz.getDeclaredMethod("setDealSkuGroupModuleVOTitle",
                DealSkuGroupModuleVO.class,
                boolean.class,
                int.class,
                int.class,
                boolean.class);

        // 设置方法为可访问
        method.setAccessible(true);

        // 准备参数
        ActivityCxt activityCxt = new ActivityCxt(); // 示例参数
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        boolean isMustGroup = true;
        int optionalNum = 1;
        int optionalSize = 2;
        boolean supportMustAndOptionSwitch = true;
        // 调用方法
        Object result = method.invoke(taiJiProjectsModuleBuilder, dealSkuGroupModuleVO, isMustGroup, optionalNum, optionalSize,supportMustAndOptionSwitch);
        assertNotNull(dealSkuGroupModuleVO.getTitle());
    }

    @Test
    public void testSetDealSkuGroupModuleVOTitleOption() throws Throwable {
        // 获取类的Class对象
        Class<?> clazz = TaiJiProjectsModuleBuilder.class;

        // 获取buildDealSkuSequenceModel方法
        Method method = clazz.getDeclaredMethod("setDealSkuGroupModuleVOTitle",
                DealSkuGroupModuleVO.class,
                boolean.class,
                int.class,
                int.class,
                boolean.class);

        // 设置方法为可访问
        method.setAccessible(true);
        TaiJiListsCfg taiJiListsCfg = new TaiJiListsCfg();
        taiJiListsCfg.setSupportMustAndOptionSwitch(true);

        // 准备参数
        ActivityCxt activityCxt = new ActivityCxt(); // 示例参数
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        boolean isMustGroup = false;
        int optionalNum = 1;
        int optionalSize = 2;
        boolean supportMustAndOptionSwitch = true;
        // 调用方法
        Object result = method.invoke(taiJiProjectsModuleBuilder, dealSkuGroupModuleVO, isMustGroup, optionalNum, optionalSize,supportMustAndOptionSwitch);
        assertNotNull(dealSkuGroupModuleVO.getTitle());
    }
}
