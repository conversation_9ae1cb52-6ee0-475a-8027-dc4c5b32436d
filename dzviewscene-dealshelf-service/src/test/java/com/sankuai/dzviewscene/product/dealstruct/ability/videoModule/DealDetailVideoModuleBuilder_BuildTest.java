package com.sankuai.dzviewscene.product.dealstruct.ability.videoModule;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleParam;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints.DealDetailVideoModuleVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailVideoModuleBuilder_BuildTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailVideoModuleParam dealDetailVideoModuleParam;

    @Mock
    private DealDetailVideoModuleCfg dealDetailVideoModuleCfg;

    @Test
    public void testBuildDealDetailInfoModelsIsNull() throws Throwable {
        DealDetailVideoModuleBuilder builder = new DealDetailVideoModuleBuilder();
        when(activityCxt.getSource(anyString())).thenReturn(null);
        CompletableFuture<List<VideoModuleVO>> result = builder.build(activityCxt, dealDetailVideoModuleParam, dealDetailVideoModuleCfg);
        assertNull(result.get());
    }
    // This test case needs to be adjusted or omitted due to the inability to mock findVPoint
    // @Test
    // public void testBuildDealDetailInfoModelsIsNotEmptyAndDetailModelIsNotNull() throws Throwable {
    // // Test logic here
    // }
}
