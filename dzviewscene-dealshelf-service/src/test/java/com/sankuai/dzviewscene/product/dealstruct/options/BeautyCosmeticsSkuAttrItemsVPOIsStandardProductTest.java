package com.sankuai.dzviewscene.product.dealstruct.options;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.dealstruct.options.BeautyCosmeticsSkuAttrItemsVPO;
import com.sankuai.dzviewscene.product.dealstruct.options.BeautyCosmeticsSkuAttrItemsVPO.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BeautyCosmeticsSkuAttrItemsVPOIsStandardProductTest {

    @Mock
    private BeautyCosmeticsSkuAttrItemsVPO.Config config;

    private BeautyCosmeticsSkuAttrItemsVPO beautyCosmeticsSkuAttrItemsVPO = new BeautyCosmeticsSkuAttrItemsVPO();

    private boolean invokeIsStandardProductMethod(Method method, Object instance, Object... args) throws Exception {
        method.setAccessible(true);
        return (boolean) method.invoke(instance, args);
    }

    @Test
    public void testIsStandardProductDealAttrsIsNull() throws Throwable {
        Method method = BeautyCosmeticsSkuAttrItemsVPO.class.getDeclaredMethod("isStandardProduct", java.util.List.class, Config.class);
        assertFalse(invokeIsStandardProductMethod(method, beautyCosmeticsSkuAttrItemsVPO, null, config));
    }

    @Test
    public void testIsStandardProductConfigIsNull() throws Throwable {
        Method method = BeautyCosmeticsSkuAttrItemsVPO.class.getDeclaredMethod("isStandardProduct", java.util.List.class, Config.class);
        assertFalse(invokeIsStandardProductMethod(method, beautyCosmeticsSkuAttrItemsVPO, Arrays.asList(new AttrM("key", "value")), null));
    }

    @Test
    public void testIsStandardProductBeautySpaStandardProductKeyIsNull() throws Throwable {
        Method method = BeautyCosmeticsSkuAttrItemsVPO.class.getDeclaredMethod("isStandardProduct", java.util.List.class, Config.class);
        when(config.getBeautySpaStandardProductKey()).thenReturn(null);
        assertFalse(invokeIsStandardProductMethod(method, beautyCosmeticsSkuAttrItemsVPO, Arrays.asList(new AttrM("key", "value")), config));
    }

    @Test
    public void testIsStandardProductBeautySpaStandardProductFlagIsNull() throws Throwable {
        Method method = BeautyCosmeticsSkuAttrItemsVPO.class.getDeclaredMethod("isStandardProduct", java.util.List.class, Config.class);
        assertFalse(invokeIsStandardProductMethod(method, beautyCosmeticsSkuAttrItemsVPO, Arrays.asList(new AttrM("key", "value")), config));
    }

    @Test
    public void testIsStandardProductAttrValueIsNotStandardProductFlag() throws Throwable {
        Method method = BeautyCosmeticsSkuAttrItemsVPO.class.getDeclaredMethod("isStandardProduct", java.util.List.class, Config.class);
        when(config.getBeautySpaStandardProductKey()).thenReturn("key");
        when(config.getBeautySpaStandardProductFlag()).thenReturn("flag");
        assertFalse(invokeIsStandardProductMethod(method, beautyCosmeticsSkuAttrItemsVPO, Arrays.asList(new AttrM("key", "value")), config));
    }
}
