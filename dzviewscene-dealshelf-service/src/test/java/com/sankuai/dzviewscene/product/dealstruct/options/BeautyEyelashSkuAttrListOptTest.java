package com.sankuai.dzviewscene.product.dealstruct.options;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.ExhibitsItemModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.BeautyEyelashSkuAttrListOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import org.junit.*;

import static org.mockito.Mockito.*;

/**
 * BeautyMakeupSkuAttrListOpt.compute 方法的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyEyelashSkuAttrListOptTest {
    @Mock
    private BeautyEyelashSkuAttrListOpt.Config config;
    private BeautyEyelashSkuAttrListOpt beautyEyelashSkuAttrListOpt;
    private ActivityCxt context;
    private SkuAttrListVP.Param param;

    private String configJson = "{\n" +
            "    \"skuAttrModels\":[\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"款式类型\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && (styletype == '个性化量身定制（不限款式）' || styletype == '个性化量身定制（限自然风格）' || styletype == '个性化量身定制（限浓密风格）' || styletype == '不限款式' || styletype == '限自然风格款' || styletype == '限浓密风格款'|| styletype == '限自然款式' || styletype == '限浓密款式' || styletype == '限网红款式')\",\n" +
            "                    \"skuAttrBuildExpression\":\"styletype\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && styletype == '指定款式n选1' && count(string.split(optionalstyle,'、'))>1\",\n" +
            "                    \"skuAttrBuildExpression\":\"string.replace_all(optionalstyle,'、','/')+count(string.split(optionalstyle,'、'))+'选1'\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && styletype == '指定款式n选1' && count(string.split(optionalstyle,'、'))==1\",\n" +
            "                    \"skuAttrBuildExpression\":\"optionalstyle\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"睫毛材质\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && count(string.split(optionalmaterial,'、'))>1\",\n" +
            "                     \"skuAttrBuildExpression\":\"string.replace_all(string.replace_all(string.replace_all(optionalmaterial,'水貂毛','水貂毛(自然柔软)'),'蚕丝蛋白纤维毛','蚕丝蛋白纤维毛(轻柔不易变形)'),'、','/')+count(string.split(optionalmaterial,'、'))+'选1'\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && count(string.split(optionalmaterial,'、'))==1\",\n" +
            "                    \"skuAttrBuildExpression\":\"string.replace_all(string.replace_all(optionalmaterial,'水貂毛','水貂毛(自然柔软)'),'蚕丝蛋白纤维毛','蚕丝蛋白纤维毛(轻柔不易变形)')\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"睫毛颜色\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && count(string.split(eyelashcolor,'、'))>1\",\n" +
            "                    \"skuAttrBuildExpression\":\"string.replace_all(eyelashcolor,'、','/')+count(string.split(eyelashcolor,'、'))+'选1'\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && count(string.split(eyelashcolor,'、'))==1\",\n" +
            "                    \"skuAttrBuildExpression\":\"eyelashcolor\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"睫毛根数\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && eyelashnumbers != nil && eyelashnumbers =='不限根数'\",\n" +
            "                    \"skuAttrBuildExpression\":\"eyelashnumbers\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && eyelashnumbers != nil && eyelashnumbers =='限根数范围'\",\n" +
            "                    \"skuAttrBuildExpression\":\"mineyelashnumbers+'-'+maxeyelashnumbers+'根'\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && eyelashnumbers != nil && eyelashnumbers =='限指定根数'\",\n" +
            "                    \"skuAttrBuildExpression\":\"specifyeyelashnumbers+'根'\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"嫁接手法\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && graftingtechnique != nil\",\n" +
            "                    \"skuAttrBuildExpression\":\"graftingtechnique\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"品牌类型\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && BrandType != nil\",\n" +
            "                    \"skuAttrBuildExpression\":\"BrandType\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"睫毛品牌\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && eyelashbrand != nil\",\n" +
            "                    \"skuAttrBuildExpression\":\"eyelashbrand\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"参考款式\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"skuCategoryId == 2104149 && dealRelatedCaseAttr != nil\",\n" +
            "                    \"skuAttrBuildExpression\":\"dealRelatedCaseAttr\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"skuAttrTitle\":\"服务内容\",\n" +
            "            \"skuAttrBuildModelList\":[\n" +
            "                {\n" +
            "                    \"skuAttrVerifyExpression\":\"(skuCategoryId == 2104152 || skuCategoryId == 2104150 || skuCategoryId == 2104151 || skuCategoryId == 2104677)&& productContent != nil\",\n" +
            "                    \"skuAttrBuildExpression\":\"productContent\"\n" +
            "                }\n" +
            "            ]\n" +
            "        }\n" +
            "    ]\n" +
            "}";
    @Before
    public void setUp() throws Exception {
        beautyEyelashSkuAttrListOpt = new BeautyEyelashSkuAttrListOpt();
        context = new ActivityCxt();
        SkuItemDto skuItemDto = new SkuItemDto();
        List<ProductSkuCategoryModel> productCategories = new ArrayList<>();
        List<AttrM> dealAttrs = new ArrayList<>();
        param = SkuAttrListVP.Param.builder().skuItemDto(skuItemDto).productCategories(productCategories).dealAttrs(dealAttrs).build();
        // 使用反射创建SkuAttrModel的实例
        Class<?> skuAttrModelClass = Class.forName("com.sankuai.dzviewscene.product.dealstruct.options.skuList.BeautyEyelashSkuAttrListOpt$SkuAttrModel");
        Constructor<?> constructor = skuAttrModelClass.getDeclaredConstructor();
        constructor.setAccessible(true);
        Object skuAttrModelInstance = constructor.newInstance();
        // 创建SkuAttrModel的列表并设置到config mock对象中
        List<Object> skuAttrModels = new ArrayList<>();
        skuAttrModels.add(skuAttrModelInstance);
    }
    @Test
    public void testComputeWithEmptySkuAttrModels() {
        // arrange
        config = JSON.parseObject(configJson, BeautyEyelashSkuAttrListOpt.Config.class);

        // act
        List<DealSkuItemVO> result = beautyEyelashSkuAttrListOpt.compute(context, param, config);


        String attrName = "name";
        String attrValue = "value";
        List<AttrM> dealAttrs = param.getDealAttrs();
        AttrM attrM = new AttrM();
        dealAttrs.add(attrM);
        attrM.setName("dealRelatedCaseAttr");
        List<ExhibitsItemModel> exhibitsItemModels = new ArrayList<>();
        ExhibitsItemModel exhibitsItemModel = new ExhibitsItemModel();
        exhibitsItemModel.setId(1L);
        exhibitsItemModel.setName("name");
        exhibitsItemModel.setPic("https://p0.meituan.net/dpmerchantpic/a56a992c7f3b9ee1b92ad75a766479f1192670.jpg");
        exhibitsItemModels.add(exhibitsItemModel);
        attrM.setValue(JSON.toJSONString(exhibitsItemModels));
        result = beautyEyelashSkuAttrListOpt.compute(context, param, config);


        beautyEyelashSkuAttrListOpt.buildDealSkuItemVO("name", "value", true);
        beautyEyelashSkuAttrListOpt.buildDealSkuItemVO("name", "value", false);
        beautyEyelashSkuAttrListOpt.buildDealSkuItemVO("参考款式", JSON.toJSONString(exhibitsItemModels), true);

        // assert
        assertNotNull( result);
    }

}