package com.sankuai.dzviewscene.product.dealstruct.options;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuAttrItemsVP.Param;
import com.sankuai.dzviewscene.product.dealstruct.model.ExhibitsItemModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @create 2024/3/7 19:39
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyNailSkuAttrItemsVPOTest {
    @Mock
    private ActivityCxt mockContext;
    @Mock
    private Param mockParam;
    @Mock
    private BeautyNailSkuAttrItemsVPO.Config mockConfig;
    @Mock
    private SkuItemDto mockSkuItemDto;
    @Mock
    private Map<Long, BeautyNailSkuAttrItemsVPO.SkuShowAttrsModel> mockSkuCategoryId2SkuShowAttrsModelMap;
    @Mock
    private BeautyNailSkuAttrItemsVPO.SkuShowAttrsModel mockSkuShowAttrsModel;
    @InjectMocks
    private BeautyNailSkuAttrItemsVPO beautyNailSkuAttrItemsVPO;
    private static long COLOR_CATEGORY_ID;
    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        Field colorCategoryIdField = BeautyNailSkuAttrItemsVPO.class.getDeclaredField("COLOR_CATEGORY_ID");
        colorCategoryIdField.setAccessible(true);
        COLOR_CATEGORY_ID = (long) colorCategoryIdField.get(null);
        when(mockSkuCategoryId2SkuShowAttrsModelMap.get(anyLong())).thenReturn(mockSkuShowAttrsModel);
        // 假设mockSkuShowAttrsModel返回有效的attrNames和replacedSeperator
        when(mockSkuShowAttrsModel.getAttrNames()).thenReturn(Collections.singletonList("attrName"));
        when(mockSkuShowAttrsModel.getReplacedSeperator()).thenReturn(",");
    }
    @Test
    public void testComputeWhenCategoryIdIsColorCategoryId() {
        // arrange
        when(mockParam.getSkuItemDto()).thenReturn(mockSkuItemDto);
        when(mockSkuItemDto.getProductCategory()).thenReturn(COLOR_CATEGORY_ID);
        when(mockConfig.getCategoryId2SkuShowAttrsModelMap()).thenReturn(mockSkuCategoryId2SkuShowAttrsModelMap);
        when(mockSkuCategoryId2SkuShowAttrsModelMap.containsKey(COLOR_CATEGORY_ID)).thenReturn(true);
        // act
        List<DealSkuItemVO> result = beautyNailSkuAttrItemsVPO.compute(mockContext, mockParam, mockConfig);

        String attrName = "name";
        String attrValue = "value";
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        dealAttrs.add(attrM);
        attrM.setName("dealRelatedCaseAttr");
        List<ExhibitsItemModel> exhibitsItemModels = new ArrayList<>();
        ExhibitsItemModel exhibitsItemModel = new ExhibitsItemModel();
        exhibitsItemModel.setId(1L);
        exhibitsItemModel.setName("name");
        exhibitsItemModel.setPic("https://p0.meituan.net/dpmerchantpic/a56a992c7f3b9ee1b92ad75a766479f1192670.jpg");
        exhibitsItemModels.add(exhibitsItemModel);
        beautyNailSkuAttrItemsVPO.buildDealSkuItemVOByDealAttr(attrName, attrValue, dealAttrs);
        attrM.setValue(JSON.toJSONString(exhibitsItemModels));
        beautyNailSkuAttrItemsVPO.buildDealSkuItemVOByDealAttr(attrName, attrValue, dealAttrs);
        // assert
        assertNotNull(result);
    }
    // ... 保留其他测试方法不变 ...
    @Test
    public void testComputeWhenCategoryIdIsContainedInMap() {
        // arrange
        when(mockParam.getSkuItemDto()).thenReturn(mockSkuItemDto);
        when(mockSkuItemDto.getProductCategory()).thenReturn(1L);
        when(mockConfig.getCategoryId2SkuShowAttrsModelMap()).thenReturn(mockSkuCategoryId2SkuShowAttrsModelMap);
        when(mockSkuCategoryId2SkuShowAttrsModelMap.containsKey(1L)).thenReturn(true);
        // act
        List<DealSkuItemVO> result = beautyNailSkuAttrItemsVPO.compute(mockContext, mockParam, mockConfig);
        // assert
        assertNotNull(result);
    }
    // ... 其他测试方法 ...
    @Test
    public void testComputeWhenSkuItemDtoIsNull() {
        // arrange
        when(mockParam.getSkuItemDto()).thenReturn(null);
        // act
        List<DealSkuItemVO> result = beautyNailSkuAttrItemsVPO.compute(mockContext, mockParam, mockConfig);
        // assert
        assertNull(result);
    }
    @Test
    public void testComputeWhenCategoryIdIsNotContainedInMapAndNotColorCategoryId() {
        // arrange
        when(mockParam.getSkuItemDto()).thenReturn(mockSkuItemDto);
        when(mockSkuItemDto.getProductCategory()).thenReturn(2L);
        when(mockConfig.getCategoryId2SkuShowAttrsModelMap()).thenReturn(mockSkuCategoryId2SkuShowAttrsModelMap);
        when(mockSkuCategoryId2SkuShowAttrsModelMap.containsKey(2L)).thenReturn(false);
        // act
        List<DealSkuItemVO> result = beautyNailSkuAttrItemsVPO.compute(mockContext, mockParam, mockConfig);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenCategoryIdEqualColorCategoryId() {
        when(mockParam.getSkuItemDto()).thenReturn(mockSkuItemDto);
        when(mockConfig.getCategoryId2SkuShowAttrsModelMap()).thenReturn(mockSkuCategoryId2SkuShowAttrsModelMap);
        when(mockSkuItemDto.getProductCategory()).thenReturn(COLOR_CATEGORY_ID);
        when(mockSkuCategoryId2SkuShowAttrsModelMap.containsKey(anyLong())).thenReturn(false);

        List<DealSkuItemVO> result = beautyNailSkuAttrItemsVPO.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }

}