package com.sankuai.dzviewscene.product.dealstruct.options;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.model.ExhibitsItemModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.BeautyEyelashSkuAttrListOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.BeautyTatttooSkuTitleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

/**
 * BeautyMakeupSkuAttrListOpt.compute 方法的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyTatttooSkuTitleOptTest {
    @Mock
    private BeautyTatttooSkuTitleOpt.Config config;
    private BeautyTatttooSkuTitleOpt beautyTatttooSkuTitleOpt;
    private ActivityCxt context;
    private SkuListModuleVP.Param param;

    private String dealDetailModuleJson = "{\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":**********,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":**********,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"900.00\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"988.00\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2106228,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u7eb9\\\\\\\\u7709\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":988.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":355827,\\\\\\\"attrName\\\\\\\":\\\\\\\"ProjectClassification\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9879\\\\\\\\u76ee\\\\\\\\u5206\\\\\\\\u7c7b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u7ebf\\\\\\\\u6761\\\\\\\\u7709\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u7ebf\\\\\\\\u6761\\\\\\\\u7709\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":604,\\\\\\\"attrName\\\\\\\":\\\\\\\"suitCrowds\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u4eba\\\\\\\\u7fa4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u7709\\\\\\\\u6bdb\\\\\\\\u57fa\\\\\\\\u7840\\\\\\\\u6761\\\\\\\\u4ef6\\\\\\\\u597d\\\\\\\\u3001\\\\\\\\u65ad\\\\\\\\u7709\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u7709\\\\\\\\u6bdb\\\\\\\\u57fa\\\\\\\\u7840\\\\\\\\u6761\\\\\\\\u4ef6\\\\\\\\u597d\\\\\\\\u3001\\\\\\\\u65ad\\\\\\\\u7709\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":965133,\\\\\\\"attrName\\\\\\\":\\\\\\\"peise\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u914d\\\\\\\\u8272\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u4e8c\\\\\\\\u8272\\\\\\\\u6e10\\\\\\\\u53d8\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u4e8c\\\\\\\\u8272\\\\\\\\u6e10\\\\\\\\u53d8\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":965134,\\\\\\\"attrName\\\\\\\":\\\\\\\"ranliaochandi\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u67d3\\\\\\\\u6599\\\\\\\\u4ea7\\\\\\\\u5730\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u56fd\\\\\\\\u4ea7\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u56fd\\\\\\\\u4ea7\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1756,\\\\\\\"attrName\\\\\\\":\\\\\\\"servicestep\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\\u6b65\\\\\\\\u9aa4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u7709\\\\\\\\u5f62\\\\\\\\u8bbe\\\\\\\\u8ba1\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u8212\\\\\\\\u7f13\\\\\\\\u62a4\\\\\\\\u7406\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u64cd\\\\\\\\u4f5c\\\\\\\\u4e0a\\\\\\\\u8272\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u672f\\\\\\\\u540e\\\\\\\\u4fee\\\\\\\\u62a4\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u7709\\\\\\\\u5f62\\\\\\\\u8bbe\\\\\\\\u8ba1\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u8212\\\\\\\\u7f13\\\\\\\\u62a4\\\\\\\\u7406\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u64cd\\\\\\\\u4f5c\\\\\\\\u4e0a\\\\\\\\u8272\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"stepName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u672f\\\\\\\\u540e\\\\\\\\u4fee\\\\\\\\u62a4\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepDesc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u6d4b\\\\\\\\u8bd5\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2586,\\\\\\\"attrName\\\\\\\":\\\\\\\"grant\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u514d\\\\\\\\u8d39\\\\\\\\u8865\\\\\\\\u8272\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u8d60\\\\\\\\u9001\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u8d60\\\\\\\\u9001\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":965136,\\\\\\\"attrName\\\\\\\":\\\\\\\"buseshijian\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8865\\\\\\\\u8272\\\\\\\\u65f6\\\\\\\\u95f4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":965137,\\\\\\\"attrName\\\\\\\":\\\\\\\"mianfeibuse\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u514d\\\\\\\\u8d39\\\\\\\\u8865\\\\\\\\u8272\\\\\\\\u6b21\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1390,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceDuration\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"120\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"120\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":**********,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"900.00\\\",\\\"marketPrice\\\":\\\"988.00\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2106228,\\\"name\\\":\\\"\\\\u7eb9\\\\u7709\\\",\\\"copies\\\":1,\\\"marketPrice\\\":988.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":355827,\\\"attrName\\\":\\\"ProjectClassification\\\",\\\"chnName\\\":\\\"\\\\u9879\\\\u76ee\\\\u5206\\\\u7c7b\\\",\\\"attrValue\\\":\\\"\\\\u7ebf\\\\u6761\\\\u7709\\\",\\\"rawAttrValue\\\":\\\"\\\\u7ebf\\\\u6761\\\\u7709\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":604,\\\"attrName\\\":\\\"suitCrowds\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u4eba\\\\u7fa4\\\",\\\"attrValue\\\":\\\"\\\\u7709\\\\u6bdb\\\\u57fa\\\\u7840\\\\u6761\\\\u4ef6\\\\u597d\\\\u3001\\\\u65ad\\\\u7709\\\",\\\"rawAttrValue\\\":\\\"\\\\u7709\\\\u6bdb\\\\u57fa\\\\u7840\\\\u6761\\\\u4ef6\\\\u597d\\\\u3001\\\\u65ad\\\\u7709\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":965133,\\\"attrName\\\":\\\"peise\\\",\\\"chnName\\\":\\\"\\\\u914d\\\\u8272\\\",\\\"attrValue\\\":\\\"\\\\u4e8c\\\\u8272\\\\u6e10\\\\u53d8\\\",\\\"rawAttrValue\\\":\\\"\\\\u4e8c\\\\u8272\\\\u6e10\\\\u53d8\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":965134,\\\"attrName\\\":\\\"ranliaochandi\\\",\\\"chnName\\\":\\\"\\\\u67d3\\\\u6599\\\\u4ea7\\\\u5730\\\",\\\"attrValue\\\":\\\"\\\\u56fd\\\\u4ea7\\\",\\\"rawAttrValue\\\":\\\"\\\\u56fd\\\\u4ea7\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1756,\\\"attrName\\\":\\\"servicestep\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u6b65\\\\u9aa4\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u7709\\\\u5f62\\\\u8bbe\\\\u8ba1\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u8212\\\\u7f13\\\\u62a4\\\\u7406\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u64cd\\\\u4f5c\\\\u4e0a\\\\u8272\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u672f\\\\u540e\\\\u4fee\\\\u62a4\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u7709\\\\u5f62\\\\u8bbe\\\\u8ba1\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u8212\\\\u7f13\\\\u62a4\\\\u7406\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u64cd\\\\u4f5c\\\\u4e0a\\\\u8272\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u672f\\\\u540e\\\\u4fee\\\\u62a4\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2586,\\\"attrName\\\":\\\"grant\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u514d\\\\u8d39\\\\u8865\\\\u8272\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u8d60\\\\u9001\\\",\\\"rawAttrValue\\\":\\\"\\\\u8d60\\\\u9001\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":965136,\\\"attrName\\\":\\\"buseshijian\\\",\\\"chnName\\\":\\\"\\\\u8865\\\\u8272\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"3\\\",\\\"rawAttrValue\\\":\\\"3\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":965137,\\\"attrName\\\":\\\"mianfeibuse\\\",\\\"chnName\\\":\\\"\\\\u514d\\\\u8d39\\\\u8865\\\\u8272\\\\u6b21\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1390,\\\"attrName\\\":\\\"serviceDuration\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"120\\\",\\\"rawAttrValue\\\":\\\"120\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2106228,\\\"cnName\\\":\\\"\\\\u7eb9\\\\u7709\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":**********,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"900.00\\\",\\\"marketPrice\\\":\\\"988.00\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2106228,\\\"name\\\":\\\"\\\\u7eb9\\\\u7709\\\",\\\"copies\\\":1,\\\"marketPrice\\\":988.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":355827,\\\"attrName\\\":\\\"ProjectClassification\\\",\\\"chnName\\\":\\\"\\\\u9879\\\\u76ee\\\\u5206\\\\u7c7b\\\",\\\"attrValue\\\":\\\"\\\\u7ebf\\\\u6761\\\\u7709\\\"},{\\\"metaAttrId\\\":604,\\\"attrName\\\":\\\"suitCrowds\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u4eba\\\\u7fa4\\\",\\\"attrValue\\\":\\\"\\\\u7709\\\\u6bdb\\\\u57fa\\\\u7840\\\\u6761\\\\u4ef6\\\\u597d\\\\u3001\\\\u65ad\\\\u7709\\\"},{\\\"metaAttrId\\\":965133,\\\"attrName\\\":\\\"peise\\\",\\\"chnName\\\":\\\"\\\\u914d\\\\u8272\\\",\\\"attrValue\\\":\\\"\\\\u4e8c\\\\u8272\\\\u6e10\\\\u53d8\\\"},{\\\"metaAttrId\\\":965134,\\\"attrName\\\":\\\"ranliaochandi\\\",\\\"chnName\\\":\\\"\\\\u67d3\\\\u6599\\\\u4ea7\\\\u5730\\\",\\\"attrValue\\\":\\\"\\\\u56fd\\\\u4ea7\\\"},{\\\"metaAttrId\\\":1756,\\\"attrName\\\":\\\"servicestep\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u6b65\\\\u9aa4\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u7709\\\\u5f62\\\\u8bbe\\\\u8ba1\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u8212\\\\u7f13\\\\u62a4\\\\u7406\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u64cd\\\\u4f5c\\\\u4e0a\\\\u8272\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"\\\\u672f\\\\u540e\\\\u4fee\\\\u62a4\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"\\\\u6d4b\\\\u8bd5\\\\\\\"}]\\\"},{\\\"metaAttrId\\\":2586,\\\"attrName\\\":\\\"grant\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u514d\\\\u8d39\\\\u8865\\\\u8272\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u8d60\\\\u9001\\\"},{\\\"metaAttrId\\\":965136,\\\"attrName\\\":\\\"buseshijian\\\",\\\"chnName\\\":\\\"\\\\u8865\\\\u8272\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"3\\\"},{\\\"metaAttrId\\\":965137,\\\"attrName\\\":\\\"mianfeibuse\\\",\\\"chnName\\\":\\\"\\\\u514d\\\\u8d39\\\\u8865\\\\u8272\\\\u6b21\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\"},{\\\"metaAttrId\\\":1390,\\\"attrName\\\":\\\"serviceDuration\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"120\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":{\\\"totalPrice\\\":988.00,\\\"salePrice\\\":900.00,\\\"groups\\\":[{\\\"units\\\":[{\\\"skuCateId\\\":2106228,\\\"amount\\\":1,\\\"price\\\":988.0,\\\"attrValues\\\":{\\\"suitCrowds\\\":\\\"眉毛基础条件好、断眉\\\",\\\"buseshijian\\\":\\\"3\\\",\\\"ranliaochandi\\\":\\\"国产\\\",\\\"serviceDuration\\\":\\\"120\\\",\\\"servicestep\\\":\\\"[{\\\\\\\"stepName\\\\\\\":\\\\\\\"眉形设计\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"测试\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"舒缓护理\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"测试\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"操作上色\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"测试\\\\\\\"},{\\\\\\\"stepName\\\\\\\":\\\\\\\"术后修护\\\\\\\",\\\\\\\"stepDesc\\\\\\\":\\\\\\\"测试\\\\\\\"}]\\\",\\\"grant\\\":\\\"赠送\\\",\\\"ProjectClassification\\\":\\\"线条眉\\\",\\\"peise\\\":\\\"二色渐变\\\",\\\"mianfeibuse\\\":\\\"1\\\"},\\\"projectName\\\":\\\"纹眉\\\",\\\"properties\\\":\\\"纹眉；眉毛基础条件好、断眉；线条眉；二色渐变；国产；赠送\\\",\\\"skuId\\\":0}],\\\"optionalCount\\\":0}]},\\\"type\\\":\\\"uniform-structure-table\\\"}],\\\"headline\\\":\\\"团购详情\\\",\\\"version\\\":1}\"},{\"name\":\"service_type\",\"value\":\"纹眉\"}],\"dealDetailDtoModel\":{\"dealGroupId\":**********,\"skuUniStructuredDto\":{\"marketPrice\":\"988.00\",\"mustGroups\":[{\"skuItems\":[{\"attrItems\":[{\"attrName\":\"ProjectClassification\",\"attrValue\":\"线条眉\",\"chnName\":\"项目分类\",\"metaAttrId\":355827,\"rawAttrValue\":\"线条眉\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"suitCrowds\",\"attrValue\":\"眉毛基础条件好、断眉\",\"chnName\":\"适用人群\",\"metaAttrId\":604,\"rawAttrValue\":\"眉毛基础条件好、断眉\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"peise\",\"attrValue\":\"二色渐变\",\"chnName\":\"配色\",\"metaAttrId\":965133,\"rawAttrValue\":\"二色渐变\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"ranliaochandi\",\"attrValue\":\"国产\",\"chnName\":\"染料产地\",\"metaAttrId\":965134,\"rawAttrValue\":\"国产\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"servicestep\",\"attrValue\":\"[{\\\"stepName\\\":\\\"眉形设计\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"舒缓护理\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"操作上色\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"术后修护\\\",\\\"stepDesc\\\":\\\"测试\\\"}]\",\"chnName\":\"服务步骤\",\"metaAttrId\":1756,\"rawAttrValue\":\"[{\\\"stepName\\\":\\\"眉形设计\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"舒缓护理\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"操作上色\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"术后修护\\\",\\\"stepDesc\\\":\\\"测试\\\"}]\",\"sequence\":0,\"valueType\":300},{\"attrName\":\"grant\",\"attrValue\":\"赠送\",\"chnName\":\"是否提供免费补色服务\",\"metaAttrId\":2586,\"rawAttrValue\":\"赠送\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"buseshijian\",\"attrValue\":\"3\",\"chnName\":\"补色时间\",\"metaAttrId\":965136,\"rawAttrValue\":\"3\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"mianfeibuse\",\"attrValue\":\"1\",\"chnName\":\"免费补色次数\",\"metaAttrId\":965137,\"rawAttrValue\":\"1\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"serviceDuration\",\"attrValue\":\"120\",\"chnName\":\"服务时长\",\"metaAttrId\":1390,\"rawAttrValue\":\"120\",\"sequence\":0,\"valueType\":500}],\"copies\":1,\"marketPrice\":988.0,\"name\":\"纹眉\",\"productCategory\":2106228,\"skuId\":0,\"status\":10}]}],\"optionalGroups\":[],\"salePrice\":\"900.00\"},\"structType\":\"uniform-structure-table\",\"title\":\"团购详情\"},\"dealId\":**********,\"dealTitle\":\"纹绣结构化测试\",\"marketPrice\":\"988\",\"productCategories\":[{\"cnName\":\"纹眉\",\"productCategoryId\":2106228}],\"salePrice\":\"900\",\"tradeType\":3,\"unifyProduct\":false}";

    @Before
    public void setUp() throws Exception {
        beautyTatttooSkuTitleOpt = new BeautyTatttooSkuTitleOpt();
        context = new ActivityCxt();
        DealDetailInfoModel dealDetailInfoModel = JSONObject.parseObject(dealDetailModuleJson, DealDetailInfoModel.class);
        List<DouhuResultModel> douhuResultModels = Lists.newArrayList();
        param = SkuListModuleVP.Param.builder().dealDetailInfoModel(dealDetailInfoModel).douhuResultModels(douhuResultModels).build();
    }
    @Test
    public void testComputeWithEmptySkuAttrModels() {
        // arrange
        List<DealDetailSkuListModuleGroupModel> result = beautyTatttooSkuTitleOpt.compute(context, param, config);
        // assert
        assertNotNull( result);
    }

}