package com.sankuai.dzviewscene.product.dealstruct.options;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrValueModel;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSkuItemAttrItemsVPOTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DefaultSkuItemAttrItemsVPO.Param param;

    @Mock
    private DefaultSkuItemAttrItemsVPO.Config config;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private SkuAttrItemDto skuAttrItemDto;

    private DefaultSkuItemAttrItemsVPO defaultSkuItemAttrItemsVPO = new DefaultSkuItemAttrItemsVPO();

    @Test
    public void testComputeSkuItemDtoIsNull() throws Throwable {
        when(param.getSkuItemDto()).thenReturn(null);
        List<SkuAttrModel> result = defaultSkuItemAttrItemsVPO.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeAttrItemsIsEmpty() throws Throwable {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(null);
        List<SkuAttrModel> result = defaultSkuItemAttrItemsVPO.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeAttrItemIsNull() throws Throwable {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(Arrays.asList(null, skuAttrItemDto));
        List<SkuAttrModel> result = defaultSkuItemAttrItemsVPO.compute(context, param, config);
        assertEquals(1, result.size());
    }

    @Test
    public void testComputeAttrItemIsNotNull() throws Throwable {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(Arrays.asList(skuAttrItemDto, skuAttrItemDto));
        when(skuAttrItemDto.getChnName()).thenReturn("chnName");
        when(skuAttrItemDto.getAttrName()).thenReturn("attrName");
        when(skuAttrItemDto.getAttrValue()).thenReturn("attrValue");
        List<SkuAttrModel> result = defaultSkuItemAttrItemsVPO.compute(context, param, config);
        assertEquals(2, result.size());
        assertEquals("chnName", result.get(0).getName());
        assertEquals("attrName", result.get(0).getAttrName());
        assertEquals("attrValue", result.get(0).getValue().getDoc());
    }
}
