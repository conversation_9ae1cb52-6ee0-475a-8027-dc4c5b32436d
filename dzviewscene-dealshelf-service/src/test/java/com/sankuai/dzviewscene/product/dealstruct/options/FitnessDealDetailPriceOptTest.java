package com.sankuai.dzviewscene.product.dealstruct.options;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.vpoints.DealDetailPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Assert;
import org.junit.Test;

public class FitnessDealDetailPriceOptTest {

    /**
     * 健身通团购属性key
     */
    private static final String FITNESS_CROSS_KEY = "dealGroupFitnessPassConfig";

    /**
     * 健身通团购属性value
     */
    private static final String FITNESS_CROSS_VALUE = "fitnessPass";

    @Test
    public void test() {
        ActivityCxt cxt = buildCtx();

        DealDetailPriceVP.Param param = DealDetailPriceVP.Param.builder()
                .marketPrice("1000")
                .salePrice("700")
                .build();

        FitnessDealDetailPriceOpt opt = new FitnessDealDetailPriceOpt();
        DealDetailPriceModel model = opt.compute(cxt, param, new FitnessDealDetailPriceOpt.Config());

        Assert.assertNotNull(model);
        Assert.assertNull(model.getOriginalPrice());
        Assert.assertNull(model.getSalePrice());
        Assert.assertNull(model.getOriginalPriceTitle());
        Assert.assertNull(model.getSalePriceTitle());

        DealDetailPriceModel model2 = opt.compute(new ActivityCxt(), param, new FitnessDealDetailPriceOpt.Config());
        Assert.assertNotNull(model2);
        Assert.assertNotNull(model2.getOriginalPrice());
        Assert.assertNotNull(model2.getSalePrice());
        Assert.assertNotNull(model2.getOriginalPriceTitle());
        Assert.assertNotNull(model2.getSalePriceTitle());
    }

    private ActivityCxt buildCtx() {
        AttrM attrM = new AttrM();
        attrM.setName(FITNESS_CROSS_KEY);
        attrM.setValue(FITNESS_CROSS_VALUE);

        DealDetailInfoModel model = new DealDetailInfoModel();
        model.setDealAttrs(Lists.newArrayList(attrM));

        ActivityCxt cxt = new ActivityCxt();
        cxt.attachSource(DealDetailFetcher.CODE, Lists.newArrayList(model));

        return cxt;
    }

}