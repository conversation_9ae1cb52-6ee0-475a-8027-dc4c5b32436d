package com.sankuai.dzviewscene.product.dealstruct.options;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.PrimaryExaminationItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.SecondaryExaminationItemVO;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.stream.Collectors;

@RunWith(MockitoJUnitRunner.class)
public class HealthExaminationCheckItemListOptV2Test {

    HealthExaminationCheckItemListOptV2 optV2 = new HealthExaminationCheckItemListOptV2();

    @Test
    public void test_single_consist() {

        List<SkuItemDto> skuItemDtos = Lists.newArrayList(
                buildSkuItem("身高", "一般检查", "基础检查", "这是一般检查哦"),
                buildSkuItem("体重", "一般检查", "基础检查", "这是一般检查哦"),
                buildSkuItem("血压", "一般检查", "基础检查", "这是一般检查哦"),
                buildSkuItem("脉搏", "一般检查", "基础检查", "这是一般检查哦"),
                buildSkuItem("心跳", "一般检查", null, "这是一般检查哦")
        );

        List<PrimaryExaminationItemVO> examinationItemVOS = optV2.buildPrimaryExaminationItemVOS(buildConfig(), skuItemDtos);

        //一级检查
        Assert.assertTrue(examinationItemVOS.size() == 1 && examinationItemVOS.get(0).getName().equals("基础检查"));
        //二级检查
        List<SecondaryExaminationItemVO> secondaryExaminationItemList = examinationItemVOS.get(0).getSecondaryExaminationItemList();
        Assert.assertTrue(secondaryExaminationItemList.size() == 1);

        SecondaryExaminationItemVO secondaryExaminationItemVO = secondaryExaminationItemList.get(0);
        Assert.assertTrue(secondaryExaminationItemVO.getName().equals("一般检查"));
        Assert.assertTrue(secondaryExaminationItemVO.getCheckSignificance().equals("这是一般检查哦"));
        Assert.assertTrue(secondaryExaminationItemVO.getTertiaryExaminations().equals("身高、体重、血压、脉搏、心跳"));
    }


    /**
     * 商户类目一样，但是对应的一级类目不同，检查意义不一致所以为null
     */
    @Test
    public void test_single_not_consist() {

        List<SkuItemDto> skuItemDtos = Lists.newArrayList(
                buildSkuItem("身高", "一般检查", "基础检查", "这是一般检查哦"),
                buildSkuItem("体重", "一般检查", "仪器检查", "这是一般检查哦"),
                buildSkuItem("血压", "一般检查", "基础检查", "这是一般检查哦"),
                buildSkuItem("脉搏", "一般检查", "仪器检查", "哈哈哈")
        );

        List<PrimaryExaminationItemVO> examinationItemVOS = optV2.buildPrimaryExaminationItemVOS(buildConfig(), skuItemDtos);

        //一级检查
        Assert.assertTrue(examinationItemVOS.size() == 1 && examinationItemVOS.get(0).getName().equals("其他"));
        //二级检查
        List<SecondaryExaminationItemVO> secondaryExaminationItemList = examinationItemVOS.get(0).getSecondaryExaminationItemList();
        Assert.assertTrue(JSON.toJSONString(secondaryExaminationItemList.stream().map(e -> e.getName()).distinct().collect(Collectors.toList())),secondaryExaminationItemList.size() == 1);

        SecondaryExaminationItemVO secondaryExaminationItemVO = secondaryExaminationItemList.get(0);
        Assert.assertTrue(secondaryExaminationItemVO.getName().equals("一般检查"));
        Assert.assertTrue(secondaryExaminationItemVO.getCheckSignificance() == null);
        Assert.assertTrue(secondaryExaminationItemVO.getTertiaryExaminations().equals("身高、体重、血压、脉搏"));
    }


    /**
     * 多个类目
     */
    @Test
    public void test_multiple() {

        List<SkuItemDto> skuItemDtos = Lists.newArrayList(
                buildSkuItem("身高", "一般检查", "基础检查", "这是一般检查哦"),
                buildSkuItem("体重", "一般检查", "仪器检查", "这是一般检查哦"),
                buildSkuItem("血压", "一般检查", "基础检查", "这是一般检查哦"),

                buildSkuItem("甲状腺", "外科检查", "仪器检查", "哈哈哈"),
                buildSkuItem("乳房", "外科检查", "仪器检查", "哈哈哈"),
                buildSkuItem("结节", "外科检查", "仪器检查", "哈哈哈"),

                buildSkuItem("白细胞计数", "血常规", "实验室检查", "哈哈哈"),
                buildSkuItem("红细胞计数", "血常规", "实验室检查", "哈哈哈"),
                buildSkuItem("血蛋白量", "血常规", "实验室检查", "哈哈哈"),

                buildSkuItem("肾功能1", "肾功能", "仪器检查", "哈哈哈"),
                buildSkuItem("肾功能2", "肾功能", null, null),
                buildSkuItem("肾功能3", "肾功能", "实验室检查", "哈哈哈")
        );

        List<PrimaryExaminationItemVO> examinationItemVOS = optV2.buildPrimaryExaminationItemVOS(buildConfig(), skuItemDtos);

        //一级检查，三个类目，排序分别是：实验室检查，仪器检查，其他
        Assert.assertTrue(examinationItemVOS.size() == 3);
        PrimaryExaminationItemVO firstPrimary= examinationItemVOS.get(0);
        PrimaryExaminationItemVO secondPrimary= examinationItemVOS.get(1);
        PrimaryExaminationItemVO thirdPrimary= examinationItemVOS.get(2);

        Assert.assertTrue(firstPrimary.getDesc().equals("3项"));
        Assert.assertTrue(secondPrimary.getDesc().equals("3项"));
        Assert.assertTrue(thirdPrimary.getDesc().equals("6项"));
        Assert.assertTrue(firstPrimary.getName().equals("实验室检查") && secondPrimary.getName().equals("仪器检查") && thirdPrimary.getName().equals("其他"));
        //二级检查
        Assert.assertTrue(thirdPrimary.getSecondaryExaminationItemList().size() == 2);

        SecondaryExaminationItemVO secondaryExaminationItemVO1 = thirdPrimary.getSecondaryExaminationItemList().get(0);
        SecondaryExaminationItemVO secondaryExaminationItemVO2 = thirdPrimary.getSecondaryExaminationItemList().get(1);

        Assert.assertTrue(secondaryExaminationItemVO2.getName().equals("肾功能"));
        Assert.assertTrue(secondaryExaminationItemVO2.getCheckSignificance().equals("哈哈哈"));
        Assert.assertTrue(secondaryExaminationItemVO2.getTertiaryExaminations().equals("肾功能1、肾功能2、肾功能3"));

    }


    SkuItemDto buildSkuItem(String shopItemName, String shopCategoryName, String firstCategoryName, String explain) {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setName(shopItemName);
        skuItemDto.setAttrItems(Lists.newArrayList(
                build("categoryName", shopCategoryName),
                build("explain", explain),
                build("category1", firstCategoryName)));

        return skuItemDto;
    }

    public SkuAttrItemDto build(String attrName, String attrValue) {

        SkuAttrItemDto itemDto = new SkuAttrItemDto();
        itemDto.setAttrName(attrName);
        itemDto.setAttrValue(attrValue);
        return itemDto;
    }

    HealthExaminationCheckItemListOptV2.Config buildConfig() {
        String data = "{\"allMustTertiaryItemNumFormat\":\"[     {         \\\\\\\"strikethrough\\\\\\\":false,         \\\\\\\"text\\\\\\\":\\\\\\\"总计\\\\\\\",         \\\\\\\"textcolor\\\\\\\":\\\\\\\"#777777\\\\\\\",         \\\\\\\"textsize\\\\\\\":13     },     {         \\\\\\\"strikethrough\\\\\\\":false,         \\\\\\\"textstyle\\\\\\\":\\\\\\\"Bold\\\\\\\",         \\\\\\\"text\\\\\\\":\\\\\\\" %s \\\\\\\",         \\\\\\\"textcolor\\\\\\\":\\\\\\\"#111111\\\\\\\",         \\\\\\\"textsize\\\\\\\":17     },     {         \\\\\\\"strikethrough\\\\\\\":false,         \\\\\\\"text\\\\\\\":\\\\\\\"项\\\\\\\",         \\\\\\\"textcolor\\\\\\\":\\\\\\\"#777777\\\\\\\",         \\\\\\\"textsize\\\\\\\":13     } ]\",\"primaryItemsortedMap\":{\"基础检查\":1,\"实验室检查\":2,\"仪器检查\":3,\"其他\":4,\"可选项目\":5}}";
        return JSONObject.parseObject(data, HealthExaminationCheckItemListOptV2.Config.class);
    }

}