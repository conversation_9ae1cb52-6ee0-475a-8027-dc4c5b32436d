package com.sankuai.dzviewscene.product.dealstruct.options;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;


/**
 * <AUTHOR>
 * @ClassName PetHospitalDealDetailSkuListAfterProcessingVPOTest.java
 * @createTime 2024/07/15 15:47
 */

@RunWith(MockitoJUnitRunner.class)
public class PetHospitalDealDetailSkuListAfterProcessingVPOTest {
    private PetHospitalDealDetailSkuListAfterProcessingVPO vpo;
    @Mock
    private ActivityCxt mockContext;
    private PetHospitalDealDetailSkuListAfterProcessingVPO target;
    private PetHospitalDealDetailSkuListAfterProcessingVPO.Config config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        vpo = new PetHospitalDealDetailSkuListAfterProcessingVPO();
        target = new PetHospitalDealDetailSkuListAfterProcessingVPO();
        config = new PetHospitalDealDetailSkuListAfterProcessingVPO.Config();

    }

    /**
     * 测试 getVaccineBrand 方法，当 items 为空时应返回 null。
     */
    @Test
    public void testGetVaccineBrandWithEmptyItems() {
        List<DealSkuItemVO> items = new ArrayList<>();

        String result = vpo.getVaccineBrand(items);

        assertNull("当 items 为空时应返回 null", result);
    }

    /**
     * 测试 getVaccineBrand 方法，当 items 不包含疫苗品牌时应返回 null。
     */
    @Test
    public void testGetVaccineBrandWithNoVaccineBrandItem() {
        List<DealSkuItemVO> items = new ArrayList<>();
        DealSkuItemVO item = Mockito.mock(DealSkuItemVO.class);
        Mockito.when(item.getName()).thenReturn("非疫苗品牌");
        items.add(item);

        String result = vpo.getVaccineBrand(items);

        assertNull("当 items 不包含疫苗品牌时应返回 null", result);
    }

    /**
     * 测试 getVaccineBrand 方法，当 items 包含疫苗品牌时应返回正确的疫苗品牌。
     */
    @Test
    public void testGetVaccineBrandWithVaccineBrandItem() {
        List<DealSkuItemVO> items = new ArrayList<>();
        DealSkuItemVO item = Mockito.mock(DealSkuItemVO.class);
        Mockito.when(item.getName()).thenReturn("vaccineBrand");
        Mockito.when(item.getValue()).thenReturn("疫苗品牌A");
        items.add(item);

        String result = vpo.getVaccineBrand(items);

        assertEquals("当 items 包含疫苗品牌时应返回正确的疫苗品牌", "疫苗品牌A", result);
    }

    /**
     * 测试 getCommonAttrVOS 方法
     */
    @Test
    public void testGetCommonAttrVOS() {
        DealSkuItemVO item = new DealSkuItemVO();
        item.setName("vaccineBrand");
        item.setValue("妙三多");
        DealSkuItemVO item2 = new DealSkuItemVO();
        item2.setName("疫苗类型");
        item2.setValue("猫三联");
        String skuCategory = "";
        String skuName = "";

        config.setVaccineSpecificationModels(getVaccineSpecificationModels());
        List<CommonAttrVO> result = target.getCommonAttrVOS(Lists.newArrayList(item, item2), config, skuCategory, skuName, mockContext);
        assertNotNull(result);

    }

    @Test
    public void testGetCommonAttrVOS2() {
        DealSkuItemVO item = new DealSkuItemVO();
        item.setName("疫苗类型");
        item.setValue("狂犬疫苗");
        String skuCategory = "";
        String skuName = "";

        config.setVaccineSpecificationModels(getVaccineSpecificationModels());
        List<CommonAttrVO> result = target.getCommonAttrVOS(Lists.newArrayList(item), config, skuCategory, skuName, mockContext);
        assertNotNull(result);

    }

    private List<PetHospitalDealDetailSkuListAfterProcessingVPO.VaccineSpecificationModel> getVaccineSpecificationModels() {
        String vaccineSpecificationModels = "[{\"vaccineType\":\"猫三联\",\"vaccineBrand\":\"妙三多\",\"specificationContentMap\":{\"适用宠物年龄\":\"8周龄或8周龄以上的健康猫\",\"疫苗作用\":\"预防猫鼻支气管炎、嵌杯状病毒病、泛白细胞减少症\",\"接种建议\":\"8周龄或8周龄以上的健康猫首免三针，每针间隔21-28日。之后每年加强一针\"}},{\"vaccineType\":\"猫三联\",\"vaccineBrand\":\"喵益哆\",\"specificationContentMap\":{\"适用宠物年龄\":\"8周龄或8周龄以上的健康猫\",\"疫苗作用\":\"预防猫鼻支气管炎、嵌杯状病毒病、泛白细胞减少症\",\"接种建议\":\"8周龄或8周龄以上的健康猫首免两针，每针间隔21-28日。之后每年加强一针\"}},{\"vaccineType\":\"猫三联\",\"vaccineBrand\":\"瑞喵舒\",\"specificationContentMap\":{\"适用宠物年龄\":\"8周龄或8周龄以上的健康猫\",\"疫苗作用\":\"预防猫鼻支气管炎、嵌杯状病毒病、泛白细胞减少症\",\"接种建议\":\"8周龄或8周龄以上的健康猫首免三针，每针间隔21-28日。之后每年加强一针\"}},{\"vaccineType\":\"犬四联\",\"vaccineBrand\":\"英特威\",\"specificationContentMap\":{\"适用宠物年龄\":\"6周龄或6周龄以上的健康犬\",\"疫苗作用\":\"预防犬的犬瘟热、传染性肝炎、细小病毒病和副流感\",\"接种建议\":\"6周龄或6周龄以上的健康犬首次免疫三，三针每针间隔3周。后续每年加强一针\"}},{\"vaccineType\":\"犬四联\",\"vaccineBrand\":\"卫佳伍\",\"specificationContentMap\":{\"适用宠物年龄\":\"6周龄或6周龄以上的健康犬\",\"疫苗作用\":\"预防犬的犬瘟热病毒病、犬细小病毒病、犬腺病毒|型引起的传染性肝炎、犬腺病毒II型引起的呼吸道病和犬副流感病毒引起的疾病\",\"接种建议\":\"6周龄或6周龄以上的健康犬首次免疫，三针每针间隔3周。后续每年加强一针\"}},{\"vaccineType\":\"犬八联\",\"vaccineBrand\":\"卫佳捌\",\"specificationContentMap\":{\"适用宠物年龄\":\"6周龄或6周龄以上的健康犬\",\"疫苗作用\":\"预防犬的犬瘟热，犬腺病毒1型引起的传染性肝炎，犬腺病毒2型引起的呼吸道病，犬细小病毒肠炎和犬副流感，犬冠状病毒病，以及犬钩端螺旋体和黄疸出血型钩端螺旋体引起的钩端螺旋体病\",\"接种建议\":\"6周龄或6周龄以上的健康犬首次免疫，三针每针间隔3周。后续每年加强一针\"}},{\"vaccineType\":\"狂犬疫苗\",\"specificationContentMap\":{\"适用宠物年龄\":\"3月龄以上\",\"疫苗作用\":\"预防犬、猫狂犬病\",\"接种建议\":\"首免一针，后续每年一针\"}}]";
        return JSON.parseArray(vaccineSpecificationModels, PetHospitalDealDetailSkuListAfterProcessingVPO.VaccineSpecificationModel.class);
    }

}

