package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealAdditionalProjectM;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * @author: xiongyonghong
 * @create: 2024-09-24
 * @description:
 **/
@RunWith(MockitoJUnitRunner.class)
public class CommonPhotoDealAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private CommonPhotoDealAttrVOListOpt.Config config;

    @InjectMocks
    private CommonPhotoDealAttrVOListOpt opt;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        opt = new CommonPhotoDealAttrVOListOpt();
    }

    @Test
    public void testComputeWhenConfigIsNull() throws Throwable {
        assertNull(opt.compute(context, param, null));
    }

    @Test
    public void testComputeWhenParamIsNull() throws Throwable {
        assertNull(opt.compute(context, null, config));
    }

    @Test
    public void testComputeWhenDealDetailDtoModelIsNull() throws Throwable {
        when(param.getDealDetailDtoModel()).thenReturn(null);
        List<CommonPhotoDealAttrVOListOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        attrListGroupModels.add(new CommonPhotoDealAttrVOListOpt.AttrListGroupModel());
        when(config.getAttrListGroupModels()).thenReturn(attrListGroupModels);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenDealAttrsIsNull() throws Throwable {
        when(param.getDealAttrs()).thenReturn(null);
        List<CommonPhotoDealAttrVOListOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        attrListGroupModels.add(new CommonPhotoDealAttrVOListOpt.AttrListGroupModel());
        when(config.getAttrListGroupModels()).thenReturn(attrListGroupModels);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenAttrListGroupModelsIsNull() throws Throwable {
        // Fix: Mock config.getAttrListGroupModels() to return an empty list instead of null
        when(config.getAttrListGroupModels()).thenReturn(new ArrayList<>());
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenAttrListGroupModelIsNull() throws Throwable {
        List<CommonPhotoDealAttrVOListOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        attrListGroupModels.add(null);
        when(config.getAttrListGroupModels()).thenReturn(attrListGroupModels);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testCommonPhotoDealAttrVOListOpt() throws Throwable {
        String configJson = "{\"attrListGroupModels\":[{\"groupName\":\"套餐服务\",\"attrListGroupModels2\":[{\"groupName\":\"拍摄服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"shootingDurationDecimal\"]}],\"attrModelList\":[{\"displayName\":\"拍摄张数\",\"attrNameList\":[\"photoCount\"]},{\"displayName\":\"可拍景点\",\"attrNameList\":[\"photoThemeCount\",\"positionName\",\"jingdianshuoming\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"含景点\",\"displayValue\":\"\"},{\"attrValue\":\"分别为\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"photoThemeCount\",\"displayFormat\":\"含%s景点\"},{\"attrName\":\"positionName\",\"displayFormat\":\"分别为%s\",\"attrValueReplaceModels\":[{\"preStr\":\"、\",\"str\":\"，\"}]}]}]},{\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"huazhuangshizhang\"]}],\"attrModelList\":[{\"displayName\":\"妆造套数\",\"attrNameList\":[\"dressNum\",\"huazhuangtaoshu\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\"},{\"attrValue\":\"化妆\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"服装%s\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s\"}]},{\"displayName\":\"化妆品牌\",\"attrNameList\":[\"huazhuangpinpai3\"]}]}],\"attrModelList\":[{\"displayName\":\"适用人群\",\"attrNameList\":[\"suitCrowds\"]},{\"displayName\":\"可拍风格\",\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\"},{\"attrValue\":\"2\",\"displayValue\":\"\"},{\"attrValue\":\"3\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"displayFormat\":\"%s风格任选\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":2}},{\"attrName\":\"photo_style\",\"displayFormat\":\"%d款风格任选\",\"displayCount\":true,\"attrValueSeperator\":\",\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":3}}]},{\"displayName\":\"成品交付\",\"attrNameList\":[\"intensiveRepairNum\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"底片不送\",\"displayValue\":\"\"},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\"},{\"attrValue\":\"精修\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCount\",\"displayFormat\":\"底片赠送%s\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"}}]}]},{\"groupName\":\"加项说明\",\"attrModelList\":[{\"displayName\":\"加人数\",\"attrNameList\":[\"AdditionalNumberOfShots\"]},{\"displayName\":\"加服装\",\"attrNameList\":[\"jiafuzhuangtaoshufeiyong\"]},{\"displayName\":\"加妆造\",\"attrNameList\":[\"jiazhuangzaotaoshufeiyong\"]},{\"displayName\":\"加精修\",\"attrNameList\":[\"PlusFinishingCost\"]},{\"displayName\":\"加底片\",\"attrNameList\":[\"PlusFilmFee\"]}]},{\"groupName\":\"额外说明\",\"attrModelList\":[{\"displayName\":\"化妆说明\",\"attrNameList\":[\"huazhuangbuchongxinxi\"]},{\"displayName\":\"门票说明\",\"attrNameList\":[\"isFree\",\"containTicketsFree\"],\"attrValueMapModels\":[{\"attrValue\":\"否\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"isFree\",\"displayFormat\":\"含工作人员门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}},{\"attrName\":\"containTicketsFree\",\"displayFormat\":\"含顾客门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}}]},{\"displayName\":\"其他说明\",\"attrNameList\":[\"postscript\"]}]},{\"groupName\":\"服务流程\",\"attrModelList\":[{\"displayName\":\"选片时间\",\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后%s\"}]},{\"displayName\":\"取片时间\",\"attrNameList\":[\"chupianshijian\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后%s\"}]}]}]}";
        config = JSON.parseObject(configJson, CommonPhotoDealAttrVOListOpt.Config.class);
        String dealDetailDtoModelJson = "{\"dealGroupId\":1030788246,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"55.0\",\"marketPrice\":\"100.0\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":2105871,\"name\":\"景点跟拍\",\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[{\"metaAttrId\":604,\"attrName\":\"suitCrowds\",\"chnName\":\"适用人群\",\"attrValue\":\"个人、情侣、儿童\",\"rawAttrValue\":\"个人、情侣、儿童\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2702,\"attrName\":\"shootingDurationDecimal\",\"chnName\":\"拍摄时长\",\"attrValue\":\"9小时\",\"rawAttrValue\":\"9\",\"unit\":\"小时\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":2225,\"attrName\":\"photoThemeCount\",\"chnName\":\"可拍景点数\",\"attrValue\":\"12个\",\"rawAttrValue\":\"12\",\"unit\":\"个\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1484,\"attrName\":\"positionName\",\"chnName\":\"可选拍摄景点\",\"attrValue\":\"大唐不夜城\",\"rawAttrValue\":\"大唐不夜城\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1386,\"attrName\":\"photoCount\",\"chnName\":\"拍摄张数\",\"attrValue\":\"10张\",\"rawAttrValue\":\"10\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum\",\"chnName\":\"精修张数\",\"attrValue\":\"8张\",\"rawAttrValue\":\"8\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166015,\"attrName\":\"IsTheFilmPresentedFree\",\"chnName\":\"底片是否赠送\",\"attrValue\":\"底片部分赠送\",\"rawAttrValue\":\"底片部分赠送\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2224,\"attrName\":\"photoPlateAttachCount\",\"chnName\":\"底片赠送张数\",\"attrValue\":\"11张\",\"rawAttrValue\":\"11\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"14套/人\",\"rawAttrValue\":\"14\",\"unit\":\"套/人\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166012,\"attrName\":\"IsMakeupProvided\",\"chnName\":\"是否含妆造\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1602,\"attrName\":\"isFree\",\"chnName\":\"含工作人员门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2810,\"attrName\":\"containTicketsFee\",\"chnName\":\"含顾客门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2725,\"attrName\":\"postscript\",\"chnName\":\"额外说明\",\"attrValue\":\"额外说明-景点跟拍\",\"rawAttrValue\":\"额外说明-景点跟拍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1535,\"attrName\":\"shootpersoncount\",\"chnName\":\"拍摄人数\",\"attrValue\":\"3人\",\"rawAttrValue\":\"3\",\"unit\":\"人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497432,\"attrName\":\"qipairenshu\",\"chnName\":\"起拍人数\",\"attrValue\":\"4人\",\"rawAttrValue\":\"4\",\"unit\":\"人\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1493687,\"attrName\":\"jingdianshuoming\",\"chnName\":\"景点说明\",\"attrValue\":\"夜景\",\"rawAttrValue\":\"夜景\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498837,\"attrName\":\"shifouhanfuzhuang\",\"chnName\":\"是否含服装\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497431,\"attrName\":\"fuzhuangleixing\",\"chnName\":\"服装类型\",\"attrValue\":\"西装、旗袍\",\"rawAttrValue\":\"西装、旗袍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497424,\"attrName\":\"huazhuangshizhang\",\"chnName\":\"化妆时长\",\"attrValue\":\"15分钟/套\",\"rawAttrValue\":\"15\",\"unit\":\"分钟/套\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497425,\"attrName\":\"huazhuangtaoshu\",\"chnName\":\"化妆套数\",\"attrValue\":\"16套/人\",\"rawAttrValue\":\"16\",\"unit\":\"套/人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498880,\"attrName\":\"huazhuangpinpai3\",\"chnName\":\"化妆品牌\",\"attrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"rawAttrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497434,\"attrName\":\"huazhuangbuchongxinxi\",\"chnName\":\"化妆补充信息\",\"attrValue\":\"补充信息-化妆\",\"rawAttrValue\":\"补充信息-化妆\",\"unit\":null,\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166024,\"attrName\":\"AdditionalNumberOfShots\",\"chnName\":\"加拍摄人数费用\",\"attrValue\":\"10元/人\",\"rawAttrValue\":\"10\",\"unit\":\"元/人\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166022,\"attrName\":\"PlusFinishingCost\",\"chnName\":\"加精修费用\",\"attrValue\":\"23元/张\",\"rawAttrValue\":\"23\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166023,\"attrName\":\"PlusFilmFee\",\"chnName\":\"加底片费用\",\"attrValue\":\"24元/张\",\"rawAttrValue\":\"24\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":1497439,\"attrName\":\"jiafuzhuangtaoshufeiyong\",\"chnName\":\"加服装套数费用\",\"attrValue\":\"25元/张\",\"rawAttrValue\":\"25\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1497440,\"attrName\":\"jiazhuangzaotaoshufeiyong\",\"chnName\":\"加妆造套数费用\",\"attrValue\":\"26元/张\",\"rawAttrValue\":\"26\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166020,\"attrName\":\"FinishedDeliverables\",\"chnName\":\"成品交付物\",\"attrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"rawAttrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2990,\"attrName\":\"selectPicTime\",\"chnName\":\"选片时间\",\"attrValue\":\"27天\",\"rawAttrValue\":\"27\",\"unit\":\"天\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1498855,\"attrName\":\"chupianshijian\",\"chnName\":\"出片时间\",\"attrValue\":\"28天\",\"rawAttrValue\":\"28\",\"unit\":\"天\",\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"}";
        DealDetailDtoModel dealDetailDtoModel = JSON.parseObject(dealDetailDtoModelJson, DealDetailDtoModel.class);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testCommonPhotoDealAttrVOListOptWithContainStaff() throws Throwable {
        String configJson = "{\"attrListGroupModels\":[{\"groupName\":\"套餐服务\",\"attrListGroupModels2\":[{\"attrModelList\":[{\"displayName\":\"拍摄天数\",\"attrNameList\":[\"photoDays\"]},{\"displayName\":\"可拍景点个数\",\"attrNameList\":[\"photoThemeCount\"],\"attrValueMapModels\":[{\"attrValue\":\"个\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"photoThemeCount\",\"displayFormat\":\"%s个\"}]},{\"displayName\":\"内景数量\",\"attrNameList\":[\"IsinteriorNum\",\"interiorNum\"],\"attrValueMapModels\":[{\"attrValue\":\"是\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含内景\",\"priority\":0},{\"attrValue\":\"个\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"interiorNum\",\"displayFormat\":\"%s\"}]},{\"displayName\":\"外景数量\",\"attrNameList\":[\"exteriorNum\",\"IsexteriorNum\"],\"attrValueMapModels\":[{\"attrValue\":\"个\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含外景\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"exteriorNum\",\"displayFormat\":\"%s\"}]},{\"displayName\":\"景点说明\",\"attrNameList\":[\"photoEnvInfo\"]},{\"displayName\":\"机票服务\",\"attrNameList\":[\"hasAirfare\"]},{\"displayName\":\"酒店服务\",\"attrNameList\":[\"hasHotel\",\"hotelLevel\"],\"attrValueMapModels\":[{\"attrValue\":\"含酒店\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"\",\"displayValue\":\"含酒店\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"hotelLevel\",\"displayFormat\":\"%s\",\"attrName2FilteredAttrValueMap\":{\"hasHotel\":\"含酒店\"}}]}],\"groupName\":\"目的地与场景\"},{\"attrModelList\":[{\"displayName\":\"拍摄张数\",\"attrNameList\":[\"photoCount\"],\"attrValueMapModels\":[{\"attrValue\":\"张\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"photoCount\",\"displayFormat\":\"%s\"}]},{\"displayName\":\"精修张数\",\"attrNameList\":[\"intensiveRepairNum\"],\"attrValueMapModels\":[{\"attrValue\":\"张\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"%s\"}]},{\"displayName\":\"底片赠送张数\",\"attrNameList\":[\"photoPlateAttachCount\",\"IsTheFilmPresentedFree\"],\"attrValueMapModels\":[{\"attrValue\":\"底片全送\",\"displayValue\":\"底片全送\",\"priority\":0},{\"attrValue\":\"底片不送\",\"displayValue\":\"底片不送\",\"priority\":0},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"photoPlateAttachCount\",\"displayFormat\":\"赠送%s张\"}]},{\"displayName\":\"成品交付物\",\"attrNameList\":[\"FinishedDeliverables\"],\"seperator\":\"；\"}],\"groupName\":\"套餐总览\"},{\"attrModelList\":[{\"displayName\":\"服装套数\",\"attrNameList\":[\"dressNum\",\"whetherToProvideClothing\"],\"attrValueMapModels\":[{\"attrValue\":\"不提供服装\",\"displayValue\":\"不提供服装\",\"priority\":0},{\"attrValue\":\"提供服装\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"套\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"%s套/人\"}]},{\"displayName\":\"妆造套数\",\"attrNameList\":[\"makeupCount\",\"IsMakeupProvided\"],\"attrValueMapModels\":[{\"displayValue\":\"不提供化妆\",\"priority\":0},{\"attrValue\":\"提供化妆\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"套/人\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"makeupCount\",\"displayFormat\":\"%s\"}]},{\"displayName\":\"化妆品牌\",\"attrNameList\":[\"huazhuangpinpai3\"],\"seperator\":\"、\"},{\"displayName\":\"拍摄包含人员\",\"attrNameList\":[\"ContainStaff\"],\"seperator\":\"、\"}],\"groupName\":\"化妆服务\"}]},{\"attrModelList\":[{\"displayName\":\"加精修\",\"attrNameList\":[\"PlusFinishingCost\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"PlusFinishingCost\",\"displayFormat\":\"%s元/张\"}]},{\"displayName\":\"加底片\",\"attrNameList\":[\"PlusFilmFee\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"PlusFilmFee\",\"displayFormat\":\"%s元/张\"}]},{\"displayName\":\"机票补贴\",\"attrNameList\":[\"airsubsidy\",\"hasAirfare\"],\"attrValueMapModels\":[{\"attrValue\":\"元\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"部分补贴\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"含机票\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"不含机票\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"airsubsidy\",\"displayFormat\":\"%s元\",\"attrName2FilteredAttrValueMap\":{\"hasAirfare\":\"部分补贴\"}}]},{\"displayName\":\"酒店补贴\",\"attrNameList\":[\"hotelsubsidy\",\"hasHotel\"],\"attrValueMapModels\":[{\"attrValue\":\"元\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"部分补贴\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"含酒店\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"不含酒店\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"hotelsubsidy\",\"displayFormat\":\"%s元\",\"attrName2FilteredAttrValueMap\":{\"hasAirfare\":\"部分补贴\"}}]}],\"groupName\":\"加项说明\"},{\"attrModelList\":[{\"displayName\":\"选片时间\",\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后约%s天\"}]},{\"displayName\":\"出片时间\",\"attrNameList\":[\"takePicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\",\"priority\":0}],\"attrFormatModels\":[{\"attrName\":\"takePicTime\",\"displayFormat\":\"拍摄后约%s天\"}]}],\"groupName\":\"服务流程\",\"videoModuleVO\":{\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\"}}],\"taiji\":false}";
        config = JSON.parseObject(configJson, CommonPhotoDealAttrVOListOpt.Config.class);
        String dealDetailDtoModelJson = "{\"dealGroupId\":1036698628,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"234.0\",\"marketPrice\":\"1234.0\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":848,\"name\":\"旅游婚纱照\",\"copies\":1,\"status\":10,\"attrItems\":[{\"metaAttrId\":1428,\"attrName\":\"photoDays\",\"chnName\":\"拍摄天数\",\"attrValue\":\"12\",\"rawAttrValue\":\"12\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1430,\"attrName\":\"hasAirfare\",\"chnName\":\"含机票\",\"attrValue\":\"含机票\",\"rawAttrValue\":\"含机票\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1431,\"attrName\":\"hasHotel\",\"chnName\":\"含酒店\",\"attrValue\":\"含酒店\",\"rawAttrValue\":\"含酒店\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1426,\"attrName\":\"photoEnvInfo\",\"chnName\":\"景点说明\",\"attrValue\":\"景点说明\\n景点说明\\n\",\"rawAttrValue\":\"景点说明\\n景点说明\\n\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1406,\"attrName\":\"makeupCount\",\"chnName\":\"化妆造型数量\",\"attrValue\":\"2\",\"rawAttrValue\":\"2\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1386,\"attrName\":\"photoCount\",\"chnName\":\"拍摄张数\",\"attrValue\":\"12\",\"rawAttrValue\":\"12\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum\",\"chnName\":\"精修张数\",\"attrValue\":\"23\",\"rawAttrValue\":\"23\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1419,\"attrName\":\"pickupMethod\",\"chnName\":\"取件方式\",\"attrValue\":\"上门自取\",\"rawAttrValue\":\"上门自取\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3756307,\"attrName\":\"hotelLevel\",\"chnName\":\"酒店级别\",\"attrValue\":\"四星级酒店\",\"rawAttrValue\":\"四星级酒店\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2225,\"attrName\":\"photoThemeCount\",\"chnName\":\"拍摄主题数量\",\"attrValue\":\"101\",\"rawAttrValue\":\"101\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166015,\"attrName\":\"IsTheFilmPresentedFree\",\"chnName\":\"是否提供原始底片\",\"attrValue\":\"底片部分赠送\",\"rawAttrValue\":\"底片部分赠送\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2224,\"attrName\":\"photoPlateAttachCount\",\"chnName\":\"底片赠送张数\",\"attrValue\":\"12\",\"rawAttrValue\":\"12\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166020,\"attrName\":\"FinishedDeliverables\",\"chnName\":\"成品交付物\",\"attrValue\":\"[{\\\"FinishedProductName\\\":\\\"相框\\\",\\\"chengpinshuliang\\\":\\\"1\\\"}]\",\"rawAttrValue\":\"[{\\\"FinishedProductName\\\":\\\"相框\\\",\\\"chengpinshuliang\\\":\\\"1\\\"}]\",\"valueType\":300,\"sequence\":0},{\"metaAttrId\":3755691,\"attrName\":\"ContainStaff\",\"chnName\":\"拍摄包含人员\",\"attrValue\":\"[{\\\"staffNum\\\":\\\"1\\\",\\\"staffType\\\":\\\"摄影师\\\"},{\\\"staffNum\\\":\\\"2\\\",\\\"staffType\\\":\\\"化妆师\\\"}]\",\"rawAttrValue\":\"[{\\\"staffNum\\\":\\\"1\\\",\\\"staffType\\\":\\\"摄影师\\\"},{\\\"staffNum\\\":\\\"2\\\",\\\"staffType\\\":\\\"化妆师\\\"}]\",\"valueType\":300,\"sequence\":0},{\"metaAttrId\":166010,\"attrName\":\"whetherToProvideClothing\",\"chnName\":\"是否提供服装\",\"attrValue\":\"提供服装\",\"rawAttrValue\":\"提供服装\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"2\",\"rawAttrValue\":\"2\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166012,\"attrName\":\"IsMakeupProvided\",\"chnName\":\"是否提供化妆\",\"attrValue\":\"提供化妆\",\"rawAttrValue\":\"提供化妆\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498880,\"attrName\":\"huazhuangpinpai3\",\"chnName\":\"化妆品牌\",\"attrValue\":\"阿玛尼、CPB\",\"rawAttrValue\":\"阿玛尼、CPB\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":166022,\"attrName\":\"PlusFinishingCost\",\"chnName\":\"加精修费用\",\"attrValue\":\"2\",\"rawAttrValue\":\"2\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166023,\"attrName\":\"PlusFilmFee\",\"chnName\":\"加底片费用\",\"attrValue\":\"2\",\"rawAttrValue\":\"2\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":2990,\"attrName\":\"selectPicTime\",\"chnName\":\"选片时间\",\"attrValue\":\"32\",\"rawAttrValue\":\"32\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":3756309,\"attrName\":\"getPicTime\",\"chnName\":\"取片时间\",\"attrValue\":\"1\",\"rawAttrValue\":\"1\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":2725,\"attrName\":\"postscript\",\"chnName\":\"额外说明\",\"attrValue\":\"额外说明额外说明\",\"rawAttrValue\":\"额外说明额外说明\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2730,\"attrName\":\"skuCateId\",\"chnName\":\"项目分类\",\"attrValue\":\"848\",\"rawAttrValue\":\"848\",\"valueType\":402,\"sequence\":0},{\"metaAttrId\":3886062,\"attrName\":\"IsinteriorNum\",\"chnName\":\"是否包含内景\",\"attrValue\":\"否\",\"rawAttrValue\":\"否\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3886063,\"attrName\":\"IsexteriorNum\",\"chnName\":\"是否包含外景\",\"attrValue\":\"否\",\"rawAttrValue\":\"否\",\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"}";
        DealDetailDtoModel dealDetailDtoModel = JSON.parseObject(dealDetailDtoModelJson, DealDetailDtoModel.class);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testCompute_TaijiLogic() {
        // 创建测试对象
        CommonPhotoDealAttrVOListOpt commonPhotoDealAttrVOListOpt = new CommonPhotoDealAttrVOListOpt();

        // 创建mock对象
        String configJson = "{\"attrListGroupModels\":[{\"groupName\":\"套餐服务\",\"attrListGroupModels2\":[{\"groupName\":\"拍摄服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"shootingDurationDecimal\"]}],\"attrModelList\":[{\"displayName\":\"拍摄张数\",\"attrNameList\":[\"photoCount\"]},{\"displayName\":\"可拍景点\",\"attrNameList\":[\"photoThemeCount\",\"positionName\",\"jingdianshuoming\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"含景点\",\"displayValue\":\"\"},{\"attrValue\":\"分别为\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"photoThemeCount\",\"displayFormat\":\"含%s景点\"},{\"attrName\":\"positionName\",\"displayFormat\":\"分别为%s\",\"attrValueReplaceModels\":[{\"preStr\":\"、\",\"str\":\"，\"}]}]}]},{\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"huazhuangshizhang\"]}],\"attrModelList\":[{\"displayName\":\"妆造套数\",\"attrNameList\":[\"dressNum\",\"huazhuangtaoshu\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\"},{\"attrValue\":\"化妆\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"服装%s\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s\"}]},{\"displayName\":\"化妆品牌\",\"attrNameList\":[\"huazhuangpinpai3\"]}]}],\"attrModelList\":[{\"displayName\":\"适用人群\",\"attrNameList\":[\"suitCrowds\"]},{\"displayName\":\"可拍风格\",\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\"},{\"attrValue\":\"2\",\"displayValue\":\"\"},{\"attrValue\":\"3\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"displayFormat\":\"%s风格任选\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":2}},{\"attrName\":\"photo_style\",\"displayFormat\":\"%d款风格任选\",\"displayCount\":true,\"attrValueSeperator\":\",\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":3}}]},{\"displayName\":\"成品交付\",\"attrNameList\":[\"intensiveRepairNum_Desc\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"底片不送\",\"displayValue\":\"\"},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\"},{\"attrValue\":\"精修\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCount\",\"displayFormat\":\"底片赠送%s\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"}}]}]},{\"groupName\":\"加项说明\",\"attrModelList\":[{\"displayName\":\"加人数\",\"attrNameList\":[\"AdditionalNumberOfShots\"]},{\"displayName\":\"加服装\",\"attrNameList\":[\"jiafuzhuangtaoshufeiyong\"]},{\"displayName\":\"加妆造\",\"attrNameList\":[\"jiazhuangzaotaoshufeiyong\"]},{\"displayName\":\"加精修\",\"attrNameList\":[\"PlusFinishingCost\"]},{\"displayName\":\"加底片\",\"attrNameList\":[\"PlusFilmFee\"]}]},{\"groupName\":\"额外说明\",\"attrModelList\":[{\"displayName\":\"化妆说明\",\"attrNameList\":[\"huazhuangbuchongxinxi\"]},{\"displayName\":\"门票说明\",\"attrNameList\":[\"isFree\",\"containTicketsFree\"],\"attrValueMapModels\":[{\"attrValue\":\"否\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"isFree\",\"displayFormat\":\"含工作人员门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}},{\"attrName\":\"containTicketsFree\",\"displayFormat\":\"含顾客门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}}]},{\"displayName\":\"其他说明\",\"attrNameList\":[\"postscript\"]}]},{\"groupName\":\"服务流程\",\"attrModelList\":[{\"displayName\":\"选片时间\",\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后%s\"}]},{\"displayName\":\"取片时间\",\"attrNameList\":[\"chupianshijian\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后%s\"}]}]}]}";
        config = JSON.parseObject(configJson, CommonPhotoDealAttrVOListOpt.Config.class);
        String dealDetailDtoModelJson = "{\"dealGroupId\":1030788246,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"55.0\",\"marketPrice\":\"100.0\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":2105871,\"name\":\"景点跟拍\",\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[{\"metaAttrId\":604,\"attrName\":\"suitCrowds\",\"chnName\":\"适用人群\",\"attrValue\":\"个人、情侣、儿童\",\"rawAttrValue\":\"个人、情侣、儿童\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2702,\"attrName\":\"shootingDurationDecimal\",\"chnName\":\"拍摄时长\",\"attrValue\":\"9小时\",\"rawAttrValue\":\"9\",\"unit\":\"小时\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":2225,\"attrName\":\"photoThemeCount\",\"chnName\":\"可拍景点数\",\"attrValue\":\"12个\",\"rawAttrValue\":\"12\",\"unit\":\"个\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1484,\"attrName\":\"positionName\",\"chnName\":\"可选拍摄景点\",\"attrValue\":\"大唐不夜城\",\"rawAttrValue\":\"大唐不夜城\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1386,\"attrName\":\"photoCount\",\"chnName\":\"拍摄张数\",\"attrValue\":\"10张\",\"rawAttrValue\":\"10\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum_Desc\",\"chnName\":\"精修张数\",\"attrValue\":\"8张\",\"rawAttrValue\":\"8\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166015,\"attrName\":\"IsTheFilmPresentedFree\",\"chnName\":\"底片是否赠送\",\"attrValue\":\"底片部分赠送\",\"rawAttrValue\":\"底片部分赠送\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2224,\"attrName\":\"photoPlateAttachCount\",\"chnName\":\"底片赠送张数\",\"attrValue\":\"11张\",\"rawAttrValue\":\"11\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"14套/人\",\"rawAttrValue\":\"14\",\"unit\":\"套/人\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166012,\"attrName\":\"IsMakeupProvided\",\"chnName\":\"是否含妆造\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1602,\"attrName\":\"isFree\",\"chnName\":\"含工作人员门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2810,\"attrName\":\"containTicketsFee\",\"chnName\":\"含顾客门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2725,\"attrName\":\"postscript\",\"chnName\":\"额外说明\",\"attrValue\":\"额外说明-景点跟拍\",\"rawAttrValue\":\"额外说明-景点跟拍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1535,\"attrName\":\"shootpersoncount\",\"chnName\":\"拍摄人数\",\"attrValue\":\"3人\",\"rawAttrValue\":\"3\",\"unit\":\"人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497432,\"attrName\":\"qipairenshu\",\"chnName\":\"起拍人数\",\"attrValue\":\"4人\",\"rawAttrValue\":\"4\",\"unit\":\"人\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1493687,\"attrName\":\"jingdianshuoming\",\"chnName\":\"景点说明\",\"attrValue\":\"夜景\",\"rawAttrValue\":\"夜景\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498837,\"attrName\":\"shifouhanfuzhuang\",\"chnName\":\"是否含服装\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497431,\"attrName\":\"fuzhuangleixing\",\"chnName\":\"服装类型\",\"attrValue\":\"西装、旗袍\",\"rawAttrValue\":\"西装、旗袍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497424,\"attrName\":\"huazhuangshizhang\",\"chnName\":\"化妆时长\",\"attrValue\":\"15分钟/套\",\"rawAttrValue\":\"15\",\"unit\":\"分钟/套\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497425,\"attrName\":\"huazhuangtaoshu\",\"chnName\":\"化妆套数\",\"attrValue\":\"16套/人\",\"rawAttrValue\":\"16\",\"unit\":\"套/人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498880,\"attrName\":\"huazhuangpinpai3\",\"chnName\":\"化妆品牌\",\"attrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"rawAttrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497434,\"attrName\":\"huazhuangbuchongxinxi\",\"chnName\":\"化妆补充信息\",\"attrValue\":\"补充信息-化妆\",\"rawAttrValue\":\"补充信息-化妆\",\"unit\":null,\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166024,\"attrName\":\"AdditionalNumberOfShots\",\"chnName\":\"加拍摄人数费用\",\"attrValue\":\"10元/人\",\"rawAttrValue\":\"10\",\"unit\":\"元/人\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166022,\"attrName\":\"PlusFinishingCost\",\"chnName\":\"加精修费用\",\"attrValue\":\"23元/张\",\"rawAttrValue\":\"23\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166023,\"attrName\":\"PlusFilmFee\",\"chnName\":\"加底片费用\",\"attrValue\":\"24元/张\",\"rawAttrValue\":\"24\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":1497439,\"attrName\":\"jiafuzhuangtaoshufeiyong\",\"chnName\":\"加服装套数费用\",\"attrValue\":\"25元/张\",\"rawAttrValue\":\"25\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1497440,\"attrName\":\"jiazhuangzaotaoshufeiyong\",\"chnName\":\"加妆造套数费用\",\"attrValue\":\"26元/张\",\"rawAttrValue\":\"26\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166020,\"attrName\":\"FinishedDeliverables\",\"chnName\":\"成品交付物\",\"attrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"grant\\\":\\\"10\\\"}]\",\"rawAttrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2990,\"attrName\":\"selectPicTime\",\"chnName\":\"选片时间\",\"attrValue\":\"27天\",\"rawAttrValue\":\"27\",\"unit\":\"天\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1498855,\"attrName\":\"chupianshijian\",\"chnName\":\"出片时间\",\"attrValue\":\"28天\",\"rawAttrValue\":\"28\",\"unit\":\"天\",\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"}";
        DealDetailDtoModel dealDetailDtoModel = JSON.parseObject(dealDetailDtoModelJson, DealDetailDtoModel.class);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);

        // 设置mock对象的行为
        DealDetailInfoModel dealDetailInfoModel = buildDealDetailInfoModel();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);

        // 调用compute方法
        List<DealDetailStructAttrModuleGroupModel> result = commonPhotoDealAttrVOListOpt.compute(context, param, config);

        // 验证config的setTaiji方法是否被调用，并且参数为true
        assertNotNull(result);
    }



    /**
     * 测试太极团单特殊逻辑处理
     */
    @Test
    public void testComputeTaijiSpecialLogic() {
        // arrange
        when(param.getDealDetailInfoModel()).thenReturn(buildDealDetailInfoModel());
        config.setAttrListGroupModels(new ArrayList<>());

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试个成品交付 - 个写老团单
     */
    @Test
    public void testComputePersonalFinishedDeliverables() {
        // arrange
        String configJson = "{\"attrListGroupModels\":[{\"groupName\":\"套餐服务\",\"attrListGroupModels2\":[{\"groupName\":\"拍摄服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"shootingDurationDecimal\"]}],\"attrModelList\":[{\"displayName\":\"拍摄张数\",\"attrNameList\":[\"photoCount\"]},{\"displayName\":\"可拍景点\",\"attrNameList\":[\"photoThemeCount\",\"positionName\",\"jingdianshuoming\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"含景点\",\"displayValue\":\"\"},{\"attrValue\":\"分别为\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"photoThemeCount\",\"displayFormat\":\"含%s景点\"},{\"attrName\":\"positionName\",\"displayFormat\":\"分别为%s\",\"attrValueReplaceModels\":[{\"preStr\":\"、\",\"str\":\"，\"}]}]}]},{\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"huazhuangshizhang\"]}],\"attrModelList\":[{\"displayName\":\"妆造套数\",\"attrNameList\":[\"dressNum\",\"huazhuangtaoshu\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\"},{\"attrValue\":\"化妆\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"服装%s\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s\"}]},{\"displayName\":\"化妆品牌\",\"attrNameList\":[\"huazhuangpinpai3\"]}]}],\"attrModelList\":[{\"displayName\":\"适用人群\",\"attrNameList\":[\"suitCrowds\"]},{\"displayName\":\"可拍风格\",\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\"},{\"attrValue\":\"2\",\"displayValue\":\"\"},{\"attrValue\":\"3\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"displayFormat\":\"%s风格任选\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":2}},{\"attrName\":\"photo_style\",\"displayFormat\":\"%d款风格任选\",\"displayCount\":true,\"attrValueSeperator\":\",\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":3}}]},{\"displayName\":\"成品交付\",\"attrNameList\":[\"intensiveRepairNum\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"底片不送\",\"displayValue\":\"\"},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\"},{\"attrValue\":\"精修\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCount\",\"displayFormat\":\"底片赠送%s\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"}}]}]},{\"groupName\":\"加项说明\",\"attrModelList\":[{\"displayName\":\"加人数\",\"attrNameList\":[\"AdditionalNumberOfShots\"]},{\"displayName\":\"加服装\",\"attrNameList\":[\"jiafuzhuangtaoshufeiyong\"]},{\"displayName\":\"加妆造\",\"attrNameList\":[\"jiazhuangzaotaoshufeiyong\"]},{\"displayName\":\"加精修\",\"attrNameList\":[\"PlusFinishingCost\"]},{\"displayName\":\"加底片\",\"attrNameList\":[\"PlusFilmFee\"]}]},{\"groupName\":\"额外说明\",\"attrModelList\":[{\"displayName\":\"化妆说明\",\"attrNameList\":[\"huazhuangbuchongxinxi\"]},{\"displayName\":\"门票说明\",\"attrNameList\":[\"isFree\",\"containTicketsFree\"],\"attrValueMapModels\":[{\"attrValue\":\"否\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"isFree\",\"displayFormat\":\"含工作人员门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}},{\"attrName\":\"containTicketsFree\",\"displayFormat\":\"含顾客门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}}]},{\"displayName\":\"其他说明\",\"attrNameList\":[\"postscript\"]}]},{\"groupName\":\"服务流程\",\"attrModelList\":[{\"displayName\":\"选片时间\",\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后%s\"}]},{\"displayName\":\"取片时间\",\"attrNameList\":[\"chupianshijian\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后%s\"}]}]}]}";
        config = JSON.parseObject(configJson, CommonPhotoDealAttrVOListOpt.Config.class);
        String dealDetailDtoModelJson = "{\"dealGroupId\":1030788246,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"55.0\",\"marketPrice\":\"100.0\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":2105871,\"name\":\"个人写真\",\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[{\"metaAttrId\":604,\"attrName\":\"suitCrowds\",\"chnName\":\"适用人群\",\"attrValue\":\"个人、情侣、儿童\",\"rawAttrValue\":\"个人、情侣、儿童\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2702,\"attrName\":\"shootingDurationDecimal\",\"chnName\":\"拍摄时长\",\"attrValue\":\"9小时\",\"rawAttrValue\":\"9\",\"unit\":\"小时\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":2225,\"attrName\":\"photoThemeCount\",\"chnName\":\"可拍景点数\",\"attrValue\":\"12个\",\"rawAttrValue\":\"12\",\"unit\":\"个\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1484,\"attrName\":\"positionName\",\"chnName\":\"可选拍摄景点\",\"attrValue\":\"大唐不夜城\",\"rawAttrValue\":\"大唐不夜城\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1386,\"attrName\":\"photoCount\",\"chnName\":\"拍摄张数\",\"attrValue\":\"10张\",\"rawAttrValue\":\"10\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum\",\"chnName\":\"精修张数\",\"attrValue\":\"8张\",\"rawAttrValue\":\"8\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166015,\"attrName\":\"IsTheFilmPresentedFree\",\"chnName\":\"底片是否赠送\",\"attrValue\":\"底片部分赠送\",\"rawAttrValue\":\"底片部分赠送\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2224,\"attrName\":\"photoPlateAttachCount\",\"chnName\":\"底片赠送张数\",\"attrValue\":\"11张\",\"rawAttrValue\":\"11\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"14套/人\",\"rawAttrValue\":\"14\",\"unit\":\"套/人\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166012,\"attrName\":\"IsMakeupProvided\",\"chnName\":\"是否含妆造\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1602,\"attrName\":\"isFree\",\"chnName\":\"含工作人员门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2810,\"attrName\":\"containTicketsFee\",\"chnName\":\"含顾客门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2725,\"attrName\":\"postscript\",\"chnName\":\"额外说明\",\"attrValue\":\"额外说明-景点跟拍\",\"rawAttrValue\":\"额外说明-景点跟拍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1535,\"attrName\":\"shootpersoncount\",\"chnName\":\"拍摄人数\",\"attrValue\":\"3人\",\"rawAttrValue\":\"3\",\"unit\":\"人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497432,\"attrName\":\"qipairenshu\",\"chnName\":\"起拍人数\",\"attrValue\":\"4人\",\"rawAttrValue\":\"4\",\"unit\":\"人\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1493687,\"attrName\":\"jingdianshuoming\",\"chnName\":\"景点说明\",\"attrValue\":\"夜景\",\"rawAttrValue\":\"夜景\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498837,\"attrName\":\"shifouhanfuzhuang\",\"chnName\":\"是否含服装\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497431,\"attrName\":\"fuzhuangleixing\",\"chnName\":\"服装类型\",\"attrValue\":\"西装、旗袍\",\"rawAttrValue\":\"西装、旗袍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497424,\"attrName\":\"huazhuangshizhang\",\"chnName\":\"化妆时长\",\"attrValue\":\"15分钟/套\",\"rawAttrValue\":\"15\",\"unit\":\"分钟/套\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497425,\"attrName\":\"huazhuangtaoshu\",\"chnName\":\"化妆套数\",\"attrValue\":\"16套/人\",\"rawAttrValue\":\"16\",\"unit\":\"套/人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498880,\"attrName\":\"huazhuangpinpai3\",\"chnName\":\"化妆品牌\",\"attrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"rawAttrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497434,\"attrName\":\"huazhuangbuchongxinxi\",\"chnName\":\"化妆补充信息\",\"attrValue\":\"补充信息-化妆\",\"rawAttrValue\":\"补充信息-化妆\",\"unit\":null,\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166024,\"attrName\":\"AdditionalNumberOfShots\",\"chnName\":\"加拍摄人数费用\",\"attrValue\":\"10元/人\",\"rawAttrValue\":\"10\",\"unit\":\"元/人\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166022,\"attrName\":\"PlusFinishingCost\",\"chnName\":\"加精修费用\",\"attrValue\":\"23元/张\",\"rawAttrValue\":\"23\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166023,\"attrName\":\"PlusFilmFee\",\"chnName\":\"加底片费用\",\"attrValue\":\"24元/张\",\"rawAttrValue\":\"24\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":1497439,\"attrName\":\"jiafuzhuangtaoshufeiyong\",\"chnName\":\"加服装套数费用\",\"attrValue\":\"25元/张\",\"rawAttrValue\":\"25\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1497440,\"attrName\":\"jiazhuangzaotaoshufeiyong\",\"chnName\":\"加妆造套数费用\",\"attrValue\":\"26元/张\",\"rawAttrValue\":\"26\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166020,\"attrName\":\"FinishedDeliverables\",\"chnName\":\"成品交付物\",\"attrValue\":\"[{\\\"FinishedProductName\\\":\\\"个写成品交付测试\\\",\\\"quantity\\\":\\\"10\\\"}]\",\"rawAttrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2990,\"attrName\":\"selectPicTime\",\"chnName\":\"选片时间\",\"attrValue\":\"27天\",\"rawAttrValue\":\"27\",\"unit\":\"天\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1498855,\"attrName\":\"chupianshijian\",\"chnName\":\"出片时间\",\"attrValue\":\"28天\",\"rawAttrValue\":\"28\",\"unit\":\"天\",\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"}";
        DealDetailDtoModel dealDetailDtoModel = JSON.parseObject(dealDetailDtoModelJson, DealDetailDtoModel.class);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(param.getDealDetailInfoModel()).thenReturn(buildDealDetailInfoModel());

        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        Assert.assertNotNull(result);
    }


    /**
     * 测试配置中包含特殊逻辑时的处理
     */
    @Test
    public void testComputeWithTaijiLogic() throws Throwable {
        // 配置Mock行为以模拟特殊逻辑的配置
        // 其他必要的Mock配置
        String configJson = "{\"attrListGroupModels\":[{\"groupName\":\"套餐服务\",\"attrListGroupModels2\":[{\"groupName\":\"拍摄服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"shootingDurationDecimal\"]}],\"attrModelList\":[{\"displayName\":\"拍摄张数\",\"attrNameList\":[\"photoCount\"]},{\"displayName\":\"可拍景点\",\"attrNameList\":[\"photoThemeCount\",\"positionName\",\"jingdianshuoming\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"含景点\",\"displayValue\":\"\"},{\"attrValue\":\"分别为\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"photoThemeCount\",\"displayFormat\":\"含%s景点\"},{\"attrName\":\"positionName\",\"displayFormat\":\"分别为%s\",\"attrValueReplaceModels\":[{\"preStr\":\"、\",\"str\":\"，\"}]}]}]},{\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"huazhuangshizhang\"]}],\"attrModelList\":[{\"displayName\":\"妆造套数\",\"attrNameList\":[\"dressNum\",\"huazhuangtaoshu\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\"},{\"attrValue\":\"化妆\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"服装%s\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s\"}]},{\"displayName\":\"化妆品牌\",\"attrNameList\":[\"huazhuangpinpai3\"]}]}],\"attrModelList\":[{\"displayName\":\"适用人群\",\"attrNameList\":[\"suitCrowds\"]},{\"displayName\":\"可拍风格\",\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\"},{\"attrValue\":\"2\",\"displayValue\":\"\"},{\"attrValue\":\"3\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"displayFormat\":\"%s风格任选\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":2}},{\"attrName\":\"photo_style\",\"displayFormat\":\"%d款风格任选\",\"displayCount\":true,\"attrValueSeperator\":\",\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":3}}]},{\"displayName\":\"成品交付\",\"attrNameList\":[\"intensiveRepairNum\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"底片不送\",\"displayValue\":\"\"},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\"},{\"attrValue\":\"精修\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCount\",\"displayFormat\":\"底片赠送%s\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"}}]}]},{\"groupName\":\"加项说明\",\"attrModelList\":[{\"displayName\":\"加人数\",\"attrNameList\":[\"AdditionalNumberOfShots\"]},{\"displayName\":\"加服装\",\"attrNameList\":[\"jiafuzhuangtaoshufeiyong\"]},{\"displayName\":\"加妆造\",\"attrNameList\":[\"jiazhuangzaotaoshufeiyong\"]},{\"displayName\":\"加精修\",\"attrNameList\":[\"PlusFinishingCost\"]},{\"displayName\":\"加底片\",\"attrNameList\":[\"PlusFilmFee\"]}]},{\"groupName\":\"额外说明\",\"attrModelList\":[{\"displayName\":\"化妆说明\",\"attrNameList\":[\"huazhuangbuchongxinxi\"]},{\"displayName\":\"门票说明\",\"attrNameList\":[\"isFree\",\"containTicketsFree\"],\"attrValueMapModels\":[{\"attrValue\":\"否\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"isFree\",\"displayFormat\":\"含工作人员门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}},{\"attrName\":\"containTicketsFree\",\"displayFormat\":\"含顾客门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}}]},{\"displayName\":\"其他说明\",\"attrNameList\":[\"postscript\"]}]},{\"groupName\":\"服务流程\",\"attrModelList\":[{\"displayName\":\"选片时间\",\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后%s\"}]},{\"displayName\":\"取片时间\",\"attrNameList\":[\"chupianshijian\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后%s\"}]}]}]}";
        config = JSON.parseObject(configJson, CommonPhotoDealAttrVOListOpt.Config.class);
        String dealDetailDtoModelJson = "{\"dealGroupId\":1030788246,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"55.0\",\"marketPrice\":\"100.0\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":2105871,\"name\":\"个人写真\",\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[{\"metaAttrId\":604,\"attrName\":\"suitCrowds\",\"chnName\":\"适用人群\",\"attrValue\":\"个人、情侣、儿童\",\"rawAttrValue\":\"个人、情侣、儿童\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2702,\"attrName\":\"shootingDurationDecimal\",\"chnName\":\"拍摄时长\",\"attrValue\":\"9小时\",\"rawAttrValue\":\"9\",\"unit\":\"小时\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":2225,\"attrName\":\"photoThemeCount\",\"chnName\":\"可拍景点数\",\"attrValue\":\"12个\",\"rawAttrValue\":\"12\",\"unit\":\"个\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1484,\"attrName\":\"positionName\",\"chnName\":\"可选拍摄景点\",\"attrValue\":\"大唐不夜城\",\"rawAttrValue\":\"大唐不夜城\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1386,\"attrName\":\"photoCount\",\"chnName\":\"拍摄张数\",\"attrValue\":\"10张\",\"rawAttrValue\":\"10\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum\",\"chnName\":\"精修张数\",\"attrValue\":\"8张\",\"rawAttrValue\":\"8\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166015,\"attrName\":\"IsTheFilmPresentedFree\",\"chnName\":\"底片是否赠送\",\"attrValue\":\"底片部分赠送\",\"rawAttrValue\":\"底片部分赠送\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2224,\"attrName\":\"photoPlateAttachCount\",\"chnName\":\"底片赠送张数\",\"attrValue\":\"11张\",\"rawAttrValue\":\"11\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"14套/人\",\"rawAttrValue\":\"14\",\"unit\":\"套/人\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166012,\"attrName\":\"IsMakeupProvided\",\"chnName\":\"是否含妆造\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1602,\"attrName\":\"isFree\",\"chnName\":\"含工作人员门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2810,\"attrName\":\"containTicketsFee\",\"chnName\":\"含顾客门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2725,\"attrName\":\"postscript\",\"chnName\":\"额外说明\",\"attrValue\":\"额外说明-景点跟拍\",\"rawAttrValue\":\"额外说明-景点跟拍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1535,\"attrName\":\"shootpersoncount\",\"chnName\":\"拍摄人数\",\"attrValue\":\"3人\",\"rawAttrValue\":\"3\",\"unit\":\"人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497432,\"attrName\":\"qipairenshu\",\"chnName\":\"起拍人数\",\"attrValue\":\"4人\",\"rawAttrValue\":\"4\",\"unit\":\"人\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1493687,\"attrName\":\"jingdianshuoming\",\"chnName\":\"景点说明\",\"attrValue\":\"夜景\",\"rawAttrValue\":\"夜景\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498837,\"attrName\":\"shifouhanfuzhuang\",\"chnName\":\"是否含服装\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497431,\"attrName\":\"fuzhuangleixing\",\"chnName\":\"服装类型\",\"attrValue\":\"西装、旗袍\",\"rawAttrValue\":\"西装、旗袍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497424,\"attrName\":\"huazhuangshizhang\",\"chnName\":\"化妆时长\",\"attrValue\":\"15分钟/套\",\"rawAttrValue\":\"15\",\"unit\":\"分钟/套\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497425,\"attrName\":\"huazhuangtaoshu\",\"chnName\":\"化妆套数\",\"attrValue\":\"16套/人\",\"rawAttrValue\":\"16\",\"unit\":\"套/人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498880,\"attrName\":\"huazhuangpinpai3\",\"chnName\":\"化妆品牌\",\"attrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"rawAttrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497434,\"attrName\":\"huazhuangbuchongxinxi\",\"chnName\":\"化妆补充信息\",\"attrValue\":\"补充信息-化妆\",\"rawAttrValue\":\"补充信息-化妆\",\"unit\":null,\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166024,\"attrName\":\"AdditionalNumberOfShots\",\"chnName\":\"加拍摄人数费用\",\"attrValue\":\"10元/人\",\"rawAttrValue\":\"10\",\"unit\":\"元/人\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166022,\"attrName\":\"PlusFinishingCost\",\"chnName\":\"加精修费用\",\"attrValue\":\"23元/张\",\"rawAttrValue\":\"23\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166023,\"attrName\":\"PlusFilmFee\",\"chnName\":\"加底片费用\",\"attrValue\":\"24元/张\",\"rawAttrValue\":\"24\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":1497439,\"attrName\":\"jiafuzhuangtaoshufeiyong\",\"chnName\":\"加服装套数费用\",\"attrValue\":\"25元/张\",\"rawAttrValue\":\"25\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1497440,\"attrName\":\"jiazhuangzaotaoshufeiyong\",\"chnName\":\"加妆造套数费用\",\"attrValue\":\"26元/张\",\"rawAttrValue\":\"26\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166020,\"attrName\":\"FinishedDeliverables\",\"chnName\":\"成品交付物\",\"attrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"rawAttrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2990,\"attrName\":\"selectPicTime\",\"chnName\":\"选片时间\",\"attrValue\":\"27天\",\"rawAttrValue\":\"27\",\"unit\":\"天\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1498855,\"attrName\":\"chupianshijian\",\"chnName\":\"出片时间\",\"attrValue\":\"28天\",\"rawAttrValue\":\"28\",\"unit\":\"天\",\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"}";
        DealDetailDtoModel dealDetailDtoModel = JSON.parseObject(dealDetailDtoModelJson, DealDetailDtoModel.class);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(param.getDealDetailInfoModel()).thenReturn(buildDealDetailInfoModel());

        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // 断言结果符合特殊逻辑处理的预期
        assertNotNull(result);
        // 具体的断言逻辑，根据特殊逻辑的预期结果进行断言
    }

    private DealDetailInfoModel buildDealDetailInfoModel() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();

// 设置基本类型和String类型的字段
        dealDetailInfoModel.setDealId(123456);
        dealDetailInfoModel.setDesc("这是一个团购详情的描述信息");
        dealDetailInfoModel.setSalePrice("99.99");
        dealDetailInfoModel.setMarketPrice("199.99");
        dealDetailInfoModel.setDealTitle("特价团购");
        dealDetailInfoModel.setUnifyProduct(true);
        dealDetailInfoModel.setTradeType(1);

// 设置DealDetailDtoModel对象
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
// 假设DealDetailDtoModel有一个setTitle方法
        dealDetailDtoModel.setTitle("团购详情SKU结构化模型标题");
        dealDetailInfoModel.setDealDetailDtoModel(dealDetailDtoModel);

// 设置ProductSkuCategoryModel列表
        List<ProductSkuCategoryModel> productCategories = new ArrayList<>();
        ProductSkuCategoryModel categoryModel = new ProductSkuCategoryModel();
// 假设ProductSkuCategoryModel有一个setName方法
        categoryModel.setCnName("美食");
        productCategories.add(categoryModel);
        dealDetailInfoModel.setProductCategories(productCategories);

// 设置AttrM列表
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("属性名称");
        attrM.setValue("属性值");
        dealAttrs.add(attrM);
        dealDetailInfoModel.setDealAttrs(dealAttrs);

// 设置UniformStructContentModel列表
        List<UniformStructContentModel> dealModuleAttrs = new ArrayList<>();
        UniformStructContentModel uniformStructContentModel = new UniformStructContentModel();
// 假设UniformStructContentModel有一个setContent方法
        uniformStructContentModel.setData("模块内容");
        dealModuleAttrs.add(uniformStructContentModel);
        dealDetailInfoModel.setDealModuleAttrs(dealModuleAttrs);

// 设置StandardServiceProjectDTO对象
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();

        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);

// 设置DealAdditionalProjectM列表
        List<DealAdditionalProjectM> additionalProjectList = new ArrayList<>();
        DealAdditionalProjectM additionalProjectM = new DealAdditionalProjectM();
        additionalProjectList.add(additionalProjectM);
        dealDetailInfoModel.setAdditionalProjectList(additionalProjectList);
        return dealDetailInfoModel;

    }

    @Test
    public void testComputeWithVideo2() throws Throwable {
        // 配置Mock行为以模拟特殊逻辑的配置
        // 其他必要的Mock配置
        String configJson = "{\"attrListGroupModels\":[{\"groupName\":\"套餐服务\",\"attrListGroupModels2\":[{\"groupName\":\"拍摄服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"shootingDurationDecimal\"]}],\"attrModelList\":[{\"displayName\":\"拍摄张数\",\"attrNameList\":[\"photoCount\"]},{\"displayName\":\"可拍景点\",\"attrNameList\":[\"photoThemeCount\",\"positionName\",\"jingdianshuoming\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"含景点\",\"displayValue\":\"\"},{\"attrValue\":\"分别为\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"photoThemeCount\",\"displayFormat\":\"含%s景点\"},{\"attrName\":\"positionName\",\"displayFormat\":\"分别为%s\",\"attrValueReplaceModels\":[{\"preStr\":\"、\",\"str\":\"，\"}]}]}]},{\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"huazhuangshizhang\"]}],\"attrModelList\":[{\"displayName\":\"妆造套数\",\"attrNameList\":[\"dressNum\",\"huazhuangtaoshu\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\"},{\"attrValue\":\"化妆\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"服装%s\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s\"}]},{\"displayName\":\"化妆品牌\",\"attrNameList\":[\"huazhuangpinpai3\"]}]}],\"attrModelList\":[{\"displayName\":\"适用人群\",\"attrNameList\":[\"suitCrowds\"]},{\"displayName\":\"可拍风格\",\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\"},{\"attrValue\":\"2\",\"displayValue\":\"\"},{\"attrValue\":\"3\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"displayFormat\":\"%s风格任选\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":2}},{\"attrName\":\"photo_style\",\"displayFormat\":\"%d款风格任选\",\"displayCount\":true,\"attrValueSeperator\":\",\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":3}}]},{\"displayName\":\"成品交付\",\"attrNameList\":[\"intensiveRepairNum\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"底片不送\",\"displayValue\":\"\"},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\"},{\"attrValue\":\"精修\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCount\",\"displayFormat\":\"底片赠送%s\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"}}]}]},{\"groupName\":\"加项说明\",\"attrModelList\":[{\"displayName\":\"加人数\",\"attrNameList\":[\"AdditionalNumberOfShots\"]},{\"displayName\":\"加服装\",\"attrNameList\":[\"jiafuzhuangtaoshufeiyong\"]},{\"displayName\":\"加妆造\",\"attrNameList\":[\"jiazhuangzaotaoshufeiyong\"]},{\"displayName\":\"加精修\",\"attrNameList\":[\"PlusFinishingCost\"]},{\"displayName\":\"加底片\",\"attrNameList\":[\"PlusFilmFee\"]}]},{\"groupName\":\"额外说明\",\"attrModelList\":[{\"displayName\":\"化妆说明\",\"attrNameList\":[\"huazhuangbuchongxinxi\"]},{\"displayName\":\"门票说明\",\"attrNameList\":[\"isFree\",\"containTicketsFree\"],\"attrValueMapModels\":[{\"attrValue\":\"否\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"isFree\",\"displayFormat\":\"含工作人员门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}},{\"attrName\":\"containTicketsFree\",\"displayFormat\":\"含顾客门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}}]},{\"displayName\":\"其他说明\",\"attrNameList\":[\"postscript\"]}]},{\"groupName\":\"服务流程\", \"videoModuleVO\":{\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\", \"desc\":\"https://p0.meituan.net/ingee/41b0d10606ed3b7b0060438e345b5eaf10168.png\"},\"attrModelList\":[{\"displayName\":\"选片时间\",\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后%s\"}]},{\"displayName\":\"取片时间\",\"attrNameList\":[\"chupianshijian\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后%s\"}]}]}]}";
        config = JSON.parseObject(configJson, CommonPhotoDealAttrVOListOpt.Config.class);
        String dealDetailDtoModelJson = "{\"dealGroupId\":1030788246,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"55.0\",\"marketPrice\":\"100.0\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":2105871,\"name\":\"个人写真\",\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[{\"metaAttrId\":604,\"attrName\":\"suitCrowds\",\"chnName\":\"适用人群\",\"attrValue\":\"个人、情侣、儿童\",\"rawAttrValue\":\"个人、情侣、儿童\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2702,\"attrName\":\"shootingDurationDecimal\",\"chnName\":\"拍摄时长\",\"attrValue\":\"9小时\",\"rawAttrValue\":\"9\",\"unit\":\"小时\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":2225,\"attrName\":\"photoThemeCount\",\"chnName\":\"可拍景点数\",\"attrValue\":\"12个\",\"rawAttrValue\":\"12\",\"unit\":\"个\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1484,\"attrName\":\"positionName\",\"chnName\":\"可选拍摄景点\",\"attrValue\":\"大唐不夜城\",\"rawAttrValue\":\"大唐不夜城\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1386,\"attrName\":\"photoCount\",\"chnName\":\"拍摄张数\",\"attrValue\":\"10张\",\"rawAttrValue\":\"10\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum\",\"chnName\":\"精修张数\",\"attrValue\":\"0张\",\"rawAttrValue\":\"0\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166015,\"attrName\":\"IsTheFilmPresentedFree\",\"chnName\":\"底片是否赠送\",\"attrValue\":\"底片部分赠送\",\"rawAttrValue\":\"底片部分赠送\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2224,\"attrName\":\"photoPlateAttachCount\",\"chnName\":\"底片赠送张数\",\"attrValue\":\"11张\",\"rawAttrValue\":\"11\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"14套/人\",\"rawAttrValue\":\"14\",\"unit\":\"套/人\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166012,\"attrName\":\"IsMakeupProvided\",\"chnName\":\"是否含妆造\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1602,\"attrName\":\"isFree\",\"chnName\":\"含工作人员门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2810,\"attrName\":\"containTicketsFee\",\"chnName\":\"含顾客门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2725,\"attrName\":\"postscript\",\"chnName\":\"额外说明\",\"attrValue\":\"额外说明-景点跟拍\",\"rawAttrValue\":\"额外说明-景点跟拍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1535,\"attrName\":\"shootpersoncount\",\"chnName\":\"拍摄人数\",\"attrValue\":\"3人\",\"rawAttrValue\":\"3\",\"unit\":\"人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497432,\"attrName\":\"qipairenshu\",\"chnName\":\"起拍人数\",\"attrValue\":\"4人\",\"rawAttrValue\":\"4\",\"unit\":\"人\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1493687,\"attrName\":\"jingdianshuoming\",\"chnName\":\"景点说明\",\"attrValue\":\"夜景\",\"rawAttrValue\":\"夜景\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498837,\"attrName\":\"shifouhanfuzhuang\",\"chnName\":\"是否含服装\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497431,\"attrName\":\"fuzhuangleixing\",\"chnName\":\"服装类型\",\"attrValue\":\"西装、旗袍\",\"rawAttrValue\":\"西装、旗袍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497424,\"attrName\":\"huazhuangshizhang\",\"chnName\":\"化妆时长\",\"attrValue\":\"15分钟/套\",\"rawAttrValue\":\"15\",\"unit\":\"分钟/套\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497425,\"attrName\":\"huazhuangtaoshu\",\"chnName\":\"化妆套数\",\"attrValue\":\"16套/人\",\"rawAttrValue\":\"16\",\"unit\":\"套/人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498880,\"attrName\":\"huazhuangpinpai3\",\"chnName\":\"化妆品牌\",\"attrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"rawAttrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497434,\"attrName\":\"huazhuangbuchongxinxi\",\"chnName\":\"化妆补充信息\",\"attrValue\":\"补充信息-化妆\",\"rawAttrValue\":\"补充信息-化妆\",\"unit\":null,\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166024,\"attrName\":\"AdditionalNumberOfShots\",\"chnName\":\"加拍摄人数费用\",\"attrValue\":\"10元/人\",\"rawAttrValue\":\"10\",\"unit\":\"元/人\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166022,\"attrName\":\"PlusFinishingCost\",\"chnName\":\"加精修费用\",\"attrValue\":\"23元/张\",\"rawAttrValue\":\"23\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166023,\"attrName\":\"PlusFilmFee\",\"chnName\":\"加底片费用\",\"attrValue\":\"24元/张\",\"rawAttrValue\":\"24\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":1497439,\"attrName\":\"jiafuzhuangtaoshufeiyong\",\"chnName\":\"加服装套数费用\",\"attrValue\":\"25元/张\",\"rawAttrValue\":\"25\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1497440,\"attrName\":\"jiazhuangzaotaoshufeiyong\",\"chnName\":\"加妆造套数费用\",\"attrValue\":\"26元/张\",\"rawAttrValue\":\"26\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166020,\"attrName\":\"FinishedDeliverables\",\"chnName\":\"成品交付物\",\"attrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"rawAttrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2990,\"attrName\":\"selectPicTime\",\"chnName\":\"选片时间\",\"attrValue\":\"27天\",\"rawAttrValue\":\"27\",\"unit\":\"天\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1498855,\"attrName\":\"chupianshijian\",\"chnName\":\"出片时间\",\"attrValue\":\"28天\",\"rawAttrValue\":\"28\",\"unit\":\"天\",\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"}";
        DealDetailDtoModel dealDetailDtoModel = JSON.parseObject(dealDetailDtoModelJson, DealDetailDtoModel.class);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(param.getDealDetailInfoModel()).thenReturn(buildDealDetailInfoModel());

        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // 断言结果符合特殊逻辑处理的预期
        assertNotNull(result);
        // 具体的断言逻辑，根据特殊逻辑的预期结果进行断言
    }

    @Test
    public void testAttrName2JoinValues() throws Throwable {
        // 配置Mock行为以模拟特殊逻辑的配置
        // 其他必要的Mock配置
        String configJson = "{\"attrListGroupModels\":[{\"groupName\":\"套餐服务\",\"attrListGroupModels2\":[{\"groupName\":\"拍摄服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"shootingDurationDecimal\"]}],\"attrModelList\":[{\"displayName\":\"拍摄张数\",\"attrNameList\":[\"photoCount\"]},{\"displayName\":\"可拍景点\",\"attrNameList\":[\"photoThemeCount\",\"positionName\",\"jingdianshuoming\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"含景点\",\"displayValue\":\"\"},{\"attrValue\":\"分别为\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"photoThemeCount\",\"displayFormat\":\"含%s景点\"},{\"attrName\":\"positionName\",\"displayFormat\":\"分别为%s\",\"attrValueReplaceModels\":[{\"preStr\":\"、\",\"str\":\"，\"}]}]}]},{\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrNameList\":[\"huazhuangshizhang\"]}],\"attrModelList\":[{\"displayName\":\"妆造套数\",\"attrNameList\":[\"dressNum\",\"huazhuangtaoshu\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\"},{\"attrValue\":\"化妆\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"服装%s\", \"attrName2JoinValues\":\"fuzhuangleixing\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s\"}]},{\"displayName\":\"化妆品牌\",\"attrNameList\":[\"huazhuangpinpai3\"]}]}],\"attrModelList\":[{\"displayName\":\"适用人群\",\"attrNameList\":[\"suitCrowds\"]},{\"displayName\":\"可拍风格\",\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\"},{\"attrValue\":\"2\",\"displayValue\":\"\"},{\"attrValue\":\"3\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"displayFormat\":\"%s风格任选\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":2}},{\"attrName\":\"photo_style\",\"displayFormat\":\"%d款风格任选\",\"displayCount\":true,\"attrValueSeperator\":\",\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":3}}]},{\"displayName\":\"成品交付\",\"attrNameList\":[\"intensiveRepairNum\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"seperator\":\"、\",\"attrValueMapModels\":[{\"attrValue\":\"底片不送\",\"displayValue\":\"\"},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\"},{\"attrValue\":\"精修\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCount\",\"displayFormat\":\"底片赠送%s\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"}}]}]},{\"groupName\":\"加项说明\",\"attrModelList\":[{\"displayName\":\"加人数\",\"attrNameList\":[\"AdditionalNumberOfShots\"]},{\"displayName\":\"加服装\",\"attrNameList\":[\"jiafuzhuangtaoshufeiyong\"]},{\"displayName\":\"加妆造\",\"attrNameList\":[\"jiazhuangzaotaoshufeiyong\"]},{\"displayName\":\"加精修\",\"attrNameList\":[\"PlusFinishingCost\"]},{\"displayName\":\"加底片\",\"attrNameList\":[\"PlusFilmFee\"]}]},{\"groupName\":\"额外说明\",\"attrModelList\":[{\"displayName\":\"化妆说明\",\"attrNameList\":[\"huazhuangbuchongxinxi\"]},{\"displayName\":\"门票说明\",\"attrNameList\":[\"isFree\",\"containTicketsFree\"],\"attrValueMapModels\":[{\"attrValue\":\"否\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"isFree\",\"displayFormat\":\"含工作人员门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}},{\"attrName\":\"containTicketsFree\",\"displayFormat\":\"含顾客门票\",\"attrName2FilteredAttrValueMap\":{\"isFree\":\"是\"}}]},{\"displayName\":\"其他说明\",\"attrNameList\":[\"postscript\"]}]},{\"groupName\":\"服务流程\", \"videoModuleVO\":{\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\", \"desc\":\"https://p0.meituan.net/ingee/41b0d10606ed3b7b0060438e345b5eaf10168.png\"},\"attrModelList\":[{\"displayName\":\"选片时间\",\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后%s\"}]},{\"displayName\":\"取片时间\",\"attrNameList\":[\"chupianshijian\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后\",\"displayValue\":\"\"}],\"attrFormatModels\":[{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后%s\"}]}]}]}";
        config = JSON.parseObject(configJson, CommonPhotoDealAttrVOListOpt.Config.class);
        String dealDetailDtoModelJson = "{\"dealGroupId\":1030788246,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"55.0\",\"marketPrice\":\"100.0\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":2105871,\"name\":\"个人写真\",\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[{\"metaAttrId\":604,\"attrName\":\"suitCrowds\",\"chnName\":\"适用人群\",\"attrValue\":\"个人、情侣、儿童\",\"rawAttrValue\":\"个人、情侣、儿童\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2702,\"attrName\":\"shootingDurationDecimal\",\"chnName\":\"拍摄时长\",\"attrValue\":\"9小时\",\"rawAttrValue\":\"9\",\"unit\":\"小时\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":2225,\"attrName\":\"photoThemeCount\",\"chnName\":\"可拍景点数\",\"attrValue\":\"12个\",\"rawAttrValue\":\"12\",\"unit\":\"个\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1484,\"attrName\":\"positionName\",\"chnName\":\"可选拍摄景点\",\"attrValue\":\"大唐不夜城\",\"rawAttrValue\":\"大唐不夜城\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1386,\"attrName\":\"photoCount\",\"chnName\":\"拍摄张数\",\"attrValue\":\"10张\",\"rawAttrValue\":\"10\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum\",\"chnName\":\"精修张数\",\"attrValue\":\"0张\",\"rawAttrValue\":\"0\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166015,\"attrName\":\"IsTheFilmPresentedFree\",\"chnName\":\"底片是否赠送\",\"attrValue\":\"底片部分赠送\",\"rawAttrValue\":\"底片部分赠送\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2224,\"attrName\":\"photoPlateAttachCount\",\"chnName\":\"底片赠送张数\",\"attrValue\":\"11张\",\"rawAttrValue\":\"11\",\"unit\":\"张\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"14套/人\",\"rawAttrValue\":\"14\",\"unit\":\"套/人\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":166012,\"attrName\":\"IsMakeupProvided\",\"chnName\":\"是否含妆造\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1602,\"attrName\":\"isFree\",\"chnName\":\"含工作人员门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2810,\"attrName\":\"containTicketsFee\",\"chnName\":\"含顾客门票\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2725,\"attrName\":\"postscript\",\"chnName\":\"额外说明\",\"attrValue\":\"额外说明-景点跟拍\",\"rawAttrValue\":\"额外说明-景点跟拍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1535,\"attrName\":\"shootpersoncount\",\"chnName\":\"拍摄人数\",\"attrValue\":\"3人\",\"rawAttrValue\":\"3\",\"unit\":\"人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497432,\"attrName\":\"qipairenshu\",\"chnName\":\"起拍人数\",\"attrValue\":\"4人\",\"rawAttrValue\":\"4\",\"unit\":\"人\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1493687,\"attrName\":\"jingdianshuoming\",\"chnName\":\"景点说明\",\"attrValue\":\"夜景\",\"rawAttrValue\":\"夜景\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498837,\"attrName\":\"shifouhanfuzhuang\",\"chnName\":\"是否含服装\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497431,\"attrName\":\"fuzhuangleixing\",\"chnName\":\"服装类型\",\"attrValue\":\"西装、旗袍\",\"rawAttrValue\":\"西装、旗袍\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497424,\"attrName\":\"huazhuangshizhang\",\"chnName\":\"化妆时长\",\"attrValue\":\"15分钟/套\",\"rawAttrValue\":\"15\",\"unit\":\"分钟/套\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497425,\"attrName\":\"huazhuangtaoshu\",\"chnName\":\"化妆套数\",\"attrValue\":\"16套/人\",\"rawAttrValue\":\"16\",\"unit\":\"套/人\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1498880,\"attrName\":\"huazhuangpinpai3\",\"chnName\":\"化妆品牌\",\"attrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"rawAttrValue\":\"圣罗兰、Bobbi Brown、兰蔻\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1497434,\"attrName\":\"huazhuangbuchongxinxi\",\"chnName\":\"化妆补充信息\",\"attrValue\":\"补充信息-化妆\",\"rawAttrValue\":\"补充信息-化妆\",\"unit\":null,\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166024,\"attrName\":\"AdditionalNumberOfShots\",\"chnName\":\"加拍摄人数费用\",\"attrValue\":\"10元/人\",\"rawAttrValue\":\"10\",\"unit\":\"元/人\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166022,\"attrName\":\"PlusFinishingCost\",\"chnName\":\"加精修费用\",\"attrValue\":\"23元/张\",\"rawAttrValue\":\"23\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":166023,\"attrName\":\"PlusFilmFee\",\"chnName\":\"加底片费用\",\"attrValue\":\"24元/张\",\"rawAttrValue\":\"24\",\"unit\":\"元/张\",\"valueType\":400,\"sequence\":0},{\"metaAttrId\":1497439,\"attrName\":\"jiafuzhuangtaoshufeiyong\",\"chnName\":\"加服装套数费用\",\"attrValue\":\"25元/张\",\"rawAttrValue\":\"25\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":1497440,\"attrName\":\"jiazhuangzaotaoshufeiyong\",\"chnName\":\"加妆造套数费用\",\"attrValue\":\"26元/张\",\"rawAttrValue\":\"26\",\"unit\":\"元/张\",\"valueType\":501,\"sequence\":0},{\"metaAttrId\":166020,\"attrName\":\"FinishedDeliverables\",\"chnName\":\"成品交付物\",\"attrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"rawAttrValue\":\"[{\\\"FinishedProductName\\\":\\\"真皮\\\",\\\"chengpinshuliang\\\":\\\"10\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2990,\"attrName\":\"selectPicTime\",\"chnName\":\"选片时间\",\"attrValue\":\"27天\",\"rawAttrValue\":\"27\",\"unit\":\"天\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1498855,\"attrName\":\"chupianshijian\",\"chnName\":\"出片时间\",\"attrValue\":\"28天\",\"rawAttrValue\":\"28\",\"unit\":\"天\",\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"}";
        DealDetailDtoModel dealDetailDtoModel = JSON.parseObject(dealDetailDtoModelJson, DealDetailDtoModel.class);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(param.getDealDetailInfoModel()).thenReturn(buildDealDetailInfoModel());

        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // 断言结果符合特殊逻辑处理的预期
        assertNotNull(result);
        // 具体的断言逻辑，根据特殊逻辑的预期结果进行断言
    }

    @Test
    public void testGetDealDetailAttrFromAttrs() throws Throwable {

        String jsonStr = "{\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1033909062,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1033909062,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"800.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"1000.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104880,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u513f\\\\\\\\u7ae5\\\\\\\\u6444\\\\\\\\u5f71\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":166010,\\\\\\\"attrName\\\\\\\":\\\\\\\"whetherToProvideClothing\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1382,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5957\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5957\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166013,\\\\\\\"attrName\\\\\\\":\\\\\\\"ShootingMethod\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u65b9\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5230\\\\\\\\u5e97\\\\\\\\u62cd\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5230\\\\\\\\u5e97\\\\\\\\u62cd\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166014,\\\\\\\"attrName\\\\\\\":\\\\\\\"ShootingScene\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u573a\\\\\\\\u666f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5185\\\\\\\\u666f+\\\\\\\\u5916\\\\\\\\u666f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5185\\\\\\\\u666f+\\\\\\\\u5916\\\\\\\\u666f\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1387,\\\\\\\"attrName\\\\\\\":\\\\\\\"intensiveRepairNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u7cbe\\\\\\\\u4fee\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"5\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166015,\\\\\\\"attrName\\\\\\\":\\\\\\\"IsTheFilmPresentedFree\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u8d60\\\\\\\\u9001\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u6307\\\\\\\\u5b9a\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u6307\\\\\\\\u5b9a\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2224,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoPlateAttachCount\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\\u8d60\\\\\\\\u9001\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"6\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1386,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoCount\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"10\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":572,\\\\\\\"attrName\\\\\\\":\\\\\\\"duration\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\\u5206\\\\\\\\u949f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5206\\\\\\\\u949f\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1591,\\\\\\\"attrName\\\\\\\":\\\\\\\"startAge\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6700\\\\\\\\u5c0f\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u5e74\\\\\\\\u9f84\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5c81\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5c81\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1592,\\\\\\\"attrName\\\\\\\":\\\\\\\"endAge\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6700\\\\\\\\u5927\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u5e74\\\\\\\\u9f84\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"18\\\\\\\\u5c81\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"18\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5c81\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166032,\\\\\\\"attrName\\\\\\\":\\\\\\\"WithParentChildPhoto\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u53ef\\\\\\\\u62cd\\\\\\\\u4eb2\\\\\\\\u5b50\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u62cd\\\\\\\\u4eb2\\\\\\\\u5b50\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u62cd\\\\\\\\u4eb2\\\\\\\\u5b50\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166017,\\\\\\\"attrName\\\\\\\":\\\\\\\"GroupPhotoThemeSets\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\\u4e3b\\\\\\\\u9898\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\\u5957\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5957\\\\\\\",\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166018,\\\\\\\"attrName\\\\\\\":\\\\\\\"WhetherToProvidePhotoClothing\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\" \\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7b80\\\\\\\\u5355\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\uff08\\\\\\\\u5982\\\\\\\\u767dT\\\\\\\\uff09\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7b80\\\\\\\\u5355\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\uff08\\\\\\\\u5982\\\\\\\\u767dT\\\\\\\\uff09\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166019,\\\\\\\"attrName\\\\\\\":\\\\\\\"ProvidePhotoMakeup\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7b80\\\\\\\\u5355\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7b80\\\\\\\\u5355\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2356,\\\\\\\"attrName\\\\\\\":\\\\\\\"hasGuider\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5f15\\\\\\\\u5bfc\\\\\\\\u5e08\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5f15\\\\\\\\u5bfc\\\\\\\\u5e08\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5f15\\\\\\\\u5bfc\\\\\\\\u5e08\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166020,\\\\\\\"attrName\\\\\\\":\\\\\\\"FinishedDeliverables\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6210\\\\\\\\u54c1\\\\\\\\u4ea4\\\\\\\\u4ed8\\\\\\\\u7269\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"quantity\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"1\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"FinishedProductName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u76f8\\\\\\\\u518c\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"quantity\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"FinishedProductName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u76f8\\\\\\\\u6846\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"quantity\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"1\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"FinishedProductName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u76f8\\\\\\\\u518c\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"quantity\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"FinishedProductName\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u76f8\\\\\\\\u6846\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166022,\\\\\\\"attrName\\\\\\\":\\\\\\\"PlusFinishingCost\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u52a0\\\\\\\\u7cbe\\\\\\\\u4fee\\\\\\\\u8d39\\\\\\\\u7528\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"50\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"50\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":400,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166023,\\\\\\\"attrName\\\\\\\":\\\\\\\"PlusFilmFee\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u52a0\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\\u8d39\\\\\\\\u7528\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"30\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"30\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":400,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2990,\\\\\\\"attrName\\\\\\\":\\\\\\\"selectPicTime\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9009\\\\\\\\u7247\\\\\\\\u65f6\\\\\\\\u95f4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2984,\\\\\\\"attrName\\\\\\\":\\\\\\\"takePicTime\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u53d6\\\\\\\\u7247\\\\\\\\u65f6\\\\\\\\u95f4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"14\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"14\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2725,\\\\\\\"attrName\\\\\\\":\\\\\\\"postscript\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\\u4e0b\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\\u4e0b\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1033909062,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"800.0\\\",\\\"marketPrice\\\":\\\"1000.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104880,\\\"name\\\":\\\"\\\\u513f\\\\u7ae5\\\\u6444\\\\u5f71\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":166010,\\\"attrName\\\":\\\"whetherToProvideClothing\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1382,\\\"attrName\\\":\\\"dressNum\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\",\\\"rawAttrValue\\\":\\\"2\\\",\\\"unit\\\":\\\"\\\\u5957\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166013,\\\"attrName\\\":\\\"ShootingMethod\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u5230\\\\u5e97\\\\u62cd\\\",\\\"rawAttrValue\\\":\\\"\\\\u5230\\\\u5e97\\\\u62cd\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166014,\\\"attrName\\\":\\\"ShootingScene\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u5185\\\\u666f+\\\\u5916\\\\u666f\\\",\\\"rawAttrValue\\\":\\\"\\\\u5185\\\\u666f+\\\\u5916\\\\u666f\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1387,\\\"attrName\\\":\\\"intensiveRepairNum\\\",\\\"chnName\\\":\\\"\\\\u7cbe\\\\u4fee\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"5\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"5\\\",\\\"unit\\\":\\\"\\\\u5f20\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166015,\\\"attrName\\\":\\\"IsTheFilmPresentedFree\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u662f\\\\u5426\\\\u8d60\\\\u9001\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u6307\\\\u5b9a\\\\u5f20\\\\u6570\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u6307\\\\u5b9a\\\\u5f20\\\\u6570\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2224,\\\"attrName\\\":\\\"photoPlateAttachCount\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u8d60\\\\u9001\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"6\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"6\\\",\\\"unit\\\":\\\"\\\\u5f20\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1386,\\\"attrName\\\":\\\"photoCount\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"10\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"10\\\",\\\"unit\\\":\\\"\\\\u5f20\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":572,\\\"attrName\\\":\\\"duration\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\\u5206\\\\u949f\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":\\\"\\\\u5206\\\\u949f\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1591,\\\"attrName\\\":\\\"startAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5c0f\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"2\\\\u5c81\\\",\\\"rawAttrValue\\\":\\\"2\\\",\\\"unit\\\":\\\"\\\\u5c81\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1592,\\\"attrName\\\":\\\"endAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5927\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"18\\\\u5c81\\\",\\\"rawAttrValue\\\":\\\"18\\\",\\\"unit\\\":\\\"\\\\u5c81\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166032,\\\"attrName\\\":\\\"WithParentChildPhoto\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"attrValue\\\":\\\"\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"rawAttrValue\\\":\\\"\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166017,\\\"attrName\\\":\\\"GroupPhotoThemeSets\\\",\\\"chnName\\\":\\\"\\\\u5408\\\\u5f71\\\\u4e3b\\\\u9898\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\\u5957\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":\\\"\\\\u5957\\\",\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166018,\\\"attrName\\\":\\\"WhetherToProvidePhotoClothing\\\",\\\"chnName\\\":\\\" \\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7b80\\\\u5355\\\\u670d\\\\u88c5\\\\uff08\\\\u5982\\\\u767dT\\\\uff09\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7b80\\\\u5355\\\\u670d\\\\u88c5\\\\uff08\\\\u5982\\\\u767dT\\\\uff09\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7b80\\\\u5355\\\\u5986\\\\u9020\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7b80\\\\u5355\\\\u5986\\\\u9020\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2356,\\\"attrName\\\":\\\"hasGuider\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166020,\\\"attrName\\\":\\\"FinishedDeliverables\\\",\\\"chnName\\\":\\\"\\\\u6210\\\\u54c1\\\\u4ea4\\\\u4ed8\\\\u7269\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"quantity\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"\\\\u76f8\\\\u518c\\\\\\\"},{\\\\\\\"quantity\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"\\\\u76f8\\\\u6846\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"quantity\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"\\\\u76f8\\\\u518c\\\\\\\"},{\\\\\\\"quantity\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"\\\\u76f8\\\\u6846\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166022,\\\"attrName\\\":\\\"PlusFinishingCost\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u7cbe\\\\u4fee\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"50\\\\u5143/\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"50\\\",\\\"unit\\\":\\\"\\\\u5143/\\\\u5f20\\\",\\\"valueType\\\":400,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166023,\\\"attrName\\\":\\\"PlusFilmFee\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u5e95\\\\u7247\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"30\\\\u5143/\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"30\\\",\\\"unit\\\":\\\"\\\\u5143/\\\\u5f20\\\",\\\"valueType\\\":400,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2990,\\\"attrName\\\":\\\"selectPicTime\\\",\\\"chnName\\\":\\\"\\\\u9009\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"7\\\",\\\"rawAttrValue\\\":\\\"7\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2984,\\\"attrName\\\":\\\"takePicTime\\\",\\\"chnName\\\":\\\"\\\\u53d6\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"14\\\",\\\"rawAttrValue\\\":\\\"14\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2725,\\\"attrName\\\":\\\"postscript\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u4e0b\\\",\\\"rawAttrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u4e0b\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104880,\\\"cnName\\\":\\\"\\\\u513f\\\\u7ae5\\\\u6444\\\\u5f71\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1033909062,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"800.0\\\",\\\"marketPrice\\\":\\\"1000.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104880,\\\"name\\\":\\\"\\\\u513f\\\\u7ae5\\\\u6444\\\\u5f71\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":166010,\\\"attrName\\\":\\\"whetherToProvideClothing\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\"},{\\\"metaAttrId\\\":1382,\\\"attrName\\\":\\\"dressNum\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\"},{\\\"metaAttrId\\\":166013,\\\"attrName\\\":\\\"ShootingMethod\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u5230\\\\u5e97\\\\u62cd\\\"},{\\\"metaAttrId\\\":166014,\\\"attrName\\\":\\\"ShootingScene\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u5185\\\\u666f+\\\\u5916\\\\u666f\\\"},{\\\"metaAttrId\\\":1387,\\\"attrName\\\":\\\"intensiveRepairNum\\\",\\\"chnName\\\":\\\"\\\\u7cbe\\\\u4fee\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"5\\\\u5f20\\\"},{\\\"metaAttrId\\\":166015,\\\"attrName\\\":\\\"IsTheFilmPresentedFree\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u662f\\\\u5426\\\\u8d60\\\\u9001\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u6307\\\\u5b9a\\\\u5f20\\\\u6570\\\"},{\\\"metaAttrId\\\":2224,\\\"attrName\\\":\\\"photoPlateAttachCount\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u8d60\\\\u9001\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"6\\\\u5f20\\\"},{\\\"metaAttrId\\\":1386,\\\"attrName\\\":\\\"photoCount\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"10\\\\u5f20\\\"},{\\\"metaAttrId\\\":572,\\\"attrName\\\":\\\"duration\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\\u5206\\\\u949f\\\"},{\\\"metaAttrId\\\":1591,\\\"attrName\\\":\\\"startAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5c0f\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"2\\\\u5c81\\\"},{\\\"metaAttrId\\\":1592,\\\"attrName\\\":\\\"endAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5927\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"18\\\\u5c81\\\"},{\\\"metaAttrId\\\":166032,\\\"attrName\\\":\\\"WithParentChildPhoto\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"attrValue\\\":\\\"\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\"},{\\\"metaAttrId\\\":166017,\\\"attrName\\\":\\\"GroupPhotoThemeSets\\\",\\\"chnName\\\":\\\"\\\\u5408\\\\u5f71\\\\u4e3b\\\\u9898\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\\u5957\\\"},{\\\"metaAttrId\\\":166018,\\\"attrName\\\":\\\"WhetherToProvidePhotoClothing\\\",\\\"chnName\\\":\\\" \\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7b80\\\\u5355\\\\u670d\\\\u88c5\\\\uff08\\\\u5982\\\\u767dT\\\\uff09\\\"},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7b80\\\\u5355\\\\u5986\\\\u9020\\\"},{\\\"metaAttrId\\\":2356,\\\"attrName\\\":\\\"hasGuider\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\"},{\\\"metaAttrId\\\":166020,\\\"attrName\\\":\\\"FinishedDeliverables\\\",\\\"chnName\\\":\\\"\\\\u6210\\\\u54c1\\\\u4ea4\\\\u4ed8\\\\u7269\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"quantity\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"\\\\u76f8\\\\u518c\\\\\\\"},{\\\\\\\"quantity\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"\\\\u76f8\\\\u6846\\\\\\\"}]\\\"},{\\\"metaAttrId\\\":166022,\\\"attrName\\\":\\\"PlusFinishingCost\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u7cbe\\\\u4fee\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"50\\\\u5143/\\\\u5f20\\\"},{\\\"metaAttrId\\\":166023,\\\"attrName\\\":\\\"PlusFilmFee\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u5e95\\\\u7247\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"30\\\\u5143/\\\\u5f20\\\"},{\\\"metaAttrId\\\":2990,\\\"attrName\\\":\\\"selectPicTime\\\",\\\"chnName\\\":\\\"\\\\u9009\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"7\\\"},{\\\"metaAttrId\\\":2984,\\\"attrName\\\":\\\"takePicTime\\\",\\\"chnName\\\":\\\"\\\\u53d6\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"14\\\"},{\\\"metaAttrId\\\":2725,\\\"attrName\\\":\\\"postscript\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u4e0b\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"团购详情\\\",\\\"content\\\":[{\\\"type\\\":\\\"uniform-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":1000.0,\\\"salePrice\\\":800.0,\\\"groups\\\":[{\\\"optionalCount\\\":0,\\\"units\\\":[{\\\"skuCateId\\\":2104880,\\\"projectName\\\":\\\"儿童摄影\\\",\\\"amount\\\":1,\\\"attrValues\\\":{\\\"startAge\\\":\\\"2\\\",\\\"PlusFinishingCost\\\":\\\"50\\\",\\\"Supplementary_makeup_infor\\\":\\\"补充信息说明下\\\",\\\"ProvidePhotoMakeup\\\":\\\"提供简单妆造\\\",\\\"numberscenes_shot\\\":\\\"4\\\",\\\"postscript\\\":\\\"额外说明下\\\",\\\"IsMakeupProvided\\\":\\\"提供化妆\\\",\\\"photoCount\\\":\\\"10\\\",\\\"duration\\\":\\\"60\\\",\\\"dressNum\\\":\\\"2\\\",\\\"takePicTime\\\":\\\"14\\\",\\\"Makeup_brand\\\":\\\"圣罗兰、TomFord、Dior、自定义1、自定义2\\\",\\\"clothing_additional_parents\\\":\\\"是\\\",\\\"whetherToProvideClothing\\\":\\\"提供服装\\\",\\\"makeup_additional_parents\\\":\\\"提供化妆\\\",\\\"Makeup_duration\\\":\\\"30\\\",\\\"shootpersoncount\\\":\\\"1\\\",\\\"Fee_for_additional_parents\\\":\\\"55\\\",\\\"Fee_for_additional_children\\\":\\\"\\\",\\\"hasGuider\\\":\\\"提供引导师\\\",\\\"photoPlateAttachCount\\\":\\\"6\\\",\\\"WithParentChildPhoto\\\":\\\"可拍亲子合影\\\",\\\"GroupPhotoThemeSets\\\":\\\"1\\\",\\\"photoEnvInfo\\\":\\\"白天、自定义1、自定义2、自定义3\\\",\\\"IsTheFilmPresentedFree\\\":\\\"提供指定张数\\\",\\\"PlusFilmFee\\\":\\\"30\\\",\\\"skuCateId\\\":\\\"2104880\\\",\\\"endAge\\\":\\\"18\\\",\\\"Num_makeupsets\\\":\\\"1\\\",\\\"WhetherToProvidePhotoClothing\\\":\\\"提供简单服装（如白T）\\\",\\\"intensiveRepairNum\\\":\\\"5\\\",\\\"FinishedDeliverables\\\":\\\"[{\\\\\\\"quantity\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"相册\\\\\\\"},{\\\\\\\"quantity\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"FinishedProductName\\\\\\\":\\\\\\\"相框\\\\\\\"}]\\\",\\\"selectPicTime\\\":\\\"7\\\",\\\"ShootingMethod\\\":\\\"到店拍\\\",\\\"ShootingScene\\\":\\\"内景+外景\\\"}}]}]}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":1000.0,\\\"groups\\\":[{\\\"units\\\":[{\\\"amount\\\":1,\\\"serviceItemName\\\":\\\"儿童摄影\\\",\\\"serviceItemValue\\\":{\\\"objectVersion\\\":8,\\\"objectValues\\\":{\\\"startAge\\\":\\\"2\\\",\\\"PlusFinishingCost\\\":\\\"50\\\",\\\"Supplementary_makeup_infor\\\":\\\"补充信息说明下\\\",\\\"ProvidePhotoMakeup\\\":\\\"提供简单妆造\\\",\\\"numberscenes_shot\\\":\\\"4\\\",\\\"postscript\\\":\\\"额外说明下\\\",\\\"IsMakeupProvided\\\":\\\"提供化妆\\\",\\\"photoCount\\\":10,\\\"duration\\\":\\\"60\\\",\\\"dressNum\\\":\\\"2\\\",\\\"takePicTime\\\":\\\"14\\\",\\\"Makeup_brand\\\":[\\\"圣罗兰\\\",\\\"TomFord\\\",\\\"Dior\\\",\\\"自定义1\\\",\\\"自定义2\\\"],\\\"clothing_additional_parents\\\":\\\"是\\\",\\\"whetherToProvideClothing\\\":\\\"提供服装\\\",\\\"makeup_additional_parents\\\":\\\"提供化妆\\\",\\\"Makeup_duration\\\":\\\"30\\\",\\\"shootpersoncount\\\":\\\"1\\\",\\\"Fee_for_additional_parents\\\":\\\"55\\\",\\\"Fee_for_additional_children\\\":\\\"\\\",\\\"hasGuider\\\":\\\"提供引导师\\\",\\\"photoPlateAttachCount\\\":6,\\\"WithParentChildPhoto\\\":\\\"可拍亲子合影\\\",\\\"GroupPhotoThemeSets\\\":\\\"1\\\",\\\"photoEnvInfo\\\":[\\\"白天\\\",\\\"自定义1\\\",\\\"自定义2\\\",\\\"自定义3\\\"],\\\"IsTheFilmPresentedFree\\\":\\\"提供指定张数\\\",\\\"PlusFilmFee\\\":\\\"30\\\",\\\"skuCateId\\\":2104880,\\\"endAge\\\":\\\"18\\\",\\\"Num_makeupsets\\\":\\\"1\\\",\\\"WhetherToProvidePhotoClothing\\\":\\\"提供简单服装（如白T）\\\",\\\"intensiveRepairNum\\\":\\\"5\\\",\\\"FinishedDeliverables\\\":[{\\\"quantity\\\":1,\\\"FinishedProductName\\\":\\\"相册\\\"},{\\\"quantity\\\":2,\\\"FinishedProductName\\\":\\\"相框\\\"}],\\\"selectPicTime\\\":\\\"7\\\",\\\"ShootingMethod\\\":\\\"到店拍\\\",\\\"ShootingScene\\\":\\\"内景+外景\\\"},\\\"objectId\\\":21633965}}],\\\"optionalCount\\\":0}]}}]}\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"}],\"dealDetailDtoModel\":{\"dealGroupId\":1033909062,\"skuUniStructuredDto\":{\"marketPrice\":\"1000.0\",\"mustGroups\":[{\"skuItems\":[{\"attrItems\":[{\"attrName\":\"whetherToProvideClothing\",\"attrValue\":\"提供服装\",\"chnName\":\"是否提供服装\",\"metaAttrId\":166010,\"rawAttrValue\":\"提供服装\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"dressNum\",\"attrValue\":\"2套\",\"chnName\":\"服装套数\",\"metaAttrId\":1382,\"rawAttrValue\":\"2\",\"sequence\":0,\"unit\":\"套\",\"valueType\":401},{\"attrName\":\"ShootingMethod\",\"attrValue\":\"到店拍\",\"chnName\":\"拍摄方式\",\"metaAttrId\":166013,\"rawAttrValue\":\"到店拍\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"ShootingScene\",\"attrValue\":\"内景+外景\",\"chnName\":\"拍摄场景\",\"metaAttrId\":166014,\"rawAttrValue\":\"内景+外景\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"intensiveRepairNum\",\"attrValue\":\"5张\",\"chnName\":\"精修张数\",\"metaAttrId\":1387,\"rawAttrValue\":\"5\",\"sequence\":0,\"unit\":\"张\",\"valueType\":401},{\"attrName\":\"IsTheFilmPresentedFree\",\"attrValue\":\"提供指定张数\",\"chnName\":\"底片是否赠送\",\"metaAttrId\":166015,\"rawAttrValue\":\"提供指定张数\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"photoPlateAttachCount\",\"attrValue\":\"6张\",\"chnName\":\"底片赠送张数\",\"metaAttrId\":2224,\"rawAttrValue\":\"6\",\"sequence\":0,\"unit\":\"张\",\"valueType\":401},{\"attrName\":\"photoCount\",\"attrValue\":\"10张\",\"chnName\":\"拍摄张数\",\"metaAttrId\":1386,\"rawAttrValue\":\"10\",\"sequence\":0,\"unit\":\"张\",\"valueType\":401},{\"attrName\":\"duration\",\"attrValue\":\"60分钟\",\"chnName\":\"拍摄时长\",\"metaAttrId\":572,\"rawAttrValue\":\"60\",\"sequence\":0,\"unit\":\"分钟\",\"valueType\":401},{\"attrName\":\"startAge\",\"attrValue\":\"2岁\",\"chnName\":\"最小适用年龄\",\"metaAttrId\":1591,\"rawAttrValue\":\"2\",\"sequence\":0,\"unit\":\"岁\",\"valueType\":401},{\"attrName\":\"endAge\",\"attrValue\":\"18岁\",\"chnName\":\"最大适用年龄\",\"metaAttrId\":1592,\"rawAttrValue\":\"18\",\"sequence\":0,\"unit\":\"岁\",\"valueType\":401},{\"attrName\":\"WithParentChildPhoto\",\"attrValue\":\"可拍亲子合影\",\"chnName\":\"是否可拍亲子合影\",\"metaAttrId\":166032,\"rawAttrValue\":\"可拍亲子合影\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"GroupPhotoThemeSets\",\"attrValue\":\"1套\",\"chnName\":\"合影主题套数\",\"metaAttrId\":166017,\"rawAttrValue\":\"1\",\"sequence\":0,\"unit\":\"套\",\"valueType\":500},{\"attrName\":\"WhetherToProvidePhotoClothing\",\"attrValue\":\"提供简单服装（如白T）\",\"chnName\":\" 是否提供合影服装\",\"metaAttrId\":166018,\"rawAttrValue\":\"提供简单服装（如白T）\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"ProvidePhotoMakeup\",\"attrValue\":\"提供简单妆造\",\"chnName\":\"是否提供合影妆造\",\"metaAttrId\":166019,\"rawAttrValue\":\"提供简单妆造\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"hasGuider\",\"attrValue\":\"提供引导师\",\"chnName\":\"是否提供引导师\",\"metaAttrId\":2356,\"rawAttrValue\":\"提供引导师\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"FinishedDeliverables\",\"attrValue\":\"[{\\\"quantity\\\":\\\"1\\\",\\\"FinishedProductName\\\":\\\"相册\\\"},{\\\"quantity\\\":\\\"2\\\",\\\"FinishedProductName\\\":\\\"相框\\\"}]\",\"chnName\":\"成品交付物\",\"metaAttrId\":166020,\"rawAttrValue\":\"[{\\\"quantity\\\":\\\"1\\\",\\\"FinishedProductName\\\":\\\"相册\\\"},{\\\"quantity\\\":\\\"2\\\",\\\"FinishedProductName\\\":\\\"相框\\\"}]\",\"sequence\":0,\"valueType\":300},{\"attrName\":\"PlusFinishingCost\",\"attrValue\":\"50元/张\",\"chnName\":\"加精修费用\",\"metaAttrId\":166022,\"rawAttrValue\":\"50\",\"sequence\":0,\"unit\":\"元/张\",\"valueType\":400},{\"attrName\":\"PlusFilmFee\",\"attrValue\":\"30元/张\",\"chnName\":\"加底片费用\",\"metaAttrId\":166023,\"rawAttrValue\":\"30\",\"sequence\":0,\"unit\":\"元/张\",\"valueType\":400},{\"attrName\":\"selectPicTime\",\"attrValue\":\"7\",\"chnName\":\"选片时间\",\"metaAttrId\":2990,\"rawAttrValue\":\"7\",\"sequence\":0,\"valueType\":401},{\"attrName\":\"takePicTime\",\"attrValue\":\"14\",\"chnName\":\"取片时间\",\"metaAttrId\":2984,\"rawAttrValue\":\"14\",\"sequence\":0,\"valueType\":401},{\"attrName\":\"postscript\",\"attrValue\":\"额外说明下\",\"chnName\":\"额外说明\",\"metaAttrId\":2725,\"rawAttrValue\":\"额外说明下\",\"sequence\":0,\"valueType\":500}],\"copies\":1,\"name\":\"儿童摄影\",\"productCategory\":2104880,\"skuId\":0,\"status\":10}]}],\"optionalGroups\":[],\"salePrice\":\"800.0\"},\"structType\":\"uniform-structure-table\",\"title\":\"团购详情\"},\"dealDetailInfoModel\":{\"additionalProjectList\":[],\"dealAttrs\":[{\"$ref\":\"$.dealAttrs[0]\"},{\"$ref\":\"$.dealAttrs[1]\"},{\"$ref\":\"$.dealAttrs[2]\"}],\"dealDetailDtoModel\":{\"$ref\":\"$.dealDetailDtoModel\"},\"dealId\":1033909062,\"productCategories\":[{\"cnName\":\"儿童摄影\",\"productCategoryId\":2104880}],\"unifyProduct\":true},\"productCategories\":[{\"$ref\":\"$.dealDetailInfoModel.productCategories[0]\"}]}";
        param = JSON.parseObject(jsonStr, DealAttrVOListVP.Param.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrListGroupModels2\":[{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"shootpersoncount\",\"displayFormat\":\"%s人\"}],\"attrNameList\":[\"PhotoKidsType\",\"shootpersoncount\"],\"displayName\":\"拍摄人数\"},{\"attrFormatModels\":[{\"attrName\":\"photoCount\",\"displayFormat\":\"%s张\"}],\"attrNameList\":[\"photoCount\"],\"attrValueMapModels\":[{\"attrValue\":\"张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"拍摄张数\"},{\"attrNameList\":[\"WithParentChildPhoto\",\"parents_and_children\"],\"attrValueMapModels\":[{\"attrValue\":\"可拍亲子合影\",\"displayValue\":\"含合影\",\"priority\":0},{\"attrValue\":\"不可拍亲子合影\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"有无合影\"},{\"attrNameList\":[\"hasGuider\",\"guide_provided\"],\"displayName\":\"辅助拍摄\"},{\"attrFormatModels\":[{\"attrName\":\"numberscenes_shot\",\"displayFormat\":\"含%s个景点\"}],\"attrNameList\":[\"ShootingScene\",\"numberscenes_shot\",\"photoEnvInfo\"],\"attrValueMapModels\":[{\"attrValue\":\"含个景点\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍场景\",\"seperator\":\"、\"}],\"groupName\":\"拍摄服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"儿童服装%s套/人\"},{\"attrName\":\"dressNumOld\",\"displayFormat\":\"儿童服装%s\"},{\"attrName\":\"Num_makeupsets\",\"displayFormat\":\"化妆%s套/人\"},{\"attrName\":\"WhetherToProvidePhotoClothingOld\",\"displayFormat\":\"家长%s\"},{\"attrName\":\"clothing_for_group\",\"displayFormat\":\"家长%s\"}],\"attrNameList\":[\"dressNum\",\"Num_makeupsets\",\"WhetherToProvidePhotoClothing\",\"clothing_for_group\",\"ProvidePhotoMakeup\"],\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"化妆\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"妆造套数\",\"seperator\":\"、\"},{\"attrNameList\":[\"Makeup_brand\"],\"displayName\":\"化妆品牌\",\"seperator\":\"、\"}],\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"Makeup_duration\",\"displayFormat\":\"%s分钟/套\"}],\"attrNameList\":[\"Makeup_duration\"],\"attrValueMapModels\":[{\"attrValue\":\"分钟/套\",\"displayValue\":\"\",\"priority\":0}]}]}],\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"minApplicableAgeStr\",\"displayFormat\":\"%s岁\"},{\"attrName\":\"maxApplicableAgeStr\",\"displayFormat\":\"%s岁\"}],\"attrNameList\":[\"startAge\",\"endAge\",\"minApplicableAgeStr\",\"maxApplicableAgeStr\"],\"displayName\":\"适用年龄\",\"seperator\":\"-\"},{\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"2\"},\"displayFormat\":\"%s风格任选\"},{\"attrName\":\"photo_style\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"3\"},\"attrValueSeperator\":\",\",\"displayCount\":true,\"displayFormat\":\"%d款风格任选\"}],\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\",\"priority\":0},{\"attrValue\":\"2\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"3\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍风格\"},{\"attrFormatModels\":[{\"attrName\":\"duration\",\"displayFormat\":\"%s小时\"},{\"attrName\":\"durationOld\",\"attrValueReplaceModels\":[{\"preStr\":\".*分钟\",\"str\":\"\"}]}],\"attrNameList\":[\"duration\"],\"attrValueMapModels\":[{\"attrValue\":\"小时\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"服务时长\"},{\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum_Desc\",\"displayFormat\":\"精修%s张\"},{\"attrName\":\"intensiveRepairNumOld\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCountOld\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"},\"displayFormat\":\"底片赠送%s\"},{\"attrName\":\"photoPlateAttachCount\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"提供指定张数\"},\"displayFormat\":\"底片赠送%s张\"}],\"attrNameList\":[\"intensiveRepairNum_Desc\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"attrValueMapModels\":[{\"attrValue\":\"不提供\",\"displayValue\":\"底片不送\",\"priority\":0},{\"attrValue\":\"底片赠送\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"精修\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"提供指定张数\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"成品交付\",\"seperator\":\"、\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"AdditionalNumberOfShotsOld\",\"displayFormat\":\"儿童%s\"},{\"attrName\":\"Fee_for_additional_children\",\"displayFormat\":\"儿童%s元/人\"}],\"attrNameList\":[\"AdditionalNumberOfShots\",\"Fee_for_additional_children\",\"makeup_additional_children\",\"clothing_additional_children\"],\"attrValueMapModels\":[{\"attrValue\":\"提供化妆\",\"displayValue\":\"含妆造\",\"priority\":0},{\"attrValue\":\"不提供化妆\",\"displayValue\":\"不含妆造\",\"priority\":0},{\"attrValue\":\"是\",\"displayValue\":\"含服装\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含服装\",\"priority\":0}],\"displayName\":\"加人数\",\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"Fee_for_additional_parents\",\"displayFormat\":\"成人%s元/人\"}],\"attrNameList\":[\"Fee_for_additional_parents\",\"makeup_additional_parents\",\"clothing_additional_parents\"],\"attrValueMapModels\":[{\"attrValue\":\"提供化妆\",\"displayValue\":\"含妆造\",\"priority\":0},{\"attrValue\":\"不提供化妆\",\"displayValue\":\"不含妆造\",\"priority\":0},{\"attrValue\":\"是\",\"displayValue\":\"含服装\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含服装\",\"priority\":0}],\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFinishingCost\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFinishingCost\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"加精修\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFilmFee\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFilmFee\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"加底片\"}],\"groupName\":\"加项说明\"},{\"attrModelList\":[{\"attrNameList\":[\"Supplementary_makeup_infor\"],\"displayName\":\"化妆说明\"},{\"attrNameList\":[\"postscript\"],\"displayName\":\"其他说明\"}],\"groupName\":\"额外说明\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后约%s天\"},{\"attrName\":\"selectPicTimeOld\",\"displayFormat\":\"拍摄后约%s天\"}],\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"选片时间\"},{\"attrFormatModels\":[{\"attrName\":\"takePicTime\",\"displayFormat\":\"选片后约%s天\"},{\"attrName\":\"takePicTimeOld\",\"displayFormat\":\"选片后约%s天\"}],\"attrNameList\":[\"takePicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"取片时间\"}],\"groupName\":\"服务流程\",\"videoModuleVO\":{\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\"}}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testMakeUpDealDetail(){
        String jsonStr = "{\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1033809976,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1033809976,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"80.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"100.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2105870,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u9020\\\\\\\\u578b\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":528,\\\\\\\"attrName\\\\\\\":\\\\\\\"usepeoplenum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\\u4eba\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u4eba\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1675,\\\\\\\"attrName\\\\\\\":\\\\\\\"servicemethod\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\\u65b9\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u4e0a\\\\\\\\u95e8\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u4e0a\\\\\\\\u95e8\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2641,\\\\\\\"attrName\\\\\\\":\\\\\\\"makeupHairstyle\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u542b\\\\\\\\u53d1\\\\\\\\u578b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2480,\\\\\\\"attrName\\\\\\\":\\\\\\\"giftMakeup\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u542b\\\\\\\\u914d\\\\\\\\u9970\\\\\\\\u9053\\\\\\\\u5177\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2725,\\\\\\\"attrName\\\\\\\":\\\\\\\"postscript\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\\u51fa\\\\\\\\u5904\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\\u51fa\\\\\\\\u5904\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1498981,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangleixing2\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u7c7b\\\\\\\\u578b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u65b0\\\\\\\\u5a18\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u5bb4\\\\\\\\u4f1a\\\\\\\\u88c5\\\\\\\\u3001\\\\\\\\u7537\\\\\\\\u58eb\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u4e0a\\\\\\\\u955c\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59861\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59862\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u65b0\\\\\\\\u5a18\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u5bb4\\\\\\\\u4f1a\\\\\\\\u88c5\\\\\\\\u3001\\\\\\\\u7537\\\\\\\\u58eb\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u4e0a\\\\\\\\u955c\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59861\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59862\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497424,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangshizhang\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497425,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangtaoshu\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1498880,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangpinpai3\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u724c\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"Dior\\\\\\\\u3001NARS\\\\\\\\u3001\\\\\\\\u7eaa\\\\\\\\u68b5\\\\\\\\u5e0c\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e491\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u97f32\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"Dior\\\\\\\\u3001NARS\\\\\\\\u3001\\\\\\\\u7eaa\\\\\\\\u68b5\\\\\\\\u5e0c\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e491\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u97f32\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497447,\\\\\\\"attrName\\\\\\\":\\\\\\\"peishidaojubaohan\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u914d\\\\\\\\u9970\\\\\\\\u9053\\\\\\\\u5177\\\\\\\\u5305\\\\\\\\u542b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5934\\\\\\\\u9970\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5934\\\\\\\\u9970\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497609,\\\\\\\"attrName\\\\\\\":\\\\\\\"jiarenshufeiyong\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u52a0\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\\u8d39\\\\\\\\u7528\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"100\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"100\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1500923,\\\\\\\"attrName\\\\\\\":\\\\\\\"ewaijiaxiang2\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u52a0\\\\\\\\u9879\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u776b\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"30\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u7709\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"99\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u52a0\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"100\\\\\\\\u5143/\\\\\\\\u4eba\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u776b\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"30\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u7709\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"99\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u52a0\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"100\\\\\\\\u5143/\\\\\\\\u4eba\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1033809976,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"80.0\\\",\\\"marketPrice\\\":\\\"100.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2105870,\\\"name\\\":\\\"\\\\u5316\\\\u5986\\\\u9020\\\\u578b\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":528,\\\"attrName\\\":\\\"usepeoplenum\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\\u4eba\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":\\\"\\\\u4eba\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1675,\\\"attrName\\\":\\\"servicemethod\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u4e0a\\\\u95e8\\\",\\\"rawAttrValue\\\":\\\"\\\\u4e0a\\\\u95e8\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2641,\\\"attrName\\\":\\\"makeupHairstyle\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u53d1\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\",\\\"rawAttrValue\\\":\\\"\\\\u662f\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2480,\\\"attrName\\\":\\\"giftMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\",\\\"rawAttrValue\\\":\\\"\\\\u662f\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2725,\\\"attrName\\\":\\\"postscript\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u51fa\\\\u5904\\\",\\\"rawAttrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u51fa\\\\u5904\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1498981,\\\"attrName\\\":\\\"huazhuangleixing2\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u65b0\\\\u5a18\\\\u5986\\\\u3001\\\\u5bb4\\\\u4f1a\\\\u88c5\\\\u3001\\\\u7537\\\\u58eb\\\\u5986\\\\u3001\\\\u4e0a\\\\u955c\\\\u5986\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59861\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59862\\\",\\\"rawAttrValue\\\":\\\"\\\\u65b0\\\\u5a18\\\\u5986\\\\u3001\\\\u5bb4\\\\u4f1a\\\\u88c5\\\\u3001\\\\u7537\\\\u58eb\\\\u5986\\\\u3001\\\\u4e0a\\\\u955c\\\\u5986\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59861\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59862\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497424,\\\"attrName\\\":\\\"huazhuangshizhang\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497425,\\\"attrName\\\":\\\"huazhuangtaoshu\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1498880,\\\"attrName\\\":\\\"huazhuangpinpai3\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u54c1\\\\u724c\\\",\\\"attrValue\\\":\\\"Dior\\\\u3001NARS\\\\u3001\\\\u7eaa\\\\u68b5\\\\u5e0c\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u4e491\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u97f32\\\",\\\"rawAttrValue\\\":\\\"Dior\\\\u3001NARS\\\\u3001\\\\u7eaa\\\\u68b5\\\\u5e0c\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u4e491\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u97f32\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497447,\\\"attrName\\\":\\\"peishidaojubaohan\\\",\\\"chnName\\\":\\\"\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\\u5305\\\\u542b\\\",\\\"attrValue\\\":\\\"\\\\u5934\\\\u9970\\\",\\\"rawAttrValue\\\":\\\"\\\\u5934\\\\u9970\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497609,\\\"attrName\\\":\\\"jiarenshufeiyong\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"100\\\",\\\"rawAttrValue\\\":\\\"100\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1500923,\\\"attrName\\\":\\\"ewaijiaxiang2\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u52a0\\\\u9879\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u776b\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u7709\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100\\\\u5143/\\\\u4eba\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u776b\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u7709\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100\\\\u5143/\\\\u4eba\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2105870,\\\"cnName\\\":\\\"\\\\u5316\\\\u5986\\\\u9020\\\\u578b\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1033809976,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"80.0\\\",\\\"marketPrice\\\":\\\"100.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2105870,\\\"name\\\":\\\"\\\\u5316\\\\u5986\\\\u9020\\\\u578b\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":528,\\\"attrName\\\":\\\"usepeoplenum\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\\u4eba\\\"},{\\\"metaAttrId\\\":1675,\\\"attrName\\\":\\\"servicemethod\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u4e0a\\\\u95e8\\\"},{\\\"metaAttrId\\\":2641,\\\"attrName\\\":\\\"makeupHairstyle\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u53d1\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\"},{\\\"metaAttrId\\\":2480,\\\"attrName\\\":\\\"giftMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\"},{\\\"metaAttrId\\\":2725,\\\"attrName\\\":\\\"postscript\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u51fa\\\\u5904\\\"},{\\\"metaAttrId\\\":1498981,\\\"attrName\\\":\\\"huazhuangleixing2\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u65b0\\\\u5a18\\\\u5986\\\\u3001\\\\u5bb4\\\\u4f1a\\\\u88c5\\\\u3001\\\\u7537\\\\u58eb\\\\u5986\\\\u3001\\\\u4e0a\\\\u955c\\\\u5986\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59861\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59862\\\"},{\\\"metaAttrId\\\":1497424,\\\"attrName\\\":\\\"huazhuangshizhang\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":1497425,\\\"attrName\\\":\\\"huazhuangtaoshu\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\"},{\\\"metaAttrId\\\":1498880,\\\"attrName\\\":\\\"huazhuangpinpai3\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u54c1\\\\u724c\\\",\\\"attrValue\\\":\\\"Dior\\\\u3001NARS\\\\u3001\\\\u7eaa\\\\u68b5\\\\u5e0c\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u4e491\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u97f32\\\"},{\\\"metaAttrId\\\":1497447,\\\"attrName\\\":\\\"peishidaojubaohan\\\",\\\"chnName\\\":\\\"\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\\u5305\\\\u542b\\\",\\\"attrValue\\\":\\\"\\\\u5934\\\\u9970\\\"},{\\\"metaAttrId\\\":1497609,\\\"attrName\\\":\\\"jiarenshufeiyong\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"100\\\"},{\\\"metaAttrId\\\":1500923,\\\"attrName\\\":\\\"ewaijiaxiang2\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u52a0\\\\u9879\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u776b\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u7709\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100\\\\u5143/\\\\u4eba\\\\\\\"}]\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"团购详情\\\",\\\"content\\\":[{\\\"type\\\":\\\"uniform-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":100.0,\\\"salePrice\\\":80.0,\\\"groups\\\":[{\\\"optionalCount\\\":0,\\\"units\\\":[{\\\"skuCateId\\\":2105870,\\\"projectName\\\":\\\"化妆造型\\\",\\\"amount\\\":1,\\\"attrValues\\\":{\\\"peishidaojubaohan\\\":\\\"头饰\\\",\\\"ewaijiaxiang2\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"假睫毛\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"假眉毛\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"加人数\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100元/人\\\\\\\"}]\\\",\\\"huazhuangleixing2\\\":\\\"新娘妆、宴会装、男士妆、上镜妆、自定义化妆1、自定义化妆2\\\",\\\"postscript\\\":\\\"额外说明出处\\\",\\\"giftMakeup\\\":\\\"是\\\",\\\"huazhuangshizhang\\\":\\\"60\\\",\\\"makeupHairstyle\\\":\\\"是\\\",\\\"usepeoplenum\\\":\\\"1\\\",\\\"skuCateId\\\":\\\"2105870\\\",\\\"servicemethod\\\":\\\"上门\\\",\\\"huazhuangtaoshu\\\":\\\"1\\\",\\\"jiarenshufeiyong\\\":\\\"100\\\",\\\"huazhuangpinpai3\\\":\\\"Dior、NARS、纪梵希、化妆品自定义1、化妆品自定音2\\\"}}]}]}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":100.0,\\\"groups\\\":[{\\\"units\\\":[{\\\"amount\\\":1,\\\"serviceItemName\\\":\\\"化妆造型\\\",\\\"serviceItemValue\\\":{\\\"objectVersion\\\":3,\\\"objectValues\\\":{\\\"peishidaojubaohan\\\":\\\"头饰\\\",\\\"ewaijiaxiang2\\\":[{\\\"xiangmuneirong\\\":\\\"假睫毛\\\",\\\"feiyong\\\":\\\"30\\\"},{\\\"xiangmuneirong\\\":\\\"假眉毛\\\",\\\"feiyong\\\":\\\"99\\\"},{\\\"xiangmuneirong\\\":\\\"加人数\\\",\\\"feiyong\\\":\\\"100元/人\\\"}],\\\"huazhuangleixing2\\\":[\\\"新娘妆\\\",\\\"宴会装\\\",\\\"男士妆\\\",\\\"上镜妆\\\",\\\"自定义化妆1\\\",\\\"自定义化妆2\\\"],\\\"postscript\\\":\\\"额外说明出处\\\",\\\"giftMakeup\\\":\\\"是\\\",\\\"huazhuangshizhang\\\":\\\"60\\\",\\\"makeupHairstyle\\\":\\\"是\\\",\\\"usepeoplenum\\\":1,\\\"skuCateId\\\":2105870,\\\"servicemethod\\\":\\\"上门\\\",\\\"huazhuangtaoshu\\\":\\\"1\\\",\\\"jiarenshufeiyong\\\":\\\"100\\\",\\\"huazhuangpinpai3\\\":[\\\"Dior\\\",\\\"NARS\\\",\\\"纪梵希\\\",\\\"化妆品自定义1\\\",\\\"化妆品自定音2\\\"]},\\\"objectId\\\":21355707}}],\\\"optionalCount\\\":0}]}},{\\\"type\\\":\\\"richtext\\\",\\\"data\\\":\\\"补充信息说明\\\"}]}\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"}],\"dealDetailDtoModel\":{\"dealGroupId\":1033809976,\"skuUniStructuredDto\":{\"marketPrice\":\"100.0\",\"mustGroups\":[{\"skuItems\":[{\"attrItems\":[{\"attrName\":\"usepeoplenum\",\"attrValue\":\"1人\",\"chnName\":\"适用人数\",\"metaAttrId\":528,\"rawAttrValue\":\"1\",\"sequence\":0,\"unit\":\"人\",\"valueType\":401},{\"attrName\":\"servicemethod\",\"attrValue\":\"上门\",\"chnName\":\"服务方式\",\"metaAttrId\":1675,\"rawAttrValue\":\"上门\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"makeupHairstyle\",\"attrValue\":\"是\",\"chnName\":\"是否含发型\",\"metaAttrId\":2641,\"rawAttrValue\":\"是\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"giftMakeup\",\"attrValue\":\"是\",\"chnName\":\"是否含配饰道具\",\"metaAttrId\":2480,\"rawAttrValue\":\"是\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"postscript\",\"attrValue\":\"额外说明出处\",\"chnName\":\"额外说明\",\"metaAttrId\":2725,\"rawAttrValue\":\"额外说明出处\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"huazhuangleixing2\",\"attrValue\":\"新娘妆、宴会装、男士妆、上镜妆、自定义化妆1、自定义化妆2\",\"chnName\":\"化妆类型\",\"metaAttrId\":1498981,\"rawAttrValue\":\"新娘妆、宴会装、男士妆、上镜妆、自定义化妆1、自定义化妆2\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"huazhuangshizhang\",\"attrValue\":\"60\",\"chnName\":\"化妆时长\",\"metaAttrId\":1497424,\"rawAttrValue\":\"60\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"huazhuangtaoshu\",\"attrValue\":\"1\",\"chnName\":\"化妆套数\",\"metaAttrId\":1497425,\"rawAttrValue\":\"1\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"huazhuangpinpai3\",\"attrValue\":\"Dior、NARS、纪梵希、化妆品自定义1、化妆品自定音2\",\"chnName\":\"化妆品牌\",\"metaAttrId\":1498880,\"rawAttrValue\":\"Dior、NARS、纪梵希、化妆品自定义1、化妆品自定音2\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"peishidaojubaohan\",\"attrValue\":\"头饰\",\"chnName\":\"配饰道具包含\",\"metaAttrId\":1497447,\"rawAttrValue\":\"头饰\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"jiarenshufeiyong\",\"attrValue\":\"100\",\"chnName\":\"加人数费用\",\"metaAttrId\":1497609,\"rawAttrValue\":\"100\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"ewaijiaxiang2\",\"attrValue\":\"[{\\\"xiangmuneirong\\\":\\\"假睫毛\\\",\\\"feiyong\\\":\\\"30\\\"},{\\\"xiangmuneirong\\\":\\\"假眉毛\\\",\\\"feiyong\\\":\\\"99\\\"},{\\\"xiangmuneirong\\\":\\\"加人数\\\",\\\"feiyong\\\":\\\"100元/人\\\"}]\",\"chnName\":\"额外加项\",\"metaAttrId\":1500923,\"rawAttrValue\":\"[{\\\"xiangmuneirong\\\":\\\"假睫毛\\\",\\\"feiyong\\\":\\\"30\\\"},{\\\"xiangmuneirong\\\":\\\"假眉毛\\\",\\\"feiyong\\\":\\\"99\\\"},{\\\"xiangmuneirong\\\":\\\"加人数\\\",\\\"feiyong\\\":\\\"100元/人\\\"}]\",\"sequence\":0,\"valueType\":300}],\"copies\":1,\"name\":\"化妆造型\",\"productCategory\":2105870,\"skuId\":0,\"status\":10}]}],\"optionalGroups\":[],\"salePrice\":\"80.0\"},\"structType\":\"uniform-structure-table\",\"title\":\"团购详情\"},\"dealDetailInfoModel\":{\"additionalProjectList\":[],\"dealAttrs\":[{\"$ref\":\"$.dealAttrs[0]\"},{\"$ref\":\"$.dealAttrs[1]\"},{\"$ref\":\"$.dealAttrs[2]\"}],\"dealDetailDtoModel\":{\"$ref\":\"$.dealDetailDtoModel\"},\"dealId\":1033809976,\"desc\":\"补充信息说明\",\"productCategories\":[{\"cnName\":\"化妆造型\",\"productCategoryId\":2105870}],\"unifyProduct\":true},\"productCategories\":[{\"$ref\":\"$.dealDetailInfoModel.productCategories[0]\"}]}";
        param = JSON.parseObject(jsonStr, DealAttrVOListVP.Param.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrModelList\":[{\"attrNameList\":[\"huazhuangleixing2\"],\"displayName\":\"妆造类型\"},{\"attrFormatModels\":[{\"attrName\":\"makeupHairstyle\",\"attrValueReplaceModels\":[{\"preStr\":\"是\",\"str\":\"含发型\"}]},{\"attrName\":\"giftMakeup\",\"attrValueReplaceModels\":[{\"preStr\":\"是\",\"str\":\"含配饰道具\"}]},{\"attrName\":\"huazhuangshizhang\",\"displayFormat\":\"化妆时长%s分钟/套\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s套\"}],\"attrNameList\":[\"huazhuangshizhang\",\"huazhuangtaoshu\",\"makeupHairstyle\",\"giftMakeup\",\"peishidaojubaohan\"],\"displayName\":\"妆造信息\",\"seperator\":\"、\"},{\"attrNameList\":[\"huazhuangpinpai3\"],\"displayName\":\"化妆品牌\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"jiarenshufeiyong\",\"displayFormat\":\"%s元/人\"}],\"attrNameList\":[\"jiarenshufeiyong\"],\"displayName\":\"加人数\"},{\"attrFormatModels\":[{\"attrName\":\"ewaijiaxiang2\",\"attrValueReplaceModels\":[{\"preStr\":\".*\",\"str\":\"\"}]}],\"attrNameList\":[\"ewaijiaxiang2\"],\"dynamicDisplayNameKey\":\"ewaijiaxiang2\"}],\"groupName\":\"加项说明\"},{\"attrModelList\":[{\"attrNameList\":[\"postscript\"],\"displayName\":\"其他说明\"}],\"groupName\":\"额外说明\"}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }


    @Test
    public  void testChildrenPhotoDealDetail(){
        String jsonStr = "{\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1034160664,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1034160664,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"800.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"1000.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104880,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u513f\\\\\\\\u7ae5\\\\\\\\u6444\\\\\\\\u5f71\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":166010,\\\\\\\"attrName\\\\\\\":\\\\\\\"whetherToProvideClothing\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1382,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"3\\\\\\\\u5957\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5957\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166013,\\\\\\\"attrName\\\\\\\":\\\\\\\"ShootingMethod\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u65b9\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5230\\\\\\\\u5e97\\\\\\\\u62cd\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5230\\\\\\\\u5e97\\\\\\\\u62cd\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166014,\\\\\\\"attrName\\\\\\\":\\\\\\\"ShootingScene\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u573a\\\\\\\\u666f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5185\\\\\\\\u666f+\\\\\\\\u5916\\\\\\\\u666f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5185\\\\\\\\u666f+\\\\\\\\u5916\\\\\\\\u666f\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1387,\\\\\\\"attrName\\\\\\\":\\\\\\\"intensiveRepairNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u7cbe\\\\\\\\u4fee\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"5\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166015,\\\\\\\"attrName\\\\\\\":\\\\\\\"IsTheFilmPresentedFree\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u8d60\\\\\\\\u9001\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u540c\\\\\\\\u5e95\\\\\\\\u539f\\\\\\\\u7247\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u540c\\\\\\\\u5e95\\\\\\\\u539f\\\\\\\\u7247\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1386,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoCount\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"10\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":572,\\\\\\\"attrName\\\\\\\":\\\\\\\"duration\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\\u5206\\\\\\\\u949f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5206\\\\\\\\u949f\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1591,\\\\\\\"attrName\\\\\\\":\\\\\\\"startAge\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6700\\\\\\\\u5c0f\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u5e74\\\\\\\\u9f84\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\\u5c81\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5c81\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1592,\\\\\\\"attrName\\\\\\\":\\\\\\\"endAge\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6700\\\\\\\\u5927\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u5e74\\\\\\\\u9f84\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"18\\\\\\\\u5c81\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"18\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5c81\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166032,\\\\\\\"attrName\\\\\\\":\\\\\\\"WithParentChildPhoto\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u53ef\\\\\\\\u62cd\\\\\\\\u4eb2\\\\\\\\u5b50\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u62cd\\\\\\\\u4eb2\\\\\\\\u5b50\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u62cd\\\\\\\\u4eb2\\\\\\\\u5b50\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166017,\\\\\\\"attrName\\\\\\\":\\\\\\\"GroupPhotoThemeSets\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\\u4e3b\\\\\\\\u9898\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5957\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5957\\\\\\\",\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166018,\\\\\\\"attrName\\\\\\\":\\\\\\\"WhetherToProvidePhotoClothing\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\" \\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7cbe\\\\\\\\u81f4\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7cbe\\\\\\\\u81f4\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166019,\\\\\\\"attrName\\\\\\\":\\\\\\\"ProvidePhotoMakeup\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5408\\\\\\\\u5f71\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7cbe\\\\\\\\u81f4\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u7cbe\\\\\\\\u81f4\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2356,\\\\\\\"attrName\\\\\\\":\\\\\\\"hasGuider\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5f15\\\\\\\\u5bfc\\\\\\\\u5e08\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5f15\\\\\\\\u5bfc\\\\\\\\u5e08\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u5f15\\\\\\\\u5bfc\\\\\\\\u5e08\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166020,\\\\\\\"attrName\\\\\\\":\\\\\\\"FinishedDeliverables\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6210\\\\\\\\u54c1\\\\\\\\u4ea4\\\\\\\\u4ed8\\\\\\\\u7269\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166022,\\\\\\\"attrName\\\\\\\":\\\\\\\"PlusFinishingCost\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u52a0\\\\\\\\u7cbe\\\\\\\\u4fee\\\\\\\\u8d39\\\\\\\\u7528\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"50\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"50\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":400,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166023,\\\\\\\"attrName\\\\\\\":\\\\\\\"PlusFilmFee\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u52a0\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\\u8d39\\\\\\\\u7528\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"30\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"30\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5143/\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":400,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2990,\\\\\\\"attrName\\\\\\\":\\\\\\\"selectPicTime\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9009\\\\\\\\u7247\\\\\\\\u65f6\\\\\\\\u95f4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2984,\\\\\\\"attrName\\\\\\\":\\\\\\\"takePicTime\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u53d6\\\\\\\\u7247\\\\\\\\u65f6\\\\\\\\u95f4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"14\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"14\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1034160664,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"800.0\\\",\\\"marketPrice\\\":\\\"1000.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104880,\\\"name\\\":\\\"\\\\u513f\\\\u7ae5\\\\u6444\\\\u5f71\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":166010,\\\"attrName\\\":\\\"whetherToProvideClothing\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1382,\\\"attrName\\\":\\\"dressNum\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"3\\\\u5957\\\",\\\"rawAttrValue\\\":\\\"3\\\",\\\"unit\\\":\\\"\\\\u5957\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166013,\\\"attrName\\\":\\\"ShootingMethod\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u5230\\\\u5e97\\\\u62cd\\\",\\\"rawAttrValue\\\":\\\"\\\\u5230\\\\u5e97\\\\u62cd\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166014,\\\"attrName\\\":\\\"ShootingScene\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u5185\\\\u666f+\\\\u5916\\\\u666f\\\",\\\"rawAttrValue\\\":\\\"\\\\u5185\\\\u666f+\\\\u5916\\\\u666f\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1387,\\\"attrName\\\":\\\"intensiveRepairNum\\\",\\\"chnName\\\":\\\"\\\\u7cbe\\\\u4fee\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"5\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"5\\\",\\\"unit\\\":\\\"\\\\u5f20\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166015,\\\"attrName\\\":\\\"IsTheFilmPresentedFree\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u662f\\\\u5426\\\\u8d60\\\\u9001\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u540c\\\\u5e95\\\\u539f\\\\u7247\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u540c\\\\u5e95\\\\u539f\\\\u7247\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1386,\\\"attrName\\\":\\\"photoCount\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"10\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"10\\\",\\\"unit\\\":\\\"\\\\u5f20\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":572,\\\"attrName\\\":\\\"duration\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\\u5206\\\\u949f\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":\\\"\\\\u5206\\\\u949f\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1591,\\\"attrName\\\":\\\"startAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5c0f\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"1\\\\u5c81\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":\\\"\\\\u5c81\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1592,\\\"attrName\\\":\\\"endAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5927\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"18\\\\u5c81\\\",\\\"rawAttrValue\\\":\\\"18\\\",\\\"unit\\\":\\\"\\\\u5c81\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166032,\\\"attrName\\\":\\\"WithParentChildPhoto\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"attrValue\\\":\\\"\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"rawAttrValue\\\":\\\"\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166017,\\\"attrName\\\":\\\"GroupPhotoThemeSets\\\",\\\"chnName\\\":\\\"\\\\u5408\\\\u5f71\\\\u4e3b\\\\u9898\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\",\\\"rawAttrValue\\\":\\\"2\\\",\\\"unit\\\":\\\"\\\\u5957\\\",\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166018,\\\"attrName\\\":\\\"WhetherToProvidePhotoClothing\\\",\\\"chnName\\\":\\\" \\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7cbe\\\\u81f4\\\\u670d\\\\u88c5\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7cbe\\\\u81f4\\\\u670d\\\\u88c5\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7cbe\\\\u81f4\\\\u5986\\\\u9020\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7cbe\\\\u81f4\\\\u5986\\\\u9020\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2356,\\\"attrName\\\":\\\"hasGuider\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"rawAttrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166020,\\\"attrName\\\":\\\"FinishedDeliverables\\\",\\\"chnName\\\":\\\"\\\\u6210\\\\u54c1\\\\u4ea4\\\\u4ed8\\\\u7269\\\",\\\"attrValue\\\":\\\"\\\",\\\"rawAttrValue\\\":\\\"\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166022,\\\"attrName\\\":\\\"PlusFinishingCost\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u7cbe\\\\u4fee\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"50\\\\u5143/\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"50\\\",\\\"unit\\\":\\\"\\\\u5143/\\\\u5f20\\\",\\\"valueType\\\":400,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166023,\\\"attrName\\\":\\\"PlusFilmFee\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u5e95\\\\u7247\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"30\\\\u5143/\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"30\\\",\\\"unit\\\":\\\"\\\\u5143/\\\\u5f20\\\",\\\"valueType\\\":400,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2990,\\\"attrName\\\":\\\"selectPicTime\\\",\\\"chnName\\\":\\\"\\\\u9009\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"7\\\",\\\"rawAttrValue\\\":\\\"7\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2984,\\\"attrName\\\":\\\"takePicTime\\\",\\\"chnName\\\":\\\"\\\\u53d6\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"14\\\",\\\"rawAttrValue\\\":\\\"14\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104880,\\\"cnName\\\":\\\"\\\\u513f\\\\u7ae5\\\\u6444\\\\u5f71\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1034160664,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"800.0\\\",\\\"marketPrice\\\":\\\"1000.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104880,\\\"name\\\":\\\"\\\\u513f\\\\u7ae5\\\\u6444\\\\u5f71\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":166010,\\\"attrName\\\":\\\"whetherToProvideClothing\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u88c5\\\"},{\\\"metaAttrId\\\":1382,\\\"attrName\\\":\\\"dressNum\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"3\\\\u5957\\\"},{\\\"metaAttrId\\\":166013,\\\"attrName\\\":\\\"ShootingMethod\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u5230\\\\u5e97\\\\u62cd\\\"},{\\\"metaAttrId\\\":166014,\\\"attrName\\\":\\\"ShootingScene\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u5185\\\\u666f+\\\\u5916\\\\u666f\\\"},{\\\"metaAttrId\\\":1387,\\\"attrName\\\":\\\"intensiveRepairNum\\\",\\\"chnName\\\":\\\"\\\\u7cbe\\\\u4fee\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"5\\\\u5f20\\\"},{\\\"metaAttrId\\\":166015,\\\"attrName\\\":\\\"IsTheFilmPresentedFree\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u662f\\\\u5426\\\\u8d60\\\\u9001\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u540c\\\\u5e95\\\\u539f\\\\u7247\\\"},{\\\"metaAttrId\\\":1386,\\\"attrName\\\":\\\"photoCount\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"10\\\\u5f20\\\"},{\\\"metaAttrId\\\":572,\\\"attrName\\\":\\\"duration\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\\u5206\\\\u949f\\\"},{\\\"metaAttrId\\\":1591,\\\"attrName\\\":\\\"startAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5c0f\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"1\\\\u5c81\\\"},{\\\"metaAttrId\\\":1592,\\\"attrName\\\":\\\"endAge\\\",\\\"chnName\\\":\\\"\\\\u6700\\\\u5927\\\\u9002\\\\u7528\\\\u5e74\\\\u9f84\\\",\\\"attrValue\\\":\\\"18\\\\u5c81\\\"},{\\\"metaAttrId\\\":166032,\\\"attrName\\\":\\\"WithParentChildPhoto\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\",\\\"attrValue\\\":\\\"\\\\u53ef\\\\u62cd\\\\u4eb2\\\\u5b50\\\\u5408\\\\u5f71\\\"},{\\\"metaAttrId\\\":166017,\\\"attrName\\\":\\\"GroupPhotoThemeSets\\\",\\\"chnName\\\":\\\"\\\\u5408\\\\u5f71\\\\u4e3b\\\\u9898\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\"},{\\\"metaAttrId\\\":166018,\\\"attrName\\\":\\\"WhetherToProvidePhotoClothing\\\",\\\"chnName\\\":\\\" \\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u670d\\\\u88c5\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7cbe\\\\u81f4\\\\u670d\\\\u88c5\\\"},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5408\\\\u5f71\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u7cbe\\\\u81f4\\\\u5986\\\\u9020\\\"},{\\\"metaAttrId\\\":2356,\\\"attrName\\\":\\\"hasGuider\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\",\\\"attrValue\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u5f15\\\\u5bfc\\\\u5e08\\\"},{\\\"metaAttrId\\\":166020,\\\"attrName\\\":\\\"FinishedDeliverables\\\",\\\"chnName\\\":\\\"\\\\u6210\\\\u54c1\\\\u4ea4\\\\u4ed8\\\\u7269\\\",\\\"attrValue\\\":\\\"\\\"},{\\\"metaAttrId\\\":166022,\\\"attrName\\\":\\\"PlusFinishingCost\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u7cbe\\\\u4fee\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"50\\\\u5143/\\\\u5f20\\\"},{\\\"metaAttrId\\\":166023,\\\"attrName\\\":\\\"PlusFilmFee\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u5e95\\\\u7247\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"30\\\\u5143/\\\\u5f20\\\"},{\\\"metaAttrId\\\":2990,\\\"attrName\\\":\\\"selectPicTime\\\",\\\"chnName\\\":\\\"\\\\u9009\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"7\\\"},{\\\"metaAttrId\\\":2984,\\\"attrName\\\":\\\"takePicTime\\\",\\\"chnName\\\":\\\"\\\\u53d6\\\\u7247\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"14\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"团购详情\\\",\\\"content\\\":[{\\\"type\\\":\\\"uniform-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":1000.0,\\\"salePrice\\\":800.0,\\\"groups\\\":[{\\\"optionalCount\\\":0,\\\"units\\\":[{\\\"skuCateId\\\":2104880,\\\"projectName\\\":\\\"儿童摄影\\\",\\\"amount\\\":1,\\\"attrValues\\\":{\\\"startAge\\\":\\\"1\\\",\\\"PlusFinishingCost\\\":\\\"50\\\",\\\"ProvidePhotoMakeup\\\":\\\"提供精致妆造\\\",\\\"IsMakeupProvided\\\":\\\"提供化妆\\\",\\\"photoCount\\\":\\\"10\\\",\\\"duration\\\":\\\"60\\\",\\\"dressNum\\\":\\\"3\\\",\\\"takePicTime\\\":\\\"14\\\",\\\"Makeup_brand\\\":\\\"Dior\\\",\\\"whetherToProvideClothing\\\":\\\"提供服装\\\",\\\"Makeup_duration\\\":\\\"30\\\",\\\"shootpersoncount\\\":\\\"1\\\",\\\"hasGuider\\\":\\\"提供引导师\\\",\\\"WithParentChildPhoto\\\":\\\"可拍亲子合影\\\",\\\"GroupPhotoThemeSets\\\":\\\"2\\\",\\\"IsTheFilmPresentedFree\\\":\\\"提供同底原片\\\",\\\"PlusFilmFee\\\":\\\"30\\\",\\\"skuCateId\\\":\\\"2104880\\\",\\\"endAge\\\":\\\"18\\\",\\\"Num_makeupsets\\\":\\\"2\\\",\\\"WhetherToProvidePhotoClothing\\\":\\\"提供精致服装\\\",\\\"intensiveRepairNum\\\":\\\"5\\\",\\\"FinishedDeliverables\\\":\\\"\\\",\\\"selectPicTime\\\":\\\"7\\\",\\\"ShootingMethod\\\":\\\"到店拍\\\",\\\"ShootingScene\\\":\\\"内景+外景\\\"}}]}]}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":1000.0,\\\"groups\\\":[{\\\"units\\\":[{\\\"amount\\\":1,\\\"serviceItemName\\\":\\\"儿童摄影\\\",\\\"serviceItemValue\\\":{\\\"objectVersion\\\":8,\\\"objectValues\\\":{\\\"startAge\\\":\\\"1\\\",\\\"PlusFinishingCost\\\":\\\"50\\\",\\\"ProvidePhotoMakeup\\\":\\\"提供精致妆造\\\",\\\"IsMakeupProvided\\\":\\\"提供化妆\\\",\\\"photoCount\\\":10,\\\"duration\\\":\\\"60\\\",\\\"dressNum\\\":\\\"3\\\",\\\"takePicTime\\\":\\\"14\\\",\\\"Makeup_brand\\\":[\\\"Dior\\\"],\\\"whetherToProvideClothing\\\":\\\"提供服装\\\",\\\"Makeup_duration\\\":\\\"30\\\",\\\"shootpersoncount\\\":\\\"1\\\",\\\"hasGuider\\\":\\\"提供引导师\\\",\\\"WithParentChildPhoto\\\":\\\"可拍亲子合影\\\",\\\"GroupPhotoThemeSets\\\":\\\"2\\\",\\\"IsTheFilmPresentedFree\\\":\\\"提供同底原片\\\",\\\"PlusFilmFee\\\":\\\"30\\\",\\\"skuCateId\\\":2104880,\\\"endAge\\\":\\\"18\\\",\\\"Num_makeupsets\\\":\\\"2\\\",\\\"WhetherToProvidePhotoClothing\\\":\\\"提供精致服装\\\",\\\"intensiveRepairNum\\\":\\\"5\\\",\\\"FinishedDeliverables\\\":[],\\\"selectPicTime\\\":\\\"7\\\",\\\"ShootingMethod\\\":\\\"到店拍\\\",\\\"ShootingScene\\\":\\\"内景+外景\\\"},\\\"objectId\\\":21633965}}],\\\"optionalCount\\\":0}]}}]}\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"}],\"dealDetailDtoModel\":{\"dealGroupId\":1034160664,\"skuUniStructuredDto\":{\"marketPrice\":\"1000.0\",\"mustGroups\":[{\"skuItems\":[{\"attrItems\":[{\"attrName\":\"whetherToProvideClothing\",\"attrValue\":\"提供服装\",\"chnName\":\"是否提供服装\",\"metaAttrId\":166010,\"rawAttrValue\":\"提供服装\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"dressNum\",\"attrValue\":\"3套\",\"chnName\":\"服装套数\",\"metaAttrId\":1382,\"rawAttrValue\":\"3\",\"sequence\":0,\"unit\":\"套\",\"valueType\":401},{\"attrName\":\"ShootingMethod\",\"attrValue\":\"到店拍\",\"chnName\":\"拍摄方式\",\"metaAttrId\":166013,\"rawAttrValue\":\"到店拍\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"ShootingScene\",\"attrValue\":\"内景+外景\",\"chnName\":\"拍摄场景\",\"metaAttrId\":166014,\"rawAttrValue\":\"内景+外景\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"intensiveRepairNum\",\"attrValue\":\"5张\",\"chnName\":\"精修张数\",\"metaAttrId\":1387,\"rawAttrValue\":\"5\",\"sequence\":0,\"unit\":\"张\",\"valueType\":401},{\"attrName\":\"IsTheFilmPresentedFree\",\"attrValue\":\"提供同底原片\",\"chnName\":\"底片是否赠送\",\"metaAttrId\":166015,\"rawAttrValue\":\"提供同底原片\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"photoCount\",\"attrValue\":\"10张\",\"chnName\":\"拍摄张数\",\"metaAttrId\":1386,\"rawAttrValue\":\"10\",\"sequence\":0,\"unit\":\"张\",\"valueType\":401},{\"attrName\":\"duration\",\"attrValue\":\"60分钟\",\"chnName\":\"拍摄时长\",\"metaAttrId\":572,\"rawAttrValue\":\"60\",\"sequence\":0,\"unit\":\"分钟\",\"valueType\":401},{\"attrName\":\"startAge\",\"attrValue\":\"1岁\",\"chnName\":\"最小适用年龄\",\"metaAttrId\":1591,\"rawAttrValue\":\"1\",\"sequence\":0,\"unit\":\"岁\",\"valueType\":401},{\"attrName\":\"endAge\",\"attrValue\":\"18岁\",\"chnName\":\"最大适用年龄\",\"metaAttrId\":1592,\"rawAttrValue\":\"18\",\"sequence\":0,\"unit\":\"岁\",\"valueType\":401},{\"attrName\":\"WithParentChildPhoto\",\"attrValue\":\"可拍亲子合影\",\"chnName\":\"是否可拍亲子合影\",\"metaAttrId\":166032,\"rawAttrValue\":\"可拍亲子合影\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"GroupPhotoThemeSets\",\"attrValue\":\"2套\",\"chnName\":\"合影主题套数\",\"metaAttrId\":166017,\"rawAttrValue\":\"2\",\"sequence\":0,\"unit\":\"套\",\"valueType\":500},{\"attrName\":\"WhetherToProvidePhotoClothing\",\"attrValue\":\"提供精致服装\",\"chnName\":\" 是否提供合影服装\",\"metaAttrId\":166018,\"rawAttrValue\":\"提供精致服装\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"ProvidePhotoMakeup\",\"attrValue\":\"提供精致妆造\",\"chnName\":\"是否提供合影妆造\",\"metaAttrId\":166019,\"rawAttrValue\":\"提供精致妆造\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"hasGuider\",\"attrValue\":\"提供引导师\",\"chnName\":\"是否提供引导师\",\"metaAttrId\":2356,\"rawAttrValue\":\"提供引导师\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"FinishedDeliverables\",\"attrValue\":\"\",\"chnName\":\"成品交付物\",\"metaAttrId\":166020,\"rawAttrValue\":\"\",\"sequence\":0,\"valueType\":300},{\"attrName\":\"PlusFinishingCost\",\"attrValue\":\"50元/张\",\"chnName\":\"加精修费用\",\"metaAttrId\":166022,\"rawAttrValue\":\"50\",\"sequence\":0,\"unit\":\"元/张\",\"valueType\":400},{\"attrName\":\"PlusFilmFee\",\"attrValue\":\"30元/张\",\"chnName\":\"加底片费用\",\"metaAttrId\":166023,\"rawAttrValue\":\"30\",\"sequence\":0,\"unit\":\"元/张\",\"valueType\":400},{\"attrName\":\"selectPicTime\",\"attrValue\":\"7\",\"chnName\":\"选片时间\",\"metaAttrId\":2990,\"rawAttrValue\":\"7\",\"sequence\":0,\"valueType\":401},{\"attrName\":\"takePicTime\",\"attrValue\":\"14\",\"chnName\":\"取片时间\",\"metaAttrId\":2984,\"rawAttrValue\":\"14\",\"sequence\":0,\"valueType\":401}],\"copies\":1,\"name\":\"儿童摄影\",\"productCategory\":2104880,\"skuId\":0,\"status\":10}]}],\"optionalGroups\":[],\"salePrice\":\"800.0\"},\"structType\":\"uniform-structure-table\",\"title\":\"团购详情\"},\"dealDetailInfoModel\":{\"additionalProjectList\":[],\"dealAttrs\":[{\"$ref\":\"$.dealAttrs[0]\"},{\"$ref\":\"$.dealAttrs[1]\"},{\"$ref\":\"$.dealAttrs[2]\"}],\"dealDetailDtoModel\":{\"$ref\":\"$.dealDetailDtoModel\"},\"dealId\":1034160664,\"productCategories\":[{\"cnName\":\"儿童摄影\",\"productCategoryId\":2104880}],\"unifyProduct\":true},\"productCategories\":[{\"$ref\":\"$.dealDetailInfoModel.productCategories[0]\"}]}";
        param = JSON.parseObject(jsonStr, DealAttrVOListVP.Param.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrListGroupModels2\":[{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"shootpersoncount\",\"displayFormat\":\"%s人\"}],\"attrNameList\":[\"PhotoKidsType\",\"shootpersoncount\"],\"displayName\":\"拍摄人数\"},{\"attrFormatModels\":[{\"attrName\":\"photoCount\",\"displayFormat\":\"%s张\"}],\"attrNameList\":[\"photoCount\"],\"attrValueMapModels\":[{\"attrValue\":\"张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"拍摄张数\"},{\"attrNameList\":[\"WithParentChildPhoto\",\"parents_and_children\"],\"attrValueMapModels\":[{\"attrValue\":\"可拍亲子合影\",\"displayValue\":\"含合影\",\"priority\":0},{\"attrValue\":\"不可拍亲子合影\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"有无合影\"},{\"attrNameList\":[\"hasGuider\",\"guide_provided\"],\"displayName\":\"辅助拍摄\"},{\"attrFormatModels\":[{\"attrName\":\"numberscenes_shot\",\"displayFormat\":\"含%s个景点\"}],\"attrNameList\":[\"ShootingScene\",\"numberscenes_shot\",\"photoEnvInfo\"],\"attrValueMapModels\":[{\"attrValue\":\"含个景点\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍场景\",\"seperator\":\"、\"}],\"groupName\":\"拍摄服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"儿童服装%s套/人\"},{\"attrName\":\"dressNumOld\",\"displayFormat\":\"儿童服装%s\"},{\"attrName\":\"Num_makeupsets\",\"displayFormat\":\"化妆%s套/人\"},{\"attrName\":\"WhetherToProvidePhotoClothingOld\",\"displayFormat\":\"家长%s\"},{\"attrName\":\"clothing_for_group\",\"displayFormat\":\"家长%s\"}],\"attrNameList\":[\"dressNum\",\"Num_makeupsets\",\"WhetherToProvidePhotoClothing\",\"clothing_for_group\",\"ProvidePhotoMakeup\"],\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"化妆\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"妆造套数\",\"seperator\":\"、\"},{\"attrNameList\":[\"Makeup_brand\"],\"displayName\":\"化妆品牌\",\"seperator\":\"、\"}],\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"Makeup_duration\",\"displayFormat\":\"%s分钟/套\"}],\"attrNameList\":[\"Makeup_duration\"],\"attrValueMapModels\":[{\"attrValue\":\"分钟/套\",\"displayValue\":\"\",\"priority\":0}]}]}],\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"minApplicableAgeStr\",\"displayFormat\":\"%s岁\"},{\"attrName\":\"maxApplicableAgeStr\",\"displayFormat\":\"%s岁\"}],\"attrNameList\":[\"startAge\",\"endAge\",\"minApplicableAgeStr\",\"maxApplicableAgeStr\"],\"displayName\":\"适用年龄\",\"seperator\":\"-\"},{\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"2\"},\"displayFormat\":\"%s风格任选\"},{\"attrName\":\"photo_style\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"3\"},\"attrValueSeperator\":\",\",\"displayCount\":true,\"displayFormat\":\"%d款风格任选\"}],\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\",\"priority\":0},{\"attrValue\":\"2\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"3\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍风格\"},{\"attrFormatModels\":[{\"attrName\":\"duration\",\"displayFormat\":\"%s小时\"},{\"attrName\":\"durationOld\",\"attrValueReplaceModels\":[{\"preStr\":\".*分钟\",\"str\":\"\"}]}],\"attrNameList\":[\"duration\"],\"attrValueMapModels\":[{\"attrValue\":\"小时\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"服务时长\"},{\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum_Desc\",\"displayFormat\":\"精修%s张\"},{\"attrName\":\"intensiveRepairNumOld\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCountOld\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"},\"displayFormat\":\"底片赠送%s\"},{\"attrName\":\"photoPlateAttachCount\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"提供指定张数\"},\"displayFormat\":\"底片赠送%s张\"}],\"attrNameList\":[\"intensiveRepairNum_Desc\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"attrValueMapModels\":[{\"attrValue\":\"不提供\",\"displayValue\":\"底片不送\",\"priority\":0},{\"attrValue\":\"底片赠送\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"精修\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"提供指定张数\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"成品交付\",\"seperator\":\"、\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"AdditionalNumberOfShotsOld\",\"displayFormat\":\"儿童%s\"},{\"attrName\":\"Fee_for_additional_children\",\"displayFormat\":\"儿童%s元/人\"}],\"attrNameList\":[\"AdditionalNumberOfShots\",\"Fee_for_additional_children\",\"makeup_additional_children\",\"clothing_additional_children\"],\"attrValueMapModels\":[{\"attrValue\":\"提供化妆\",\"displayValue\":\"含妆造\",\"priority\":0},{\"attrValue\":\"不提供化妆\",\"displayValue\":\"不含妆造\",\"priority\":0},{\"attrValue\":\"是\",\"displayValue\":\"含服装\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含服装\",\"priority\":0}],\"displayName\":\"加人数\",\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"Fee_for_additional_parents\",\"displayFormat\":\"成人%s元/人\"}],\"attrNameList\":[\"Fee_for_additional_parents\",\"makeup_additional_parents\",\"clothing_additional_parents\"],\"attrValueMapModels\":[{\"attrValue\":\"提供化妆\",\"displayValue\":\"含妆造\",\"priority\":0},{\"attrValue\":\"不提供化妆\",\"displayValue\":\"不含妆造\",\"priority\":0},{\"attrValue\":\"是\",\"displayValue\":\"含服装\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含服装\",\"priority\":0}],\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFinishingCost\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFinishingCost\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"加精修\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFilmFee\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFilmFee\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"加底片\"}],\"groupName\":\"加项说明\"},{\"attrModelList\":[{\"attrNameList\":[\"Supplementary_makeup_infor\"],\"displayName\":\"化妆说明\"},{\"attrNameList\":[\"postscript\"],\"displayName\":\"其他说明\"}],\"groupName\":\"额外说明\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后约%s天\"},{\"attrName\":\"selectPicTimeOld\",\"displayFormat\":\"拍摄后约%s天\"}],\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"选片时间\"},{\"attrFormatModels\":[{\"attrName\":\"takePicTime\",\"displayFormat\":\"选片后约%s天\"},{\"attrName\":\"takePicTimeOld\",\"displayFormat\":\"选片后约%s天\"}],\"attrNameList\":[\"takePicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"取片时间\"}],\"groupName\":\"服务流程\",\"videoModuleVO\":{\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\"}}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    // 成品交付测试
    @Test
    public void testChengPingJiaofu(){
        String jsonStr = "[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1033841877,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1033841877,\\\\\\\"title\\\\\\\":null,\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"mustGroups\\\\\\\":[],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"html\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1033841877,\\\"title\\\":null,\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":[],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"html\\\"},\\\"productCategories\\\":[],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1033841877,\\\"title\\\":null,\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":null,\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":{\\\"totalPrice\\\":1000.0,\\\"groups\\\":[]},\\\"type\\\":\\\"serviceItem-structure-table\\\"}],\\\"headline\\\":\\\"团购详情\\\",\\\"version\\\":1}\"},{\"name\":\"PlusFinishingCost\",\"value\":\"12\"},{\"name\":\"Supplementary_makeup_infor\",\"value\":\"补充信息说明\"},{\"name\":\"photoPlateAttachCount\",\"value\":\"3\"},{\"name\":\"numberscenes_shot\",\"value\":\"13\"},{\"name\":\"postscript\",\"value\":\"额外说明补充\"},{\"name\":\"IsMakeupProvided\",\"value\":\"提供化妆\"},{\"name\":\"Does_clothing\",\"value\":\"是\"},{\"name\":\"photoEnvInfo\",\"value\":\"白天,自定义说明1,自定义说明2\"},{\"name\":\"IsTheFilmPresentedFree\",\"value\":\"底片部分赠送\"},{\"name\":\"PlusFilmFee\",\"value\":\"13\"},{\"name\":\"photoCount\",\"value\":\"12\"},{\"name\":\"duration\",\"value\":\"8\"},{\"name\":\"dressNum\",\"value\":\"2\"},{\"name\":\"takePicTime\",\"value\":\"3\"},{\"name\":\"Makeup_brand\",\"value\":\"阿玛尼,NARS,化妆1,化妆2\"},{\"name\":\"whetherToProvideClothing\",\"value\":\"提供服装\"},{\"name\":\"Num_makeupsets\",\"value\":\"3\"},{\"name\":\"AdditionalNumberOfShots\",\"value\":\"66\"},{\"name\":\"intensiveRepairNum\",\"value\":\"11\"},{\"name\":\"Makeup_duration\",\"value\":\"54\"},{\"name\":\"Does_makeup\",\"value\":\"提供化妆\"},{\"name\":\"FinishedDeliverables\",\"value\":\"{\\\"FinishedProductName\\\":\\\"相框\\\",\\\"grant\\\":\\\"1\\\"},{\\\"FinishedProductName\\\":\\\"相册\\\",\\\"grant\\\":\\\"1\\\"}\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"selectPicTime\",\"value\":\"3\"},{\"name\":\"ShootingScene\",\"value\":\"内景+外景\"}]";
        List<AttrM> dealAttrs = JSON.parseArray(jsonStr, AttrM.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrListGroupModels2\":[{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"shootpersoncount\",\"displayFormat\":\"%s人\"}],\"attrNameList\":[\"PhotoKidsType\",\"shootpersoncount\"],\"displayName\":\"拍摄人数\"},{\"attrFormatModels\":[{\"attrName\":\"photoCount\",\"displayFormat\":\"%s张\"}],\"attrNameList\":[\"photoCount\"],\"attrValueMapModels\":[{\"attrValue\":\"张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"拍摄张数\"},{\"attrNameList\":[\"WithParentChildPhoto\",\"parents_and_children\"],\"attrValueMapModels\":[{\"attrValue\":\"可拍亲子合影\",\"displayValue\":\"含合影\",\"priority\":0},{\"attrValue\":\"不可拍亲子合影\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"有无合影\"},{\"attrNameList\":[\"hasGuider\",\"guide_provided\"],\"displayName\":\"辅助拍摄\"},{\"attrFormatModels\":[{\"attrName\":\"numberscenes_shot\",\"displayFormat\":\"含%s个景点\"}],\"attrNameList\":[\"ShootingScene\",\"numberscenes_shot\",\"photoEnvInfo\"],\"attrValueMapModels\":[{\"attrValue\":\"含个景点\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍场景\",\"seperator\":\"、\"}],\"groupName\":\"拍摄服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"儿童服装%s套/人\"},{\"attrName\":\"dressNumOld\",\"displayFormat\":\"儿童服装%s\"},{\"attrName\":\"Num_makeupsets\",\"displayFormat\":\"化妆%s套/人\"},{\"attrName\":\"WhetherToProvidePhotoClothingOld\",\"displayFormat\":\"家长%s\"},{\"attrName\":\"clothing_for_group\",\"displayFormat\":\"家长%s\"}],\"attrNameList\":[\"dressNum\",\"Num_makeupsets\",\"WhetherToProvidePhotoClothing\",\"clothing_for_group\",\"ProvidePhotoMakeup\"],\"attrValueMapModels\":[{\"attrValue\":\"服装\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"化妆\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"妆造套数\",\"seperator\":\"、\"},{\"attrNameList\":[\"Makeup_brand\"],\"displayName\":\"化妆品牌\",\"seperator\":\"、\"}],\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"Makeup_duration\",\"displayFormat\":\"%s分钟/套\"}],\"attrNameList\":[\"Makeup_duration\"],\"attrValueMapModels\":[{\"attrValue\":\"分钟/套\",\"displayValue\":\"\",\"priority\":0}]}]}],\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"minApplicableAgeStr\",\"displayFormat\":\"%s岁\"},{\"attrName\":\"maxApplicableAgeStr\",\"displayFormat\":\"%s岁\"}],\"attrNameList\":[\"startAge\",\"endAge\",\"minApplicableAgeStr\",\"maxApplicableAgeStr\"],\"displayName\":\"适用年龄\",\"seperator\":\"-\"},{\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"2\"},\"displayFormat\":\"%s风格任选\"},{\"attrName\":\"photo_style\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"3\"},\"attrValueSeperator\":\",\",\"displayCount\":true,\"displayFormat\":\"%d款风格任选\"}],\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\",\"priority\":0},{\"attrValue\":\"2\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"3\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍风格\"},{\"attrFormatModels\":[{\"attrName\":\"duration\",\"displayFormat\":\"%s小时\"},{\"attrName\":\"durationOld\",\"attrValueReplaceModels\":[{\"preStr\":\".*分钟\",\"str\":\"\"}]}],\"attrNameList\":[\"duration\"],\"attrValueMapModels\":[{\"attrValue\":\"小时\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"服务时长\"},{\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum_Desc\",\"displayFormat\":\"精修%s张\"},{\"attrName\":\"intensiveRepairNumOld\",\"displayFormat\":\"精修%s\"},{\"attrName\":\"photoPlateAttachCountOld\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"},\"displayFormat\":\"底片赠送%s\"},{\"attrName\":\"photoPlateAttachCount\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"提供指定张数\"},\"displayFormat\":\"底片赠送%s张\"}],\"attrNameList\":[\"intensiveRepairNum_Desc\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"FinishedDeliverables\"],\"attrValueMapModels\":[{\"attrValue\":\"不提供\",\"displayValue\":\"底片不送\",\"priority\":0},{\"attrValue\":\"底片赠送\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"精修\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"提供指定张数\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"成品交付\",\"seperator\":\"、\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"AdditionalNumberOfShotsOld\",\"displayFormat\":\"儿童%s\"},{\"attrName\":\"Fee_for_additional_children\",\"displayFormat\":\"儿童%s元/人\"}],\"attrNameList\":[\"AdditionalNumberOfShots\",\"Fee_for_additional_children\",\"makeup_additional_children\",\"clothing_additional_children\"],\"attrValueMapModels\":[{\"attrValue\":\"提供化妆\",\"displayValue\":\"含妆造\",\"priority\":0},{\"attrValue\":\"不提供化妆\",\"displayValue\":\"不含妆造\",\"priority\":0},{\"attrValue\":\"是\",\"displayValue\":\"含服装\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含服装\",\"priority\":0}],\"displayName\":\"加人数\",\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"Fee_for_additional_parents\",\"displayFormat\":\"成人%s元/人\"}],\"attrNameList\":[\"Fee_for_additional_parents\",\"makeup_additional_parents\",\"clothing_additional_parents\"],\"attrValueMapModels\":[{\"attrValue\":\"提供化妆\",\"displayValue\":\"含妆造\",\"priority\":0},{\"attrValue\":\"不提供化妆\",\"displayValue\":\"不含妆造\",\"priority\":0},{\"attrValue\":\"是\",\"displayValue\":\"含服装\",\"priority\":0},{\"attrValue\":\"否\",\"displayValue\":\"不含服装\",\"priority\":0}],\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFinishingCost\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFinishingCost\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"加精修\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFilmFee\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFilmFee\"],\"attrValueMapModels\":[{\"attrValue\":\"元/张\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"加底片\"}],\"groupName\":\"加项说明\"},{\"attrModelList\":[{\"attrNameList\":[\"Supplementary_makeup_infor\"],\"displayName\":\"化妆说明\"},{\"attrNameList\":[\"postscript\"],\"displayName\":\"其他说明\"}],\"groupName\":\"额外说明\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后约%s天\"},{\"attrName\":\"selectPicTimeOld\",\"displayFormat\":\"拍摄后约%s天\"}],\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"选片时间\"},{\"attrFormatModels\":[{\"attrName\":\"takePicTime\",\"displayFormat\":\"选片后约%s天\"},{\"attrName\":\"takePicTimeOld\",\"displayFormat\":\"选片后约%s天\"}],\"attrNameList\":[\"takePicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"取片时间\"}],\"groupName\":\"服务流程\",\"videoModuleVO\":{\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\"}}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        assertNotNull(result);
    }

    // 可拍地点测试
    @Test
    public void testLocationsAvailable(){
        String jsonStr = "[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1033841877,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1033841877,\\\\\\\"title\\\\\\\":null,\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"mustGroups\\\\\\\":[],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"html\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1033841877,\\\"title\\\":null,\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":[],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"html\\\"},\\\"productCategories\\\":[],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1033841877,\\\"title\\\":null,\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":null,\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":{\\\"totalPrice\\\":1000.0,\\\"groups\\\":[]},\\\"type\\\":\\\"serviceItem-structure-table\\\"}],\\\"headline\\\":\\\"团购详情\\\",\\\"version\\\":1}\"},{\"name\":\"PlusFinishingCost\",\"value\":\"12\"},{\"name\":\"Supplementary_makeup_infor\",\"value\":\"补充信息说明\"},{\"name\":\"photoPlateAttachCount\",\"value\":\"3\"},{\"name\":\"numberscenes_shot\",\"value\":\"13\"},{\"name\":\"postscript\",\"value\":\"额外说明补充\"},{\"name\":\"IsMakeupProvided\",\"value\":\"提供化妆\"},{\"name\":\"Does_clothing\",\"value\":\"是\"},{\"name\":\"photoEnvInfo\",\"value\":\"白天,自定义说明1,自定义说明2\"},{\"name\":\"IsTheFilmPresentedFree\",\"value\":\"底片部分赠送\"},{\"name\":\"PlusFilmFee\",\"value\":\"13\"},{\"name\":\"photoCount\",\"value\":\"12\"},{\"name\":\"duration\",\"value\":\"8\"},{\"name\":\"dressNum\",\"value\":\"2\"},{\"name\":\"takePicTime\",\"value\":\"3\"},{\"name\":\"Makeup_brand\",\"value\":\"阿玛尼,NARS,化妆1,化妆2\"},{\"name\":\"whetherToProvideClothing\",\"value\":\"提供服装\"},{\"name\":\"Num_makeupsets\",\"value\":\"3\"},{\"name\":\"AdditionalNumberOfShots\",\"value\":\"66\"},{\"name\":\"intensiveRepairNum\",\"value\":\"11\"},{\"name\":\"Makeup_duration\",\"value\":\"54\"},{\"name\":\"Does_makeup\",\"value\":\"提供化妆\"},{\"name\":\"FinishedDeliverables\",\"value\":\"{\\\"FinishedProductName\\\":\\\"相框\\\",\\\"grant\\\":\\\"1\\\"},{\\\"FinishedProductName\\\":\\\"相册\\\",\\\"grant\\\":\\\"1\\\"}\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"selectPicTime\",\"value\":\"3\"},{\"name\":\"ShootingScene\",\"value\":\"内景+外景\"}]";
        List<AttrM> dealAttrs = JSON.parseArray(jsonStr, AttrM.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrListGroupModels2\":[{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"Starting_num\",\"displayFormat\":\"%s人\"}],\"attrNameList\":[\"Starting_num\"],\"displayName\":\"起拍人数\"},{\"attrFormatModels\":[{\"attrName\":\"photoCount\",\"displayFormat\":\"%s张\"}],\"attrNameList\":[\"photoCount\"],\"displayName\":\"拍摄张数\"},{\"attrNameList\":[\"jiweipeizhi\"],\"displayName\":\"机位配置\",\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"Num_locations_available\",\"displayFormat\":\"含%s个地点\"},{\"attrName\":\"Locations_available\",\"attrValueReplaceModels\":[{\"preStr\":\"、\",\"str\":\"，\"}],\"displayFormat\":\"地点为%s\"}],\"attrNameList\":[\"Num_locations_available\",\"Locations_available\"],\"attrValueMapModels\":[{\"attrValue\":\"景点为\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍地点\",\"seperator\":\"、\"}],\"groupName\":\"拍摄服务\",\"groupSubtitleAttrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"shootingDuratonDecimal\",\"displayFormat\":\"%s小时\"}],\"attrNameList\":[\"shootingDuratonDecimal\"]}]},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"dressNum\",\"displayFormat\":\"服装%s套/人\"},{\"attrName\":\"Num_makeupsets\",\"displayFormat\":\"化妆%s套/人\"}],\"attrNameList\":[\"dressNum\",\"Num_makeupsets\",\"whetherToProvideClothing\",\"IsMakeupProvided\"],\"attrValueMapModels\":[{\"attrValue\":\"提供服装\",\"displayValue\":\"含服装\",\"priority\":0},{\"attrValue\":\"不提供服装\",\"displayValue\":\"不含服装\",\"priority\":0},{\"attrValue\":\"提供化妆\",\"displayValue\":\"含化妆\",\"priority\":0},{\"attrValue\":\"不提供化妆\",\"displayValue\":\"不含化妆\",\"priority\":0}],\"displayName\":\"妆造套数\",\"seperator\":\"、\"},{\"attrNameList\":[\"Clothing_type\"],\"displayName\":\"服装类型\",\"seperator\":\"、\"},{\"attrNameList\":[\"Makeup_brand\"],\"displayName\":\"化妆品牌\",\"seperator\":\"、\"}],\"groupName\":\"妆造服务\",\"groupSubtitleAttrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"Makeup_duration\",\"displayFormat\":\"%s分钟/套\"}],\"attrNameList\":[\"Makeup_duration\"]}]}],\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"2\"},\"displayFormat\":\"%s风格任选\"},{\"attrName\":\"photo_style\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"3\"},\"attrValueSeperator\":\",\",\"displayCount\":true,\"displayFormat\":\"%d款风格任选\"}],\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\",\"priority\":0},{\"attrValue\":\"2\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"3\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍风格\"},{\"attrNameList\":[\"applyscene\"],\"displayName\":\"适用场景\",\"seperator\":\"、\"},{\"attrFormatModels\":[{\"attrName\":\"duration\",\"displayFormat\":\"%s小时\"}],\"attrNameList\":[\"duration\"],\"displayName\":\"服务时长\"},{\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s张\"},{\"attrName\":\"photoPlateAttachCount\",\"attrName2FilteredAttrValueMap\":{\"IsTheFilmPresentedFree\":\"底片部分赠送\"},\"displayFormat\":\"底片赠送%s张\"},{\"attrName\":\"videoDuration\",\"displayFormat\":\"含%s分钟视频\"}],\"attrNameList\":[\"intensiveRepairNum\",\"IsTheFilmPresentedFree\",\"photoPlateAttachCount\",\"videoDuration\",\"shifouhanshipinpaishe\",\"FinishedDeliverables\"],\"attrValueMapModels\":[{\"attrValue\":\"底片部分赠送\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"精修0张\",\"displayValue\":\"不含精修\",\"priority\":0},{\"attrValue\":\"是\",\"displayValue\":\"含视频拍摄\",\"priority\":0}],\"displayName\":\"成品交付\",\"seperator\":\"、\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"AdditionalNumberOfShots\",\"displayFormat\":\"%s元/人\"}],\"attrNameList\":[\"AdditionalNumberOfShots\"],\"displayName\":\"加人数\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFinishingCost\",\"displayFormat\":\"%s元/张\"},{\"attrName\":\"jiajingxiufeiyong\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFinishingCost\",\"jiajingxiufeiyong\"],\"displayName\":\"加精修\"},{\"attrFormatModels\":[{\"attrName\":\"PlusFilmFee\",\"displayFormat\":\"%s元/张\"},{\"attrName\":\"jiadipianfeiyong\",\"displayFormat\":\"%s元/张\"}],\"attrNameList\":[\"PlusFilmFee\",\"jiadipianfeiyong\"],\"displayName\":\"加底片\"}],\"groupName\":\"加项说明\"},{\"attrModelList\":[{\"attrNameList\":[\"Supplementary_makeup_infor\"],\"displayName\":\"化妆说明\"},{\"attrNameList\":[\"postscript\",\"ewaishuoming\"],\"displayName\":\"其他说明\"}],\"groupName\":\"额外说明\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后约%s天\"}],\"attrNameList\":[\"selectPicTime\"],\"displayName\":\"选片时间\"},{\"attrFormatModels\":[{\"attrName\":\"takePicTime\",\"displayFormat\":\"选片后约%s天\"},{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后约%s天\"}],\"attrNameList\":[\"takePicTime\",\"chupianshijian\"],\"displayName\":\"取片时间\"}],\"groupName\":\"服务流程\",\"videoModuleVO\":{\"desc\":\"https://p0.meituan.net/ingee/41b0d10606ed3b7b0060438e345b5eaf10168.png\",\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\"}}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        assertNotNull(result);
    }

    // 化妆造型动态标题测试
    @Test
    public void testMakeUpDynamicTitle(){
        String jsonStr = "[{\"name\":\"usepeoplenum\",\"value\":\"1人\"},{\"name\":\"servicemethod\",\"value\":\"上门\"},{\"name\":\"makeupHairstyle\",\"value\":\"是\"},{\"name\":\"giftMakeup\",\"value\":\"是\"},{\"name\":\"postscript\",\"value\":\"额外说明出处\"},{\"name\":\"huazhuangleixing2\",\"value\":\"新娘妆、宴会装、男士妆、上镜妆、自定义化妆1、自定义化妆2\"},{\"name\":\"huazhuangshizhang\",\"value\":\"60\"},{\"name\":\"huazhuangtaoshu\",\"value\":\"1\"},{\"name\":\"huazhuangpinpai3\",\"value\":\"Dior、NARS、纪梵希、化妆品自定义1、化妆品自定音2\"},{\"name\":\"peishidaojubaohan\",\"value\":\"头饰\"},{\"name\":\"jiarenshufeiyong\",\"value\":\"100\"},{\"name\":\"ewaijiaxiang2\",\"value\":\"[{\\\"xiangmuneirong\\\":\\\"假睫毛\\\",\\\"feiyong\\\":\\\"30\\\"},{\\\"xiangmuneirong\\\":\\\"假眉毛\\\",\\\"feiyong\\\":\\\"99\\\"},{\\\"xiangmuneirong\\\":\\\"加人数\\\",\\\"feiyong\\\":\\\"100元/人\\\"}]\"},{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1033809976,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1033809976,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"80.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"100.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2105870,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u9020\\\\\\\\u578b\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":528,\\\\\\\"attrName\\\\\\\":\\\\\\\"usepeoplenum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\\u4eba\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u4eba\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1675,\\\\\\\"attrName\\\\\\\":\\\\\\\"servicemethod\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\\u65b9\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u4e0a\\\\\\\\u95e8\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u4e0a\\\\\\\\u95e8\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2641,\\\\\\\"attrName\\\\\\\":\\\\\\\"makeupHairstyle\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u542b\\\\\\\\u53d1\\\\\\\\u578b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2480,\\\\\\\"attrName\\\\\\\":\\\\\\\"giftMakeup\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\\u5426\\\\\\\\u542b\\\\\\\\u914d\\\\\\\\u9970\\\\\\\\u9053\\\\\\\\u5177\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u662f\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2725,\\\\\\\"attrName\\\\\\\":\\\\\\\"postscript\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\\u51fa\\\\\\\\u5904\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\\u51fa\\\\\\\\u5904\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1498981,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangleixing2\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u7c7b\\\\\\\\u578b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u65b0\\\\\\\\u5a18\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u5bb4\\\\\\\\u4f1a\\\\\\\\u88c5\\\\\\\\u3001\\\\\\\\u7537\\\\\\\\u58eb\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u4e0a\\\\\\\\u955c\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59861\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59862\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u65b0\\\\\\\\u5a18\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u5bb4\\\\\\\\u4f1a\\\\\\\\u88c5\\\\\\\\u3001\\\\\\\\u7537\\\\\\\\u58eb\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u4e0a\\\\\\\\u955c\\\\\\\\u5986\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59861\\\\\\\\u3001\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5316\\\\\\\\u59862\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497424,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangshizhang\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497425,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangtaoshu\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1498880,\\\\\\\"attrName\\\\\\\":\\\\\\\"huazhuangpinpai3\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u724c\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"Dior\\\\\\\\u3001NARS\\\\\\\\u3001\\\\\\\\u7eaa\\\\\\\\u68b5\\\\\\\\u5e0c\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e491\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u97f32\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"Dior\\\\\\\\u3001NARS\\\\\\\\u3001\\\\\\\\u7eaa\\\\\\\\u68b5\\\\\\\\u5e0c\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e491\\\\\\\\u3001\\\\\\\\u5316\\\\\\\\u5986\\\\\\\\u54c1\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u97f32\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497447,\\\\\\\"attrName\\\\\\\":\\\\\\\"peishidaojubaohan\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u914d\\\\\\\\u9970\\\\\\\\u9053\\\\\\\\u5177\\\\\\\\u5305\\\\\\\\u542b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5934\\\\\\\\u9970\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5934\\\\\\\\u9970\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1497609,\\\\\\\"attrName\\\\\\\":\\\\\\\"jiarenshufeiyong\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u52a0\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\\u8d39\\\\\\\\u7528\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"100\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"100\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1500923,\\\\\\\"attrName\\\\\\\":\\\\\\\"ewaijiaxiang2\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u989d\\\\\\\\u5916\\\\\\\\u52a0\\\\\\\\u9879\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u776b\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"30\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u7709\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"99\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u52a0\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"100\\\\\\\\u5143/\\\\\\\\u4eba\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u776b\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"30\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5047\\\\\\\\u7709\\\\\\\\u6bdb\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"99\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"xiangmuneirong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u52a0\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"feiyong\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"100\\\\\\\\u5143/\\\\\\\\u4eba\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1033809976,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"80.0\\\",\\\"marketPrice\\\":\\\"100.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2105870,\\\"name\\\":\\\"\\\\u5316\\\\u5986\\\\u9020\\\\u578b\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":528,\\\"attrName\\\":\\\"usepeoplenum\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\\u4eba\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":\\\"\\\\u4eba\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1675,\\\"attrName\\\":\\\"servicemethod\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u4e0a\\\\u95e8\\\",\\\"rawAttrValue\\\":\\\"\\\\u4e0a\\\\u95e8\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2641,\\\"attrName\\\":\\\"makeupHairstyle\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u53d1\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\",\\\"rawAttrValue\\\":\\\"\\\\u662f\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2480,\\\"attrName\\\":\\\"giftMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\",\\\"rawAttrValue\\\":\\\"\\\\u662f\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2725,\\\"attrName\\\":\\\"postscript\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u51fa\\\\u5904\\\",\\\"rawAttrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u51fa\\\\u5904\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1498981,\\\"attrName\\\":\\\"huazhuangleixing2\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u65b0\\\\u5a18\\\\u5986\\\\u3001\\\\u5bb4\\\\u4f1a\\\\u88c5\\\\u3001\\\\u7537\\\\u58eb\\\\u5986\\\\u3001\\\\u4e0a\\\\u955c\\\\u5986\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59861\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59862\\\",\\\"rawAttrValue\\\":\\\"\\\\u65b0\\\\u5a18\\\\u5986\\\\u3001\\\\u5bb4\\\\u4f1a\\\\u88c5\\\\u3001\\\\u7537\\\\u58eb\\\\u5986\\\\u3001\\\\u4e0a\\\\u955c\\\\u5986\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59861\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59862\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497424,\\\"attrName\\\":\\\"huazhuangshizhang\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497425,\\\"attrName\\\":\\\"huazhuangtaoshu\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1498880,\\\"attrName\\\":\\\"huazhuangpinpai3\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u54c1\\\\u724c\\\",\\\"attrValue\\\":\\\"Dior\\\\u3001NARS\\\\u3001\\\\u7eaa\\\\u68b5\\\\u5e0c\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u4e491\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u97f32\\\",\\\"rawAttrValue\\\":\\\"Dior\\\\u3001NARS\\\\u3001\\\\u7eaa\\\\u68b5\\\\u5e0c\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u4e491\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u97f32\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497447,\\\"attrName\\\":\\\"peishidaojubaohan\\\",\\\"chnName\\\":\\\"\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\\u5305\\\\u542b\\\",\\\"attrValue\\\":\\\"\\\\u5934\\\\u9970\\\",\\\"rawAttrValue\\\":\\\"\\\\u5934\\\\u9970\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1497609,\\\"attrName\\\":\\\"jiarenshufeiyong\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"100\\\",\\\"rawAttrValue\\\":\\\"100\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1500923,\\\"attrName\\\":\\\"ewaijiaxiang2\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u52a0\\\\u9879\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u776b\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u7709\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100\\\\u5143/\\\\u4eba\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u776b\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u7709\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100\\\\u5143/\\\\u4eba\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2105870,\\\"cnName\\\":\\\"\\\\u5316\\\\u5986\\\\u9020\\\\u578b\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1033809976,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"80.0\\\",\\\"marketPrice\\\":\\\"100.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2105870,\\\"name\\\":\\\"\\\\u5316\\\\u5986\\\\u9020\\\\u578b\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":528,\\\"attrName\\\":\\\"usepeoplenum\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\\u4eba\\\"},{\\\"metaAttrId\\\":1675,\\\"attrName\\\":\\\"servicemethod\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u52a1\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u4e0a\\\\u95e8\\\"},{\\\"metaAttrId\\\":2641,\\\"attrName\\\":\\\"makeupHairstyle\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u53d1\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\"},{\\\"metaAttrId\\\":2480,\\\"attrName\\\":\\\"giftMakeup\\\",\\\"chnName\\\":\\\"\\\\u662f\\\\u5426\\\\u542b\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\",\\\"attrValue\\\":\\\"\\\\u662f\\\"},{\\\"metaAttrId\\\":2725,\\\"attrName\\\":\\\"postscript\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u989d\\\\u5916\\\\u8bf4\\\\u660e\\\\u51fa\\\\u5904\\\"},{\\\"metaAttrId\\\":1498981,\\\"attrName\\\":\\\"huazhuangleixing2\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u65b0\\\\u5a18\\\\u5986\\\\u3001\\\\u5bb4\\\\u4f1a\\\\u88c5\\\\u3001\\\\u7537\\\\u58eb\\\\u5986\\\\u3001\\\\u4e0a\\\\u955c\\\\u5986\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59861\\\\u3001\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5316\\\\u59862\\\"},{\\\"metaAttrId\\\":1497424,\\\"attrName\\\":\\\"huazhuangshizhang\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":1497425,\\\"attrName\\\":\\\"huazhuangtaoshu\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"1\\\"},{\\\"metaAttrId\\\":1498880,\\\"attrName\\\":\\\"huazhuangpinpai3\\\",\\\"chnName\\\":\\\"\\\\u5316\\\\u5986\\\\u54c1\\\\u724c\\\",\\\"attrValue\\\":\\\"Dior\\\\u3001NARS\\\\u3001\\\\u7eaa\\\\u68b5\\\\u5e0c\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u4e491\\\\u3001\\\\u5316\\\\u5986\\\\u54c1\\\\u81ea\\\\u5b9a\\\\u97f32\\\"},{\\\"metaAttrId\\\":1497447,\\\"attrName\\\":\\\"peishidaojubaohan\\\",\\\"chnName\\\":\\\"\\\\u914d\\\\u9970\\\\u9053\\\\u5177\\\\u5305\\\\u542b\\\",\\\"attrValue\\\":\\\"\\\\u5934\\\\u9970\\\"},{\\\"metaAttrId\\\":1497609,\\\"attrName\\\":\\\"jiarenshufeiyong\\\",\\\"chnName\\\":\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\u8d39\\\\u7528\\\",\\\"attrValue\\\":\\\"100\\\"},{\\\"metaAttrId\\\":1500923,\\\"attrName\\\":\\\"ewaijiaxiang2\\\",\\\"chnName\\\":\\\"\\\\u989d\\\\u5916\\\\u52a0\\\\u9879\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u776b\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u5047\\\\u7709\\\\u6bdb\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"\\\\u52a0\\\\u4eba\\\\u6570\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100\\\\u5143/\\\\u4eba\\\\\\\"}]\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"团购详情\\\",\\\"content\\\":[{\\\"type\\\":\\\"uniform-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":100.0,\\\"salePrice\\\":80.0,\\\"groups\\\":[{\\\"optionalCount\\\":0,\\\"units\\\":[{\\\"skuCateId\\\":2105870,\\\"projectName\\\":\\\"化妆造型\\\",\\\"amount\\\":1,\\\"attrValues\\\":{\\\"peishidaojubaohan\\\":\\\"头饰\\\",\\\"ewaijiaxiang2\\\":\\\"[{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"假睫毛\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"30\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"假眉毛\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"99\\\\\\\"},{\\\\\\\"xiangmuneirong\\\\\\\":\\\\\\\"加人数\\\\\\\",\\\\\\\"feiyong\\\\\\\":\\\\\\\"100元/人\\\\\\\"}]\\\",\\\"huazhuangleixing2\\\":\\\"新娘妆、宴会装、男士妆、上镜妆、自定义化妆1、自定义化妆2\\\",\\\"postscript\\\":\\\"额外说明出处\\\",\\\"giftMakeup\\\":\\\"是\\\",\\\"huazhuangshizhang\\\":\\\"60\\\",\\\"makeupHairstyle\\\":\\\"是\\\",\\\"usepeoplenum\\\":\\\"1\\\",\\\"skuCateId\\\":\\\"2105870\\\",\\\"servicemethod\\\":\\\"上门\\\",\\\"huazhuangtaoshu\\\":\\\"1\\\",\\\"jiarenshufeiyong\\\":\\\"100\\\",\\\"huazhuangpinpai3\\\":\\\"Dior、NARS、纪梵希、化妆品自定义1、化妆品自定音2\\\"}}]}]}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":100.0,\\\"groups\\\":[{\\\"units\\\":[{\\\"amount\\\":1,\\\"serviceItemName\\\":\\\"化妆造型\\\",\\\"serviceItemValue\\\":{\\\"objectVersion\\\":3,\\\"objectValues\\\":{\\\"peishidaojubaohan\\\":\\\"头饰\\\",\\\"ewaijiaxiang2\\\":[{\\\"xiangmuneirong\\\":\\\"假睫毛\\\",\\\"feiyong\\\":\\\"30\\\"},{\\\"xiangmuneirong\\\":\\\"假眉毛\\\",\\\"feiyong\\\":\\\"99\\\"},{\\\"xiangmuneirong\\\":\\\"加人数\\\",\\\"feiyong\\\":\\\"100元/人\\\"}],\\\"huazhuangleixing2\\\":[\\\"新娘妆\\\",\\\"宴会装\\\",\\\"男士妆\\\",\\\"上镜妆\\\",\\\"自定义化妆1\\\",\\\"自定义化妆2\\\"],\\\"postscript\\\":\\\"额外说明出处\\\",\\\"giftMakeup\\\":\\\"是\\\",\\\"huazhuangshizhang\\\":\\\"60\\\",\\\"makeupHairstyle\\\":\\\"是\\\",\\\"usepeoplenum\\\":1,\\\"skuCateId\\\":2105870,\\\"servicemethod\\\":\\\"上门\\\",\\\"huazhuangtaoshu\\\":\\\"1\\\",\\\"jiarenshufeiyong\\\":\\\"100\\\",\\\"huazhuangpinpai3\\\":[\\\"Dior\\\",\\\"NARS\\\",\\\"纪梵希\\\",\\\"化妆品自定义1\\\",\\\"化妆品自定音2\\\"]},\\\"objectId\\\":21355707}}],\\\"optionalCount\\\":0}]}},{\\\"type\\\":\\\"richtext\\\",\\\"data\\\":\\\"补充信息说明\\\"}]}\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"}]";
        List<AttrM> dealAttrs = JSON.parseArray(jsonStr, AttrM.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrModelList\":[{\"attrNameList\":[\"huazhuangleixing2\"],\"displayName\":\"妆造类型\"},{\"attrFormatModels\":[{\"attrName\":\"makeupHairstyle\",\"attrValueReplaceModels\":[{\"preStr\":\"是\",\"str\":\"含发型\"}]},{\"attrName\":\"giftMakeup\",\"attrValueReplaceModels\":[{\"preStr\":\"是\",\"str\":\"含配饰道具\"}]},{\"attrName\":\"huazhuangshizhang\",\"displayFormat\":\"化妆时长%s分钟/套\"},{\"attrName\":\"huazhuangtaoshu\",\"displayFormat\":\"化妆%s套\"}],\"attrNameList\":[\"huazhuangshizhang\",\"huazhuangtaoshu\",\"makeupHairstyle\",\"giftMakeup\",\"peishidaojubaohan\"],\"displayName\":\"妆造信息\",\"seperator\":\"、\"},{\"attrNameList\":[\"huazhuangpinpai3\"],\"displayName\":\"化妆品牌\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"jiarenshufeiyong\",\"displayFormat\":\"%s元/人\"}],\"attrNameList\":[\"jiarenshufeiyong\"],\"displayName\":\"加人数\"},{\"attrFormatModels\":[{\"attrName\":\"ewaijiaxiang2\",\"attrValueReplaceModels\":[{\"preStr\":\".*\",\"str\":\"\"}]}],\"attrNameList\":[\"ewaijiaxiang2\"],\"dynamicDisplayNameKey\":\"ewaijiaxiang2\"}],\"groupName\":\"加项说明\"},{\"attrModelList\":[{\"attrNameList\":[\"postscript\"],\"displayName\":\"其他说明\"}],\"groupName\":\"额外说明\"}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        assertNotNull(result);
    }

    // 换装体验特殊逻辑
    @Test
    public void testContainDress(){
        String jsonStr = "[{\"name\":\"availablePeopleNum\",\"value\":\"自定义人数2\"},{\"name\":\"period\",\"value\":\"自定义\"},{\"name\":\"containDress\",\"value\":\"服饰租赁\"},{\"name\":\"dressNumStr\",\"value\":\"2套\"},{\"name\":\"dressStyle\",\"value\":\"服装样式自定\"},{\"name\":\"ProvidePhotoMakeup\",\"value\":\"仅发型\"},{\"name\":\"makeupService\",\"value\":\"精致妆容\"},{\"name\":\"photoStyle\",\"value\":\"摄影师跟拍\"},{\"name\":\"photoEnv\",\"value\":\"灯光布置\"},{\"name\":\"photoService\",\"value\":\"白蛇服务说明\"},{\"name\":\"warmtip\",\"value\":\"温馨提示说明\"},{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1034268527,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1034268527,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"50.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"19388.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2105982,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u6362\\\\\\\\u88c5\\\\\\\\u4f53\\\\\\\\u9a8c\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":500.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":2602,\\\\\\\"attrName\\\\\\\":\\\\\\\"availablePeopleNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u7528\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u4eba\\\\\\\\u65702\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u4eba\\\\\\\\u65702\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":573,\\\\\\\"attrName\\\\\\\":\\\\\\\"period\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u7528\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2644,\\\\\\\"attrName\\\\\\\":\\\\\\\"containDress\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u9970\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u9970\\\\\\\\u79df\\\\\\\\u8d41\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u9970\\\\\\\\u79df\\\\\\\\u8d41\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2349,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressNumStr\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5957\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5957\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1383,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressStyle\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u6837\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u6837\\\\\\\\u5f0f\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u6837\\\\\\\\u5f0f\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166019,\\\\\\\"attrName\\\\\\\":\\\\\\\"ProvidePhotoMakeup\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5986\\\\\\\\u9020\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u4ec5\\\\\\\\u53d1\\\\\\\\u578b\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u4ec5\\\\\\\\u53d1\\\\\\\\u578b\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2645,\\\\\\\"attrName\\\\\\\":\\\\\\\"makeupService\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5177\\\\\\\\u4f53\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u7cbe\\\\\\\\u81f4\\\\\\\\u5986\\\\\\\\u5bb9\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u7cbe\\\\\\\\u81f4\\\\\\\\u5986\\\\\\\\u5bb9\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1423,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoStyle\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u7c7b\\\\\\\\u578b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u6444\\\\\\\\u5f71\\\\\\\\u5e08\\\\\\\\u8ddf\\\\\\\\u62cd\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u6444\\\\\\\\u5f71\\\\\\\\u5e08\\\\\\\\u8ddf\\\\\\\\u62cd\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1384,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoEnv\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u573a\\\\\\\\u666f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u706f\\\\\\\\u5149\\\\\\\\u5e03\\\\\\\\u7f6e\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u706f\\\\\\\\u5149\\\\\\\\u5e03\\\\\\\\u7f6e\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2646,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoService\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u767d\\\\\\\\u86c7\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u767d\\\\\\\\u86c7\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":529,\\\\\\\"attrName\\\\\\\":\\\\\\\"warmtip\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6e29\\\\\\\\u99a8\\\\\\\\u63d0\\\\\\\\u9192\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u6e29\\\\\\\\u99a8\\\\\\\\u63d0\\\\\\\\u793a\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u6e29\\\\\\\\u99a8\\\\\\\\u63d0\\\\\\\\u793a\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2105982,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u9ec4\\\\\\\\u5e84\\\\\\\\u4f53\\\\\\\\u9a8c\\\\\\\\u4e8c\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":18888.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":2602,\\\\\\\"attrName\\\\\\\":\\\\\\\"availablePeopleNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u7528\\\\\\\\u4eba\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"4\\\\\\\\u4eba\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"4\\\\\\\\u4eba\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":573,\\\\\\\"attrName\\\\\\\":\\\\\\\"period\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u7528\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\\u5c0f\\\\\\\\u65f6\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\\u5c0f\\\\\\\\u65f6\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2644,\\\\\\\"attrName\\\\\\\":\\\\\\\"containDress\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u670d\\\\\\\\u9970\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5e97\\\\\\\\u5185\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5e97\\\\\\\\u5185\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2349,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressNumStr\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5957\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5957\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1383,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressStyle\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u6837\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u54e5\\\\\\\\u7279\\\\\\\\u98ce\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u54e5\\\\\\\\u7279\\\\\\\\u98ce\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":166019,\\\\\\\"attrName\\\\\\\":\\\\\\\"ProvidePhotoMakeup\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5986\\\\\\\\u9020\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u4ec5\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u9970\\\\\\\\u54c1\\\\\\\\u53ca\\\\\\\\u9053\\\\\\\\u5177\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u4ec5\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\\u9970\\\\\\\\u54c1\\\\\\\\u53ca\\\\\\\\u9053\\\\\\\\u5177\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2645,\\\\\\\"attrName\\\\\\\":\\\\\\\"makeupService\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5177\\\\\\\\u4f53\\\\\\\\u5986\\\\\\\\u9020\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5177\\\\\\\\u4f53\\\\\\\\u5986\\\\\\\\u9020\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5177\\\\\\\\u4f53\\\\\\\\u5986\\\\\\\\u9020\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1423,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoStyle\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u7c7b\\\\\\\\u578b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u62cd\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u62cd\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":529,\\\\\\\"attrName\\\\\\\":\\\\\\\"warmtip\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6e29\\\\\\\\u99a8\\\\\\\\u63d0\\\\\\\\u9192\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u95ee\\\\\\\\u9898\\\\\\\\u5199\\\\\\\\u7684\\\\\\\\u5565\\\\\\\\u7684\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u95ee\\\\\\\\u9898\\\\\\\\u5199\\\\\\\\u7684\\\\\\\\u5565\\\\\\\\u7684\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1034268527,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"50.0\\\",\\\"marketPrice\\\":\\\"19388.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2105982,\\\"name\\\":\\\"\\\\u6362\\\\u88c5\\\\u4f53\\\\u9a8c\\\",\\\"copies\\\":1,\\\"marketPrice\\\":500.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":2602,\\\"attrName\\\":\\\"availablePeopleNum\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u4eba\\\\u65702\\\",\\\"rawAttrValue\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u4eba\\\\u65702\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":573,\\\"attrName\\\":\\\"period\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\",\\\"rawAttrValue\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2644,\\\"attrName\\\":\\\"containDress\\\",\\\"chnName\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u9970\\\",\\\"attrValue\\\":\\\"\\\\u670d\\\\u9970\\\\u79df\\\\u8d41\\\",\\\"rawAttrValue\\\":\\\"\\\\u670d\\\\u9970\\\\u79df\\\\u8d41\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2349,\\\"attrName\\\":\\\"dressNumStr\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\",\\\"rawAttrValue\\\":\\\"2\\\\u5957\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1383,\\\"attrName\\\":\\\"dressStyle\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u6837\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u670d\\\\u88c5\\\\u6837\\\\u5f0f\\\\u81ea\\\\u5b9a\\\",\\\"rawAttrValue\\\":\\\"\\\\u670d\\\\u88c5\\\\u6837\\\\u5f0f\\\\u81ea\\\\u5b9a\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u5986\\\\u9020\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u4ec5\\\\u53d1\\\\u578b\\\",\\\"rawAttrValue\\\":\\\"\\\\u4ec5\\\\u53d1\\\\u578b\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2645,\\\"attrName\\\":\\\"makeupService\\\",\\\"chnName\\\":\\\"\\\\u5177\\\\u4f53\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u7cbe\\\\u81f4\\\\u5986\\\\u5bb9\\\",\\\"rawAttrValue\\\":\\\"\\\\u7cbe\\\\u81f4\\\\u5986\\\\u5bb9\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1423,\\\"attrName\\\":\\\"photoStyle\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u6444\\\\u5f71\\\\u5e08\\\\u8ddf\\\\u62cd\\\",\\\"rawAttrValue\\\":\\\"\\\\u6444\\\\u5f71\\\\u5e08\\\\u8ddf\\\\u62cd\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1384,\\\"attrName\\\":\\\"photoEnv\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u706f\\\\u5149\\\\u5e03\\\\u7f6e\\\",\\\"rawAttrValue\\\":\\\"\\\\u706f\\\\u5149\\\\u5e03\\\\u7f6e\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2646,\\\"attrName\\\":\\\"photoService\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u767d\\\\u86c7\\\\u670d\\\\u52a1\\\\u8bf4\\\\u660e\\\",\\\"rawAttrValue\\\":\\\"\\\\u767d\\\\u86c7\\\\u670d\\\\u52a1\\\\u8bf4\\\\u660e\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":529,\\\"attrName\\\":\\\"warmtip\\\",\\\"chnName\\\":\\\"\\\\u6e29\\\\u99a8\\\\u63d0\\\\u9192\\\",\\\"attrValue\\\":\\\"\\\\u6e29\\\\u99a8\\\\u63d0\\\\u793a\\\\u8bf4\\\\u660e\\\",\\\"rawAttrValue\\\":\\\"\\\\u6e29\\\\u99a8\\\\u63d0\\\\u793a\\\\u8bf4\\\\u660e\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]},{\\\"skuId\\\":0,\\\"productCategory\\\":2105982,\\\"name\\\":\\\"\\\\u9ec4\\\\u5e84\\\\u4f53\\\\u9a8c\\\\u4e8c\\\",\\\"copies\\\":1,\\\"marketPrice\\\":18888.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":2602,\\\"attrName\\\":\\\"availablePeopleNum\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"4\\\\u4eba\\\",\\\"rawAttrValue\\\":\\\"4\\\\u4eba\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":573,\\\"attrName\\\":\\\"period\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"1\\\\u5c0f\\\\u65f6\\\",\\\"rawAttrValue\\\":\\\"1\\\\u5c0f\\\\u65f6\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2644,\\\"attrName\\\":\\\"containDress\\\",\\\"chnName\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u9970\\\",\\\"attrValue\\\":\\\"\\\\u5e97\\\\u5185\\\\u63d0\\\\u4f9b\\\",\\\"rawAttrValue\\\":\\\"\\\\u5e97\\\\u5185\\\\u63d0\\\\u4f9b\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2349,\\\"attrName\\\":\\\"dressNumStr\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\",\\\"rawAttrValue\\\":\\\"2\\\\u5957\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1383,\\\"attrName\\\":\\\"dressStyle\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u6837\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u54e5\\\\u7279\\\\u98ce\\\\u670d\\\\u88c5\\\",\\\"rawAttrValue\\\":\\\"\\\\u54e5\\\\u7279\\\\u98ce\\\\u670d\\\\u88c5\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u5986\\\\u9020\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u4ec5\\\\u63d0\\\\u4f9b\\\\u9970\\\\u54c1\\\\u53ca\\\\u9053\\\\u5177\\\",\\\"rawAttrValue\\\":\\\"\\\\u4ec5\\\\u63d0\\\\u4f9b\\\\u9970\\\\u54c1\\\\u53ca\\\\u9053\\\\u5177\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2645,\\\"attrName\\\":\\\"makeupService\\\",\\\"chnName\\\":\\\"\\\\u5177\\\\u4f53\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u5177\\\\u4f53\\\\u5986\\\\u9020\\\\u8bf4\\\\u660e\\\",\\\"rawAttrValue\\\":\\\"\\\\u5177\\\\u4f53\\\\u5986\\\\u9020\\\\u8bf4\\\\u660e\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1423,\\\"attrName\\\":\\\"photoStyle\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u62cd\\\",\\\"rawAttrValue\\\":\\\"\\\\u81ea\\\\u62cd\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":529,\\\"attrName\\\":\\\"warmtip\\\",\\\"chnName\\\":\\\"\\\\u6e29\\\\u99a8\\\\u63d0\\\\u9192\\\",\\\"attrValue\\\":\\\"\\\\u95ee\\\\u9898\\\\u5199\\\\u7684\\\\u5565\\\\u7684\\\",\\\"rawAttrValue\\\":\\\"\\\\u95ee\\\\u9898\\\\u5199\\\\u7684\\\\u5565\\\\u7684\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2105982,\\\"cnName\\\":\\\"\\\\u6362\\\\u88c5\\\\u4f53\\\\u9a8c\\\"},{\\\"productCategoryId\\\":2105982,\\\"cnName\\\":\\\"\\\\u6362\\\\u88c5\\\\u4f53\\\\u9a8c\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1034268527,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"50.0\\\",\\\"marketPrice\\\":\\\"19388.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2105982,\\\"name\\\":\\\"\\\\u6362\\\\u88c5\\\\u4f53\\\\u9a8c\\\",\\\"copies\\\":1,\\\"marketPrice\\\":500.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":2602,\\\"attrName\\\":\\\"availablePeopleNum\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u4eba\\\\u65702\\\"},{\\\"metaAttrId\\\":573,\\\"attrName\\\":\\\"period\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\"},{\\\"metaAttrId\\\":2644,\\\"attrName\\\":\\\"containDress\\\",\\\"chnName\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u9970\\\",\\\"attrValue\\\":\\\"\\\\u670d\\\\u9970\\\\u79df\\\\u8d41\\\"},{\\\"metaAttrId\\\":2349,\\\"attrName\\\":\\\"dressNumStr\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\"},{\\\"metaAttrId\\\":1383,\\\"attrName\\\":\\\"dressStyle\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u6837\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u670d\\\\u88c5\\\\u6837\\\\u5f0f\\\\u81ea\\\\u5b9a\\\"},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u5986\\\\u9020\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u4ec5\\\\u53d1\\\\u578b\\\"},{\\\"metaAttrId\\\":2645,\\\"attrName\\\":\\\"makeupService\\\",\\\"chnName\\\":\\\"\\\\u5177\\\\u4f53\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u7cbe\\\\u81f4\\\\u5986\\\\u5bb9\\\"},{\\\"metaAttrId\\\":1423,\\\"attrName\\\":\\\"photoStyle\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u6444\\\\u5f71\\\\u5e08\\\\u8ddf\\\\u62cd\\\"},{\\\"metaAttrId\\\":1384,\\\"attrName\\\":\\\"photoEnv\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u706f\\\\u5149\\\\u5e03\\\\u7f6e\\\"},{\\\"metaAttrId\\\":2646,\\\"attrName\\\":\\\"photoService\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u767d\\\\u86c7\\\\u670d\\\\u52a1\\\\u8bf4\\\\u660e\\\"},{\\\"metaAttrId\\\":529,\\\"attrName\\\":\\\"warmtip\\\",\\\"chnName\\\":\\\"\\\\u6e29\\\\u99a8\\\\u63d0\\\\u9192\\\",\\\"attrValue\\\":\\\"\\\\u6e29\\\\u99a8\\\\u63d0\\\\u793a\\\\u8bf4\\\\u660e\\\"}]},{\\\"skuId\\\":0,\\\"productCategory\\\":2105982,\\\"name\\\":\\\"\\\\u9ec4\\\\u5e84\\\\u4f53\\\\u9a8c\\\\u4e8c\\\",\\\"copies\\\":1,\\\"marketPrice\\\":18888.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":2602,\\\"attrName\\\":\\\"availablePeopleNum\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u4eba\\\\u6570\\\",\\\"attrValue\\\":\\\"4\\\\u4eba\\\"},{\\\"metaAttrId\\\":573,\\\"attrName\\\":\\\"period\\\",\\\"chnName\\\":\\\"\\\\u53ef\\\\u7528\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"1\\\\u5c0f\\\\u65f6\\\"},{\\\"metaAttrId\\\":2644,\\\"attrName\\\":\\\"containDress\\\",\\\"chnName\\\":\\\"\\\\u63d0\\\\u4f9b\\\\u670d\\\\u9970\\\",\\\"attrValue\\\":\\\"\\\\u5e97\\\\u5185\\\\u63d0\\\\u4f9b\\\"},{\\\"metaAttrId\\\":2349,\\\"attrName\\\":\\\"dressNumStr\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"2\\\\u5957\\\"},{\\\"metaAttrId\\\":1383,\\\"attrName\\\":\\\"dressStyle\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u6837\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u54e5\\\\u7279\\\\u98ce\\\\u670d\\\\u88c5\\\"},{\\\"metaAttrId\\\":166019,\\\"attrName\\\":\\\"ProvidePhotoMakeup\\\",\\\"chnName\\\":\\\"\\\\u5986\\\\u9020\\\\u670d\\\\u52a1\\\",\\\"attrValue\\\":\\\"\\\\u4ec5\\\\u63d0\\\\u4f9b\\\\u9970\\\\u54c1\\\\u53ca\\\\u9053\\\\u5177\\\"},{\\\"metaAttrId\\\":2645,\\\"attrName\\\":\\\"makeupService\\\",\\\"chnName\\\":\\\"\\\\u5177\\\\u4f53\\\\u5986\\\\u9020\\\",\\\"attrValue\\\":\\\"\\\\u5177\\\\u4f53\\\\u5986\\\\u9020\\\\u8bf4\\\\u660e\\\"},{\\\"metaAttrId\\\":1423,\\\"attrName\\\":\\\"photoStyle\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u62cd\\\"},{\\\"metaAttrId\\\":529,\\\"attrName\\\":\\\"warmtip\\\",\\\"chnName\\\":\\\"\\\\u6e29\\\\u99a8\\\\u63d0\\\\u9192\\\",\\\"attrValue\\\":\\\"\\\\u95ee\\\\u9898\\\\u5199\\\\u7684\\\\u5565\\\\u7684\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"团购详情\\\",\\\"content\\\":[{\\\"type\\\":\\\"uniform-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":19388.0,\\\"salePrice\\\":50.0,\\\"groups\\\":[{\\\"optionalCount\\\":0,\\\"units\\\":[{\\\"skuCateId\\\":2105982,\\\"projectName\\\":\\\"换装体验\\\",\\\"price\\\":500.0,\\\"amount\\\":1,\\\"attrValues\\\":{\\\"photoEnv\\\":\\\"灯光布置\\\",\\\"skuCateId\\\":\\\"2105982\\\",\\\"period\\\":\\\"自定义\\\",\\\"warmtip\\\":\\\"温馨提示说明\\\",\\\"ProvidePhotoMakeup\\\":\\\"仅发型\\\",\\\"containDress\\\":\\\"服饰租赁\\\",\\\"dressStyle\\\":\\\"服装样式自定\\\",\\\"dressNumStr\\\":\\\"2套\\\",\\\"photoService\\\":\\\"白蛇服务说明\\\",\\\"photoStyle\\\":\\\"摄影师跟拍\\\",\\\"makeupService\\\":\\\"精致妆容\\\",\\\"availablePeopleNum\\\":\\\"自定义人数2\\\"}},{\\\"skuCateId\\\":2105982,\\\"projectName\\\":\\\"黄庄体验二\\\",\\\"price\\\":18888.0,\\\"amount\\\":1,\\\"attrValues\\\":{\\\"skuCateId\\\":\\\"2105982\\\",\\\"period\\\":\\\"1小时\\\",\\\"warmtip\\\":\\\"问题写的啥的\\\",\\\"ProvidePhotoMakeup\\\":\\\"仅提供饰品及道具\\\",\\\"containDress\\\":\\\"店内提供\\\",\\\"dressStyle\\\":\\\"哥特风服装\\\",\\\"dressNumStr\\\":\\\"2套\\\",\\\"photoStyle\\\":\\\"自拍\\\",\\\"makeupService\\\":\\\"具体妆造说明\\\",\\\"availablePeopleNum\\\":\\\"4人\\\"}}]}]}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":19388.0,\\\"groups\\\":[{\\\"units\\\":[{\\\"amount\\\":1,\\\"serviceItemName\\\":\\\"换装体验\\\",\\\"price\\\":500.0,\\\"serviceItemValue\\\":{\\\"objectVersion\\\":4,\\\"objectValues\\\":{\\\"photoEnv\\\":\\\"灯光布置\\\",\\\"skuCateId\\\":2105982,\\\"period\\\":\\\"自定义\\\",\\\"warmtip\\\":\\\"温馨提示说明\\\",\\\"ProvidePhotoMakeup\\\":\\\"仅发型\\\",\\\"containDress\\\":\\\"服饰租赁\\\",\\\"dressStyle\\\":\\\"服装样式自定\\\",\\\"dressNumStr\\\":\\\"2套\\\",\\\"photoService\\\":\\\"白蛇服务说明\\\",\\\"photoStyle\\\":\\\"摄影师跟拍\\\",\\\"makeupService\\\":\\\"精致妆容\\\",\\\"availablePeopleNum\\\":\\\"自定义人数2\\\"},\\\"objectId\\\":21392806}},{\\\"amount\\\":1,\\\"serviceItemName\\\":\\\"黄庄体验二\\\",\\\"price\\\":18888.0,\\\"serviceItemValue\\\":{\\\"objectVersion\\\":4,\\\"objectValues\\\":{\\\"skuCateId\\\":2105982,\\\"period\\\":\\\"1小时\\\",\\\"warmtip\\\":\\\"问题写的啥的\\\",\\\"ProvidePhotoMakeup\\\":\\\"仅提供饰品及道具\\\",\\\"containDress\\\":\\\"店内提供\\\",\\\"dressStyle\\\":\\\"哥特风服装\\\",\\\"dressNumStr\\\":\\\"2套\\\",\\\"photoStyle\\\":\\\"自拍\\\",\\\"makeupService\\\":\\\"具体妆造说明\\\",\\\"availablePeopleNum\\\":\\\"4人\\\"},\\\"objectId\\\":21392806}}],\\\"optionalCount\\\":0}]}},{\\\"type\\\":\\\"richtext\\\",\\\"data\\\":\\\"补充信息处\\\"}]}\"},{\"name\":\"photo_style\",\"value\":\"1335458025545,1337081025545,1336708025545,1336707025545,1336706025545,1336705025545,1333875025545,1335437025545,1335438025545,1335436025545,1335176025545,1335178025545,1335101025545,1333935025545,1335233025545,1334792025545,1334015025545,1334791025545,1334785025545,1333939025545,1333997025545,1334754025545,1334733025545,1334668025545,1334662025545,1334377025545,1334011025545,1333915025545,1333993025545,1333886025545,1333873025545,1333904025545,1333908025545,1333897025545,1333885025545,1333874025545\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"relateThemeMode\",\"value\":\"3\"}]";
        List<AttrM> dealAttrs = JSON.parseArray(jsonStr, AttrM.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrListGroupModels2\":[{\"attrModelList\":[{\"attrNameList\":[\"photoStyle\"],\"displayName\":\"拍摄形式\",\"seperator\":\"、\"},{\"attrNameList\":[\"photoEnv\"],\"displayName\":\"拍摄场景\"},{\"attrNameList\":[\"photoEquipment\"],\"displayName\":\"拍摄设备\"},{\"attrNameList\":[\"photoService\"],\"displayName\":\"成品交付\"}],\"groupName\":\"拍摄服务\"},{\"attrModelList\":[{\"attrNameList\":[\"containDress\"],\"displayName\":\"服装包含\"},{\"attrNameList\":[\"dressStyle\"],\"displayName\":\"服装样式\"},{\"attrNameList\":[\"ProvidePhotoMakeup\",\"makeupService\"],\"displayName\":\"化妆包含\",\"seperator\":\"、\"}],\"groupName\":\"妆造服务\"}],\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"relatePartThemeType\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"2\"},\"displayFormat\":\"%s风格任选\"},{\"attrName\":\"photo_style\",\"attrName2FilteredAttrValueMap\":{\"relateThemeMode\":\"3\"},\"attrValueSeperator\":\",\",\"displayCount\":true,\"displayFormat\":\"%d款风格任选\"}],\"attrNameList\":[\"relateThemeMode\",\"relatePartThemeType\",\"photo_style\"],\"attrValueMapModels\":[{\"attrValue\":\"1\",\"displayValue\":\"全店风格任选\",\"priority\":0},{\"attrValue\":\"2\",\"displayValue\":\"\",\"priority\":0},{\"attrValue\":\"3\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"可拍风格\"},{\"attrNameList\":[\"availablePeopleNum\"],\"displayName\":\"适用人数\",\"seperator\":\"、\"},{\"attrNameList\":[\"durationStr\"],\"displayName\":\"可用时长\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrNameList\":[\"addedServices\"],\"displayName\":\"赠送说明\"},{\"attrNameList\":[\"warmtip\"],\"displayName\":\"其他说明\"}],\"groupName\":\"额外说明\"}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        assertNotNull(result);
    }
    // 团体照精修特殊逻辑
    @Test
    public void testPhotoPlateGrant(){
        String jsonStr = "[{\"name\":\"spuCategory\",\"value\":\"签证照、团体照、身份证照、职业照、儿童入学照\"},{\"name\":\"poseProduct\",\"value\":\"整体妆面、发型设计、隐形内衣、精华素、卸妆洁面\"},{\"name\":\"dressNum\",\"value\":\"3套/人\"},{\"name\":\"dressStyle\",\"value\":\"自备、店内提供\"},{\"name\":\"photoEnv\",\"value\":\"棚内、外景、灯光布置\"},{\"name\":\"photoBackground\",\"value\":\"白、灰、粉、油画布景、书房布景\"},{\"name\":\"equipment\",\"value\":\"可达自定义输入\"},{\"name\":\"photoCount\",\"value\":\"50张\"},{\"name\":\"intensiveRepairNum\",\"value\":\"10张\"},{\"name\":\"photoPlateGrant\",\"value\":\"原始底片\"},{\"name\":\"photoOutput\",\"value\":\"输出自定义大大大\"},{\"name\":\"applytarget\",\"value\":\"家庭、婚礼、求婚、活动、会议、聚会、访谈\"},{\"name\":\"duration\",\"value\":\"2小时\"},{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1034261170,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1034261170,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"100.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"500.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":840,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u4f53\\\\\\\\u7167\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":500.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":642,\\\\\\\"attrName\\\\\\\":\\\\\\\"spuCategory\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9002\\\\\\\\u7528\\\\\\\\u573a\\\\\\\\u666f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u7b7e\\\\\\\\u8bc1\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u56e2\\\\\\\\u4f53\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u8eab\\\\\\\\u4efd\\\\\\\\u8bc1\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u804c\\\\\\\\u4e1a\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u513f\\\\\\\\u7ae5\\\\\\\\u5165\\\\\\\\u5b66\\\\\\\\u7167\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u7b7e\\\\\\\\u8bc1\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u56e2\\\\\\\\u4f53\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u8eab\\\\\\\\u4efd\\\\\\\\u8bc1\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u804c\\\\\\\\u4e1a\\\\\\\\u7167\\\\\\\\u3001\\\\\\\\u513f\\\\\\\\u7ae5\\\\\\\\u5165\\\\\\\\u5b66\\\\\\\\u7167\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1381,\\\\\\\"attrName\\\\\\\":\\\\\\\"poseProduct\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9020\\\\\\\\u578b\\\\\\\\u9879\\\\\\\\u76ee\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u6574\\\\\\\\u4f53\\\\\\\\u5986\\\\\\\\u9762\\\\\\\\u3001\\\\\\\\u53d1\\\\\\\\u578b\\\\\\\\u8bbe\\\\\\\\u8ba1\\\\\\\\u3001\\\\\\\\u9690\\\\\\\\u5f62\\\\\\\\u5185\\\\\\\\u8863\\\\\\\\u3001\\\\\\\\u7cbe\\\\\\\\u534e\\\\\\\\u7d20\\\\\\\\u3001\\\\\\\\u5378\\\\\\\\u5986\\\\\\\\u6d01\\\\\\\\u9762\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u6574\\\\\\\\u4f53\\\\\\\\u5986\\\\\\\\u9762\\\\\\\\u3001\\\\\\\\u53d1\\\\\\\\u578b\\\\\\\\u8bbe\\\\\\\\u8ba1\\\\\\\\u3001\\\\\\\\u9690\\\\\\\\u5f62\\\\\\\\u5185\\\\\\\\u8863\\\\\\\\u3001\\\\\\\\u7cbe\\\\\\\\u534e\\\\\\\\u7d20\\\\\\\\u3001\\\\\\\\u5378\\\\\\\\u5986\\\\\\\\u6d01\\\\\\\\u9762\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1382,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u5957\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"3\\\\\\\\u5957/\\\\\\\\u4eba\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5957/\\\\\\\\u4eba\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1383,\\\\\\\"attrName\\\\\\\":\\\\\\\"dressStyle\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u88c5\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u5907\\\\\\\\u3001\\\\\\\\u5e97\\\\\\\\u5185\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u5907\\\\\\\\u3001\\\\\\\\u5e97\\\\\\\\u5185\\\\\\\\u63d0\\\\\\\\u4f9b\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1384,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoEnv\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u573a\\\\\\\\u666f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u68da\\\\\\\\u5185\\\\\\\\u3001\\\\\\\\u5916\\\\\\\\u666f\\\\\\\\u3001\\\\\\\\u706f\\\\\\\\u5149\\\\\\\\u5e03\\\\\\\\u7f6e\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u68da\\\\\\\\u5185\\\\\\\\u3001\\\\\\\\u5916\\\\\\\\u666f\\\\\\\\u3001\\\\\\\\u706f\\\\\\\\u5149\\\\\\\\u5e03\\\\\\\\u7f6e\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1385,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoBackground\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u80cc\\\\\\\\u666f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u767d\\\\\\\\u3001\\\\\\\\u7070\\\\\\\\u3001\\\\\\\\u7c89\\\\\\\\u3001\\\\\\\\u6cb9\\\\\\\\u753b\\\\\\\\u5e03\\\\\\\\u666f\\\\\\\\u3001\\\\\\\\u4e66\\\\\\\\u623f\\\\\\\\u5e03\\\\\\\\u666f\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u767d\\\\\\\\u3001\\\\\\\\u7070\\\\\\\\u3001\\\\\\\\u7c89\\\\\\\\u3001\\\\\\\\u6cb9\\\\\\\\u753b\\\\\\\\u5e03\\\\\\\\u666f\\\\\\\\u3001\\\\\\\\u4e66\\\\\\\\u623f\\\\\\\\u5e03\\\\\\\\u666f\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":975,\\\\\\\"attrName\\\\\\\":\\\\\\\"equipment\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u4f7f\\\\\\\\u7528\\\\\\\\u4eea\\\\\\\\u5668\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u8fbe\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u8f93\\\\\\\\u5165\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u53ef\\\\\\\\u8fbe\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u8f93\\\\\\\\u5165\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1386,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoCount\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"50\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"50\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1387,\\\\\\\"attrName\\\\\\\":\\\\\\\"intensiveRepairNum\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u7cbe\\\\\\\\u4fee\\\\\\\\u5f20\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"10\\\\\\\\u5f20\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5f20\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1388,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoPlateGrant\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\\u5168\\\\\\\\u9001\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u539f\\\\\\\\u59cb\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u539f\\\\\\\\u59cb\\\\\\\\u5e95\\\\\\\\u7247\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1389,\\\\\\\"attrName\\\\\\\":\\\\\\\"photoOutput\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u7167\\\\\\\\u7247\\\\\\\\u8f93\\\\\\\\u51fa\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u8f93\\\\\\\\u51fa\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5927\\\\\\\\u5927\\\\\\\\u5927\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u8f93\\\\\\\\u51fa\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5927\\\\\\\\u5927\\\\\\\\u5927\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":787,\\\\\\\"attrName\\\\\\\":\\\\\\\"applytarget\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u5bf9\\\\\\\\u8c61\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5bb6\\\\\\\\u5ead\\\\\\\\u3001\\\\\\\\u5a5a\\\\\\\\u793c\\\\\\\\u3001\\\\\\\\u6c42\\\\\\\\u5a5a\\\\\\\\u3001\\\\\\\\u6d3b\\\\\\\\u52a8\\\\\\\\u3001\\\\\\\\u4f1a\\\\\\\\u8bae\\\\\\\\u3001\\\\\\\\u805a\\\\\\\\u4f1a\\\\\\\\u3001\\\\\\\\u8bbf\\\\\\\\u8c08\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5bb6\\\\\\\\u5ead\\\\\\\\u3001\\\\\\\\u5a5a\\\\\\\\u793c\\\\\\\\u3001\\\\\\\\u6c42\\\\\\\\u5a5a\\\\\\\\u3001\\\\\\\\u6d3b\\\\\\\\u52a8\\\\\\\\u3001\\\\\\\\u4f1a\\\\\\\\u8bae\\\\\\\\u3001\\\\\\\\u805a\\\\\\\\u4f1a\\\\\\\\u3001\\\\\\\\u8bbf\\\\\\\\u8c08\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":572,\\\\\\\"attrName\\\\\\\":\\\\\\\"duration\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u62cd\\\\\\\\u6444\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2\\\\\\\\u5c0f\\\\\\\\u65f6\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u5c0f\\\\\\\\u65f6\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1034261170,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"100.0\\\",\\\"marketPrice\\\":\\\"500.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":840,\\\"name\\\":\\\"\\\\u56e2\\\\u4f53\\\\u7167\\\",\\\"copies\\\":1,\\\"marketPrice\\\":500.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":642,\\\"attrName\\\":\\\"spuCategory\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u7b7e\\\\u8bc1\\\\u7167\\\\u3001\\\\u56e2\\\\u4f53\\\\u7167\\\\u3001\\\\u8eab\\\\u4efd\\\\u8bc1\\\\u7167\\\\u3001\\\\u804c\\\\u4e1a\\\\u7167\\\\u3001\\\\u513f\\\\u7ae5\\\\u5165\\\\u5b66\\\\u7167\\\",\\\"rawAttrValue\\\":\\\"\\\\u7b7e\\\\u8bc1\\\\u7167\\\\u3001\\\\u56e2\\\\u4f53\\\\u7167\\\\u3001\\\\u8eab\\\\u4efd\\\\u8bc1\\\\u7167\\\\u3001\\\\u804c\\\\u4e1a\\\\u7167\\\\u3001\\\\u513f\\\\u7ae5\\\\u5165\\\\u5b66\\\\u7167\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1381,\\\"attrName\\\":\\\"poseProduct\\\",\\\"chnName\\\":\\\"\\\\u9020\\\\u578b\\\\u9879\\\\u76ee\\\",\\\"attrValue\\\":\\\"\\\\u6574\\\\u4f53\\\\u5986\\\\u9762\\\\u3001\\\\u53d1\\\\u578b\\\\u8bbe\\\\u8ba1\\\\u3001\\\\u9690\\\\u5f62\\\\u5185\\\\u8863\\\\u3001\\\\u7cbe\\\\u534e\\\\u7d20\\\\u3001\\\\u5378\\\\u5986\\\\u6d01\\\\u9762\\\",\\\"rawAttrValue\\\":\\\"\\\\u6574\\\\u4f53\\\\u5986\\\\u9762\\\\u3001\\\\u53d1\\\\u578b\\\\u8bbe\\\\u8ba1\\\\u3001\\\\u9690\\\\u5f62\\\\u5185\\\\u8863\\\\u3001\\\\u7cbe\\\\u534e\\\\u7d20\\\\u3001\\\\u5378\\\\u5986\\\\u6d01\\\\u9762\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1382,\\\"attrName\\\":\\\"dressNum\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"3\\\\u5957/\\\\u4eba\\\",\\\"rawAttrValue\\\":\\\"3\\\",\\\"unit\\\":\\\"\\\\u5957/\\\\u4eba\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1383,\\\"attrName\\\":\\\"dressStyle\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u63d0\\\\u4f9b\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u5907\\\\u3001\\\\u5e97\\\\u5185\\\\u63d0\\\\u4f9b\\\",\\\"rawAttrValue\\\":\\\"\\\\u81ea\\\\u5907\\\\u3001\\\\u5e97\\\\u5185\\\\u63d0\\\\u4f9b\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1384,\\\"attrName\\\":\\\"photoEnv\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u68da\\\\u5185\\\\u3001\\\\u5916\\\\u666f\\\\u3001\\\\u706f\\\\u5149\\\\u5e03\\\\u7f6e\\\",\\\"rawAttrValue\\\":\\\"\\\\u68da\\\\u5185\\\\u3001\\\\u5916\\\\u666f\\\\u3001\\\\u706f\\\\u5149\\\\u5e03\\\\u7f6e\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1385,\\\"attrName\\\":\\\"photoBackground\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u80cc\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u767d\\\\u3001\\\\u7070\\\\u3001\\\\u7c89\\\\u3001\\\\u6cb9\\\\u753b\\\\u5e03\\\\u666f\\\\u3001\\\\u4e66\\\\u623f\\\\u5e03\\\\u666f\\\",\\\"rawAttrValue\\\":\\\"\\\\u767d\\\\u3001\\\\u7070\\\\u3001\\\\u7c89\\\\u3001\\\\u6cb9\\\\u753b\\\\u5e03\\\\u666f\\\\u3001\\\\u4e66\\\\u623f\\\\u5e03\\\\u666f\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":975,\\\"attrName\\\":\\\"equipment\\\",\\\"chnName\\\":\\\"\\\\u4f7f\\\\u7528\\\\u4eea\\\\u5668\\\",\\\"attrValue\\\":\\\"\\\\u53ef\\\\u8fbe\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u8f93\\\\u5165\\\",\\\"rawAttrValue\\\":\\\"\\\\u53ef\\\\u8fbe\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u8f93\\\\u5165\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1386,\\\"attrName\\\":\\\"photoCount\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"50\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"50\\\",\\\"unit\\\":\\\"\\\\u5f20\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1387,\\\"attrName\\\":\\\"intensiveRepairNum\\\",\\\"chnName\\\":\\\"\\\\u7cbe\\\\u4fee\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"10\\\\u5f20\\\",\\\"rawAttrValue\\\":\\\"10\\\",\\\"unit\\\":\\\"\\\\u5f20\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1388,\\\"attrName\\\":\\\"photoPlateGrant\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u5168\\\\u9001\\\",\\\"attrValue\\\":\\\"\\\\u539f\\\\u59cb\\\\u5e95\\\\u7247\\\",\\\"rawAttrValue\\\":\\\"\\\\u539f\\\\u59cb\\\\u5e95\\\\u7247\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1389,\\\"attrName\\\":\\\"photoOutput\\\",\\\"chnName\\\":\\\"\\\\u7167\\\\u7247\\\\u8f93\\\\u51fa\\\",\\\"attrValue\\\":\\\"\\\\u8f93\\\\u51fa\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5927\\\\u5927\\\\u5927\\\",\\\"rawAttrValue\\\":\\\"\\\\u8f93\\\\u51fa\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5927\\\\u5927\\\\u5927\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":787,\\\"attrName\\\":\\\"applytarget\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5bf9\\\\u8c61\\\",\\\"attrValue\\\":\\\"\\\\u5bb6\\\\u5ead\\\\u3001\\\\u5a5a\\\\u793c\\\\u3001\\\\u6c42\\\\u5a5a\\\\u3001\\\\u6d3b\\\\u52a8\\\\u3001\\\\u4f1a\\\\u8bae\\\\u3001\\\\u805a\\\\u4f1a\\\\u3001\\\\u8bbf\\\\u8c08\\\",\\\"rawAttrValue\\\":\\\"\\\\u5bb6\\\\u5ead\\\\u3001\\\\u5a5a\\\\u793c\\\\u3001\\\\u6c42\\\\u5a5a\\\\u3001\\\\u6d3b\\\\u52a8\\\\u3001\\\\u4f1a\\\\u8bae\\\\u3001\\\\u805a\\\\u4f1a\\\\u3001\\\\u8bbf\\\\u8c08\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":572,\\\"attrName\\\":\\\"duration\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"2\\\\u5c0f\\\\u65f6\\\",\\\"rawAttrValue\\\":\\\"2\\\",\\\"unit\\\":\\\"\\\\u5c0f\\\\u65f6\\\",\\\"valueType\\\":401,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":840,\\\"cnName\\\":\\\"\\\\u5feb\\\\u7167\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1034261170,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"100.0\\\",\\\"marketPrice\\\":\\\"500.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":840,\\\"name\\\":\\\"\\\\u56e2\\\\u4f53\\\\u7167\\\",\\\"copies\\\":1,\\\"marketPrice\\\":500.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":642,\\\"attrName\\\":\\\"spuCategory\\\",\\\"chnName\\\":\\\"\\\\u9002\\\\u7528\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u7b7e\\\\u8bc1\\\\u7167\\\\u3001\\\\u56e2\\\\u4f53\\\\u7167\\\\u3001\\\\u8eab\\\\u4efd\\\\u8bc1\\\\u7167\\\\u3001\\\\u804c\\\\u4e1a\\\\u7167\\\\u3001\\\\u513f\\\\u7ae5\\\\u5165\\\\u5b66\\\\u7167\\\"},{\\\"metaAttrId\\\":1381,\\\"attrName\\\":\\\"poseProduct\\\",\\\"chnName\\\":\\\"\\\\u9020\\\\u578b\\\\u9879\\\\u76ee\\\",\\\"attrValue\\\":\\\"\\\\u6574\\\\u4f53\\\\u5986\\\\u9762\\\\u3001\\\\u53d1\\\\u578b\\\\u8bbe\\\\u8ba1\\\\u3001\\\\u9690\\\\u5f62\\\\u5185\\\\u8863\\\\u3001\\\\u7cbe\\\\u534e\\\\u7d20\\\\u3001\\\\u5378\\\\u5986\\\\u6d01\\\\u9762\\\"},{\\\"metaAttrId\\\":1382,\\\"attrName\\\":\\\"dressNum\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u5957\\\\u6570\\\",\\\"attrValue\\\":\\\"3\\\\u5957/\\\\u4eba\\\"},{\\\"metaAttrId\\\":1383,\\\"attrName\\\":\\\"dressStyle\\\",\\\"chnName\\\":\\\"\\\\u670d\\\\u88c5\\\\u63d0\\\\u4f9b\\\",\\\"attrValue\\\":\\\"\\\\u81ea\\\\u5907\\\\u3001\\\\u5e97\\\\u5185\\\\u63d0\\\\u4f9b\\\"},{\\\"metaAttrId\\\":1384,\\\"attrName\\\":\\\"photoEnv\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u573a\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u68da\\\\u5185\\\\u3001\\\\u5916\\\\u666f\\\\u3001\\\\u706f\\\\u5149\\\\u5e03\\\\u7f6e\\\"},{\\\"metaAttrId\\\":1385,\\\"attrName\\\":\\\"photoBackground\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u80cc\\\\u666f\\\",\\\"attrValue\\\":\\\"\\\\u767d\\\\u3001\\\\u7070\\\\u3001\\\\u7c89\\\\u3001\\\\u6cb9\\\\u753b\\\\u5e03\\\\u666f\\\\u3001\\\\u4e66\\\\u623f\\\\u5e03\\\\u666f\\\"},{\\\"metaAttrId\\\":975,\\\"attrName\\\":\\\"equipment\\\",\\\"chnName\\\":\\\"\\\\u4f7f\\\\u7528\\\\u4eea\\\\u5668\\\",\\\"attrValue\\\":\\\"\\\\u53ef\\\\u8fbe\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u8f93\\\\u5165\\\"},{\\\"metaAttrId\\\":1386,\\\"attrName\\\":\\\"photoCount\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"50\\\\u5f20\\\"},{\\\"metaAttrId\\\":1387,\\\"attrName\\\":\\\"intensiveRepairNum\\\",\\\"chnName\\\":\\\"\\\\u7cbe\\\\u4fee\\\\u5f20\\\\u6570\\\",\\\"attrValue\\\":\\\"10\\\\u5f20\\\"},{\\\"metaAttrId\\\":1388,\\\"attrName\\\":\\\"photoPlateGrant\\\",\\\"chnName\\\":\\\"\\\\u5e95\\\\u7247\\\\u5168\\\\u9001\\\",\\\"attrValue\\\":\\\"\\\\u539f\\\\u59cb\\\\u5e95\\\\u7247\\\"},{\\\"metaAttrId\\\":1389,\\\"attrName\\\":\\\"photoOutput\\\",\\\"chnName\\\":\\\"\\\\u7167\\\\u7247\\\\u8f93\\\\u51fa\\\",\\\"attrValue\\\":\\\"\\\\u8f93\\\\u51fa\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5927\\\\u5927\\\\u5927\\\"},{\\\"metaAttrId\\\":787,\\\"attrName\\\":\\\"applytarget\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u5bf9\\\\u8c61\\\",\\\"attrValue\\\":\\\"\\\\u5bb6\\\\u5ead\\\\u3001\\\\u5a5a\\\\u793c\\\\u3001\\\\u6c42\\\\u5a5a\\\\u3001\\\\u6d3b\\\\u52a8\\\\u3001\\\\u4f1a\\\\u8bae\\\\u3001\\\\u805a\\\\u4f1a\\\\u3001\\\\u8bbf\\\\u8c08\\\"},{\\\"metaAttrId\\\":572,\\\"attrName\\\":\\\"duration\\\",\\\"chnName\\\":\\\"\\\\u62cd\\\\u6444\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"2\\\\u5c0f\\\\u65f6\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"团购详情\\\",\\\"content\\\":[{\\\"type\\\":\\\"uniform-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":500.0,\\\"salePrice\\\":100.0,\\\"groups\\\":[{\\\"optionalCount\\\":0,\\\"units\\\":[{\\\"skuCateId\\\":840,\\\"projectName\\\":\\\"团体照\\\",\\\"price\\\":500.0,\\\"amount\\\":1,\\\"attrValues\\\":{\\\"photoPlateGrant\\\":\\\"原始底片\\\",\\\"dressStyle\\\":\\\"自备、店内提供\\\",\\\"equipment\\\":\\\"可达自定义输入\\\",\\\"photoCount\\\":\\\"50\\\",\\\"dressNum\\\":\\\"3\\\",\\\"photoEnv\\\":\\\"棚内、外景、灯光布置\\\",\\\"duration\\\":\\\"2\\\",\\\"skuCateId\\\":\\\"840\\\",\\\"photoOutput\\\":\\\"输出自定义大大大\\\",\\\"applytarget\\\":\\\"家庭、婚礼、求婚、活动、会议、聚会、访谈\\\",\\\"spuCategory\\\":\\\"签证照、团体照、身份证照、职业照、儿童入学照\\\",\\\"intensiveRepairNum\\\":\\\"10\\\",\\\"photoBackground\\\":\\\"白、灰、粉、油画布景、书房布景\\\",\\\"poseProduct\\\":\\\"整体妆面、发型设计、隐形内衣、精华素、卸妆洁面\\\"}}]}]}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":500.0,\\\"groups\\\":[{\\\"units\\\":[{\\\"amount\\\":1,\\\"serviceItemName\\\":\\\"团体照\\\",\\\"price\\\":500.0,\\\"serviceItemValue\\\":{\\\"objectVersion\\\":2,\\\"objectValues\\\":{\\\"photoPlateGrant\\\":[\\\"原始底片\\\"],\\\"dressStyle\\\":[\\\"自备\\\",\\\"店内提供\\\"],\\\"equipment\\\":\\\"可达自定义输入\\\",\\\"photoCount\\\":50,\\\"dressNum\\\":3,\\\"photoEnv\\\":[\\\"棚内\\\",\\\"外景\\\",\\\"灯光布置\\\"],\\\"duration\\\":\\\"2\\\",\\\"skuCateId\\\":840,\\\"photoOutput\\\":\\\"输出自定义大大大\\\",\\\"applytarget\\\":[\\\"家庭\\\",\\\"婚礼\\\",\\\"求婚\\\",\\\"活动\\\",\\\"会议\\\",\\\"聚会\\\",\\\"访谈\\\"],\\\"spuCategory\\\":[\\\"签证照\\\",\\\"团体照\\\",\\\"身份证照\\\",\\\"职业照\\\",\\\"儿童入学照\\\"],\\\"intensiveRepairNum\\\":\\\"10\\\",\\\"photoBackground\\\":[\\\"白\\\",\\\"灰\\\",\\\"粉\\\",\\\"油画布景\\\",\\\"书房布景\\\"],\\\"poseProduct\\\":[\\\"整体妆面\\\",\\\"发型设计\\\",\\\"隐形内衣\\\",\\\"精华素\\\",\\\"卸妆洁面\\\"]},\\\"objectId\\\":21397498}}],\\\"optionalCount\\\":0}]}},{\\\"type\\\":\\\"richtext\\\",\\\"data\\\":\\\"补充信息说明\\\"}]}\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"}]";
        List<AttrM> dealAttrs = JSON.parseArray(jsonStr, AttrM.class);
        jsonStr = "{\"attrListGroupModels\":[{\"attrListGroupModels2\":[{\"attrModelList\":[{\"attrNameList\":[\"applytarget\"],\"displayName\":\"适用对象\",\"seperator\":\"、\"},{\"attrNameList\":[\"photoCount\"],\"displayName\":\"拍摄张数\"},{\"attrNameList\":[\"photoEnv\"],\"displayName\":\"拍摄场景\"},{\"attrNameList\":[\"photoBackground\"],\"displayName\":\"拍摄背景\",\"seperator\":\"、\"},{\"attrNameList\":[\"equipment\"],\"displayName\":\"适用设备\"}],\"groupName\":\"拍摄服务\"},{\"attrModelList\":[{\"attrNameList\":[\"dressStyle\"],\"displayName\":\"服装样式\",\"seperator\":\"、\"},{\"attrNameList\":[\"dressNum\"],\"displayName\":\"服装套数\"},{\"attrNameList\":[\"poseProduct\"],\"displayName\":\"其他包含\"}],\"groupName\":\"妆造服务\"}],\"attrModelList\":[{\"attrNameList\":[\"spuCategory\"],\"displayName\":\"适用场景\",\"seperator\":\"、\"},{\"attrNameList\":[\"duration\"],\"displayName\":\"服务时长\"},{\"attrFormatModels\":[{\"attrName\":\"intensiveRepairNum\",\"displayFormat\":\"精修%s\"}],\"attrNameList\":[\"intensiveRepairNum\",\"photoPlateGrant\",\"photoOutput\"],\"attrValueMapModels\":[{\"attrValue\":\"原始底片\",\"displayValue\":\"底片全送\",\"priority\":0},{\"attrValue\":\"精修底片\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"成品交付\",\"seperator\":\"、\"}],\"groupName\":\"套餐服务\"},{\"attrModelList\":[{\"attrFormatModels\":[{\"attrName\":\"selectPicTime\",\"displayFormat\":\"拍摄后约%s\"}],\"attrNameList\":[\"selectPicTime\"],\"attrValueMapModels\":[{\"attrValue\":\"拍摄后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"选片时间\"},{\"attrFormatModels\":[{\"attrName\":\"chupianshijian\",\"displayFormat\":\"选片后约%s\"}],\"attrNameList\":[\"chupianshijian\"],\"attrValueMapModels\":[{\"attrValue\":\"选片后约\",\"displayValue\":\"\",\"priority\":0}],\"displayName\":\"取片时间\"}],\"groupName\":\"服务流程\",\"videoModuleVO\":{\"url\":\"https://p0.meituan.net/ingee/889dd492417dbca031d2421f41810e7911633.png\"}}],\"taiji\":false}";
        config = JSON.parseObject(jsonStr, CommonPhotoDealAttrVOListOpt.Config.class);
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        // 执行测试方法
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        assertNotNull(result);
    }
}
