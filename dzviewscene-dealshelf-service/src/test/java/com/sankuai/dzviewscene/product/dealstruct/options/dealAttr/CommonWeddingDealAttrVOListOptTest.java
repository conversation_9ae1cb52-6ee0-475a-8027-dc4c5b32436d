package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.CommonWeddingDealAttrVOListOpt.AttrListGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.CommonWeddingDealAttrVOListOpt.MergeSortMapJoinFilterAttrModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CommonWeddingDealAttrVOListOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private CommonWeddingDealAttrVOListOpt.Config config;

    private static final String SERVICE_STAFF_KEY = "serviceStaff";
    private static final String CLOTHING_STYLES_KEY = "clothingStyles";

    // Test when config is null
    @Test
    public void testComputeConfigIsNull() throws Throwable {
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, null);
        assertNull(result);
    }

    // Test when param is null
    @Test
    public void testComputeParamIsNull() throws Throwable {
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, null, config);
        assertNull(result);
    }

    // Test when attrListGroupModels is null
    @Test
    public void testComputeAttrListGroupModelsIsNull() throws Throwable {
        when(config.getAttrListGroupModels()).thenReturn(null);
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNull(result);
    }

    // Test when attrListGroupModels is empty
    @Test
    public void testComputeAttrListGroupModelsIsEmpty() throws Throwable {
        when(config.getAttrListGroupModels()).thenReturn(Collections.emptyList());
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNull(result);
    }

    // Test when attrListGroupModels contains one model
    @Test
    public void testComputeAttrListGroupModelsContainsOneModel() throws Throwable {
        AttrListGroupModel mockAttrListGroupModel = mock(AttrListGroupModel.class);
        MergeSortMapJoinFilterAttrModel mockMergeSortMapJoinFilterAttrModel = mock(MergeSortMapJoinFilterAttrModel.class);
        // Mocking non-empty attrModelList with the correct type and ensuring it leads to a non-empty result
        when(mockMergeSortMapJoinFilterAttrModel.getDisplayName()).thenReturn("Test Display Name");
        when(mockMergeSortMapJoinFilterAttrModel.getAttrNameList()).thenReturn(Collections.singletonList("Test Attr Name"));
        when(mockMergeSortMapJoinFilterAttrModel.getAttrValueMapModels()).thenReturn(Collections.emptyList());
        when(mockMergeSortMapJoinFilterAttrModel.getAttrFormatModels()).thenReturn(Collections.emptyList());
        when(mockAttrListGroupModel.getAttrModelList()).thenReturn(Collections.singletonList(mockMergeSortMapJoinFilterAttrModel));
        when(config.getAttrListGroupModels()).thenReturn(Collections.singletonList(mockAttrListGroupModel));
        // Mocking the param to return non-empty lists
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("Test Attr Name", "Test Attr Value")));
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        // Creating the actual object instead of mocking the private method
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testComputeAttrListGroupModelsContainsMatchedAttrValueModels() throws Throwable {
        AttrListGroupModel mockAttrListGroupModel = mock(AttrListGroupModel.class);
        MergeSortMapJoinFilterAttrModel mockMergeSortMapJoinFilterAttrModel = mock(
                MergeSortMapJoinFilterAttrModel.class);
        // Mocking non-empty attrModelList with the correct type and ensuring it leads to a non-empty result
        when(mockMergeSortMapJoinFilterAttrModel.getDisplayName()).thenReturn("display");
        when(mockMergeSortMapJoinFilterAttrModel.getAttrNameList()).thenReturn(Collections.singletonList("attrName"));

        CommonWeddingDealAttrVOListOpt.AttrValueMapModel attrValueMapModel = mock(
                CommonWeddingDealAttrVOListOpt.AttrValueMapModel.class);
        when(attrValueMapModel.getAttrValue()).thenReturn("test");
        when(attrValueMapModel.getDisplayValue()).thenReturn("测试值");

        when(mockMergeSortMapJoinFilterAttrModel.getAttrValueMapModels())
                .thenReturn(Collections.singletonList(attrValueMapModel));
        when(mockMergeSortMapJoinFilterAttrModel.getAttrFormatModels()).thenReturn(Collections.emptyList());
        when(mockAttrListGroupModel.getAttrModelList())
                .thenReturn(Collections.singletonList(mockMergeSortMapJoinFilterAttrModel));
        when(config.getAttrListGroupModels()).thenReturn(Collections.singletonList(mockAttrListGroupModel));
        // Mocking the param to return non-empty lists
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("attrName", "test")));
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        // Creating the actual object instead of mocking the private method
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().size());
        assertEquals("display", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().size());
        assertEquals("测试值", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0));
    }

    @Test
    public void testComputeAttrListGroupModelsContainsMatchedAttrFormatModels() throws Throwable {
        AttrListGroupModel mockAttrListGroupModel = mock(AttrListGroupModel.class);
        MergeSortMapJoinFilterAttrModel mockMergeSortMapJoinFilterAttrModel = mock(
                MergeSortMapJoinFilterAttrModel.class);
        // Mocking non-empty attrModelList with the correct type and ensuring it leads to a non-empty result
        when(mockMergeSortMapJoinFilterAttrModel.getDisplayName()).thenReturn("display");
        when(mockMergeSortMapJoinFilterAttrModel.getAttrNameList()).thenReturn(Collections.singletonList("attrName"));

        CommonWeddingDealAttrVOListOpt.AttrFormatModel mockAttrFormatModel = mock(
                CommonWeddingDealAttrVOListOpt.AttrFormatModel.class);
        when(mockAttrFormatModel.getAttrName()).thenReturn("attrName");
        when(mockAttrFormatModel.getDisplayFormat()).thenReturn("%s测试值");

        when(mockMergeSortMapJoinFilterAttrModel.getAttrValueMapModels()).thenReturn(Collections.emptyList());
        when(mockMergeSortMapJoinFilterAttrModel.getAttrFormatModels())
                .thenReturn(Collections.singletonList(mockAttrFormatModel));
        when(mockAttrListGroupModel.getAttrModelList())
                .thenReturn(Collections.singletonList(mockMergeSortMapJoinFilterAttrModel));
        when(config.getAttrListGroupModels()).thenReturn(Collections.singletonList(mockAttrListGroupModel));
        // Mocking the param to return non-empty lists
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("attrName", "test")));
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        // Creating the actual object instead of mocking the private method
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().size());
        assertEquals("display", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().size());
        assertEquals("test测试值", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0));
    }

    @Test
    public void testComputeAttrListGroupModelsContainsMatchedValueMapAndFormatModels() throws Throwable {
        AttrListGroupModel mockAttrListGroupModel = mock(AttrListGroupModel.class);
        MergeSortMapJoinFilterAttrModel mockMergeSortMapJoinFilterAttrModel = mock(
                MergeSortMapJoinFilterAttrModel.class);
        // Mocking non-empty attrModelList with the correct type and ensuring it leads to a non-empty result
        when(mockMergeSortMapJoinFilterAttrModel.getDisplayName()).thenReturn("display");
        when(mockMergeSortMapJoinFilterAttrModel.getAttrNameList()).thenReturn(Collections.singletonList("attrName"));

        CommonWeddingDealAttrVOListOpt.AttrFormatModel mockAttrFormatModel = mock(CommonWeddingDealAttrVOListOpt.AttrFormatModel.class);
        when(mockAttrFormatModel.getAttrName()).thenReturn("attrName");
        when(mockAttrFormatModel.getDisplayFormat()).thenReturn("包含测试值");
        when(mockAttrFormatModel.getFilterAttrValues()).thenReturn(Collections.singletonList("是"));

        CommonWeddingDealAttrVOListOpt.AttrValueMapModel attrValueMapModel = mock(
                CommonWeddingDealAttrVOListOpt.AttrValueMapModel.class);
        when(attrValueMapModel.getAttrValue()).thenReturn("是");

        when(mockMergeSortMapJoinFilterAttrModel.getAttrValueMapModels()).thenReturn(Collections.singletonList(attrValueMapModel));
        when(mockMergeSortMapJoinFilterAttrModel.getAttrFormatModels()).thenReturn(Collections.singletonList(mockAttrFormatModel));
        when(mockAttrListGroupModel.getAttrModelList())
                .thenReturn(Collections.singletonList(mockMergeSortMapJoinFilterAttrModel));
        when(config.getAttrListGroupModels()).thenReturn(Collections.singletonList(mockAttrListGroupModel));
        // Mocking the param to return non-empty lists
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("attrName", "是")));
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        // Creating the actual object instead of mocking the private method
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().size());
        assertEquals("display", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().size());
        assertEquals("包含测试值", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0));
    }

    @Test
    public void testComputeAttrListGroupModelsContainsNotMatchedValueMapAndFormatModels() throws Throwable {
        AttrListGroupModel mockAttrListGroupModel = mock(AttrListGroupModel.class);
        MergeSortMapJoinFilterAttrModel mockMergeSortMapJoinFilterAttrModel = mock(
                MergeSortMapJoinFilterAttrModel.class);
        // Mocking non-empty attrModelList with the correct type and ensuring it leads to a non-empty result
        when(mockMergeSortMapJoinFilterAttrModel.getDisplayName()).thenReturn("display");
        when(mockMergeSortMapJoinFilterAttrModel.getAttrNameList()).thenReturn(Collections.singletonList("attrName"));

        CommonWeddingDealAttrVOListOpt.AttrFormatModel mockAttrFormatModel = mock(CommonWeddingDealAttrVOListOpt.AttrFormatModel.class);
        when(mockAttrFormatModel.getAttrName()).thenReturn("attrName");
        when(mockAttrFormatModel.getFilterAttrValues()).thenReturn(Collections.singletonList("是"));

        CommonWeddingDealAttrVOListOpt.AttrValueMapModel attrValueMapModel = mock(
                CommonWeddingDealAttrVOListOpt.AttrValueMapModel.class);
        when(attrValueMapModel.getAttrValue()).thenReturn("否");
        when(attrValueMapModel.getDisplayValue()).thenReturn("");

        when(mockMergeSortMapJoinFilterAttrModel.getAttrValueMapModels()).thenReturn(Collections.singletonList(attrValueMapModel));
        when(mockMergeSortMapJoinFilterAttrModel.getAttrFormatModels()).thenReturn(Collections.singletonList(mockAttrFormatModel));
        when(mockAttrListGroupModel.getAttrModelList())
                .thenReturn(Collections.singletonList(mockMergeSortMapJoinFilterAttrModel));
        when(config.getAttrListGroupModels()).thenReturn(Collections.singletonList(mockAttrListGroupModel));
        // Mocking the param to return non-empty lists
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("attrName", "否")));
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        // Creating the actual object instead of mocking the private method
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeAttrListGroupModelsContainsClothingStyle() throws Throwable {
        AttrListGroupModel mockAttrListGroupModel = mock(AttrListGroupModel.class);
        MergeSortMapJoinFilterAttrModel mockMergeSortMapJoinFilterAttrModel = mock(MergeSortMapJoinFilterAttrModel.class);
        // Mocking non-empty attrModelList with the correct type and ensuring it leads to a non-empty result
        when(mockMergeSortMapJoinFilterAttrModel.getDisplayName()).thenReturn("包含服装类型");
        when(mockMergeSortMapJoinFilterAttrModel.getSeperator()).thenReturn("、");
        when(mockMergeSortMapJoinFilterAttrModel.getAttrNameList()).thenReturn(Collections.singletonList(CLOTHING_STYLES_KEY));
        when(mockMergeSortMapJoinFilterAttrModel.getAttrValueMapModels()).thenReturn(Collections.emptyList());
        when(mockMergeSortMapJoinFilterAttrModel.getAttrFormatModels()).thenReturn(Collections.emptyList());
        when(mockAttrListGroupModel.getAttrModelList()).thenReturn(Collections.singletonList(mockMergeSortMapJoinFilterAttrModel));
        when(config.getAttrListGroupModels()).thenReturn(Collections.singletonList(mockAttrListGroupModel));
        // Mocking the param to return non-empty lists
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM(CLOTHING_STYLES_KEY, "{clothingTypes:\"主纱\",clothingCount:1},{clothingTypes:\"礼服\",clothingCount:2.0}")));
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        // Creating the actual object instead of mocking the private method
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().size());
        assertEquals("包含服装类型", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().size());
        assertEquals("主纱×1、礼服×2", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0));
    }

    @Test
    public void testComputeAttrListGroupModelsContainsServiceStaff() throws Throwable {
        AttrListGroupModel mockAttrListGroupModel = mock(AttrListGroupModel.class);
        MergeSortMapJoinFilterAttrModel mockMergeSortMapJoinFilterAttrModel = mock(MergeSortMapJoinFilterAttrModel.class);
        // Mocking non-empty attrModelList with the correct type and ensuring it leads to a non-empty result
        when(mockMergeSortMapJoinFilterAttrModel.getDisplayName()).thenReturn("服务人员");
        when(mockMergeSortMapJoinFilterAttrModel.getSeperator()).thenReturn("、");
        when(mockMergeSortMapJoinFilterAttrModel.getAttrNameList()).thenReturn(Collections.singletonList(SERVICE_STAFF_KEY));
        when(mockMergeSortMapJoinFilterAttrModel.getAttrValueMapModels()).thenReturn(Collections.emptyList());
        when(mockMergeSortMapJoinFilterAttrModel.getAttrFormatModels()).thenReturn(Collections.emptyList());
        when(mockAttrListGroupModel.getAttrModelList()).thenReturn(Collections.singletonList(mockMergeSortMapJoinFilterAttrModel));
        when(config.getAttrListGroupModels()).thenReturn(Collections.singletonList(mockAttrListGroupModel));
        // Mocking the param to return non-empty lists
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM(SERVICE_STAFF_KEY, "[{staffType:\"司仪\",staffCount:1.0},{staffType:\"策划师\",staffCount:2}]")));
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        // Creating the actual object instead of mocking the private method
        CommonWeddingDealAttrVOListOpt opt = new CommonWeddingDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().size());
        assertEquals("服务人员", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().size());
        assertEquals("司仪×1、策划师×2", result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0));
    }

}