package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CourseContentDealAttrVOListOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private CourseContentDealAttrVOListOpt.Config config;

    @Mock
    private CourseContentDealAttrVOListOpt.Param param;

    /**
     * Test when dealAttrs contains valid course content
     */
    @Test
    public void testComputeWithValidCourseContent() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = Arrays.asList(new AttrM("courseContent", "{\"contentList\":[\"content1\",\"content2\"]}"));
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertEquals(1, result.size());
        assertEquals("课程内容", result.get(0).getGroupName());
        assertEquals(1, result.get(0).getDealDetailStructAttrModuleVOS().size());
        assertEquals(Arrays.asList("content1", "content2"), result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues());
    }

    /**
     * Test when buildCourseContentStateDealDetailStructAttrModuleVO returns null
     */
    @Test
    public void testComputeWhenModuleVOIsNull() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = Arrays.asList(new AttrM("courseContent", "invalid json"));
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealAttrs is empty
     */
    @Test
    public void testComputeWithEmptyDealAttrs() throws Throwable {
        // arrange
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }
}
