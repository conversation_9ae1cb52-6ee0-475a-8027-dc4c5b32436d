package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CourseContentDealAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private CourseContentDealAttrVOListOpt.Config config;

    @Mock
    private DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO;

    @Mock
    private DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel;

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        assertNull(opt.compute(context, param, null));
    }

    @Test
    public void testComputeParamIsNull() throws Throwable {
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        assertNull(opt.compute(context, null, config));
    }

    @Test
    public void testComputeDealAttrsIsNull() throws Throwable {
        when(param.getDealAttrs()).thenReturn(null);
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        assertNull(opt.compute(context, param, config));
    }

    @Test
    public void testComputeBuildCourseContentStateDealDetailStructAttrModuleVOIsNull() throws Throwable {
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        assertNull(opt.compute(context, param, config));
    }

    @Test
    public void testComputeBuildDealDetailStructAttrModuleGroupModelIsNull() throws Throwable {
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        CourseContentDealAttrVOListOpt opt = new CourseContentDealAttrVOListOpt();
        assertNull(opt.compute(context, param, config));
    }

    @Test
    public void testComputeNormal() throws Throwable {
        List<AttrM> mockDealAttrs = new ArrayList<>();
        mockDealAttrs.add(new AttrM("courseContent", "Some content"));
        List<DealDetailStructAttrModuleVO> mockModuleVOs = new ArrayList<>();
        mockModuleVOs.add(dealDetailStructAttrModuleVO);
        when(dealDetailStructAttrModuleGroupModel.getDealDetailStructAttrModuleVOS()).thenReturn(mockModuleVOs);
        // Mock the method that is expected to return the mocked dealDetailStructAttrModuleGroupModel
        CourseContentDealAttrVOListOpt opt = spy(new CourseContentDealAttrVOListOpt());
        doReturn(Arrays.asList(dealDetailStructAttrModuleGroupModel)).when(opt).compute(context, param, config);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(dealDetailStructAttrModuleGroupModel.getDealDetailStructAttrModuleVOS(), result.get(0).getDealDetailStructAttrModuleVOS());
    }
}
