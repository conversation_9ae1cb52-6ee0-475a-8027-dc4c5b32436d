package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP.Param;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import org.mockito.InjectMocks;
import java.lang.reflect.Method;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrVOListBuiltByScriptExceptionOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param param;

    @Mock
    private DealAttrVOListBuiltByScriptExceptionOpt.Config config;

    @Mock
    private DealDetailDtoModel dealDetailDtoModel;

    @InjectMocks
    private DealAttrVOListBuiltByScriptExceptionOpt dealAttrVOListBuiltByScriptExceptionOpt;

    private Method buildDealDetailStructAttrModuleVOListMethod;

    @Before
    public void setUp() throws Exception {
        // Use reflection to access the private method
        buildDealDetailStructAttrModuleVOListMethod = DealAttrVOListBuiltByScriptExceptionOpt.class.getDeclaredMethod("buildDealDetailStructAttrModuleVOList", ActivityCxt.class, DealAttrVOListBuiltByScriptExceptionOpt.AttrListGroupModel.class, List.class, List.class);
        buildDealDetailStructAttrModuleVOListMethod.setAccessible(true);
    }

    @After
    public void tearDown() {
        // Reset the accessibility of the method
        buildDealDetailStructAttrModuleVOListMethod.setAccessible(false);
    }

    /**
     * Test compute method when config is null, should return null
     */
    @Test
    public void testComputeConfigIsNull() throws Throwable {
        // arrange
        // act
        List<DealDetailStructAttrModuleGroupModel> result = dealAttrVOListBuiltByScriptExceptionOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }

    /**
     * Test compute method when config.getAttrListGroupModels() is empty, should return null
     */
    @Test
    public void testComputeAttrListGroupModelsIsEmpty() throws Throwable {
        // arrange
        when(config.getAttrListGroupModels()).thenReturn(Collections.emptyList());
        // act
        List<DealDetailStructAttrModuleGroupModel> result = dealAttrVOListBuiltByScriptExceptionOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test compute method when param.getDealAttrs() or param.getDealDetailDtoModel() is null, should return null
     */
    @Test
    public void testComputeDealAttrsOrDealDetailDtoModelIsNull() throws Throwable {
        // arrange
        when(config.getAttrListGroupModels()).thenReturn(Collections.emptyList());
        // act
        List<DealDetailStructAttrModuleGroupModel> result = dealAttrVOListBuiltByScriptExceptionOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }
}
