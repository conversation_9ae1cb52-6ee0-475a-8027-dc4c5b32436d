package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EduLodgeDetailVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;


    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsEmpty() {
        // arrange
        EduLodgeDetailVOListOpt opt = new EduLodgeDetailVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, new EduLodgeDetailVOListOpt.Config());

        // assert
        assertNull(result);
    }

    @Test
    public void testComputeResultListIsNotEmpty() throws Throwable {
        // arrange
        AttrM roomSpecAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_BOARDING_SPECIFICATION, "房间规格1");
        AttrM roomFacilityAttr = buildAttr(EduLodgeDetailVOListOpt.ROOM_FACILITY,
                JsonCodec.encodeWithUTF8(Lists.newArrayList("房间设施1", "房间设施2")));
        AttrM studyRoomBeginTimeAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_STUDY_ROOM_BEGIN_TIME, "07:10");
        AttrM studyRoomEndTimeAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_STUDY_ROOM_END_TIME, "09:10");
        AttrM canteenSupplyMealTypeAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_CANTEEN_SUPPLY_MEAL_TYPE,
                JsonCodec.encodeWithUTF8(Lists.newArrayList("食堂供应1", "食堂供应2")));
        AttrM otherFacilityAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_OTHER_FACILITY,
                JsonCodec.encodeWithUTF8(Lists.newArrayList("其他设施1", "其他设施2")));

        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(roomSpecAttr, roomFacilityAttr, studyRoomBeginTimeAttr,
                studyRoomEndTimeAttr, canteenSupplyMealTypeAttr, otherFacilityAttr));

        EduLodgeDetailVOListOpt.Config config = new EduLodgeDetailVOListOpt.Config();
        EduLodgeDetailVOListOpt opt = new EduLodgeDetailVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        DealDetailStructAttrModuleGroupModel groupModel = result.get(0);
        assertTrue("寄宿详情".equals(groupModel.getGroupName()));
        assertTrue(groupModel.getDealDetailStructAttrModuleVOS().size() == 5);
        assertTrue("房间规格".equals(groupModel.getDealDetailStructAttrModuleVOS().get(0).getAttrName()));
        assertTrue("房间规格1".equals(groupModel.getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0)));
        assertTrue("房间设施".equals(groupModel.getDealDetailStructAttrModuleVOS().get(1).getAttrName()));
        assertTrue("房间设施1、房间设施2".equals(groupModel.getDealDetailStructAttrModuleVOS().get(1).getAttrValues().get(0)));
        assertTrue("自习室".equals(groupModel.getDealDetailStructAttrModuleVOS().get(2).getAttrName()));
        assertTrue("07:10-09:10开放".equals(groupModel.getDealDetailStructAttrModuleVOS().get(2).getAttrValues().get(0)));
        assertTrue("食堂供应".equals(groupModel.getDealDetailStructAttrModuleVOS().get(3).getAttrName()));
        assertTrue("食堂供应1、食堂供应2".equals(groupModel.getDealDetailStructAttrModuleVOS().get(3).getAttrValues().get(0)));
        assertTrue("其他设施".equals(groupModel.getDealDetailStructAttrModuleVOS().get(4).getAttrName()));
        assertTrue("其他设施1、其他设施2".equals(groupModel.getDealDetailStructAttrModuleVOS().get(4).getAttrValues().get(0)));
    }


    @Test
    public void testComputeResultListWithFreeMessage() throws Throwable {
        // arrange
        AttrM studyRoomBeginTimeAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_STUDY_ROOM_BEGIN_TIME, "07:10");
        AttrM studyRoomEndTimeAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_STUDY_ROOM_END_TIME, "09:10");
        AttrM canteenSupplyMealTypeAttr = buildAttr(EduLodgeDetailVOListOpt.ATTR_CANTEEN_SUPPLY_MEAL_TYPE,
                JsonCodec.encodeWithUTF8(Lists.newArrayList("食堂供应1", "食堂供应2")));
        AttrM freeCanteenSupplyFacilityAttr = buildAttr(EduLodgeDetailVOListOpt.FREE_CANTEEN_SUPPLY, "是");
        AttrM freeStudyRoomFacilityAttr = buildAttr(EduLodgeDetailVOListOpt.FREE_STUDY_ROOM, "是");

        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(studyRoomBeginTimeAttr, studyRoomEndTimeAttr, canteenSupplyMealTypeAttr, freeCanteenSupplyFacilityAttr, freeStudyRoomFacilityAttr));

        EduLodgeDetailVOListOpt.Config config = new EduLodgeDetailVOListOpt.Config();
        EduLodgeDetailVOListOpt opt = new EduLodgeDetailVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        DealDetailStructAttrModuleGroupModel groupModel = result.get(0);
        assertTrue("寄宿详情".equals(groupModel.getGroupName()));
        assertTrue(groupModel.getDealDetailStructAttrModuleVOS().size() == 2);
        assertTrue("自习室".equals(groupModel.getDealDetailStructAttrModuleVOS().get(0).getAttrName()));
        assertTrue("07:10-09:10开放，免费使用".equals(groupModel.getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0)));
        assertTrue("食堂供应".equals(groupModel.getDealDetailStructAttrModuleVOS().get(1).getAttrName()));
        assertTrue("食堂供应1、食堂供应2，免费使用".equals(groupModel.getDealDetailStructAttrModuleVOS().get(1).getAttrValues().get(0)));
    }


    @Test
    public void testComputeResultListWithFreeMessageAndPrefixEmpty() throws Throwable {
        // arrange
        AttrM freeCanteenSupplyFacilityAttr = buildAttr(EduLodgeDetailVOListOpt.FREE_CANTEEN_SUPPLY, "是");
        AttrM freeStudyRoomFacilityAttr = buildAttr(EduLodgeDetailVOListOpt.FREE_STUDY_ROOM, "是");

        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(freeCanteenSupplyFacilityAttr, freeStudyRoomFacilityAttr));

        EduLodgeDetailVOListOpt.Config config = new EduLodgeDetailVOListOpt.Config();
        EduLodgeDetailVOListOpt opt = new EduLodgeDetailVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result == null);
    }

    private AttrM buildAttr(String attrName, String value) {
        AttrM attrM = new AttrM();
        attrM.setName(attrName);
        attrM.setValue(value);
        return attrM;
    }

}
