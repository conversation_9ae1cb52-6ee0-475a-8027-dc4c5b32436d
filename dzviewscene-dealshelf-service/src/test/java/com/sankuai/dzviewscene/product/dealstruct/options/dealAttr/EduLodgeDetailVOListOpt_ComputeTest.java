package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduLodgeDetailVOListOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private EduLodgeDetailVOListOpt.Config config;

    @InjectMocks
    private EduLodgeDetailVOListOpt opt;

    @Test
    public void testComputeReturnNullWhenDealDetailStructAttrModuleVOSIsEmpty() throws Throwable {
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }
}
