package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EduOnlineDealPhysicalGiftVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsEmpty() {
        // arrange
        EduOnlineDealPhysicalGiftVOListOpt opt = new EduOnlineDealPhysicalGiftVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, new EduOnlineDealPhysicalGiftVOListOpt.Config());

        // assert
        assertNull(result);
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表不为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForDP() {
        // arrange
        List<EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift> physicalGifts = getGifts();

        AttrM materialAttrM = new AttrM();
        materialAttrM.setName(EduOnlineDealPhysicalGiftVOListOpt.ATTR_MATERIAL_LIST);
        materialAttrM.setValue(JsonCodec.encodeWithUTF8(physicalGifts));

        AttrM deliveryAttrM = new AttrM();
        deliveryAttrM.setName(EduOnlineDealPhysicalGiftVOListOpt.ATTR_SUPPORT_DELIVERY);
        deliveryAttrM.setValue("是");

        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(materialAttrM, deliveryAttrM));

        EduOnlineDealPhysicalGiftVOListOpt.Config config = new EduOnlineDealPhysicalGiftVOListOpt.Config();
        EduOnlineDealPhysicalGiftVOListOpt opt = new EduOnlineDealPhysicalGiftVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().size() == 2);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName().equals("测试1"));
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(1).getAttrName().equals("测试2"));
        assertTrue(result.get(0).getGroupName().equals(config.getPhysicalGiftTitle()));
        assertTrue(result.get(0).getGroupSubtitle().equals(config.getPhysicalGiftSubTitle()));
    }

    /**
     * 测试compute方法，当不支持配送时
     * 期望subtitle为空
     */
    @Test
    public void testSubtitleAsNotSupportDelivery() {
        // arrange
        List<EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift> physicalGifts = getGifts();

        AttrM materialAttrM = new AttrM();
        materialAttrM.setName(EduOnlineDealPhysicalGiftVOListOpt.ATTR_MATERIAL_LIST);
        materialAttrM.setValue(JsonCodec.encodeWithUTF8(physicalGifts));


        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(materialAttrM));

        EduOnlineDealPhysicalGiftVOListOpt.Config config = new EduOnlineDealPhysicalGiftVOListOpt.Config();
        EduOnlineDealPhysicalGiftVOListOpt opt = new EduOnlineDealPhysicalGiftVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(StringUtils.isBlank(result.get(0).getGroupSubtitle()));
    }

    @NotNull
    private List<EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift> getGifts() {
        List<EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift> list = new ArrayList<>();
        EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift gift = new EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift();
        gift.setMaterialName("测试1");
        gift.setMaterialType("纸质资料");
        list.add(gift);
        EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift gift2 = new EduOnlineDealPhysicalGiftVOListOpt.PhysicalGift();
        gift2.setMaterialName("测试2");
        gift.setMaterialType("电子资料");
        list.add(gift2);
        return list;
    }
}
