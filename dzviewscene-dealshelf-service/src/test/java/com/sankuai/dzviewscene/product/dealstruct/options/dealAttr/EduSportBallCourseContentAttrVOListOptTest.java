package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import java.util.stream.Collectors;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduSportBallCourseContentAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private EduSportBallCourseContentAttrVOListOpt.Config config;

    @Mock
    private DealAttrVOListVP.Param param;

    // Helper method to setup parameters for course contents
    private void setupParamForCourseContents(List<String> courseContents) {
        // Convert List<String> to List<AttrM>
        List<AttrM> attrMs = courseContents.stream().map(content -> new AttrM("content", content)).collect(Collectors.toList());
    }

    @Test(expected = NullPointerException.class)
    public void testComputeParamIsNull() throws Throwable {
        EduSportBallCourseContentAttrVOListOpt opt = new EduSportBallCourseContentAttrVOListOpt();
        opt.compute(context, null, config);
    }

    @Test
    public void testComputeCourseContentsIsNull() throws Throwable {
        EduSportBallCourseContentAttrVOListOpt opt = new EduSportBallCourseContentAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeCourseContentsIsEmpty() throws Throwable {
        EduSportBallCourseContentAttrVOListOpt opt = new EduSportBallCourseContentAttrVOListOpt();
        setupParamForCourseContents(java.util.Collections.emptyList());
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }
}
