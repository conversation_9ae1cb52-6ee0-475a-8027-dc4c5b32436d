package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import static org.junit.Assert.*;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @author: created by hang.yu on 2024/4/25 10:42
 */
@RunWith(MockitoJUnitRunner.class)
public class FirstSkuAttrVOListOptTest {

    @InjectMocks
    private FirstSkuAttrVOListOpt firstSkuAttrVOListOpt;

    private DealDetailInfoModel buildDealDetailInfoModel() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setSkuId(1L);
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("chineseName");
        skuAttrItemDto.setAttrValue("雅迪");
        skuItemDto.setAttrItems(Lists.newArrayList(skuAttrItemDto));
        mustSkuItemsGroupDto.setSkuItems(Lists.newArrayList(skuItemDto));
        skuUniStructuredDto.setMustGroups(Lists.newArrayList(mustSkuItemsGroupDto));
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        dealDetailInfoModel.setDealDetailDtoModel(dealDetailDtoModel);
        return dealDetailInfoModel;
    }

    @Test
    public void compute() {
        FirstSkuAttrVOListOpt.Config config = new FirstSkuAttrVOListOpt.Config();
        config.setGroupName("商品属性");
        config.setShowNum(4);
        config.setFoldStr("查看更多");
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setShowName("品牌");
        attrConfig.setAttrNames(Lists.newArrayList("chineseName"));
        config.setAttrConfigs(Lists.newArrayList(attrConfig));
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, config);
        Assert.assertEquals(0, result.size());
        ActivityCxt context = new ActivityCxt();
        param.setDealDetailInfoModel(buildDealDetailInfoModel());
        result = firstSkuAttrVOListOpt.compute(context, param, config);
        Assert.assertNotNull(result);
    }

    @Test
    public void buildAttrList() {
        List<DealDetailStructAttrModuleVO> result = firstSkuAttrVOListOpt.buildAttrList(null, null);
        Assert.assertEquals(0, result.size());
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("chineseName");
        skuAttrItemDto.setAttrValue("雅迪");
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setShowName("品牌");
        attrConfig.setShowSwitch(false);
        attrConfig.setAttrNames(Lists.newArrayList("chineseName"));
        result = firstSkuAttrVOListOpt.buildAttrList(Lists.newArrayList(skuAttrItemDto), Lists.newArrayList(attrConfig));
        Assert.assertNotNull(result);
        FirstSkuAttrVOListOpt.AttrConfig attrConfig2 = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig2.setShowName("品牌");
        attrConfig2.setShowSwitch(true);
        attrConfig2.setAttrNames(Lists.newArrayList("chineseName"));
        result = firstSkuAttrVOListOpt.buildAttrList(Lists.newArrayList(skuAttrItemDto), Lists.newArrayList(attrConfig2));
        Assert.assertNotNull(result);
    }

    /**
     * Test case where attrValue contains the spliceStr
     * This specifically targets the missed line 76
     */
    @Test
    public void testGetAttrValue_WhenAttrValueContainsSpliceStr() {
        // arrange
        FirstSkuAttrVOListOpt firstSkuAttrVOListOpt = new FirstSkuAttrVOListOpt();
        String attrValue = "value1,value2";
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setFormatTemplate("%s个");
        attrConfig.setSpliceStr(",");
        // act
        String result = firstSkuAttrVOListOpt.getAttrValue(attrValue, attrConfig);
        // assert
        assertEquals("value1,value2", result);
    }
}
