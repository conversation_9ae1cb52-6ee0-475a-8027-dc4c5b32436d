package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.mockito.Mockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class FirstSkuAttrVOListOpt_BuildAttrListTest {

    private FirstSkuAttrVOListOpt firstSkuAttrVOListOpt;

    @Before
    public void setUp() {
        firstSkuAttrVOListOpt = new FirstSkuAttrVOListOpt();
    }

    @Test
    public void testBuildAttrListAttrConfigsIsNull() throws Throwable {
        List<DealDetailStructAttrModuleVO> result = firstSkuAttrVOListOpt.buildAttrList(new ArrayList<>(), null);
        Assert.assertTrue("Expected an empty list when attrConfigs is null", result.isEmpty());
    }

    @Test
    public void testBuildAttrListAllAttrConfigShowSwitchIsFalse() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("chineseName");
        skuAttrItemDto.setAttrValue("雅迪");
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setShowSwitch(false);
        List<DealDetailStructAttrModuleVO> result = firstSkuAttrVOListOpt.buildAttrList(new ArrayList<>(Arrays.asList(skuAttrItemDto)), new ArrayList<>(Arrays.asList(attrConfig)));
        Assert.assertTrue("Expected an empty list when all attrConfig showSwitch is false", result.isEmpty());
    }

    @Test
    public void testBuildAttrListAttrNameNotInList() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("chineseName");
        skuAttrItemDto.setAttrValue("雅迪");
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setShowSwitch(true);
        attrConfig.setAttrNames(new ArrayList<>(Arrays.asList("englishName")));
        List<DealDetailStructAttrModuleVO> result = firstSkuAttrVOListOpt.buildAttrList(new ArrayList<>(Arrays.asList(skuAttrItemDto)), new ArrayList<>(Arrays.asList(attrConfig)));
        Assert.assertTrue("Expected an empty list when attrName not in list", result.isEmpty());
    }

    @Test
    public void testBuildAttrListAttrValueIsNotNull() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("chineseName");
        skuAttrItemDto.setAttrValue("雅迪");
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setShowSwitch(true);
        attrConfig.setAttrNames(new ArrayList<>(Arrays.asList("chineseName")));
        attrConfig.setShowName("Brand");
        List<DealDetailStructAttrModuleVO> result = firstSkuAttrVOListOpt.buildAttrList(new ArrayList<>(Arrays.asList(skuAttrItemDto)), new ArrayList<>(Arrays.asList(attrConfig)));
        Assert.assertFalse("Expected a non-empty list when attrValue is not null", result.isEmpty());
        Assert.assertEquals("Expected one item in the result list", 1, result.size());
        DealDetailStructAttrModuleVO moduleVO = result.get(0);
        Assert.assertEquals("Expected the attrName to match", "Brand", moduleVO.getAttrName());
        Assert.assertTrue("Expected the attrValues to contain the given value", moduleVO.getAttrValues().contains("雅迪"));
    }
}
