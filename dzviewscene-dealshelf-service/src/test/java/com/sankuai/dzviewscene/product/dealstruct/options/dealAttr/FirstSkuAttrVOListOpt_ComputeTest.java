package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import java.util.ArrayList;
import java.util.List;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class FirstSkuAttrVOListOpt_ComputeTest {

    private FirstSkuAttrVOListOpt firstSkuAttrVOListOpt;

    private FirstSkuAttrVOListOpt.Config mockConfig;

    @Before
    public void setUp() {
        firstSkuAttrVOListOpt = new FirstSkuAttrVOListOpt();
        mockConfig = mock(FirstSkuAttrVOListOpt.Config.class);
        when(mockConfig.getGroupName()).thenReturn("Test Group Name");
        when(mockConfig.getShowNum()).thenReturn(5);
        when(mockConfig.getFoldStr()).thenReturn("Test Fold String");
        List<FirstSkuAttrVOListOpt.AttrConfig> attrConfigs = new ArrayList<>();
        // Assuming AttrConfig has a constructor or a method to set properties
        // This part is pseudo-code as the actual implementation details of AttrConfig are not provided
        // attrConfigs.add(new FirstSkuAttrVOListOpt.AttrConfig(...));
        when(mockConfig.getAttrConfigs()).thenReturn(attrConfigs);
    }

    @Test
    public void testComputeWhenDealDetailDtoModelIsNull() throws Throwable {
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenSkuUniStructuredDtoIsNull() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().dealDetailDtoModel(dealDetailDtoModel).build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenMustGroupsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().dealDetailDtoModel(dealDetailDtoModel).build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenMustGroupsIsNotEmptyButFirstMustSkuItemsGroupDtoIsNull() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        skuUniStructuredDto.setMustGroups(mustGroups);
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().dealDetailDtoModel(dealDetailDtoModel).build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenMustGroupsIsNotEmptyAndFirstMustSkuItemsGroupDtoIsNotNullButSkuItemsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        mustGroups.add(mustSkuItemsGroupDto);
        skuUniStructuredDto.setMustGroups(mustGroups);
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().dealDetailDtoModel(dealDetailDtoModel).build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenMustGroupsIsNotEmptyAndFirstMustSkuItemsGroupDtoIsNotNullAndSkuItemsIsNotEmptyButFirstSkuItemDtoIsNull() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        List<SkuItemDto> skuItems = new ArrayList<>();
        mustSkuItemsGroupDto.setSkuItems(skuItems);
        mustGroups.add(mustSkuItemsGroupDto);
        skuUniStructuredDto.setMustGroups(mustGroups);
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().dealDetailDtoModel(dealDetailDtoModel).build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenMustGroupsIsNotEmptyAndFirstMustSkuItemsGroupDtoIsNotNullAndSkuItemsIsNotEmptyAndFirstSkuItemDtoIsNotNullButAttrItemsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        List<SkuItemDto> skuItems = new ArrayList<>();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItems.add(skuItemDto);
        mustSkuItemsGroupDto.setSkuItems(skuItems);
        mustGroups.add(mustSkuItemsGroupDto);
        skuUniStructuredDto.setMustGroups(mustGroups);
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().dealDetailDtoModel(dealDetailDtoModel).build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenMustGroupsIsNotEmptyAndFirstMustSkuItemsGroupDtoIsNotNullAndSkuItemsIsNotEmptyAndFirstSkuItemDtoIsNotNullAndAttrItemsIsNotEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        List<SkuItemDto> skuItems = new ArrayList<>();
        SkuItemDto skuItemDto = new SkuItemDto();
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        attrItems.add(new SkuAttrItemDto());
        skuItemDto.setAttrItems(attrItems);
        skuItems.add(skuItemDto);
        mustSkuItemsGroupDto.setSkuItems(skuItems);
        mustGroups.add(mustSkuItemsGroupDto);
        skuUniStructuredDto.setMustGroups(mustGroups);
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder().dealDetailDtoModel(dealDetailDtoModel).build();
        List<DealDetailStructAttrModuleGroupModel> result = firstSkuAttrVOListOpt.compute(null, param, mockConfig);
        Assert.assertFalse(result.isEmpty());
    }
}
