package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class FirstSkuAttrVOListOpt_GetAttrValueTest {

    /**
     * 测试 attrValue 为空的情况
     */
    @Test
    public void testGetAttrValueAttrValueIsNull() {
        LifeCleanAttrVOListOpt.AttrListGroupModel attrListGroupModel = new LifeCleanAttrVOListOpt.AttrListGroupModel();
        attrListGroupModel.setSelfSupport(true);
        // arrange
        FirstSkuAttrVOListOpt opt = new FirstSkuAttrVOListOpt();
        String attrValue = null;
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        // act
        String result = opt.getAttrValue(attrValue, attrConfig);
        // assert
        assertNull(result);
    }

    /**
     * 测试 attrValue 不为空，attrConfig.getFormatTemplate() 为空的情况
     */
    @Test
    public void testGetAttrValueFormatTemplateIsNull() {
        // arrange
        FirstSkuAttrVOListOpt opt = new FirstSkuAttrVOListOpt();
        String attrValue = "test";
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        // act
        String result = opt.getAttrValue(attrValue, attrConfig);
        // assert
        assertEquals(attrValue, result);
    }

    /**
     * 测试 attrValue 不为空，attrConfig.getFormatTemplate() 不为空，attrConfig.getSpliceStr() 为空的情况
     */
    @Test
    public void testGetAttrValueSpliceStrIsNull() {
        // arrange
        FirstSkuAttrVOListOpt opt = new FirstSkuAttrVOListOpt();
        String attrValue = "test";
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setFormatTemplate("formatTemplate");
        // act
        String result = opt.getAttrValue(attrValue, attrConfig);
        // assert
        assertEquals("formatTemplate", result);
    }

    /**
     * 测试 attrValue 包含 attrConfig.getSpliceStr() 的情况
     */
    @Test
    public void testGetAttrValueAttrValueContainsSpliceStr() {
        // arrange
        FirstSkuAttrVOListOpt opt = new FirstSkuAttrVOListOpt();
        String attrValue = "test";
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setSpliceStr("spliceStr");
        // act
        String result = opt.getAttrValue(attrValue, attrConfig);
        // assert
        assertEquals(attrValue, result);
    }

    /**
     * 测试 attrValue 不包含 attrConfig.getSpliceStr() 的情况
     */
    @Test
    public void testGetAttrValueAttrValueNotContainsSpliceStr() {
        // arrange
        FirstSkuAttrVOListOpt opt = new FirstSkuAttrVOListOpt();
        String attrValue = "test";
        FirstSkuAttrVOListOpt.AttrConfig attrConfig = new FirstSkuAttrVOListOpt.AttrConfig();
        attrConfig.setFormatTemplate("formatTemplate");
        attrConfig.setSpliceStr("spliceStr");
        // act
        String result = opt.getAttrValue(attrValue, attrConfig);
        // assert
        assertEquals("formatTemplate", result);
    }
}
