package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import org.junit.Before;

public class LifeCleanAttrVOListOptTest {

    private LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt;

    private ActivityCxt context;

    private DealAttrVOListVP.Param param;

    private LifeCleanAttrVOListOpt.Config config;

    public static DealAttrVOListVP.Param getParam() {
        String str = "{\n" +
                "  \"dealAttrs\": [\n" +
                "    {\n" +
                "      \"name\": \"Color\",\n" +
                "      \"value\": \"Red\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"dealDetailDtoModel\": {\n" +
                "    \"skuId\": \"12345\",\n" +
                "    \"skuName\": \"Sample SKU\"\n" +
                "  },\n" +
                "  \"productCategories\": [\n" +
                "    {\n" +
                "      \"categoryId\": \"67890\",\n" +
                "      \"categoryName\": \"Sample Category\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"dealDetailInfoModel\": {\n" +
                "    \"dealId\": 1,\n" +
                "    \"dealDetailDtoModel\": {\n" +
                "      \"skuId\": \"12345\",\n" +
                "      \"skuName\": \"Sample SKU\"\n" +
                "    },\n" +
                "    \"productCategories\": [\n" +
                "      {\n" +
                "        \"categoryId\": \"67890\",\n" +
                "        \"categoryName\": \"Sample Category\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"desc\": \"Sample description\",\n" +
                "    \"salePrice\": \"100.00\",\n" +
                "    \"marketPrice\": \"150.00\",\n" +
                "    \"dealTitle\": \"Sample Deal\",\n" +
                "    \"dealAttrs\": [\n" +
                "      {\n" +
                "        \"name\": \"Color\",\n" +
                "        \"value\": \"Red\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}\n";
        return JSON.parseObject(str, DealAttrVOListVP.Param.class);
    }

    @Test
    public void testComputeWhenDealAttrsIsEmpty() throws Throwable {
        LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt = new LifeCleanAttrVOListOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealAttrVOListVP.Param param = mock(DealAttrVOListVP.Param.class);
        LifeCleanAttrVOListOpt.Config config = mock(LifeCleanAttrVOListOpt.Config.class);
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        DealAttrVOListVP.Param param1=getParam();
        assertNotNull("Should not return null when deal attributes are empty", lifeCleanAttrVOListOpt.compute(context, param1, config));
    }

    @Test
    public void testComputeWhenDealAttrsIsNotEmpty() throws Throwable {
        LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt = new LifeCleanAttrVOListOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealAttrVOListVP.Param param = mock(DealAttrVOListVP.Param.class);
        LifeCleanAttrVOListOpt.Config config = mock(LifeCleanAttrVOListOpt.Config.class);
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("attr1", "value1"));
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        when(param.getDealDetailDtoModel()).thenReturn(new DealDetailDtoModel());
        DealAttrVOListVP.Param param1=getParam();
        assertNotNull("Should not return null when deal attributes are not empty", lifeCleanAttrVOListOpt.compute(context, param1, config));
    }

    @Test
    public void testComputeWhenAttrListGroupModelsIsEmpty() throws Throwable {
        LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt = new LifeCleanAttrVOListOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealAttrVOListVP.Param param = mock(DealAttrVOListVP.Param.class);
        LifeCleanAttrVOListOpt.Config config = mock(LifeCleanAttrVOListOpt.Config.class);
        when(config.getAttrListGroupModels()).thenReturn(new ArrayList<>());
        DealAttrVOListVP.Param param1=getParam();
        assertNotNull("Should not return null when attribute list group models are empty", lifeCleanAttrVOListOpt.compute(context, param1, config));
    }

    @Test
    public void testComputeWhenAttrListGroupModelsIsNotEmpty() throws Throwable {
        LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt = new LifeCleanAttrVOListOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealAttrVOListVP.Param param = mock(DealAttrVOListVP.Param.class);
        LifeCleanAttrVOListOpt.Config config = mock(LifeCleanAttrVOListOpt.Config.class);
        List<LifeCleanAttrVOListOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        attrListGroupModels.add(new LifeCleanAttrVOListOpt.AttrListGroupModel());
        when(config.getAttrListGroupModels()).thenReturn(attrListGroupModels);
        DealAttrVOListVP.Param param1=getParam();
        assertNotNull("Should not return null when attribute list group models are not empty", lifeCleanAttrVOListOpt.compute(context, param1, config));
    }


}
