package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PregnantPhotoDealAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private PregnantPhotoDealAttrVOListOpt.Config config;

    @InjectMocks
    private PregnantPhotoDealAttrVOListOpt pregnantPhotoDealAttrVOListOpt;

    private DealDetailDtoModel createDealDetailDtoModelWithSkuAttrs() {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        // Create SkuAttrItemDto
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("testAttrName");
        skuAttrItemDto.setAttrValue("testAttrValue");
        // Create SkuItemDto
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Collections.singletonList(skuAttrItemDto));
        // Create MustSkuItemsGroupDto
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        mustSkuItemsGroupDto.setSkuItems(Collections.singletonList(skuItemDto));
        // Set up DealDetailSkuUniStructuredDto
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        skuUniStructuredDto.setMustGroups(Collections.singletonList(mustSkuItemsGroupDto));
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        return dealDetailDtoModel;
    }

    @Test
    public void testComputeWhenConfigIsNull() throws Throwable {
        PregnantPhotoDealAttrVOListOpt opt = new PregnantPhotoDealAttrVOListOpt();
        assertNull(opt.compute(context, param, null));
    }

    @Test
    public void testComputeWhenParamIsNull() throws Throwable {
        PregnantPhotoDealAttrVOListOpt opt = new PregnantPhotoDealAttrVOListOpt();
        assertNull(opt.compute(context, null, config));
    }

    @Test
    public void testComputeWhenDealDetailDtoModelIsNull() throws Throwable {
        PregnantPhotoDealAttrVOListOpt opt = new PregnantPhotoDealAttrVOListOpt();
        when(param.getDealDetailDtoModel()).thenReturn(null);
        List<PregnantPhotoDealAttrVOListOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        attrListGroupModels.add(new PregnantPhotoDealAttrVOListOpt.AttrListGroupModel());
        when(config.getAttrListGroupModels()).thenReturn(attrListGroupModels);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenDealAttrsIsNull() throws Throwable {
        PregnantPhotoDealAttrVOListOpt opt = new PregnantPhotoDealAttrVOListOpt();
        when(param.getDealAttrs()).thenReturn(null);
        List<PregnantPhotoDealAttrVOListOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        attrListGroupModels.add(new PregnantPhotoDealAttrVOListOpt.AttrListGroupModel());
        when(config.getAttrListGroupModels()).thenReturn(attrListGroupModels);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenAttrListGroupModelsIsNull() throws Throwable {
        PregnantPhotoDealAttrVOListOpt opt = new PregnantPhotoDealAttrVOListOpt();
        // Fix: Mock config.getAttrListGroupModels() to return an empty list instead of null
        when(config.getAttrListGroupModels()).thenReturn(new ArrayList<>());
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeWhenAttrListGroupModelIsNull() throws Throwable {
        PregnantPhotoDealAttrVOListOpt opt = new PregnantPhotoDealAttrVOListOpt();
        List<PregnantPhotoDealAttrVOListOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        attrListGroupModels.add(null);
        when(config.getAttrListGroupModels()).thenReturn(attrListGroupModels);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    /**
     * Test compute method when firstSkuAttrs is not empty
     */
    @Test
    public void testComputeWithFirstSkuAttrs() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        PregnantPhotoDealAttrVOListOpt.Config config = new PregnantPhotoDealAttrVOListOpt.Config();
        config.setAttrListGroupModels(new ArrayList<>());
        DealDetailDtoModel dealDetailDtoModel = createDealDetailDtoModelWithSkuAttrs();
        PregnantPhotoDealAttrVOListOpt.Param param = PregnantPhotoDealAttrVOListOpt.Param.builder().dealDetailDtoModel(dealDetailDtoModel).dealAttrs(new ArrayList<>()).build();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = pregnantPhotoDealAttrVOListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    /**
     * Test compute method when dealAttrs is not empty
     */
    @Test
    public void testComputeWithDealAttrs() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        PregnantPhotoDealAttrVOListOpt.Config config = new PregnantPhotoDealAttrVOListOpt.Config();
        config.setAttrListGroupModels(new ArrayList<>());
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("testName", "testValue"));
        PregnantPhotoDealAttrVOListOpt.Param param = PregnantPhotoDealAttrVOListOpt.Param.builder().dealDetailDtoModel(new DealDetailDtoModel()).dealAttrs(dealAttrs).build();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = pregnantPhotoDealAttrVOListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    /**
     * Test compute method when both firstSkuAttrs and dealAttrs are not empty
     */
    @Test
    public void testComputeWithBothAttrs() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        PregnantPhotoDealAttrVOListOpt.Config config = new PregnantPhotoDealAttrVOListOpt.Config();
        config.setAttrListGroupModels(new ArrayList<>());
        DealDetailDtoModel dealDetailDtoModel = createDealDetailDtoModelWithSkuAttrs();
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("testName", "testValue"));
        PregnantPhotoDealAttrVOListOpt.Param param = PregnantPhotoDealAttrVOListOpt.Param.builder().dealDetailDtoModel(dealDetailDtoModel).dealAttrs(dealAttrs).build();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = pregnantPhotoDealAttrVOListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }
}
