package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.SelfServiceCarWashAttrVOListOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SelfServiceCarWashAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private Config config;

    @InjectMocks
    private SelfServiceCarWashAttrVOListOpt selfServiceCarWashAttrVOListOpt;


    @Before
    public void setUp() {
    }

    @Test
    public void testComputeWhenInputIsNull() throws Throwable {
        assertNull("Expected compute to return null when inputs are null", selfServiceCarWashAttrVOListOpt.compute(null, null, null));
    }

    @Test
    public void testComputeWhenDealAttrsIsEmpty() throws Throwable {
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        assertNull("Expected compute to return null when dealAttrs is empty", selfServiceCarWashAttrVOListOpt.compute(context, param, config));
    }

    @Test
    public void testComputeWhenFirstMustGroupFirstSkuAttrListIsEmpty() throws Throwable {
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        assertNull("Expected compute to return null when first must group first sku attr list is empty", selfServiceCarWashAttrVOListOpt.compute(context, param, config));
    }
    /**
     * 测试buildUnionAttrDisplayValue方法，当attrModels为空时
     */
    @Test
    public void testBuildUnionAttrDisplayValueWithEmptyAttrModels() {
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        String result = selfServiceCarWashAttrVOListOpt.buildUnionAttrDisplayValue(skuAttrItems, Collections.emptyList());
        assertEquals("", result);
    }

    /**
     * 测试buildUnionAttrDisplayValue方法，当attrModels为null时
     */
    @Test
    public void testBuildUnionAttrDisplayValueWithNullAttrModels() {
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();

        String result = selfServiceCarWashAttrVOListOpt.buildUnionAttrDisplayValue(skuAttrItems, null);
        assertEquals("", result);
    }

    /**
     * 测试buildUnionAttrDisplayValue方法，当skuAttrItems为空时
     */
    @Test
    public void testBuildUnionAttrDisplayValueWithEmptySkuAttrItems() {
        SelfServiceCarWashAttrVOListOpt.AttrModel attrModel = new SelfServiceCarWashAttrVOListOpt.AttrModel();
        attrModel.setAttrName("attrName");
        attrModel.setAttrValueDesc("Description: %s");
        List<SelfServiceCarWashAttrVOListOpt.AttrModel> attrModels = Collections.singletonList(attrModel);
        String result = selfServiceCarWashAttrVOListOpt.buildUnionAttrDisplayValue(Collections.emptyList(), attrModels);
        assertEquals("", result);
    }

    /**
     * 测试buildUnionAttrDisplayValue方法，正常情况
     */
    @Test
    public void testBuildUnionAttrDisplayValueNormal() {
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("attrName");
        skuAttrItemDto.setAttrValue("attrValue");
        skuAttrItems.add(skuAttrItemDto);
        SelfServiceCarWashAttrVOListOpt.AttrModel attrModel = new SelfServiceCarWashAttrVOListOpt.AttrModel();
        attrModel.setAttrName("attrName");
        attrModel.setAttrValueDesc("Description: %s");
        List<SelfServiceCarWashAttrVOListOpt.AttrModel> attrModels = Collections.singletonList(attrModel);


        String result = selfServiceCarWashAttrVOListOpt.buildUnionAttrDisplayValue(skuAttrItems, attrModels);
        assertEquals("Description: attrValue", result);
    }
}
