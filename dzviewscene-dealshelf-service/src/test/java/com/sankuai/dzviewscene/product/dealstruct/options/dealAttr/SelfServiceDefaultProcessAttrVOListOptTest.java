package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.dianping.customer.dto.CustomerShopNew;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.joygeneral.api.thirdpart.dto.Response;
import com.sankuai.FileUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.testng.collections.Lists;

@RunWith(MockitoJUnitRunner.class)
public class SelfServiceDefaultProcessAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SelfServiceDefaultProcessAttrVOListOpt.Param param;

    @Mock
    private SelfServiceDefaultProcessAttrVOListOpt.Config config;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private AtomFacadeService atomFacadeService;

    @InjectMocks
    private SelfServiceDefaultProcessAttrVOListOpt opt;

    @InjectMocks
    private SelfServiceCarWashDefaultProcessAttrVOListOpt carWashDefaultProcessAttrVOListOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        SelfServiceDefaultProcessAttrVOListOpt opt = new SelfServiceDefaultProcessAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testProcessSwitchIsFalse() throws Throwable {
        SelfServiceDefaultProcessAttrVOListOpt opt = new SelfServiceDefaultProcessAttrVOListOpt();
        when(config.getProcessSwitch()).thenReturn(false);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeParamIsNull() throws Throwable {
        SelfServiceDefaultProcessAttrVOListOpt opt = new SelfServiceDefaultProcessAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, null, config);
        assertNull(result);
    }

    @Test
    public void testComputeDealAttrsIsEmpty() throws Throwable {
        SelfServiceDefaultProcessAttrVOListOpt opt = new SelfServiceDefaultProcessAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeDealDetailDtoModelContainsMerchantConfig() throws Throwable {
        SelfServiceDefaultProcessAttrVOListOpt opt = new SelfServiceDefaultProcessAttrVOListOpt();
        // Assuming DealDetailDtoModel is accessible and // when(param.getDealDetailDtoModel()).thenReturn(mock(DealDetailDtoModel.class));
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeProcessSwitchIsFalse() throws Throwable {
        SelfServiceDefaultProcessAttrVOListOpt opt = new SelfServiceDefaultProcessAttrVOListOpt();
        when(config.getProcessSwitch()).thenReturn(false);
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeSuccessFor1805(){
        ActivityCxt activityCxt = new ActivityCxt();
        SelfServiceDefaultProcessAttrVOListOpt.Config config = JSON.parseObject(FileUtil.file2str("selfServiceOptConfig.json"), SelfServiceDefaultProcessAttrVOListOpt.Config.class);
        Response response = Response.success(true);
        CustomerShopNew customerShopNew = new CustomerShopNew();
        customerShopNew.setCustomerID(43542790);
        HaimaResponse haimaResponse = JSON.parseObject(FileUtil.file2str("selfServiceOptHaima.json"), HaimaResponse.class);

        HashMap<String, Object> params = new HashMap<>();
        params.put("dpPoiIdL",123456L);
        params.put("dealCategoryId",1805);
        params.put("productId",1234);
        activityCxt.setParameters(params);
        when(compositeAtomService.getHaiMaResponse(any())).thenReturn(CompletableFuture.completedFuture(haimaResponse));
        when(atomFacadeService.getCustomerShopsByShopIdsNew(anyList())).thenReturn(CompletableFuture.completedFuture(Lists.newArrayList(customerShopNew)));
        when(compositeAtomService.queryAutoOpenTable(any())).thenReturn(CompletableFuture.completedFuture(response));
        List<DealDetailStructAttrModuleGroupModel> compute1 = opt.compute(activityCxt, param, config);
        assertNull(compute1);
        customerShopNew.setCustomerID(1111111);
        List<DealDetailStructAttrModuleGroupModel> compute2 = opt.compute(activityCxt, param, config);
        assertNotNull(compute2);
    }

    /**
     * 洗车机
     */
    @Test
    public void testComputeSuccessFor1503(){
        ActivityCxt activityCxt = new ActivityCxt();
        HaimaResponse haimaResponse = JSON.parseObject(FileUtil.file2str("selfServiceOptHaima.json"), HaimaResponse.class);
        SelfServiceDefaultProcessAttrVOListOpt.Config config = JSON.parseObject(FileUtil.file2str("selfServiceOptConfig.json"), SelfServiceDefaultProcessAttrVOListOpt.Config.class);
        CustomerShopNew customerShopNew = new CustomerShopNew();
        customerShopNew.setCustomerID(43542790);

        HashMap<String, Object> params = new HashMap<>();
        params.put("dpPoiIdL",123456L);
        params.put("dealCategoryId",1503);
        params.put("productId",1234);
        activityCxt.setParameters(params);
        when(compositeAtomService.getHaiMaResponse(any())).thenReturn(CompletableFuture.completedFuture(haimaResponse));
        when(atomFacadeService.getCustomerShopsByShopIdsNew(anyList())).thenReturn(CompletableFuture.completedFuture(Lists.newArrayList(customerShopNew)));
        List<DealDetailStructAttrModuleGroupModel> compute = carWashDefaultProcessAttrVOListOpt.compute(activityCxt, param, config);
        assertNotNull(compute);
    }
}
