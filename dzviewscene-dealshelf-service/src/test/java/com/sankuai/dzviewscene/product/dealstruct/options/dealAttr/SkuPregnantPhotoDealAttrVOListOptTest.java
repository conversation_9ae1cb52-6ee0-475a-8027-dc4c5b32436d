package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SkuPregnantPhotoDealAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private SkuPregnantPhotoDealAttrVOListOpt.Config config;

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        SkuPregnantPhotoDealAttrVOListOpt opt = new SkuPregnantPhotoDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, null);
        assertTrue(result == null || result.isEmpty());
    }

    @Test
    public void testComputeParamIsNull() throws Throwable {
        SkuPregnantPhotoDealAttrVOListOpt opt = new SkuPregnantPhotoDealAttrVOListOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, null, config);
        assertTrue(result == null || result.isEmpty());
    }

    @Test
    public void testComputeDealAttrsIsEmpty() throws Throwable {
        SkuPregnantPhotoDealAttrVOListOpt opt = new SkuPregnantPhotoDealAttrVOListOpt();
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        assertTrue(opt.compute(context, param, config).isEmpty());
    }

    @Test
    public void testComputeAttrListGroupModelsIsEmpty() throws Throwable {
        SkuPregnantPhotoDealAttrVOListOpt opt = new SkuPregnantPhotoDealAttrVOListOpt();
        when(config.getAttrListGroupModels()).thenReturn(new ArrayList<>());
        assertTrue(opt.compute(context, param, config).isEmpty());
    }

    @Test
    public void testComputeBuildDealDetailStructAttrModuleVOListReturnsNull() throws Throwable {
        SkuPregnantPhotoDealAttrVOListOpt opt = new SkuPregnantPhotoDealAttrVOListOpt();
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("name", "value"));
        when(param.getDealAttrs()).thenReturn(dealAttrs);
        when(config.getAttrListGroupModels()).thenReturn(new ArrayList<>());
        assertTrue(opt.compute(context, param, config).isEmpty());
    }

    @Test
    public void testCompute_FilterEqualAttr_NotEqual() {
        SkuPregnantPhotoDealAttrVOListOpt opt = new SkuPregnantPhotoDealAttrVOListOpt();
        String configStr = "{\n" +
                "    \"attrListGroupModels\": [\n" +
                "      {\n" +
                "        \"groupName\": \"房型设施\",\n" +
                "        \"attrModelList\": [\n" +
                "          {\n" +
                "            \"displayName\": \"面积\",\n" +
                "            \"attrNameList\": [\n" +
                "              \"minArea\",\n" +
                "              \"maxArea\"\n" +
                "            ],\n" +
                "            \"attrFormatModels\": [\n" +
                "              {\n" +
                "                \"attrName\": \"minArea\",\n" +
                "                \"displayFormat\": \"%sM²\"\n" +
                "              },\n" +
                "              {\n" +
                "                \"attrName\": \"maxArea\",\n" +
                "                \"displayFormat\": \"%sM²\"\n" +
                "              }\n" +
                "            ],\n" +
                "            \"seperator\": \"-\",\n" +
                "            \"icon\": \"https://p0.meituan.net/dztgdetailimages/cab612ebf7204f7cfa87303091417b90843.png\",\n" +
                "            \"filterEqualAttr\": true\n" +
                "          }\n" +
                "        ]\n" +
                "    }\n" +
                "    ]\n" +
                "  }\n" +
                "  ";
        SkuPregnantPhotoDealAttrVOListOpt.Config config = JSON.parseObject(configStr, SkuPregnantPhotoDealAttrVOListOpt.Config.class);
        AttrM attr1 = new AttrM();
        attr1.setName("minArea");
        attr1.setValue("13");
        AttrM attr2 = new AttrM();
        attr2.setName("maxArea");
        attr2.setValue("14");
        List<AttrM> dealAttrs = Lists.newArrayList(attr1, attr2);
        when(param.getDealAttrs()).thenReturn(dealAttrs);

        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0).equals("13M²-14M²"));
    }

    @Test
    public void testCompute_FilterEqualAttr_Equal() {
        SkuPregnantPhotoDealAttrVOListOpt opt = new SkuPregnantPhotoDealAttrVOListOpt();
        String configStr = "{\n" +
                "    \"attrListGroupModels\": [\n" +
                "      {\n" +
                "        \"groupName\": \"房型设施\",\n" +
                "        \"attrModelList\": [\n" +
                "          {\n" +
                "            \"displayName\": \"面积\",\n" +
                "            \"attrNameList\": [\n" +
                "              \"minArea\",\n" +
                "              \"maxArea\"\n" +
                "            ],\n" +
                "            \"attrFormatModels\": [\n" +
                "              {\n" +
                "                \"attrName\": \"minArea\",\n" +
                "                \"displayFormat\": \"%sM²\"\n" +
                "              },\n" +
                "              {\n" +
                "                \"attrName\": \"maxArea\",\n" +
                "                \"displayFormat\": \"%sM²\"\n" +
                "              }\n" +
                "            ],\n" +
                "            \"seperator\": \"-\",\n" +
                "            \"icon\": \"https://p0.meituan.net/dztgdetailimages/cab612ebf7204f7cfa87303091417b90843.png\",\n" +
                "            \"filterEqualAttr\": true\n" +
                "          }\n" +
                "        ]\n" +
                "    }\n" +
                "    ]\n" +
                "  }\n" +
                "  ";
        SkuPregnantPhotoDealAttrVOListOpt.Config config = JSON.parseObject(configStr, SkuPregnantPhotoDealAttrVOListOpt.Config.class);
        AttrM attr1 = new AttrM();
        attr1.setName("minArea");
        attr1.setValue("13");
        AttrM attr2 = new AttrM();
        attr2.setName("maxArea");
        attr2.setValue("13");
        List<AttrM> dealAttrs = Lists.newArrayList(attr1, attr2);
        when(param.getDealAttrs()).thenReturn(dealAttrs);

        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrValues().get(0).equals("13M²"));

    }
}
