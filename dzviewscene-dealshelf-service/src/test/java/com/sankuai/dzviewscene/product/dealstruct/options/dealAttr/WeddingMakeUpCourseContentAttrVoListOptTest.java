package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WeddingMakeUpCourseContentAttrVoListOptTest {
    @Mock
    private ActivityCxt ctx;
    @Mock
    private WeddingMakeUpCourseContentAttrVOListOpt.Config config;
    @Mock
    private WeddingMakeUpCourseContentAttrVOListOpt.Param param;
    @InjectMocks
    private WeddingMakeUpCourseContentAttrVOListOpt opt;
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        String paramJson = "{\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1033997286,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1033997286,\\\\\\\"title\\\\\\\":null,\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"mustGroups\\\\\\\":[],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"html\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1033997286,\\\"title\\\":null,\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":[],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"html\\\"},\\\"productCategories\\\":[],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1033997286,\\\"title\\\":null,\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":null,\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":{\\\"totalPrice\\\":200.0,\\\"groups\\\":[]},\\\"type\\\":\\\"serviceItem-structure-table\\\"}],\\\"headline\\\":\\\"团购详情\\\",\\\"version\\\":1}\"},{\"name\":\"MakeupCourseContent\",\"value\":\"{\\\"CosmeticsCourseContent\\\":\\\"基础化妆技巧\\\"},{\\\"CosmeticsCourseContent\\\":\\\"才咋混改\\\"},{\\\"CosmeticsCourseContent\\\":\\\"兴趣培训3\\\"}\"}],\"dealDetailDtoModel\":{\"dealGroupId\":1033997286,\"skuUniStructuredDto\":{\"mustGroups\":[],\"optionalGroups\":[]},\"structType\":\"html\"},\"dealDetailInfoModel\":{\"additionalProjectList\":[],\"dealAttrs\":[{\"$ref\":\"$.dealAttrs[0]\"},{\"$ref\":\"$.dealAttrs[1]\"},{\"$ref\":\"$.dealAttrs[2]\"}],\"dealDetailDtoModel\":{\"$ref\":\"$.dealDetailDtoModel\"},\"dealId\":1033997286,\"unifyProduct\":true}}";
        param = JSON.parseObject(paramJson, WeddingMakeUpCourseContentAttrVOListOpt.Param.class);
    }
    /**
     * 测试param和config为null时的场景
     */
    @Test
    public void testComputeParamAndConfigNull() {
        WeddingMakeUpCourseContentAttrVOListOpt opt = new WeddingMakeUpCourseContentAttrVOListOpt();
        assertNull(opt.compute(null, null, null));
    }

    /**
     * 测试compute方法，当attrName为空时
     */
    @Test
    public void testComputeAttrNameEmpty() throws Throwable {
        // arrange
        config.setAttrName("");

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(ctx, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试compute方法，正常场景
     */
    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        when(config.getAttrName()).thenReturn("MakeupCourseContent");

        // 此处应根据实际情况mock相关方法，例如mock获取课程内容的方法，返回预期的课程内容列表

        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(ctx, param, config);

        // assert
        assertNotNull(result);
        // 根据预期的课程内容列表，进行进一步的断言
    }

}
