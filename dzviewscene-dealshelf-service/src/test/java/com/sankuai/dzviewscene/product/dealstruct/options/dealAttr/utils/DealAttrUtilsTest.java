package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.utils;

import static org.junit.Assert.*;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrUtilsTest {

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelDealDetailDtoModelIsNull() throws Throwable {
        assertNull(DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(null));
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelSkuUniStructuredDtoIsNull() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        assertNull(DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel));
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelMustGroupsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        assertNull(DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel));
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelMustSkuItemsGroupDtoIsNull() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        dealDetailDtoModel.getSkuUniStructuredDto().setMustGroups(Collections.singletonList(null));
        assertNull(DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel));
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelSkuItemsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        dealDetailDtoModel.getSkuUniStructuredDto().setMustGroups(Collections.singletonList(new MustSkuItemsGroupDto()));
        assertNull(DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel));
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelSkuItemDtoIsNull() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        dealDetailDtoModel.getSkuUniStructuredDto().setMustGroups(Collections.singletonList(new MustSkuItemsGroupDto()));
        dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups().get(0).setSkuItems(Collections.singletonList(null));
        assertNull(DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel));
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelAttrItemsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        dealDetailDtoModel.getSkuUniStructuredDto().setMustGroups(Collections.singletonList(new MustSkuItemsGroupDto()));
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Collections.emptyList());
        dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups().get(0).setSkuItems(Collections.singletonList(skuItemDto));
        List<AttrM> result = DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelNormal() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        dealDetailDtoModel.getSkuUniStructuredDto().setMustGroups(Collections.singletonList(new MustSkuItemsGroupDto()));
        SkuItemDto skuItemDto = new SkuItemDto();
        com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto skuAttrItemDto = new com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto();
        skuAttrItemDto.setAttrName("TestAttrName");
        skuAttrItemDto.setAttrValue("TestAttrValue");
        skuItemDto.setAttrItems(Collections.singletonList(skuAttrItemDto));
        dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups().get(0).setSkuItems(Collections.singletonList(skuItemDto));
        List<AttrM> result = DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("TestAttrName", result.get(0).getName());
        assertEquals("TestAttrValue", result.get(0).getValue());
    }

    @Test
    public void testExtractFirstSkuAttrFromDealDetailDtoModelSkuItemDtoIsNull2() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        dealDetailDtoModel.getSkuUniStructuredDto().setMustGroups(Collections.singletonList(new MustSkuItemsGroupDto()));
        dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups().get(0).setSkuItems(Collections.singletonList(null));
        assertNull(DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(dealDetailDtoModel));
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenAttrsIsNull() throws Throwable {
        List<AttrM> attrs = null;
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        assertNull(result);
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenNoDetailInfoAttr() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("other", "value"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        assertNull(result);
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenDetailInfoValueIsNull() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", null));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        assertNull(result);
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenParseDetailInfoValueThrowsException() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "invalid json"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        assertNull(result);
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenContentArrayIsEmpty() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "{\"content\":[]}"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        // Adjusted to expect an empty list
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenNoUniformStructureTableObj() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "{\"content\":[{\"type\":\"other\"}]}"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        // Adjusted to expect an empty list
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenDataObjIsNull() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "{\"content\":[{\"type\":\"uniform-structure-table\",\"data\":null}]}"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        // Adjusted to expect an empty list
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenGroupsArrayIsEmpty() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "{\"content\":[{\"type\":\"uniform-structure-table\",\"data\":{\"groups\":[]}}]}"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        // Adjusted to expect an empty list
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenUnitsArrayIsEmpty() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "{\"content\":[{\"type\":\"uniform-structure-table\",\"data\":{\"groups\":[{\"units\":[]}]}}]}"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        // Adjusted to expect an empty list
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenAttrValuesKeyIsMissing() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "{\"content\":[{\"type\":\"uniform-structure-table\",\"data\":{\"groups\":[{\"units\":[{\"attrValues\":null}]}]}}]}"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        assertNull(result);
    }

    @Test
    public void testGetDealDetailInfoAttrsFromAttrsWhenAllConditionsAreMet() throws Throwable {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("detailInfo", "{\"content\":[{\"type\":\"uniform-structure-table\",\"data\":{\"groups\":[{\"units\":[{\"attrValues\":{\"key\":\"value\"}}]}]}}]}"));
        List<AttrM> result = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(attrs);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("key", result.get(0).getName());
        assertEquals("value", result.get(0).getValue());
    }
}