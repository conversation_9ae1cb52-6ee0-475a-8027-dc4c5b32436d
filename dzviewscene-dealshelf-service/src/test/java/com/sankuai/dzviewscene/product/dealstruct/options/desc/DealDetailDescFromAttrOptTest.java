package com.sankuai.dzviewscene.product.dealstruct.options.desc;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints.DealDetailDescVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailDescFromAttrOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealDetailDescVP.Param param;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    private AttrM buildAttr(String attrName, String value) {
        AttrM attrM = new AttrM();
        attrM.setName(attrName);
        attrM.setValue(value);
        return attrM;
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsEmpty() {
        // arrange
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        // act
        String result = opt.compute(context, param, new DealDetailDescFromAttrOpt.Config());
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeResultListIsNotEmpty() throws Throwable {
        // arrange
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getAttrValueFromAllAttrs("attrName")).thenReturn("房间规格1");
        DealDetailDescFromAttrOpt.Config config = new DealDetailDescFromAttrOpt.Config();
        config.setAttrKey("attrName");
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertTrue("房间规格1".equals(result));
    }

    @Test
    public void testComputeWhenParamIsNull() throws Throwable {
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        assertNull(opt.compute(new ActivityCxt(), null, new DealDetailDescFromAttrOpt.Config()));
    }

    @Test
    public void testComputeWhenDealDetailInfoModelIsNull() throws Throwable {
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        DealDetailDescVP.Param param = DealDetailDescVP.Param.builder().build();
        assertNull(opt.compute(new ActivityCxt(), param, new DealDetailDescFromAttrOpt.Config()));
    }

    @Test
    public void testComputeWhenConfigIsNull() throws Throwable {
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        DealDetailDescVP.Param param = DealDetailDescVP.Param.builder().build();
        assertNull(opt.compute(new ActivityCxt(), param, null));
    }

    @Test
    public void testComputeWhenAttrKeyIsEmpty() throws Throwable {
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        DealDetailDescVP.Param param = DealDetailDescVP.Param.builder().build();
        assertNull(opt.compute(new ActivityCxt(), param, new DealDetailDescFromAttrOpt.Config()));
    }

    @Test
    public void testComputeWhenDealAttrsIsEmpty() throws Throwable {
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        DealDetailDescFromAttrOpt.Config config = new DealDetailDescFromAttrOpt.Config();
        config.setAttrKey("attrKey");
        DealDetailDescVP.Param param = DealDetailDescVP.Param.builder().dealDetailInfoModel(new DealDetailInfoModel()).build();
        assertNull(opt.compute(new ActivityCxt(), param, config));
    }

    @Test
    public void testComputeWhenNoAttrMWithNameEqualsAttrKey() throws Throwable {
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        DealDetailDescFromAttrOpt.Config config = new DealDetailDescFromAttrOpt.Config();
        config.setAttrKey("attrKey");
        DealDetailInfoModel model = new DealDetailInfoModel();
        model.setDealAttrs(Arrays.asList(new AttrM("otherKey", "value")));
        DealDetailDescVP.Param param = DealDetailDescVP.Param.builder().dealDetailInfoModel(model).build();
        assertNull(opt.compute(new ActivityCxt(), param, config));
    }

    @Test
    public void testComputeWhenAttrMWithNameEqualsAttrKeyExists() throws Throwable {
        DealDetailDescFromAttrOpt opt = new DealDetailDescFromAttrOpt();
        DealDetailDescFromAttrOpt.Config config = new DealDetailDescFromAttrOpt.Config();
        config.setAttrKey("attrKey");
        DealDetailInfoModel model = new DealDetailInfoModel();
        model.setDealAttrs(Arrays.asList(new AttrM("attrKey", "value")));
        DealDetailDescVP.Param param = DealDetailDescVP.Param.builder().dealDetailInfoModel(model).build();
        assertEquals("value", opt.compute(new ActivityCxt(), param, config));
    }
}
