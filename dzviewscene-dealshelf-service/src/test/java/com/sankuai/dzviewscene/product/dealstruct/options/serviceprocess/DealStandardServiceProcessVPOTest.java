package com.sankuai.dzviewscene.product.dealstruct.options.serviceprocess;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess.vcpoints.DealStandardServiceProcessVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.serviceprocess.DealStandardServiceProcessVPO.Config;
import com.sankuai.dzviewscene.product.dealstruct.options.serviceprocess.DealStandardServiceProcessVPO.WearableNailServiceProcess;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceProcessVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStandardServiceProcessVPOTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private DealStandardServiceProcessVP.Param param;
    @Mock
    private Config config;

    private DealStandardServiceProcessVPO dealStandardServiceProcessVPO;

    private MockedStatic<DealDetailUtils> dealDetailUtilsMockedStatic;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dealStandardServiceProcessVPO = new DealStandardServiceProcessVPO();
        dealDetailUtilsMockedStatic = mockStatic(DealDetailUtils.class);
    }

    @After
    public void tearDown() {
        dealDetailUtilsMockedStatic.close();
    }

    /**
     * 测试compute方法，当dealDetailInfoModels为空时
     */
    @Test
    public void testComputeWhenDealDetailInfoModelsIsEmpty() {
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());

        List<StandardServiceProcessVO> result = dealStandardServiceProcessVPO.compute(activityCxt, param, config);

        assertNull(result);
        verify(activityCxt, times(1)).getSource(DealDetailFetcher.CODE);
    }

    /**
     * 测试compute方法，当dealAttrs为空时
     */
    @Test
    public void testComputeWhenDealAttrsIsEmpty() {
        DealDetailInfoModel dealDetailInfoModel = mock(DealDetailInfoModel.class);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Collections.emptyList());
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));

        List<StandardServiceProcessVO> result = dealStandardServiceProcessVPO.compute(activityCxt, param, config);

        assertNull(result);
        verify(activityCxt, times(1)).getSource(DealDetailFetcher.CODE);
    }

    /**
     * 测试compute方法，正常情况
     */
    @Test
    public void testComputeNormalCase() {
        DealDetailInfoModel dealDetailInfoModel = mock(DealDetailInfoModel.class);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Arrays.asList(new AttrM("name", "value")));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));

        Map<String, List<WearableNailServiceProcess>> wearableNailServiceProcessMap = new HashMap<>();
        wearableNailServiceProcessMap.put("freeWearingAtStore", Collections.singletonList(new WearableNailServiceProcess()));
//        when(config.getWearableNailServiceProcessMap()).thenReturn(wearableNailServiceProcessMap);

        List<StandardServiceProcessVO> result = dealStandardServiceProcessVPO.compute(activityCxt, param, config);

        assertNull(result);
//        assertFalse(result.isEmpty());
//        verify(activityCxt, times(1)).getSource(DealDetailFetcher.CODE);
//        verify(config, times(1)).getWearableNailServiceProcessMap();
    }


    /**
     * 测试穿戴甲类型为穿戴甲且可到店佩戴时返回正确的服务流程
     */
    @Test
    public void testGetWearableNailDealStandardServiceProcess_WearableNailCanWearAtStore() {
        // arrange
        List<AttrM> dealAttrs = Arrays.asList(new AttrM("type", "wearableNail"), new AttrM("canWearAtStore", "true"));
        Map<String, List<WearableNailServiceProcess>> wearableNailServiceProcessMap = Maps.newHashMap();
        WearableNailServiceProcess wearableNailServiceProcess = new WearableNailServiceProcess();
        wearableNailServiceProcess.setIcon("xxx");
        wearableNailServiceProcess.setStepName("step1");
        wearableNailServiceProcess.setRightArrowIcon("yyy");
        wearableNailServiceProcessMap.put("notFreeWearingAtStore", Lists.newArrayList(wearableNailServiceProcess));
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.isWearableNail(any())).thenReturn(true);
        when(config.getWearableNailServiceProcessMap()).thenReturn(wearableNailServiceProcessMap);

        // act
        List<StandardServiceProcessVO> result = dealStandardServiceProcessVPO.getWearableNailDealStandardServiceProcess(dealAttrs, config);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("step1", result.get(0).getStepName());
    }
}
