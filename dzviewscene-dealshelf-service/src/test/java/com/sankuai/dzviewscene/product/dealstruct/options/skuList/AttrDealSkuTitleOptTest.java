package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AttrDealSkuTitleOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private SkuTitleVP.Param param;

    @Mock
    private AttrDealSkuTitleOpt.Config config;

    @Test
    public void testComputeAttrKeyIsNull() throws Throwable {
        when(config.getAttrKey()).thenReturn(null);
        when(param.getSkuTitle()).thenReturn("skuTitle");
        AttrDealSkuTitleOpt attrDealSkuTitleOpt = new AttrDealSkuTitleOpt();
        String result = attrDealSkuTitleOpt.compute(activityCxt, param, config);
        assertEquals("skuTitle", result);
    }

    @Test
    public void testComputeAttrKeyIsNotNullButValueIsNull() throws Throwable {
        when(config.getAttrKey()).thenReturn("attrKey");
        when(param.getSkuTitle()).thenReturn("skuTitle");
        when(param.getDealAttrs()).thenReturn(null);
        AttrDealSkuTitleOpt attrDealSkuTitleOpt = new AttrDealSkuTitleOpt();
        String result = attrDealSkuTitleOpt.compute(activityCxt, param, config);
        assertEquals("skuTitle", result);
    }

    @Test
    public void testComputeAttrKeyIsNotNullValueIsNotNullButValueAliaMapIsNull() throws Throwable {
        when(config.getAttrKey()).thenReturn("attrKey");
        AttrM attrM = new AttrM("attrKey", "value");
        when(param.getDealAttrs()).thenReturn(Arrays.asList(attrM));
        when(config.getValueAliaMap()).thenReturn(null);
        AttrDealSkuTitleOpt attrDealSkuTitleOpt = new AttrDealSkuTitleOpt();
        String result = attrDealSkuTitleOpt.compute(activityCxt, param, config);
        assertEquals("value", result);
    }

    @Test
    public void testComputeAttrKeyIsNotNullValueIsNotNullValueAliaMapIsNotNullButValueNotInValueAliaMap() throws Throwable {
        when(config.getAttrKey()).thenReturn("attrKey");
        AttrM attrM = new AttrM("attrKey", "value");
        when(param.getDealAttrs()).thenReturn(Arrays.asList(attrM));
        Map<String, String> valueAliaMap = new HashMap<>();
        valueAliaMap.put("aliaValue", "value");
        when(config.getValueAliaMap()).thenReturn(valueAliaMap);
        AttrDealSkuTitleOpt attrDealSkuTitleOpt = new AttrDealSkuTitleOpt();
        String result = attrDealSkuTitleOpt.compute(activityCxt, param, config);
        assertEquals("value", result);
    }

    @Test
    public void testComputeAttrKeyIsNotNullValueIsNotNullValueAliaMapIsNotNullAndValueInValueAliaMap() throws Throwable {
        when(config.getAttrKey()).thenReturn("attrKey");
        AttrM attrM = new AttrM("attrKey", "value");
        when(param.getDealAttrs()).thenReturn(Arrays.asList(attrM));
        Map<String, String> valueAliaMap = new HashMap<>();
        // Correctly map the original value to its alias
        valueAliaMap.put("value", "aliaValue");
        when(config.getValueAliaMap()).thenReturn(valueAliaMap);
        AttrDealSkuTitleOpt attrDealSkuTitleOpt = new AttrDealSkuTitleOpt();
        String result = attrDealSkuTitleOpt.compute(activityCxt, param, config);
        assertEquals("aliaValue", result);
    }
}
