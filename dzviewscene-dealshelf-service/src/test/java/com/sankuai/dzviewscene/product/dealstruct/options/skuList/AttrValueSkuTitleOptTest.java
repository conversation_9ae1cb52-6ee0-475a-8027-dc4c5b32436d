package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AttrValueSkuTitleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuTitleVP.Param param;

    @Mock
    private AttrValueSkuTitleOpt.Config config;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private AttrM attrM;

    @Test
    public void testComputeSkuItemDtoIsNull() {
        when(param.getSkuItemDto()).thenReturn(null);
        AttrValueSkuTitleOpt opt = new AttrValueSkuTitleOpt();
        String result = opt.compute(context, param, config);
        assertEquals(param.getSkuTitle(), result);
    }

    @Test
    public void testComputeAttrItemsIsEmpty() {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(Collections.emptyList());
        AttrValueSkuTitleOpt opt = new AttrValueSkuTitleOpt();
        String result = opt.compute(context, param, config);
        assertEquals(param.getSkuTitle(), result);
    }

    @Test
    public void testComputeConfigIsNull() {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(Collections.singletonList(new SkuAttrItemDto()));
        AttrValueSkuTitleOpt opt = new AttrValueSkuTitleOpt();
        String result = opt.compute(context, param, null);
        assertEquals(param.getSkuTitle(), result);
    }

    @Test
    public void testComputeProductCategoryListIsEmpty() {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(Collections.singletonList(new SkuAttrItemDto()));
        when(config.getProductCategoryList()).thenReturn(Collections.emptyList());
        AttrValueSkuTitleOpt opt = new AttrValueSkuTitleOpt();
        String result = opt.compute(context, param, config);
        assertEquals(param.getSkuTitle(), result);
    }

    @Test
    public void testComputeProductCategoryNotInList() {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(Collections.singletonList(new SkuAttrItemDto()));
        when(config.getProductCategoryList()).thenReturn(Collections.singletonList(1L));
        AttrValueSkuTitleOpt opt = new AttrValueSkuTitleOpt();
        String result = opt.compute(context, param, config);
        assertEquals(param.getSkuTitle(), result);
    }

    @Test
    public void testComputeProductCategoryInList() {
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getAttrItems()).thenReturn(Collections.singletonList(new SkuAttrItemDto()));
        when(config.getProductCategoryList()).thenReturn(Collections.singletonList(1L));
        AttrValueSkuTitleOpt opt = new AttrValueSkuTitleOpt();
        String result = opt.compute(context, param, config);
        // 这里的断言需要根据 buildSkuTitle 方法的具体实现来写
        assertNull(result);
    }
}
