package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BathSkuAttrListOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private BathSkuAttrListOpt.Param param;

    @Mock
    private BathSkuAttrListOpt.Config config;

    @Mock
    private SkuItemDto skuItemDto;

    /**
     * Test compute() when skuItemDto is null
     * Should return null
     */
    @Test
    public void testComputeWhenSkuItemDtoIsNull() throws Throwable {
        // arrange
        when(param.getSkuItemDto()).thenReturn(null);
        BathSkuAttrListOpt bathSkuAttrListOpt = new BathSkuAttrListOpt();
        // act
        List<DealSkuItemVO> result = bathSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test compute() with valid bath ticket data
     * Should return DealSkuItemVO list with correct value
     */
    @Test
    public void testComputeWithValidBathTicketData() throws Throwable {
        // arrange
        String expectedContent = "Test Content";
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // BATH_TICKET
        when(skuItemDto.getProductCategory()).thenReturn(895L);
        List<SkuAttrItemDto> attrItems = Lists.newArrayList();
        // Add content attribute
        SkuAttrItemDto contentAttr = new SkuAttrItemDto();
        contentAttr.setAttrName("content");
        contentAttr.setAttrValue(expectedContent);
        attrItems.add(contentAttr);
        // Add usepeoplenum attribute
        SkuAttrItemDto peopleNumAttr = new SkuAttrItemDto();
        peopleNumAttr.setAttrName("usepeoplenum");
        peopleNumAttr.setAttrValue("2人");
        attrItems.add(peopleNumAttr);
        when(skuItemDto.getAttrItems()).thenReturn(attrItems);
        when(skuItemDto.getName()).thenReturn("浴资票");
        BathSkuAttrListOpt bathSkuAttrListOpt = new BathSkuAttrListOpt();
        // act
        List<DealSkuItemVO> result = bathSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(0).getValue());
        assertTrue(result.get(0).getValue().contains(expectedContent));
    }

    /**
     * Test compute() with massage service data
     * Should return DealSkuItemVO list with formatted massage info
     */
    @Test
    public void testComputeWithMassageServiceData() throws Throwable {
        // arrange
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // MASSAGE
        when(skuItemDto.getProductCategory()).thenReturn(897L);
        List<SkuAttrItemDto> attrItems = Lists.newArrayList();
        // Add required attributes for massage service
        SkuAttrItemDto categoryAttr = new SkuAttrItemDto();
        categoryAttr.setAttrName("spuCategory");
        categoryAttr.setAttrValue("精油按摩");
        attrItems.add(categoryAttr);
        SkuAttrItemDto contentAttr = new SkuAttrItemDto();
        contentAttr.setAttrName("content");
        contentAttr.setAttrValue("专业按摩服务");
        attrItems.add(contentAttr);
        when(skuItemDto.getAttrItems()).thenReturn(attrItems);
        BathSkuAttrListOpt bathSkuAttrListOpt = new BathSkuAttrListOpt();
        // act
        List<DealSkuItemVO> result = bathSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(0).getValue());
        assertTrue(result.get(0).getValue().contains("精油按摩"));
        assertTrue(result.get(0).getValue().contains("专业按摩服务"));
    }

    /**
     * Test compute() when remark is blank
     * Should return null
     */
    @Test
    public void testComputeWhenRemarkIsBlank() throws Throwable {
        // arrange
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // BATH_TICKET
        when(skuItemDto.getProductCategory()).thenReturn(895L);
        // Set up attributes that will result in blank remark
        List<SkuAttrItemDto> attrItems = Lists.newArrayList();
        SkuAttrItemDto attr = new SkuAttrItemDto();
        attr.setAttrName("irrelevant");
        attr.setAttrValue("");
        attrItems.add(attr);
        when(skuItemDto.getAttrItems()).thenReturn(attrItems);
        BathSkuAttrListOpt bathSkuAttrListOpt = new BathSkuAttrListOpt();
        // act
        List<DealSkuItemVO> result = bathSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test compute() when remark is not blank
     * Should create DealSkuItemVO with remark value and return as list
     */
    @Test
    public void testComputeWhenRemarkNotBlank() throws Throwable {
        // arrange
        String expectedRemark = "Test Remark";
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // Mock SkuItemDto with valid data to get non-blank remark
        // BATH_TICKET
        when(skuItemDto.getProductCategory()).thenReturn(895L);
        List<SkuAttrItemDto> attrItems = Lists.newArrayList();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("content");
        attrItem.setAttrValue(expectedRemark);
        attrItems.add(attrItem);
        when(skuItemDto.getAttrItems()).thenReturn(attrItems);
        BathSkuAttrListOpt bathSkuAttrListOpt = new BathSkuAttrListOpt();
        // act
        List<DealSkuItemVO> result = bathSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedRemark, result.get(0).getValue());
    }
}
