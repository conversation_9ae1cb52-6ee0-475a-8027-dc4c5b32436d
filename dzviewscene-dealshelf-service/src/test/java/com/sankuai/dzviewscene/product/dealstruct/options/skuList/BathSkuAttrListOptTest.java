package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BathSkuAttrListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuAttrListVP.Param param;

    @Mock
    private BathSkuAttrListOpt.Config config;

    @Test
    public void testComputeWhenSkuItemDtoIsNull() throws Throwable {
        when(param.getSkuItemDto()).thenReturn(null);
        BathSkuAttrListOpt bathSkuAttrListOpt = new BathSkuAttrListOpt();
        List<DealSkuItemVO> result = bathSkuAttrListOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWhenRemarkIsNull() throws Throwable {
        when(param.getSkuItemDto()).thenReturn(new SkuItemDto());
        BathSkuAttrListOpt bathSkuAttrListOpt = new BathSkuAttrListOpt();
        List<DealSkuItemVO> result = bathSkuAttrListOpt.compute(context, param, config);
        assertNull(result);
    }
}
