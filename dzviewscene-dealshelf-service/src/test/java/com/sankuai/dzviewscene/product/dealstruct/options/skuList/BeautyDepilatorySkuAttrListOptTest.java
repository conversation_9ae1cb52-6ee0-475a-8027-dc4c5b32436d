package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.lang.reflect.Constructor;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class BeautyDepilatorySkuAttrListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private BeautyDepilatorySkuAttrListOpt.Config config;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private List<ProductSkuCategoryModel> productSkuCategoryModels;

    @Mock
    private List<AttrM> attrMs;

    private BeautyDepilatorySkuAttrListOpt.Param createParam(SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productSkuCategoryModels, List<AttrM> attrMs) throws Exception {
        Constructor<BeautyDepilatorySkuAttrListOpt.Param> constructor = BeautyDepilatorySkuAttrListOpt.Param.class.getDeclaredConstructor(SkuItemDto.class, List.class, List.class);
        constructor.setAccessible(true);
        return constructor.newInstance(skuItemDto, productSkuCategoryModels, attrMs);
    }

    @Test
    public void testComputeSkuItemDtoIsNull() throws Throwable {
        BeautyDepilatorySkuAttrListOpt opt = new BeautyDepilatorySkuAttrListOpt();
        BeautyDepilatorySkuAttrListOpt.Param param = createParam(skuItemDto, productSkuCategoryModels, attrMs);
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeAttrItemsIsEmpty() throws Throwable {
        BeautyDepilatorySkuAttrListOpt opt = new BeautyDepilatorySkuAttrListOpt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Collections.emptyList());
        BeautyDepilatorySkuAttrListOpt.Param param = createParam(skuItemDto, productSkuCategoryModels, attrMs);
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeProductCategoryNotEqual() throws Throwable {
        BeautyDepilatorySkuAttrListOpt opt = new BeautyDepilatorySkuAttrListOpt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Collections.singletonList(new SkuAttrItemDto()));
        skuItemDto.setProductCategory(123L);
        BeautyDepilatorySkuAttrListOpt.Param param = createParam(skuItemDto, productSkuCategoryModels, attrMs);
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeAddIfValidReturnTrue() throws Throwable {
        BeautyDepilatorySkuAttrListOpt opt = new BeautyDepilatorySkuAttrListOpt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Collections.singletonList(new SkuAttrItemDto()));
        skuItemDto.setProductCategory(2104709L);
        BeautyDepilatorySkuAttrListOpt.Param param = createParam(skuItemDto, productSkuCategoryModels, attrMs);
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeAddIfValidReturnFalse() throws Throwable {
        BeautyDepilatorySkuAttrListOpt opt = new BeautyDepilatorySkuAttrListOpt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Collections.singletonList(new SkuAttrItemDto()));
        skuItemDto.setProductCategory(2104709L);
        BeautyDepilatorySkuAttrListOpt.Param param = createParam(skuItemDto, productSkuCategoryModels, attrMs);
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }
}
