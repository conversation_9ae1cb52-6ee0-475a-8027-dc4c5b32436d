package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP.Param;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.BeautyEyelashSkuAttrListOpt.Config;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class BeautyEyelashSkuAttrListOptTest {

    private BeautyEyelashSkuAttrListOpt beautyEyelashSkuAttrListOpt;

    private ActivityCxt context;

    private Param param;

    private Config config;

    // Helper method to create Param instance using reflection
    private Param createParam(SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs) throws Exception {
        Class<?> paramClass = Class.forName("com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP$Param");
        Constructor<?> paramConstructor = paramClass.getDeclaredConstructor(SkuItemDto.class, List.class, List.class);
        paramConstructor.setAccessible(true);
        return (Param) paramConstructor.newInstance(skuItemDto, productCategories, dealAttrs);
    }

    /**
     * Tests the scenario where both name and value are empty, and ignoreEmptyName is false.
     */
    @Test
    public void testBuildDealSkuItemVO_NameAndValueAreEmptyAndIgnoreEmptyNameIsFalse() throws Throwable {
        BeautyEyelashSkuAttrListOpt beautyEyelashSkuAttrListOpt = new BeautyEyelashSkuAttrListOpt();
        DealSkuItemVO result = beautyEyelashSkuAttrListOpt.buildDealSkuItemVO("", "", false);
        assertNull(result);
    }

    /**
     * Tests the scenario where both name and value are empty, and ignoreEmptyName is true.
     */
    @Test
    public void testBuildDealSkuItemVO_NameAndValueAreEmptyAndIgnoreEmptyNameIsTrue() throws Throwable {
        BeautyEyelashSkuAttrListOpt beautyEyelashSkuAttrListOpt = new BeautyEyelashSkuAttrListOpt();
        DealSkuItemVO result = beautyEyelashSkuAttrListOpt.buildDealSkuItemVO("", "", true);
        assertNull(result);
    }

    /**
     * Tests the scenario where name is not REFERENCE_TYPE and value is not empty.
     */
    @Test
    public void testBuildDealSkuItemVO_NameIsNotReferenceTypeAndValueIsNotEmpty() throws Throwable {
        BeautyEyelashSkuAttrListOpt beautyEyelashSkuAttrListOpt = new BeautyEyelashSkuAttrListOpt();
        DealSkuItemVO result = beautyEyelashSkuAttrListOpt.buildDealSkuItemVO("name", "value", true);
        assertNotNull(result);
        assertEquals("name", result.getName());
        assertEquals("value", result.getValue());
    }

    @Test
    public void testComputeWithNullSkuAttrModels() throws Throwable {
        BeautyEyelashSkuAttrListOpt beautyEyelashSkuAttrListOpt = new BeautyEyelashSkuAttrListOpt();
        ActivityCxt context = new ActivityCxt();
        SkuItemDto skuItemDto = new SkuItemDto();
        List<ProductSkuCategoryModel> productCategories = new ArrayList<>();
        List<AttrM> dealAttrs = new ArrayList<>();
        Param param = createParam(skuItemDto, productCategories, dealAttrs);
        Config config = new Config();
        config.setSkuAttrModels(null);
        List<DealSkuItemVO> result = beautyEyelashSkuAttrListOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWithEmptySkuAttrModels() throws Throwable {
        BeautyEyelashSkuAttrListOpt beautyEyelashSkuAttrListOpt = new BeautyEyelashSkuAttrListOpt();
        ActivityCxt context = new ActivityCxt();
        SkuItemDto skuItemDto = new SkuItemDto();
        List<ProductSkuCategoryModel> productCategories = new ArrayList<>();
        List<AttrM> dealAttrs = new ArrayList<>();
        Param param = createParam(skuItemDto, productCategories, dealAttrs);
        Config config = new Config();
        config.setSkuAttrModels(new ArrayList<>());
        List<DealSkuItemVO> result = beautyEyelashSkuAttrListOpt.compute(context, param, config);
        assertNull(result);
    }
}
