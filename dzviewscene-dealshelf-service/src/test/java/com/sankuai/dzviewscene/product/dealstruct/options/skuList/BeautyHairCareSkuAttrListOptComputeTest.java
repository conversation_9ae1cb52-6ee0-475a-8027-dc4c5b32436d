package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BeautyHairCareSkuAttrListOptComputeTest {

    @InjectMocks
    private BeautyHairCareSkuAttrListOpt beautyHairCareSkuAttrListOpt;

    @Mock
    private ActivityCxt context;

    @Mock
    private BeautyHairCareSkuAttrListOpt.Param param;

    @Mock
    private BeautyHairCareSkuAttrListOpt.Config config;

    /**
     * Test when serviceEffect has value for non-other category
     */
    @Test
    public void testComputeWhenServiceEffectHasValue() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        // Set category2 not as "其他"
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        // Set serviceeffect
        SkuAttrItemDto serviceEffectItem = new SkuAttrItemDto();
        serviceEffectItem.setAttrName("serviceeffect");
        serviceEffectItem.setAttrValue("serviceEffectValue");
        skuAttrItems.add(serviceEffectItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("服务作用", result.get(0).getName());
        assertEquals("serviceEffectValue", result.get(0).getValue());
    }

    /**
     * Test when product has value for non-other category
     */
    @Test
    public void testComputeWhenProductHasValue() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto productItem = new SkuAttrItemDto();
        productItem.setAttrName("product");
        productItem.setAttrValue("productValue");
        skuAttrItems.add(productItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("使用产品", result.get(0).getName());
        assertEquals("productValue", result.get(0).getValue());
    }

    /**
     * Test when effectElement has value for non-other category
     */
    @Test
    public void testComputeWhenEffectElementHasValue() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto effectElementItem = new SkuAttrItemDto();
        effectElementItem.setAttrName("effectElement");
        effectElementItem.setAttrValue("effectElementValue");
        skuAttrItems.add(effectElementItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("特色成分", result.get(0).getName());
        assertEquals("effectElementValue", result.get(0).getValue());
    }

    /**
     * Test when suitCrowds has value for non-other category
     */
    @Test
    public void testComputeWhenSuitCrowdsHasValue() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto suitCrowdsItem = new SkuAttrItemDto();
        suitCrowdsItem.setAttrName("suitCrowds");
        suitCrowdsItem.setAttrValue("suitCrowdsValue");
        skuAttrItems.add(suitCrowdsItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("适宜人群", result.get(0).getName());
        assertEquals("suitCrowdsValue", result.get(0).getValue());
    }

    /**
     * Test when equipment has value for non-other category
     */
    @Test
    public void testComputeWhenEquipmentHasValue() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto equipmentItem = new SkuAttrItemDto();
        equipmentItem.setAttrName("equipment");
        equipmentItem.setAttrValue("equipmentValue");
        skuAttrItems.add(equipmentItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("使用仪器", result.get(0).getName());
        assertEquals("equipmentValue", result.get(0).getValue());
    }

    /**
     * Test when grantProduct has value
     */
    @Test
    public void testComputeWhenGrantProductHasValue() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto grantProductItem = new SkuAttrItemDto();
        grantProductItem.setAttrName("grantProduct");
        grantProductItem.setAttrValue("grantProductValue");
        skuAttrItems.add(grantProductItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("项目附赠", result.get(0).getName());
        assertEquals("grantProductValue", result.get(0).getValue());
    }

    /**
     * Test when duration has value and serviceProcessSkuItems is empty
     */
    @Test
    public void testComputeWhenDurationHasValueAndServiceProcessEmpty() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto durationItem = new SkuAttrItemDto();
        durationItem.setAttrName("duration");
        durationItem.setAttrValue("durationValue");
        skuAttrItems.add(durationItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("服务时长", result.get(0).getName());
        assertEquals("durationValue", result.get(0).getValue());
    }

    /**
     * Test when skuItemDto is null
     */
    @Test
    public void testComputeWhenSkuItemDtoIsNull() throws Throwable {
        // arrange
        when(param.getSkuItemDto()).thenReturn(null);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test when skuItemDto.attrItems is empty
     */
    @Test
    public void testComputeWhenAttrItemsIsEmpty() throws Throwable {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(new ArrayList<>());
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test when all attributes have values and not belong to other category
     */
    @Test
    public void testComputeWhenAllAttributesHaveValues() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        // Set category2 not as "其他"
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        // Set serviceeffect
        SkuAttrItemDto serviceEffectItem = new SkuAttrItemDto();
        serviceEffectItem.setAttrName("serviceeffect");
        serviceEffectItem.setAttrValue("serviceEffectValue");
        skuAttrItems.add(serviceEffectItem);
        // Set product
        SkuAttrItemDto productItem = new SkuAttrItemDto();
        productItem.setAttrName("product");
        productItem.setAttrValue("productValue");
        skuAttrItems.add(productItem);
        // Set effectElement
        SkuAttrItemDto effectElementItem = new SkuAttrItemDto();
        effectElementItem.setAttrName("effectElement");
        effectElementItem.setAttrValue("effectElementValue");
        skuAttrItems.add(effectElementItem);
        // Set suitCrowds
        SkuAttrItemDto suitCrowdsItem = new SkuAttrItemDto();
        suitCrowdsItem.setAttrName("suitCrowds");
        suitCrowdsItem.setAttrValue("suitCrowdsValue");
        skuAttrItems.add(suitCrowdsItem);
        // Set equipment
        SkuAttrItemDto equipmentItem = new SkuAttrItemDto();
        equipmentItem.setAttrName("equipment");
        equipmentItem.setAttrValue("equipmentValue");
        skuAttrItems.add(equipmentItem);
        // Set grantProduct
        SkuAttrItemDto grantProductItem = new SkuAttrItemDto();
        grantProductItem.setAttrName("grantProduct");
        grantProductItem.setAttrValue("grantProductValue");
        skuAttrItems.add(grantProductItem);
        // Set duration
        SkuAttrItemDto durationItem = new SkuAttrItemDto();
        durationItem.setAttrName("duration");
        durationItem.setAttrValue("durationValue");
        skuAttrItems.add(durationItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(7, result.size());
        // Verify serviceEffect
        assertTrue(result.stream().anyMatch(item -> "服务作用".equals(item.getName()) && "serviceEffectValue".equals(item.getValue())));
        // Verify product
        assertTrue(result.stream().anyMatch(item -> "使用产品".equals(item.getName()) && "productValue".equals(item.getValue())));
        // Verify effectElement
        assertTrue(result.stream().anyMatch(item -> "特色成分".equals(item.getName()) && "effectElementValue".equals(item.getValue())));
        // Verify suitCrowds
        assertTrue(result.stream().anyMatch(item -> "适宜人群".equals(item.getName()) && "suitCrowdsValue".equals(item.getValue())));
        // Verify equipment
        assertTrue(result.stream().anyMatch(item -> "使用仪器".equals(item.getName()) && "equipmentValue".equals(item.getValue())));
        // Verify grantProduct
        assertTrue(result.stream().anyMatch(item -> "项目附赠".equals(item.getName()) && "grantProductValue".equals(item.getValue())));
        // Verify duration
        assertTrue(result.stream().anyMatch(item -> "服务时长".equals(item.getName()) && "durationValue".equals(item.getValue())));
    }

    /**
     * Test when all attributes are empty
     */
    @Test
    public void testComputeWhenAllAttributesEmpty() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        // Set category2 not as "其他"
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
