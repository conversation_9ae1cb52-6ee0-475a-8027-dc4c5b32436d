package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class BeautyHairCareSkuAttrListOptTest {

    private BeautyHairCareSkuAttrListOpt beautyHairCareSkuAttrListOpt;

    private ActivityCxt context;

    private SkuAttrListVP.Param param;

    private BeautyHairCareSkuAttrListOpt.Config config;

    @Before
    public void setUp() {
        beautyHairCareSkuAttrListOpt = new BeautyHairCareSkuAttrListOpt();
        context = mock(ActivityCxt.class);
        param = mock(SkuAttrListVP.Param.class);
        config = mock(BeautyHairCareSkuAttrListOpt.Config.class);
    }

    @Test
    public void testComputeSkuItemDtoIsNull() throws Throwable {
        when(param.getSkuItemDto()).thenReturn(null);
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeAttrItemsIsEmpty() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeNotBelongToOtherCategoryAndAllAttrHaveValue() throws Throwable {
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("bodyPart");
        attrItem.setAttrValue("attrValue");
        skuAttrItems.add(attrItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("服务部位", result.get(0).getName());
        assertEquals("attrValue", result.get(0).getValue());
    }

    @Test
    public void testComputeBelongToOtherCategoryAndAllAttrHaveValue() throws Throwable {
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("其他");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("bodyPart");
        attrItem.setAttrValue("attrValue");
        skuAttrItems.add(attrItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("服务部位", result.get(0).getName());
        assertEquals("attrValue", result.get(0).getValue());
    }

    @Test
    public void testComputeDealSkuItemsIsNotEmpty() throws Throwable {
        List<SkuAttrItemDto> skuAttrItems = new ArrayList<>();
        SkuAttrItemDto categoryItem = new SkuAttrItemDto();
        categoryItem.setAttrName("category2");
        categoryItem.setAttrValue("notOther");
        skuAttrItems.add(categoryItem);
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("bodyPart");
        attrItem.setAttrValue("attrValue");
        skuAttrItems.add(attrItem);
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(skuAttrItems);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        List<DealSkuItemVO> result = beautyHairCareSkuAttrListOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("服务部位", result.get(0).getName());
        assertEquals("attrValue", result.get(0).getValue());
    }
}
