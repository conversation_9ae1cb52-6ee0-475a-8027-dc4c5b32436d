package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mozilla.javascript.ast.Name;

@RunWith(MockitoJUnitRunner.class)
public class ConfigurableMultiSkuListModuleOptTest {

    private ConfigurableMultiSkuListModuleOpt configurableMultiSkuListModuleOpt = new ConfigurableMultiSkuListModuleOpt();

    private ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private ConfigurableMultiSkuListModuleOpt.Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @Mock
    private DealDetailDtoModel dealDetailDtoModel;

    @Mock
    private DealDetailSkuUniStructuredDto skuUniStructuredDto;

    @Mock
    private MustSkuItemsGroupDto mustSkuItemsGroupDto;

    @Mock
    private SkuItemDto skuItemDto;

    /**
     * 测试getSkuAttrItemValue方法，当attrItems为空时，应返回null
     */
    @Test
    public void testGetSkuAttrItemValueWhenAttrItemsIsNull() throws Throwable {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        List<SkuAttrItemDto> attrItems = null;
        String attrName = "test";
        // act
        List<String> result = opt.getSkuAttrItemValue(skuItemCfg, attrItems, attrName);
        // assert
        assertNull(result);
    }

    /**
     * 测试getSkuAttrItemValue方法，当attrItems不为空，但没有attrName与输入参数相同的SkuAttrItemDto对象时，应返回空列表
     */
    @Test
    public void testGetSkuAttrItemValueWhenNoMatchedAttrName() throws Throwable {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("other");
        attrItem.setAttrValue("value");
        List<SkuAttrItemDto> attrItems = Arrays.asList(attrItem);
        String attrName = "test";
        // act
        List<String> result = opt.getSkuAttrItemValue(skuItemCfg, attrItems, attrName);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试getSkuAttrItemValue方法，当attrItems不为空，有attrName与输入参数相同的SkuAttrItemDto对象，但它们的attrValue属性都为null时，应返回空列表
     */
    @Test
    public void testGetSkuAttrItemValueWhenAttrValueIsNull() throws Throwable {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("test");
        attrItem.setAttrValue(null);
        List<SkuAttrItemDto> attrItems = Arrays.asList(attrItem);
        String attrName = "test";
        // act
        List<String> result = opt.getSkuAttrItemValue(skuItemCfg, attrItems, attrName);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试getSkuAttrItemValue方法，当attrItems不为空，有attrName与输入参数相同的SkuAttrItemDto对象，且它们的attrValue属性不为null时，应返回包含这些attrValue的列表
     */
    @Test
    public void testGetSkuAttrItemValueWhenAttrValueIsNotNull() throws Throwable {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("test");
        attrItem.setAttrValue("value");
        List<SkuAttrItemDto> attrItems = Arrays.asList(attrItem);
        String attrName = "test";
        // act
        List<String> result = opt.getSkuAttrItemValue(skuItemCfg, attrItems, attrName);
        // assert
        assertEquals(1, result.size());
        assertEquals("value", result.get(0));
    }

    /**
     * 测试getAttrValueSingleValue方法，当attrItems为空时
     */
    @Test
    public void testGetAttrValueSingleValueWhenAttrItemsIsNull() {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        String attrName = "testAttrName";
        // act
        String result = configurableMultiSkuListModuleOpt.getAttrValueSingleValue(skuItemCfg, null, attrName);
        // assert
        assertNull(result);
    }

    /**
     * 测试getAttrValueSingleValue方法，当attrItems不为空，但没有attrName与输入参数相同的SkuAttrItemDto对象时
     */
    @Test
    public void testGetAttrValueSingleValueWhenAttrItemsIsNotEmptyButNoMatchedAttrName() {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        String attrName = "testAttrName";
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("otherAttrName");
        skuAttrItemDto.setAttrValue("testAttrValue");
        // act
        String result = configurableMultiSkuListModuleOpt.getAttrValueSingleValue(skuItemCfg, Collections.singletonList(skuAttrItemDto), attrName);
        // assert
        assertNull(result);
    }

    /**
     * 测试getAttrValueSingleValue方法，当attrItems不为空，有attrName与输入参数相同的SkuAttrItemDto对象，但attrValue为null时
     */
    @Test
    public void testGetAttrValueSingleValueWhenAttrItemsIsNotEmptyAndMatchedAttrNameButAttrValueIsNull() {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        String attrName = "testAttrName";
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName(attrName);
        skuAttrItemDto.setAttrValue(null);
        // act
        String result = configurableMultiSkuListModuleOpt.getAttrValueSingleValue(skuItemCfg, Collections.singletonList(skuAttrItemDto), attrName);
        // assert
        assertNull(result);
    }

    /**
     * 测试getAttrValueSingleValue方法，当attrItems不为空，有attrName与输入参数相同的SkuAttrItemDto对象，且attrValue不为null时
     */
    @Test
    public void testGetAttrValueSingleValueWhenAttrItemsIsNotEmptyAndMatchedAttrNameAndAttrValueIsNotNull() {
        // arrange
        ConfigurableMultiSkuListModuleOpt.SkuItemCfg skuItemCfg = new ConfigurableMultiSkuListModuleOpt.SkuItemCfg();
        String attrName = "testAttrName";
        String attrValue = "testAttrValue";
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName(attrName);
        skuAttrItemDto.setAttrValue(attrValue);
        // act
        String result = configurableMultiSkuListModuleOpt.getAttrValueSingleValue(skuItemCfg, Collections.singletonList(skuAttrItemDto), attrName);
        // assert
        assertEquals(attrValue, result);
    }

    @Test(expected = NullPointerException.class)
    public void testComputeParamIsNull() throws Throwable {
        ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();
        opt.compute(activityCxt, null, config);
    }

    @Test
    public void testComputeDealDetailInfoModelIsNull() throws Throwable {
        ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(null);
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeDealDetailDtoModelIsNull() throws Throwable {
        ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(null);
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeSkuUniStructuredDtoIsNull() throws Throwable {
        ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(dealDetailDtoModel.getSkuUniStructuredDto()).thenReturn(null);
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeMustGroupsIsEmpty() throws Throwable {
        ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(dealDetailDtoModel.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(Collections.emptyList());
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeSkuItemsIsEmpty() throws Throwable {
        ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(dealDetailDtoModel.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(Collections.singletonList(mustSkuItemsGroupDto));
        when(mustSkuItemsGroupDto.getSkuItems()).thenReturn(Collections.emptyList());
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeNormal() throws Throwable {
        ConfigurableMultiSkuListModuleOpt opt = new ConfigurableMultiSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(dealDetailDtoModel.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(Collections.singletonList(mustSkuItemsGroupDto));
        when(mustSkuItemsGroupDto.getSkuItems()).thenReturn(Collections.singletonList(skuItemDto));
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
    }
}
