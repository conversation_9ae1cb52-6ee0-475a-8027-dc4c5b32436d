package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuCopiesVP;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSkuCopiesOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuCopiesVP.Param param;

    @Mock
    private DefaultSkuCopiesOpt.Config config;

    @Test
    public void testComputeWithConfigAndParamNotNullAndDisplayCopiesRuleNotNull() throws Throwable {
        // arrange
        DefaultSkuCopiesOpt defaultSkuCopiesOpt = new DefaultSkuCopiesOpt();
        when(param.getCopies()).thenReturn(0);
        // act
        String result = defaultSkuCopiesOpt.compute(context, param, config);
        // assert
        assertEquals("0份", result);
    }

    @Test
    public void testComputeWithConfigNullOrParamNullOrDisplayCopiesRuleNullButFormatNotNull() throws Throwable {
        // arrange
        DefaultSkuCopiesOpt defaultSkuCopiesOpt = new DefaultSkuCopiesOpt();
        when(config.getFormat()).thenReturn("%s份");
        when(param.getCopies()).thenReturn(1);
        // act
        String result = defaultSkuCopiesOpt.compute(context, param, config);
        // assert
        assertEquals("1份", result);
    }

    @Test
    public void testComputeWithConfigNullOrParamNullOrDisplayCopiesRuleNullAndFormatNull() throws Throwable {
        // arrange
        DefaultSkuCopiesOpt defaultSkuCopiesOpt = new DefaultSkuCopiesOpt();
        when(param.getCopies()).thenReturn(1);
        // act
        String result = defaultSkuCopiesOpt.compute(context, param, config);
        // assert
        assertEquals("1份", result);
    }
}
