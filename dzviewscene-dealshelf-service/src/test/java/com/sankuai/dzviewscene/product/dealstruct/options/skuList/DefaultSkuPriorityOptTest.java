package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriorityVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.mockito.InjectMocks;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSkuPriorityOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuPriorityVP.Param param;

    @Mock
    private DefaultSkuPriorityOpt.Config config;

    private DefaultSkuPriorityOpt defaultSkuPriorityOpt;

    @Before
    public void setUp() {
        defaultSkuPriorityOpt = new DefaultSkuPriorityOpt();
    }

    @Test
    public void testComputeWhenConfigMapIsEmpty() {
        when(config.getSkuCategory2PriorityMap()).thenReturn(Collections.emptyMap());
        Integer result = defaultSkuPriorityOpt.compute(context, param, config);
        assertEquals(Integer.MAX_VALUE, result.intValue());
    }

    @Test
    public void testComputeWhenProductCategoriesIsEmpty() {
        Integer result = defaultSkuPriorityOpt.compute(context, param, config);
        assertEquals(Integer.MAX_VALUE, result.intValue());
    }

    @Test
    public void testComputeWhenSkuItemDtoIsNull() {
        Integer result = defaultSkuPriorityOpt.compute(context, param, config);
        assertEquals(Integer.MAX_VALUE, result.intValue());
    }

    @Test
    public void testComputeWhenProductCategoryNotFound() {
        Integer result = defaultSkuPriorityOpt.compute(context, param, config);
        assertEquals(Integer.MAX_VALUE, result.intValue());
    }

    @Test
    public void testComputeWhenPriorityNotFound() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        ProductSkuCategoryModel productSkuCategoryModel = new ProductSkuCategoryModel();
        productSkuCategoryModel.setProductCategoryId(1L);
        when(config.getSkuCategory2PriorityMap()).thenReturn(new HashMap<>());
        Integer result = defaultSkuPriorityOpt.compute(context, param, config);
        assertEquals(Integer.MAX_VALUE, result.intValue());
    }

    @Test
    public void testComputeNormalCase() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        ProductSkuCategoryModel productSkuCategoryModel = new ProductSkuCategoryModel();
        productSkuCategoryModel.setProductCategoryId(1L);
        productSkuCategoryModel.setCnName("test");
        when(param.getProductCategories()).thenReturn(Collections.singletonList(productSkuCategoryModel));
        Map<String, Integer> skuCategory2PriorityMap = new HashMap<>();
        skuCategory2PriorityMap.put("test", 1);
        when(config.getSkuCategory2PriorityMap()).thenReturn(skuCategory2PriorityMap);
        Integer result = defaultSkuPriorityOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(1), result);
    }
}
