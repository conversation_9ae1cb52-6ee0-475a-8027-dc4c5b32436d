package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import java.util.ArrayList;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for DefaultSkuTitleOpt
 * Covers all scenarios of the compute method
 */
public class DefaultSkuTitleOptComputeTest {

    /**
     * Test compute method when noShowTitleFlag is true
     * Should return null regardless of other parameters
     */
    @Test
    public void testCompute_WhenNoShowTitleFlagIsTrue() {
        // arrange
        DefaultSkuTitleOpt defaultSkuTitleOpt = new DefaultSkuTitleOpt();
        ActivityCxt context = new ActivityCxt();
        DefaultSkuTitleOpt.Param param = DefaultSkuTitleOpt.Param.builder().skuTitle("Test SKU Title").build();
        DefaultSkuTitleOpt.Config config = new DefaultSkuTitleOpt.Config();
        config.setNoShowTitleFlag(true);
        // Even with skuAttrKey set, should return null
        config.setSkuAttrKey("someKey");
        // act
        String result = defaultSkuTitleOpt.compute(context, param, config);
        // assert
        assertNull("Result should be null when noShowTitleFlag is true", result);
    }

    /**
     * Test compute method when config is null
     * Should return param's skuTitle
     */
    @Test
    public void testCompute_WhenConfigIsNull() {
        // arrange
        DefaultSkuTitleOpt defaultSkuTitleOpt = new DefaultSkuTitleOpt();
        ActivityCxt context = new ActivityCxt();
        String expectedTitle = "Test SKU Title";
        DefaultSkuTitleOpt.Param param = DefaultSkuTitleOpt.Param.builder().skuTitle(expectedTitle).build();
        // act
        String result = defaultSkuTitleOpt.compute(context, param, null);
        // assert
        assertEquals("Result should be the SKU title when config is null", expectedTitle, result);
    }

    /**
     * Test compute method when skuAttrKey is set but no matching attribute found
     * Should return param's skuTitle
     */
    @Test
    public void testCompute_WhenSkuAttrKeyNotFound() {
        // arrange
        DefaultSkuTitleOpt defaultSkuTitleOpt = new DefaultSkuTitleOpt();
        ActivityCxt context = new ActivityCxt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(new ArrayList<>());
        String expectedTitle = "Test SKU Title";
        DefaultSkuTitleOpt.Param param = DefaultSkuTitleOpt.Param.builder().skuTitle(expectedTitle).skuItemDto(skuItemDto).build();
        DefaultSkuTitleOpt.Config config = new DefaultSkuTitleOpt.Config();
        config.setNoShowTitleFlag(false);
        config.setSkuAttrKey("nonExistentKey");
        // act
        String result = defaultSkuTitleOpt.compute(context, param, config);
        // assert
        assertEquals("Result should be the SKU title when attribute key not found", expectedTitle, result);
    }

    /**
     * Verification test - when noShowTitleFlag is false
     * Should not return null and continue with normal flow
     */
    @Test
    public void testCompute_WhenNoShowTitleFlagIsFalse() {
        // arrange
        DefaultSkuTitleOpt defaultSkuTitleOpt = new DefaultSkuTitleOpt();
        ActivityCxt context = new ActivityCxt();
        String expectedTitle = "Test SKU Title";
        DefaultSkuTitleOpt.Param param = DefaultSkuTitleOpt.Param.builder().skuTitle(expectedTitle).build();
        DefaultSkuTitleOpt.Config config = new DefaultSkuTitleOpt.Config();
        config.setNoShowTitleFlag(false);
        // act
        String result = defaultSkuTitleOpt.compute(context, param, config);
        // assert
        assertEquals("Result should be the SKU title when noShowTitleFlag is false", expectedTitle, result);
    }
}
