package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSkuTitleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuTitleVP.Param param;

    @Mock
    private DefaultSkuTitleOpt.Config config;

    @Ignore
    @Test
    public void testComputeConfigIsNull() {
        // arrange
        DefaultSkuTitleOpt defaultSkuTitleOpt = new DefaultSkuTitleOpt();
        when(param.getSkuTitle()).thenReturn("skuTitle");
        when(config.isNoShowTitleFlag()).thenReturn(false);
        // act
        String result = defaultSkuTitleOpt.compute(context, param, null);
        // assert
        assertEquals("skuTitle", result);
    }

    @Test
    public void testComputeSkuAttrKeyIsEmpty() {
        // arrange
        DefaultSkuTitleOpt defaultSkuTitleOpt = new DefaultSkuTitleOpt();
        when(param.getSkuTitle()).thenReturn("skuTitle");
        when(config.getSkuAttrKey()).thenReturn("");
        when(config.isNoShowTitleFlag()).thenReturn(false);
        // act
        String result = defaultSkuTitleOpt.compute(context, param, config);
        // assert
        assertEquals("skuTitle", result);
    }

    @Test
    public void testComputeSkuAttrKeyIsNotEmpty() {
        // arrange
        DefaultSkuTitleOpt defaultSkuTitleOpt = new DefaultSkuTitleOpt();
        when(param.getSkuTitle()).thenReturn("skuTitle");
        when(config.getSkuAttrKey()).thenReturn("skuAttrKey");
        when(config.isNoShowTitleFlag()).thenReturn(false);
        // act
        String result = defaultSkuTitleOpt.compute(context, param, config);
        // assert
        // 这里的断言需要根据 getSkuTitleByAtrKey 方法的具体实现来写
        assertNotNull(result);
    }
}
