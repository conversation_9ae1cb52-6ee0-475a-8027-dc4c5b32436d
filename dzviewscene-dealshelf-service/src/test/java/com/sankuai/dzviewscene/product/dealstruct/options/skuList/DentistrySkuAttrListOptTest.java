package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceremark.ZeroVaccinePriceRemarkOpt;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.Test;
import org.testng.collections.Maps;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils.getSkuAttrValueBySkuAttrName;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class DentistrySkuAttrListOptTest {
    DentistrySkuAttrListOpt.Config config;

    DentistrySkuAttrListOpt dentistrySkuAttrListOpt;

    @Test
    public void testGetValueFromDealAttr_normal() {
        dentistrySkuAttrListOpt=new DentistrySkuAttrListOpt();
        //arrange
        AttrM attrM = new AttrM();
        attrM.setName("1");
        attrM.setValue("111");
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(attrM);
        String attrKey = "1";
        //act
        String result = ReflectionTestUtils.invokeMethod(dentistrySkuAttrListOpt, "getValueFromDealAttr", dealAttrs, attrKey);

        //assert
        assertEquals("111", result);
    }

    @Test
    public void testGetValueFromDealAttr_attrKey_null() {
        dentistrySkuAttrListOpt=new DentistrySkuAttrListOpt();
        //arrange
        AttrM attrM = new AttrM();
        attrM.setName("1");
        attrM.setValue("111");
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(attrM);
        String attrKey ="";
        //act
        String result = ReflectionTestUtils.invokeMethod(dentistrySkuAttrListOpt, "getValueFromDealAttr", dealAttrs, attrKey);

        //assert
        assertNull(result);
    }

    @Test
    public void testGetValueFromDealAttr_attrM_null() {
        dentistrySkuAttrListOpt=new DentistrySkuAttrListOpt();
        //arrange
        AttrM attrM = new AttrM();
        attrM.setName("2");
        attrM.setValue("111");
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(attrM);
        String attrKey ="1";
        //act
        String result = ReflectionTestUtils.invokeMethod(dentistrySkuAttrListOpt, "getValueFromDealAttr", dealAttrs, attrKey);

        //assert
        assertNull(result);
    }
    @Test
    public void testBuildDealSkuItemsByConfig(){
        config = new DentistrySkuAttrListOpt.Config();
        dentistrySkuAttrListOpt=new DentistrySkuAttrListOpt();
        //arrange
        List<DentistrySkuAttrListOpt.DisplayAttrRuleCfg> displayAttrRules = new ArrayList<>();
        DentistrySkuAttrListOpt.DisplayAttrRuleCfg displayAttrRuleCfg = new DentistrySkuAttrListOpt.DisplayAttrRuleCfg();
        displayAttrRuleCfg.setAttrKey("dealAttrKey");
        displayAttrRuleCfg.setAttrTitle("RuleTitle");
        Map<String, String> attrValueAliasMap = Maps.newHashMap();
        attrValueAliasMap.put("AliasKey", "AliasValue");
        displayAttrRuleCfg.setAttrValueAliasMap(attrValueAliasMap);
        displayAttrRules.add(displayAttrRuleCfg);

        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("skuAttrKey");
        skuAttrItemDto.setAttrValue("skuAttrValue");
        attrItems.add(skuAttrItemDto);

        List<AttrM> dealAttrs =new ArrayList<>();
        AttrM attr = new AttrM();
        attr.setValue("dealAttrValue");
        attr.setName("dealAttrKey");
        dealAttrs.add(attr);

        config.setSkuTitle("111");
        //act
        DealSkuItemVO result = ReflectionTestUtils.invokeMethod(dentistrySkuAttrListOpt, "buildDealSkuItemsByConfig", attrItems, displayAttrRules, dealAttrs, config);

        //assert
        assertEquals("RuleTitle：dealAttrValue", result.getValueAttrs().get(0).getName());

    }

















}
