package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DentistrySkuAttrListOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuAttrListVP.Param param;

    @Mock
    private DentistrySkuAttrListOpt.Config config;

    @Test
    public void testComputeConfigIsNull() {
        DentistrySkuAttrListOpt dentistrySkuAttrListOpt = new DentistrySkuAttrListOpt();
        assertNull(dentistrySkuAttrListOpt.compute(context, param, null));
    }

    @Test
    public void testComputeParamIsNull() {
        DentistrySkuAttrListOpt dentistrySkuAttrListOpt = new DentistrySkuAttrListOpt();
        assertNull(dentistrySkuAttrListOpt.compute(context, null, config));
    }

    @Test
    public void testComputeSkuAttrDisplayRulesIsEmpty() {
        DentistrySkuAttrListOpt dentistrySkuAttrListOpt = new DentistrySkuAttrListOpt();
        assertNull(dentistrySkuAttrListOpt.compute(context, param, config));
    }

    @Test
    public void testComputeSkuItemDtoIsNull() {
        DentistrySkuAttrListOpt dentistrySkuAttrListOpt = new DentistrySkuAttrListOpt();
        assertNull(dentistrySkuAttrListOpt.compute(context, param, config));
    }

    @Test
    public void testComputeAllParamsAreNotNull() {
        DentistrySkuAttrListOpt dentistrySkuAttrListOpt = new DentistrySkuAttrListOpt();
        assertNull(dentistrySkuAttrListOpt.compute(context, param, config));
    }
}
