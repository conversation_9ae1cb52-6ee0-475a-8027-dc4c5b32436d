package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

public class FitnessSkuPriceOptTest {

    /**
     * 健身通团购属性key
     */
    private static final String FITNESS_CROSS_KEY = "dealGroupFitnessPassConfig";

    /**
     * 健身通团购属性value
     */
    private static final String FITNESS_CROSS_VALUE = "fitnessPass";

    @Test
    public void test() {
        ActivityCxt cxt = buildCtx();

        SkuPriceVP.Param param = SkuPriceVP.Param.builder().price(BigDecimal.valueOf(1000L)).build();

        FitnessSkuPriceOpt opt = new FitnessSkuPriceOpt();

        Assert.assertNull(opt.compute(cxt, param, new FitnessSkuPriceOpt.Config()));

        Assert.assertNotNull(opt.compute(new ActivityCxt(), param, new FitnessSkuPriceOpt.Config()));
    }

    private ActivityCxt buildCtx() {
        AttrM attrM = new AttrM();
        attrM.setName(FITNESS_CROSS_KEY);
        attrM.setValue(FITNESS_CROSS_VALUE);

        DealDetailInfoModel model = new DealDetailInfoModel();
        model.setDealAttrs(Lists.newArrayList(attrM));

        ActivityCxt cxt = new ActivityCxt();
        cxt.attachSource(DealDetailFetcher.CODE, Lists.newArrayList(model));

        return cxt;
    }

}