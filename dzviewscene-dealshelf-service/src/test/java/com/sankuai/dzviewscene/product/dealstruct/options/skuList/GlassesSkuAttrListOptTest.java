package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GlassesSkuAttrListOptTest {

    @Test
    public void testComputeParamIsNull() throws Throwable {
        GlassesSkuAttrListOpt glassesSkuAttrListOpt = new GlassesSkuAttrListOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        GlassesSkuAttrListOpt.Config config = mock(GlassesSkuAttrListOpt.Config.class);
        List<DealSkuItemVO> result = glassesSkuAttrListOpt.compute(context, null, config);
        assertNull(result);
    }

    @Test
    public void testComputeParamIsNotNull() throws Throwable {
        GlassesSkuAttrListOpt glassesSkuAttrListOpt = new GlassesSkuAttrListOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        GlassesSkuAttrListOpt.Config config = mock(GlassesSkuAttrListOpt.Config.class);
        SkuAttrListVP.Param param = mock(SkuAttrListVP.Param.class);
        // Mock necessary methods and objects here based on actual requirements
        SkuItemDto skuItemDto = new SkuItemDto();
        // Example category ID
        skuItemDto.setProductCategory(2104918L);
        // Example market price
        skuItemDto.setMarketPrice(new BigDecimal("100"));
        // Example copies
        skuItemDto.setCopies(1);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("key", "value")));
        List<DealSkuItemVO> result = glassesSkuAttrListOpt.compute(context, param, config);
        assertNotNull(result);
    }
}
