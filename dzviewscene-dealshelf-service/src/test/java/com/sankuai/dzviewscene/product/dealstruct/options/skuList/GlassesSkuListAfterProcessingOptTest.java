package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListAfterProcessingVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class GlassesSkuListAfterProcessingOptTest {

    @Mock
    private ActivityCxt context;

    private GlassesSkuListAfterProcessingOpt glassesSkuListAfterProcessingOpt;

    @Before
    public void setUp() {
        glassesSkuListAfterProcessingOpt = new GlassesSkuListAfterProcessingOpt();
    }

    @Test
    public void testComputeDealSkuVOSIsNull() {
        SkuListAfterProcessingVP.Param param = mock(SkuListAfterProcessingVP.Param.class);
        when(param.getDealSkuVOS()).thenReturn(null);
        List<DealSkuVO> result = glassesSkuListAfterProcessingOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeDealSkuVOItemsIsNull() {
        SkuListAfterProcessingVP.Param param = mock(SkuListAfterProcessingVP.Param.class);
        List<DealSkuVO> dealSkuVOS = new ArrayList<>();
        DealSkuVO dealSkuVO = mock(DealSkuVO.class);
        dealSkuVOS.add(dealSkuVO);
        when(param.getDealSkuVOS()).thenReturn(dealSkuVOS);
        when(dealSkuVO.getItems()).thenReturn(null);
        List<DealSkuVO> result = glassesSkuListAfterProcessingOpt.compute(context, param, null);
        assertEquals(dealSkuVOS, result);
    }

    @Test
    public void testComputeDealSkuVOItemsIsNotEmpty() {
        SkuListAfterProcessingVP.Param param = mock(SkuListAfterProcessingVP.Param.class);
        List<DealSkuVO> dealSkuVOS = new ArrayList<>();
        DealSkuVO dealSkuVO = mock(DealSkuVO.class);
        dealSkuVOS.add(dealSkuVO);
        when(param.getDealSkuVOS()).thenReturn(dealSkuVOS);
        List<DealSkuVO> result = glassesSkuListAfterProcessingOpt.compute(context, param, null);
        assertEquals(dealSkuVOS, result);
    }
}
