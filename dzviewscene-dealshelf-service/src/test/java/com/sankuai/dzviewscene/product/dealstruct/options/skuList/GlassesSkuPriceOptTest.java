package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import org.junit.runner.RunWith;
import java.math.BigDecimal;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GlassesSkuPriceOptTest {

    /**
     * Tests whether the compute method always returns null.
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // Arrange
        GlassesSkuPriceOpt glassesSkuPriceOpt = new GlassesSkuPriceOpt();
        ActivityCxt context = new ActivityCxt();
        // Use the builder pattern provided by Lombok to instantiate SkuPriceVP.Param
        SkuPriceVP.Param param = SkuPriceVP.Param.builder().price(new BigDecimal("0")).build();
        GlassesSkuPriceOpt.Config config = new GlassesSkuPriceOpt.Config();
        // Act
        String result = glassesSkuPriceOpt.compute(context, param, config);
        // Assert
        assertNull(result);
    }
}
