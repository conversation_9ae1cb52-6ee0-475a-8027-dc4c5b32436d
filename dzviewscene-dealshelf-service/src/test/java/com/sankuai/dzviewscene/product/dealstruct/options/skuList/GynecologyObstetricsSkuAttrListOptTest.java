package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 测试GynecologyObstetricsSkuAttrListOpt.compute方法
 */
public class GynecologyObstetricsSkuAttrListOptTest {

    private GynecologyObstetricsSkuAttrListOpt opt;
    private ActivityCxt context;
    private GynecologyObstetricsSkuAttrListOpt.Param param;
    private GynecologyObstetricsSkuAttrListOpt.Config config;

    @Before
    public void setUp() {
        opt = new GynecologyObstetricsSkuAttrListOpt();
        context = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(GynecologyObstetricsSkuAttrListOpt.Param.class);
        config = new GynecologyObstetricsSkuAttrListOpt.Config();
    }

    /**
     * 测试config或param为null时
     */
    @Test
    public void testComputeConfigOrParamIsNull() {
        assertNull("当config为null时应返回null", opt.compute(context, param, null));
        assertNull("当param为null时应返回null", opt.compute(context, null, config));
    }

    /**
     * 测试config.configs为null或空列表时
     */
    @Test
    public void testComputeConfigConfigsIsEmpty() {
        assertNull("当config.configs为null时应返回null", opt.compute(context, param, config));
        config.setConfigs(new ArrayList<>());
        assertTrue("当config.configs为空列表时应返回空列表", opt.compute(context, param, config).isEmpty());
    }

    /**
     * 测试attrs为null时
     */
    @Test
    public void testComputeAttrsIsNull() {
        List<GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg> configs = new ArrayList<>();
        configs.add(new GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg());
        config.setConfigs(configs);
        Mockito.when(param.getDealAttrs()).thenReturn(null);

        assertNull("当attrs为null时应返回null", opt.compute(context, param, config));
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testComputeNormalScenario() {
        List<GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg> configs = new ArrayList<>();
        GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg cfg = new GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg();
        cfg.setKey("testKey");
        cfg.setDisplayName("测试属性");
        configs.add(cfg);
        config.setConfigs(configs);

        List<AttrM> attrs = new ArrayList<>();
        AttrM attr = new AttrM();
        attr.setName("testKey");
        attr.setValue("测试值");
        attrs.add(attr);
        Mockito.when(param.getDealAttrs()).thenReturn(attrs);

        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNotNull("在正常场景下应返回非null结果", result);
        assertFalse("在正常场景下应返回非空列表", result.isEmpty());
        assertEquals("测试属性", result.get(0).getName());
        assertEquals("测试值", result.get(0).getValue());
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testComputeNormalScenarioConfig_inspection_item() {
        List<GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg> configs = new ArrayList<>();
        GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg cfg = new GynecologyObstetricsSkuAttrListOpt.DisplayRuleCfg();
        cfg.setKey("inspection_item");
        cfg.setDisplayName("测试检查项目属性");
        GynecologyObstetricsSkuAttrListOpt.InspectItemCfg inspectItemCfg = new GynecologyObstetricsSkuAttrListOpt.InspectItemCfg();
        inspectItemCfg.setIntroKey("productIntroText");
        inspectItemCfg.setPriceKey("zhongyiyangsheng_price");
        inspectItemCfg.setNameKey("subjectName");
        cfg.setConfig(inspectItemCfg);
        configs.add(cfg);
        config.setConfigs(configs);

        List<AttrM> attrs = new ArrayList<>();
        AttrM attr = new AttrM();
        attr.setName("inspection_item");
        attr.setValue("[{\"subjectName\":\"测试检查项目的值\"," +
                       "\"productIntroText\":\"测试的检查项目介绍\"," +
                       "\"zhongyiyangsheng_price\":\"100\"}]");
        attrs.add(attr);

        Mockito.when(param.getDealAttrs()).thenReturn(attrs);
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNotNull("在正常场景下应返回非null结果", result);
        assertEquals("测试检查项目的值", result.get(0).getValueAttrs().get(0).getName());
        assertEquals("共1项", result.get(0).getValue());
    }
}
