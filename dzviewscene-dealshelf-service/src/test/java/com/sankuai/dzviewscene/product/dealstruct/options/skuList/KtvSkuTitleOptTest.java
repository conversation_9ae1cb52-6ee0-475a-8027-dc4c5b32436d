package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class KtvSkuTitleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuTitleVP.Param param;

    @Mock
    private KtvSkuTitleOpt.Config config;

    private KtvSkuTitleOpt ktvSkuTitleOpt;

    @Before
    public void setUp() {
        ktvSkuTitleOpt = new KtvSkuTitleOpt();
    }

    @Test
    public void testComputeParamAndConfigValid() {
        when(param.getSkuTitle()).thenReturn("valid title");
        String result = ktvSkuTitleOpt.compute(context, param, config);
        assertEquals("valid title", result);
    }

    @Test
    public void testComputeStrategyNull() {
        when(param.getSkuTitle()).thenReturn("default title");
        String result = ktvSkuTitleOpt.compute(context, param, config);
        assertEquals("default title", result);
    }

    @Test
    public void testComputeTitleNotEmpty() {
        when(param.getSkuTitle()).thenReturn("default title");
        when(config.getTitleStrategys()).thenReturn(null);
        String result = ktvSkuTitleOpt.compute(context, param, config);
        assertEquals("default title", result);
    }

    @Test
    public void testComputeTitleEmpty() {
        when(param.getSkuTitle()).thenReturn("default title");
        when(config.getTitleStrategys()).thenReturn(null);
        String result = ktvSkuTitleOpt.compute(context, param, config);
        assertEquals("default title", result);
    }
}
