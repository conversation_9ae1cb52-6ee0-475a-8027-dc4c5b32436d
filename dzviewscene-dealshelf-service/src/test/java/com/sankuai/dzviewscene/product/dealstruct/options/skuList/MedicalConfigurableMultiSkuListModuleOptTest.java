package com.sankuai.dzviewscene.product.dealstruct.options.skuList;


import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP.Param;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.MedicalConfigurableMultiSkuListModuleOpt.Config;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.junit.*;

/**
 * Tests for {@link MedicalConfigurableMultiSkuListModuleOpt#compute(ActivityCxt, Param, Config)}.
 */
public class MedicalConfigurableMultiSkuListModuleOptTest {

    private ActivityCxt context;

    private Param param;

    private Config config;

    private DealDetailInfoModel dealDetailInfoModel;

    @Before
    public void setUp() {
        // Initialize mocks before each test case
        context = mock(ActivityCxt.class);
        param = mock(Param.class);
        config = mock(Config.class);
        dealDetailInfoModel = mock(DealDetailInfoModel.class);
    }

    @Test
    public void testCompute_NullName2ValueMap() throws Throwable {
        MedicalConfigurableMultiSkuListModuleOpt opt = new MedicalConfigurableMultiSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testChildrenDentalFilling() {
        MedicalConfigurableMultiSkuListModuleOpt opt = new MedicalConfigurableMultiSkuListModuleOpt();
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(null, getChildrenDentalFillingParam(), getChildrenDentalFillingConfig());
        assertNotNull(result);
    }

    private Param getChildrenDentalFillingParam() {
        String json = "{\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":**********,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":**********,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"33.00\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"2575.00\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":4059,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u513f\\\\\\\\u7ae5\\\\\\\\u8865\\\\\\\\u7259\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":2222.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":2357,\\\\\\\"attrName\\\\\\\":\\\\\\\"teethNumber\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9897\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"3\\\\\\\\u9897\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"unit\\\\\\\":\\\\\\\"\\\\\\\\u9897\\\\\\\",\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":678,\\\\\\\"attrName\\\\\\\":\\\\\\\"brandName\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u586b\\\\\\\\u5145\\\\\\\\u6750\\\\\\\\u6599\\\\\\\\u54c1\\\\\\\\u724c\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5fb7\\\\\\\\u56fd\\\\\\\\u897f\\\\\\\\u8bfa\\\\\\\\u5fb7\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5fb7\\\\\\\\u56fd\\\\\\\\u897f\\\\\\\\u8bfa\\\\\\\\u5fb7\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1037,\\\\\\\"attrName\\\\\\\":\\\\\\\"category1\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u4e00\\\\\\\\u7ea7\\\\\\\\u6cbb\\\\\\\\u7597\\\\\\\\u65b9\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u6811\\\\\\\\u8102\\\\\\\\u8865\\\\\\\\u7259\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u6811\\\\\\\\u8102\\\\\\\\u8865\\\\\\\\u7259\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":750,\\\\\\\"attrName\\\\\\\":\\\\\\\"material\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u586b\\\\\\\\u5145\\\\\\\\u6750\\\\\\\\u6599\\\\\\\\u7cfb\\\\\\\\u5217\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u590d\\\\\\\\u5408\\\\\\\\u6811\\\\\\\\u8102\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u590d\\\\\\\\u5408\\\\\\\\u6811\\\\\\\\u8102\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":354359,\\\\\\\"attrName\\\\\\\":\\\\\\\"ToothFillingPosition\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8865\\\\\\\\u7259\\\\\\\\u4f4d\\\\\\\\u7f6e\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u540e\\\\\\\\u7259\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u540e\\\\\\\\u7259\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":354360,\\\\\\\"attrName\\\\\\\":\\\\\\\"WarrantyPeriod\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8d28\\\\\\\\u4fdd\\\\\\\\u671f\\\\\\\\u9650\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"3\\\\\\\\u4e2a\\\\\\\\u6708\\\\\\\\u5185\\\\\\\\u6750\\\\\\\\u6599\\\\\\\\u8131\\\\\\\\u843d\\\\\\\\u514d\\\\\\\\u8d39\\\\\\\\u8865\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"3\\\\\\\\u4e2a\\\\\\\\u6708\\\\\\\\u5185\\\\\\\\u6750\\\\\\\\u6599\\\\\\\\u8131\\\\\\\\u843d\\\\\\\\u514d\\\\\\\\u8d39\\\\\\\\u8865\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":589,\\\\\\\"attrName\\\\\\\":\\\\\\\"homePkgWorkerQualification\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u64cd\\\\\\\\u4f5c\\\\\\\\u4eba\\\\\\\\u5458\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u6267\\\\\\\\u4e1a\\\\\\\\u533b\\\\\\\\u5e08\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u6267\\\\\\\\u4e1a\\\\\\\\u533b\\\\\\\\u5e08\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":70055,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u6302\\\\\\\\u53f7\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":20.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":70090,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u81ea\\\\\\\\u5b9a\\\\\\\\u4e49\\\\\\\\u5440\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":100.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":1386,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u68c0\\\\\\\\u67e5\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":110.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":1453,\\\\\\\"attrName\\\\\\\":\\\\\\\"usage\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u68c0\\\\\\\\u67e5\\\\\\\\u65b9\\\\\\\\u5f0f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u53e3\\\\\\\\u8154\\\\\\\\u5185\\\\\\\\u7aa5\\\\\\\\u955c\\\\\\\\u68c0\\\\\\\\u67e5\\\\\\\\u3001\\\\\\\\u53e3\\\\\\\\u8154\\\\\\\\u5168\\\\\\\\u666f\\\\\\\\u7247\\\\\\\\u3001\\\\\\\\u53e3\\\\\\\\u8154CT\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u53e3\\\\\\\\u8154\\\\\\\\u5185\\\\\\\\u7aa5\\\\\\\\u955c\\\\\\\\u68c0\\\\\\\\u67e5\\\\\\\\u3001\\\\\\\\u53e3\\\\\\\\u8154\\\\\\\\u5168\\\\\\\\u666f\\\\\\\\u7247\\\\\\\\u3001\\\\\\\\u53e3\\\\\\\\u8154CT\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":1390,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u9644\\\\\\\\u8d60\\\\\\\\u9879\\\\\\\\u76ee\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":123.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":2781,\\\\\\\"attrName\\\\\\\":\\\\\\\"bonusItem\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9644\\\\\\\\u8d60\\\\\\\\u540d\\\\\\\\u76ee\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u53d6\\\\\\\\u6a21\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u53d6\\\\\\\\u6a21\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2780,\\\\\\\"attrName\\\\\\\":\\\\\\\"bonusType\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u9644\\\\\\\\u8d60\\\\\\\\u7c7b\\\\\\\\u578b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u670d\\\\\\\\u52a1\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":**********,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"33.00\\\",\\\"marketPrice\\\":\\\"2575.00\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":4059,\\\"name\\\":\\\"\\\\u513f\\\\u7ae5\\\\u8865\\\\u7259\\\",\\\"copies\\\":1,\\\"marketPrice\\\":2222.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":2357,\\\"attrName\\\":\\\"teethNumber\\\",\\\"chnName\\\":\\\"\\\\u9897\\\\u6570\\\",\\\"attrValue\\\":\\\"3\\\\u9897\\\",\\\"rawAttrValue\\\":\\\"3\\\",\\\"unit\\\":\\\"\\\\u9897\\\",\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":678,\\\"attrName\\\":\\\"brandName\\\",\\\"chnName\\\":\\\"\\\\u586b\\\\u5145\\\\u6750\\\\u6599\\\\u54c1\\\\u724c\\\",\\\"attrValue\\\":\\\"\\\\u5fb7\\\\u56fd\\\\u897f\\\\u8bfa\\\\u5fb7\\\",\\\"rawAttrValue\\\":\\\"\\\\u5fb7\\\\u56fd\\\\u897f\\\\u8bfa\\\\u5fb7\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1037,\\\"attrName\\\":\\\"category1\\\",\\\"chnName\\\":\\\"\\\\u4e00\\\\u7ea7\\\\u6cbb\\\\u7597\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u6811\\\\u8102\\\\u8865\\\\u7259\\\",\\\"rawAttrValue\\\":\\\"\\\\u6811\\\\u8102\\\\u8865\\\\u7259\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":750,\\\"attrName\\\":\\\"material\\\",\\\"chnName\\\":\\\"\\\\u586b\\\\u5145\\\\u6750\\\\u6599\\\\u7cfb\\\\u5217\\\",\\\"attrValue\\\":\\\"\\\\u590d\\\\u5408\\\\u6811\\\\u8102\\\",\\\"rawAttrValue\\\":\\\"\\\\u590d\\\\u5408\\\\u6811\\\\u8102\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":354359,\\\"attrName\\\":\\\"ToothFillingPosition\\\",\\\"chnName\\\":\\\"\\\\u8865\\\\u7259\\\\u4f4d\\\\u7f6e\\\",\\\"attrValue\\\":\\\"\\\\u540e\\\\u7259\\\",\\\"rawAttrValue\\\":\\\"\\\\u540e\\\\u7259\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":354360,\\\"attrName\\\":\\\"WarrantyPeriod\\\",\\\"chnName\\\":\\\"\\\\u8d28\\\\u4fdd\\\\u671f\\\\u9650\\\",\\\"attrValue\\\":\\\"3\\\\u4e2a\\\\u6708\\\\u5185\\\\u6750\\\\u6599\\\\u8131\\\\u843d\\\\u514d\\\\u8d39\\\\u8865\\\",\\\"rawAttrValue\\\":\\\"3\\\\u4e2a\\\\u6708\\\\u5185\\\\u6750\\\\u6599\\\\u8131\\\\u843d\\\\u514d\\\\u8d39\\\\u8865\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":589,\\\"attrName\\\":\\\"homePkgWorkerQualification\\\",\\\"chnName\\\":\\\"\\\\u64cd\\\\u4f5c\\\\u4eba\\\\u5458\\\",\\\"attrValue\\\":\\\"\\\\u6267\\\\u4e1a\\\\u533b\\\\u5e08\\\",\\\"rawAttrValue\\\":\\\"\\\\u6267\\\\u4e1a\\\\u533b\\\\u5e08\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]},{\\\"skuId\\\":0,\\\"productCategory\\\":70055,\\\"name\\\":\\\"\\\\u6302\\\\u53f7\\\",\\\"copies\\\":1,\\\"marketPrice\\\":20.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[]},{\\\"skuId\\\":0,\\\"productCategory\\\":70090,\\\"name\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5440\\\",\\\"copies\\\":1,\\\"marketPrice\\\":100.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[]},{\\\"skuId\\\":0,\\\"productCategory\\\":1386,\\\"name\\\":\\\"\\\\u68c0\\\\u67e5\\\",\\\"copies\\\":1,\\\"marketPrice\\\":110.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":1453,\\\"attrName\\\":\\\"usage\\\",\\\"chnName\\\":\\\"\\\\u68c0\\\\u67e5\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u53e3\\\\u8154\\\\u5185\\\\u7aa5\\\\u955c\\\\u68c0\\\\u67e5\\\\u3001\\\\u53e3\\\\u8154\\\\u5168\\\\u666f\\\\u7247\\\\u3001\\\\u53e3\\\\u8154CT\\\",\\\"rawAttrValue\\\":\\\"\\\\u53e3\\\\u8154\\\\u5185\\\\u7aa5\\\\u955c\\\\u68c0\\\\u67e5\\\\u3001\\\\u53e3\\\\u8154\\\\u5168\\\\u666f\\\\u7247\\\\u3001\\\\u53e3\\\\u8154CT\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]},{\\\"skuId\\\":0,\\\"productCategory\\\":1390,\\\"name\\\":\\\"\\\\u9644\\\\u8d60\\\\u9879\\\\u76ee\\\",\\\"copies\\\":1,\\\"marketPrice\\\":123.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":2781,\\\"attrName\\\":\\\"bonusItem\\\",\\\"chnName\\\":\\\"\\\\u9644\\\\u8d60\\\\u540d\\\\u76ee\\\",\\\"attrValue\\\":\\\"\\\\u53d6\\\\u6a21\\\",\\\"rawAttrValue\\\":\\\"\\\\u53d6\\\\u6a21\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2780,\\\"attrName\\\":\\\"bonusType\\\",\\\"chnName\\\":\\\"\\\\u9644\\\\u8d60\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u670d\\\\u52a1\\\",\\\"rawAttrValue\\\":\\\"\\\\u670d\\\\u52a1\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":4059,\\\"cnName\\\":\\\"\\\\u513f\\\\u7ae5\\\\u8865\\\\u7259\\\"},{\\\"productCategoryId\\\":70055,\\\"cnName\\\":\\\"\\\\u6302\\\\u53f7\\\"},{\\\"productCategoryId\\\":70090,\\\"cnName\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\"},{\\\"productCategoryId\\\":1386,\\\"cnName\\\":\\\"\\\\u68c0\\\\u67e5\\\"},{\\\"productCategoryId\\\":1390,\\\"cnName\\\":\\\"\\\\u5176\\\\u5b83\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":**********,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"33.00\\\",\\\"marketPrice\\\":\\\"2575.00\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":4059,\\\"name\\\":\\\"\\\\u513f\\\\u7ae5\\\\u8865\\\\u7259\\\",\\\"copies\\\":1,\\\"marketPrice\\\":2222.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":2357,\\\"attrName\\\":\\\"teethNumber\\\",\\\"chnName\\\":\\\"\\\\u9897\\\\u6570\\\",\\\"attrValue\\\":\\\"3\\\\u9897\\\"},{\\\"metaAttrId\\\":678,\\\"attrName\\\":\\\"brandName\\\",\\\"chnName\\\":\\\"\\\\u586b\\\\u5145\\\\u6750\\\\u6599\\\\u54c1\\\\u724c\\\",\\\"attrValue\\\":\\\"\\\\u5fb7\\\\u56fd\\\\u897f\\\\u8bfa\\\\u5fb7\\\"},{\\\"metaAttrId\\\":1037,\\\"attrName\\\":\\\"category1\\\",\\\"chnName\\\":\\\"\\\\u4e00\\\\u7ea7\\\\u6cbb\\\\u7597\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u6811\\\\u8102\\\\u8865\\\\u7259\\\"},{\\\"metaAttrId\\\":750,\\\"attrName\\\":\\\"material\\\",\\\"chnName\\\":\\\"\\\\u586b\\\\u5145\\\\u6750\\\\u6599\\\\u7cfb\\\\u5217\\\",\\\"attrValue\\\":\\\"\\\\u590d\\\\u5408\\\\u6811\\\\u8102\\\"},{\\\"metaAttrId\\\":354359,\\\"attrName\\\":\\\"ToothFillingPosition\\\",\\\"chnName\\\":\\\"\\\\u8865\\\\u7259\\\\u4f4d\\\\u7f6e\\\",\\\"attrValue\\\":\\\"\\\\u540e\\\\u7259\\\"},{\\\"metaAttrId\\\":354360,\\\"attrName\\\":\\\"WarrantyPeriod\\\",\\\"chnName\\\":\\\"\\\\u8d28\\\\u4fdd\\\\u671f\\\\u9650\\\",\\\"attrValue\\\":\\\"3\\\\u4e2a\\\\u6708\\\\u5185\\\\u6750\\\\u6599\\\\u8131\\\\u843d\\\\u514d\\\\u8d39\\\\u8865\\\"},{\\\"metaAttrId\\\":589,\\\"attrName\\\":\\\"homePkgWorkerQualification\\\",\\\"chnName\\\":\\\"\\\\u64cd\\\\u4f5c\\\\u4eba\\\\u5458\\\",\\\"attrValue\\\":\\\"\\\\u6267\\\\u4e1a\\\\u533b\\\\u5e08\\\"}]},{\\\"skuId\\\":0,\\\"productCategory\\\":70055,\\\"name\\\":\\\"\\\\u6302\\\\u53f7\\\",\\\"copies\\\":1,\\\"marketPrice\\\":20.0,\\\"skuAttrs\\\":null},{\\\"skuId\\\":0,\\\"productCategory\\\":70090,\\\"name\\\":\\\"\\\\u81ea\\\\u5b9a\\\\u4e49\\\\u5440\\\",\\\"copies\\\":1,\\\"marketPrice\\\":100.0,\\\"skuAttrs\\\":null},{\\\"skuId\\\":0,\\\"productCategory\\\":1386,\\\"name\\\":\\\"\\\\u68c0\\\\u67e5\\\",\\\"copies\\\":1,\\\"marketPrice\\\":110.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":1453,\\\"attrName\\\":\\\"usage\\\",\\\"chnName\\\":\\\"\\\\u68c0\\\\u67e5\\\\u65b9\\\\u5f0f\\\",\\\"attrValue\\\":\\\"\\\\u53e3\\\\u8154\\\\u5185\\\\u7aa5\\\\u955c\\\\u68c0\\\\u67e5\\\\u3001\\\\u53e3\\\\u8154\\\\u5168\\\\u666f\\\\u7247\\\\u3001\\\\u53e3\\\\u8154CT\\\"}]},{\\\"skuId\\\":0,\\\"productCategory\\\":1390,\\\"name\\\":\\\"\\\\u9644\\\\u8d60\\\\u9879\\\\u76ee\\\",\\\"copies\\\":1,\\\"marketPrice\\\":123.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":2781,\\\"attrName\\\":\\\"bonusItem\\\",\\\"chnName\\\":\\\"\\\\u9644\\\\u8d60\\\\u540d\\\\u76ee\\\",\\\"attrValue\\\":\\\"\\\\u53d6\\\\u6a21\\\"},{\\\"metaAttrId\\\":2780,\\\"attrName\\\":\\\"bonusType\\\",\\\"chnName\\\":\\\"\\\\u9644\\\\u8d60\\\\u7c7b\\\\u578b\\\",\\\"attrValue\\\":\\\"\\\\u670d\\\\u52a1\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"toothwash_availableTime\",\"value\":\"周末节假日通用\"},{\"name\":\"dealExtraCharge\",\"value\":\"[{\\\"title\\\":\\\"验血呀\\\",\\\"price\\\":\\\"100.0元\\\"},{\\\"title\\\":\\\"麻醉呀\\\",\\\"price\\\":\\\"100.0-200.0元\\\"},{\\\"title\\\":\\\"其他呀\\\",\\\"price\\\":\\\"根据牙齿情况定价\\\"}]\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":{\\\"totalPrice\\\":2575.00,\\\"salePrice\\\":33.00,\\\"groups\\\":[{\\\"units\\\":[{\\\"skuCateId\\\":4059,\\\"amount\\\":1,\\\"price\\\":2222.0,\\\"attrValues\\\":{\\\"homePkgWorkerQualification\\\":\\\"执业医师\\\",\\\"brandName\\\":\\\"德国西诺德\\\",\\\"material\\\":\\\"复合树脂\\\",\\\"category1\\\":\\\"树脂补牙\\\",\\\"ToothFillingPosition\\\":\\\"后牙\\\",\\\"teethNumber\\\":\\\"3\\\",\\\"WarrantyPeriod\\\":\\\"3个月内材料脱落免费补\\\"},\\\"projectName\\\":\\\"儿童补牙\\\",\\\"properties\\\":\\\"儿童补牙；德国西诺德；复合树脂；树脂补牙；3颗；后牙；3个月...\\\",\\\"skuId\\\":0},{\\\"skuCateId\\\":70055,\\\"amount\\\":1,\\\"price\\\":20.0,\\\"attrValues\\\":{},\\\"projectName\\\":\\\"挂号\\\",\\\"properties\\\":\\\"\\\",\\\"skuId\\\":0},{\\\"skuCateId\\\":70090,\\\"amount\\\":1,\\\"price\\\":100.0,\\\"attrValues\\\":{},\\\"projectName\\\":\\\"自定义呀\\\",\\\"properties\\\":\\\"\\\",\\\"skuId\\\":0},{\\\"skuCateId\\\":1386,\\\"amount\\\":1,\\\"price\\\":110.0,\\\"attrValues\\\":{\\\"usage\\\":\\\"口腔内窥镜检查、口腔全景片、口腔CT\\\"},\\\"projectName\\\":\\\"检查\\\",\\\"properties\\\":\\\"检查；口腔内窥镜检查、口腔全景片、口腔CT\\\",\\\"skuId\\\":0},{\\\"skuCateId\\\":1390,\\\"amount\\\":1,\\\"price\\\":123.0,\\\"attrValues\\\":{\\\"bonusType\\\":\\\"服务\\\",\\\"bonusItem\\\":\\\"取模\\\"},\\\"projectName\\\":\\\"附赠项目\\\",\\\"properties\\\":\\\"其它；取模；服务\\\",\\\"skuId\\\":0}],\\\"optionalCount\\\":0}]},\\\"type\\\":\\\"uniform-structure-table\\\"},{\\\"data\\\":\\\"<p>补充呀</p>\\\",\\\"type\\\":\\\"richtext\\\"}],\\\"headline\\\":\\\"团购详情\\\",\\\"version\\\":1}\"},{\"name\":\"service_type\",\"value\":\"儿童补牙\"}],\"desc\":\"<p>补充呀</p>\"}";
        DealDetailInfoModel dealDetailInfoModel1 = JSON.parseObject(json, DealDetailInfoModel.class);
        return Param.builder().dealDetailInfoModel(dealDetailInfoModel1).build();
    }
    private Config getChildrenDentalFillingConfig() {
        String json = "{\"groupConfigsMap\":{\"儿童补牙\":[{\"groupName\":\"客观信息\",\"skuGroupModuleConfigs\":[{\"skuConfigList\":[{\"skuItemConfigList\":[{\"explainConfig\":{\"icon\":\"https://p0.meituan.net/ingee/884e025b5c7277df3bade91f9eea1ad51160.png\",\"popup\":{\"infos\":[{\"name\":\"牙位介绍\",\"pic\":\"https://p0.meituan.net/travelcube/59181a653b186c0b0925974722aa616049956.png\",\"value\":\"selected\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/4f9cbd0c7d57406109cd824472ef52f0128341.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"树脂补牙\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/e9b13e019b5c0133ea6e683f6951271a128656.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"嵌体补牙\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/4bfc12bf8b2d02a48ffe7ad74c1d29fa128349.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"银汞合金补牙\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/8178fe25ed721a187830e93baca0e560128470.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"玻璃离子补牙\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/c2c8a002dcbe991557b24dbca7a56e6452162.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"Z250/z250\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/2227a0e34ac8c2376fc70142bfc8b85e52495.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"Z350/z350\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/fa8e4f38bf5e8fa8afd1e17de026ad3551899.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"P60/p60\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/f23a4960738f59039aa3e08c1ff31ede52278.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"P90/p90\"},{\"addCommonAttrsToLast\":true,\"commonAttrs\":[{\"name\":\"tips\",\"values\":[\"以上描述仅供参考，选择补牙材料应综合考虑牙齿位置、损伤程度、美观要求、咀嚼习惯及经济条件，并咨询专业牙医的建议。\"]}]}],\"title\":\"补牙小帖士\",\"type\":2},\"titleConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"ToothFillingPosition\",\"mapping\":{\"前牙\":\"前牙位置\",\"后牙\":\"后牙位置\"},\"processType\":2}]},\"validateKey\":\"ToothFillingPosition\",\"validateType\":2,\"validateValue\":\"不限/所有牙位\"},\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"适用牙位\",\"mapping\":{},\"processType\":5}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"ToothFillingPosition\",\"mapping\":{},\"processType\":1}]}},{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"补牙颗数\",\"mapping\":{},\"processType\":5}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"format\":\"%s颗\",\"isList\":false,\"key\":\"teethNumber\",\"mapping\":{},\"processType\":1}]}},{\"explainConfig\":{\"icon\":\"https://p0.meituan.net/ingee/884e025b5c7277df3bade91f9eea1ad51160.png\",\"popup\":{\"infos\":[{\"name\":\"牙位介绍\",\"pic\":\"https://p0.meituan.net/travelcube/59181a653b186c0b0925974722aa616049956.png\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/4f9cbd0c7d57406109cd824472ef52f0128341.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"树脂补牙\",\"value\":\"selected\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/e9b13e019b5c0133ea6e683f6951271a128656.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"嵌体补牙\",\"value\":\"selected\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/4bfc12bf8b2d02a48ffe7ad74c1d29fa128349.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"银汞合金补牙\",\"value\":\"selected\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/8178fe25ed721a187830e93baca0e560128470.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"玻璃离子补牙\",\"value\":\"selected\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/c2c8a002dcbe991557b24dbca7a56e6452162.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"Z250/z250\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/2227a0e34ac8c2376fc70142bfc8b85e52495.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"Z350/z350\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/fa8e4f38bf5e8fa8afd1e17de026ad3551899.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"P60/p60\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/f23a4960738f59039aa3e08c1ff31ede52278.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"P90/p90\"},{\"addCommonAttrsToLast\":true,\"commonAttrs\":[{\"name\":\"tips\",\"values\":[\"以上描述仅供参考，选择补牙材料应综合考虑牙齿位置、损伤程度、美观要求、咀嚼习惯及经济条件，并咨询专业牙医的建议。\"]}]}],\"title\":\"补牙小帖士\",\"type\":2},\"title\":\"材料怎么选\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"树脂补牙/嵌体补牙/银汞合金补牙/玻璃离子补牙\"},\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"材料类型\",\"mapping\":{},\"processType\":5}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"category1\",\"mapping\":{},\"processType\":1}]}},{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"材料品牌\",\"mapping\":{},\"processType\":5}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"brandName\",\"mapping\":{},\"processType\":1}]}},{\"explainConfig\":{\"icon\":\"https://p0.meituan.net/ingee/884e025b5c7277df3bade91f9eea1ad51160.png\",\"popup\":{\"infos\":[{\"name\":\"牙位介绍\",\"pic\":\"https://p0.meituan.net/travelcube/59181a653b186c0b0925974722aa616049956.png\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/4f9cbd0c7d57406109cd824472ef52f0128341.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"树脂补牙\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/e9b13e019b5c0133ea6e683f6951271a128656.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"嵌体补牙\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/4bfc12bf8b2d02a48ffe7ad74c1d29fa128349.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"银汞合金补牙\"},{\"name\":\"材料怎么选\",\"pic\":\"https://p0.meituan.net/ingee/8178fe25ed721a187830e93baca0e560128470.png\",\"validateKey\":\"category1\",\"validateType\":1,\"validateValue\":\"玻璃离子补牙\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/c2c8a002dcbe991557b24dbca7a56e6452162.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"Z250/z250\",\"value\":\"selected\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/2227a0e34ac8c2376fc70142bfc8b85e52495.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"Z350/z350\",\"value\":\"selected\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/fa8e4f38bf5e8fa8afd1e17de026ad3551899.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"P60/p60\",\"value\":\"selected\"},{\"name\":\"3M树脂材料系列对比\",\"pic\":\"https://p0.meituan.net/ingee/f23a4960738f59039aa3e08c1ff31ede52278.png\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"P90/p90\",\"value\":\"selected\"},{\"addCommonAttrsToLast\":true,\"commonAttrs\":[{\"name\":\"tips\",\"values\":[\"以上描述仅供参考，选择补牙材料应综合考虑牙齿位置、损伤程度、美观要求、咀嚼习惯及经济条件，并咨询专业牙医的建议。\"]}]}],\"title\":\"补牙小帖士\",\"type\":2},\"title\":\"3M系列对比\",\"validateKey\":\"material\",\"validateType\":3,\"validateValue\":\"Z250/z250/Z350/z35/P60/p60/P90/p90\"},\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"材料系列\",\"mapping\":{},\"processType\":5}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"material\",\"mapping\":{},\"processType\":1}]}},{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"操作人员\",\"mapping\":{},\"processType\":5}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"homePkgWorkerQualification\",\"mapping\":{},\"processType\":1}]}}],\"type\":3},{\"priceItemConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"format\":\"¥%s\",\"isList\":false,\"mapping\":{},\"processType\":8,\"regex\":\"^¥(\\\\d+\\\\.{0,1}\\\\d*)$\"}]},\"skuItemConfigList\":[{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"70055[0].projectName\",\"mapping\":{},\"processType\":1}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"70055[0].price\",\"mapping\":{},\"priceProcess\":true,\"processType\":1}]}},{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"1386[0].projectName\",\"mapping\":{},\"processType\":1}]},\"skuItemValueConfigList\":[{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"split\":\"、\",\"valueKeys\":[{\"isList\":false,\"key\":\"1386.attrValues.usage\",\"mapping\":{},\"processType\":7}]}}],\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"1386[0].price\",\"mapping\":{},\"priceProcess\":true,\"processType\":1}]}},{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"brandName|material|category1\",\"mapping\":{},\"processType\":1}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"4059[0].price\",\"mapping\":{},\"priceProcess\":true,\"processType\":1}]}},{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"70090.projectName\",\"mapping\":{},\"processType\":7}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"70090.price\",\"mapping\":{},\"priceProcess\":true,\"processType\":7}]}}],\"titleConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"服务项目\",\"mapping\":{},\"processType\":5}]},\"type\":4},{\"priceItemConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"format\":\"¥%s\",\"isList\":false,\"mapping\":{},\"processType\":8,\"regex\":\"^¥(\\\\d+\\\\.{0,1}\\\\d*)$\"}]},\"skuItemConfigList\":[{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"1390.attrValues.bonusItem\",\"mapping\":{},\"processType\":7}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"1390.price\",\"mapping\":{},\"priceProcess\":true,\"processType\":7}]}}],\"titleConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"附赠项目\",\"mapping\":{},\"processType\":5}]},\"type\":4}]}]},{\"descConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"WarrantyPeriod\",\"mapping\":{},\"processType\":1}]},\"groupName\":\"质保服务\"},{\"groupName\":\"额外费用2\",\"skuGroupModuleConfigs\":[{\"skuConfigList\":[{\"skuItemConfigList\":[{\"nameConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"dealExtraCharge.title\",\"mapping\":{},\"processType\":7}]},\"valueConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"dealExtraCharge.price\",\"mapping\":{},\"priceProcess\":true,\"processType\":7}]}}],\"titleConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"额外费用\",\"mapping\":{},\"processType\":5}]},\"type\":4}]}]},{\"descConfig\":{\"needPostType\":false,\"needRealValues\":true,\"valueKeys\":[{\"isList\":false,\"key\":\"medical_desc\",\"mapping\":{},\"processType\":1}]},\"groupName\":\"补充信息\"}]},\"isDetailInfoParsed\":true}";
        return JSON.parseObject(json, Config.class);
    }
}
