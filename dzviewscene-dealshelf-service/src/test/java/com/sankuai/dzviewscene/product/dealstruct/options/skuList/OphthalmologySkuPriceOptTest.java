package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import java.math.BigDecimal;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class OphthalmologySkuPriceOptTest {

    /**
     * 测试 compute 方法是否返回空字符串
     */
    @Test
    public void testComputeReturnEmptyString() throws Throwable {
        // arrange
        OphthalmologySkuPriceOpt ophthalmologySkuPriceOpt = new OphthalmologySkuPriceOpt();
        ActivityCxt context = new ActivityCxt();
        // Using the builder pattern to create an instance of SkuPriceVP.Param
        SkuPriceVP.Param param = SkuPriceVP.Param.builder().price(new BigDecimal("0")).build();
        OphthalmologySkuPriceOpt.Config config = new OphthalmologySkuPriceOpt.Config();
        // act
        String result = ophthalmologySkuPriceOpt.compute(context, param, config);
        // assert
        assertEquals("", result);
    }
}
