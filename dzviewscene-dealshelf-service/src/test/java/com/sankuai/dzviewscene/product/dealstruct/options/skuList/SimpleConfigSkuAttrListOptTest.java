package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SimpleConfigSkuAttrListOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private SimpleConfigSkuAttrListOpt.Config config;

    @Mock
    private SimpleConfigSkuAttrListOpt.Param param;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private ProductSkuCategoryModel productSkuCategoryModel;

    @Test
    public void testComputeConfigIsNull() {
        SimpleConfigSkuAttrListOpt opt = new SimpleConfigSkuAttrListOpt();
        assertNull(opt.compute(activityCxt, param, null));
    }

    @Test
    public void testComputeCategory2ShowVOSIsEmpty() {
        SimpleConfigSkuAttrListOpt opt = new SimpleConfigSkuAttrListOpt();
        when(config.getCategory2ShowVOS()).thenReturn(Collections.emptyMap());
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeSkuItemDtoIsNull() {
        SimpleConfigSkuAttrListOpt opt = new SimpleConfigSkuAttrListOpt();
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeProductCategoriesIsEmpty() {
        SimpleConfigSkuAttrListOpt opt = new SimpleConfigSkuAttrListOpt();
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeCategoryNotExists() {
        SimpleConfigSkuAttrListOpt opt = new SimpleConfigSkuAttrListOpt();
        when(config.getCategory2ShowVOS()).thenReturn(new HashMap<>());
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeDealSkuItemVOListNotExists() {
        SimpleConfigSkuAttrListOpt opt = new SimpleConfigSkuAttrListOpt();
        when(config.getCategory2ShowVOS()).thenReturn(new HashMap<>());
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeDealSkuItemVOListExists() {
        SimpleConfigSkuAttrListOpt opt = new SimpleConfigSkuAttrListOpt();
        when(param.getProductCategories()).thenReturn(Collections.singletonList(productSkuCategoryModel));
        when(productSkuCategoryModel.getProductCategoryId()).thenReturn(1L);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(skuItemDto.getProductCategory()).thenReturn(1L);
        when(productSkuCategoryModel.getCnName()).thenReturn("category1");
        Map<String, List<DealSkuItemVO>> category2ShowVOS = new HashMap<>();
        category2ShowVOS.put("category1", Collections.singletonList(new DealSkuItemVO()));
        when(config.getCategory2ShowVOS()).thenReturn(category2ShowVOS);
        assertTrue(opt.compute(activityCxt, param, config).size() == 1);
    }
}
