package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class SkuCategoryTitleOptTest {

    private SkuCategoryTitleOpt skuCategoryTitleOpt = new SkuCategoryTitleOpt();

    @Test
    public void testComputeSkuItemDtoIsNull() {
        ActivityCxt context = new ActivityCxt();
        SkuTitleVP.Param param = SkuTitleVP.Param.builder().build();
        SkuCategoryTitleOpt.Config config = new SkuCategoryTitleOpt.Config();
        String result = skuCategoryTitleOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeProductCategoriesIsEmpty() {
        ActivityCxt context = new ActivityCxt();
        SkuTitleVP.Param param = SkuTitleVP.Param.builder().skuItemDto(new SkuItemDto()).build();
        SkuCategoryTitleOpt.Config config = new SkuCategoryTitleOpt.Config();
        String result = skuCategoryTitleOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeProductCategoriesNotContainsProductCategory() {
        ActivityCxt context = new ActivityCxt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        SkuTitleVP.Param param = SkuTitleVP.Param.builder().skuItemDto(skuItemDto).productCategories(Collections.singletonList(new ProductSkuCategoryModel())).build();
        SkuCategoryTitleOpt.Config config = new SkuCategoryTitleOpt.Config();
        String result = skuCategoryTitleOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeConfigSkuCategoryShowingSkuNameIsEmpty() {
        ActivityCxt context = new ActivityCxt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        ProductSkuCategoryModel productSkuCategoryModel = new ProductSkuCategoryModel();
        productSkuCategoryModel.setProductCategoryId(1L);
        productSkuCategoryModel.setCnName("test");
        SkuTitleVP.Param param = SkuTitleVP.Param.builder().skuItemDto(skuItemDto).productCategories(Collections.singletonList(productSkuCategoryModel)).build();
        SkuCategoryTitleOpt.Config config = new SkuCategoryTitleOpt.Config();
        String result = skuCategoryTitleOpt.compute(context, param, config);
        assertEquals("test", result);
    }

    @Test
    public void testComputeConfigSkuCategoryShowingSkuNameContainsSkuCategory() {
        ActivityCxt context = new ActivityCxt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        skuItemDto.setName("testName");
        ProductSkuCategoryModel productSkuCategoryModel = new ProductSkuCategoryModel();
        productSkuCategoryModel.setProductCategoryId(1L);
        productSkuCategoryModel.setCnName("test");
        SkuTitleVP.Param param = SkuTitleVP.Param.builder().skuItemDto(skuItemDto).productCategories(Collections.singletonList(productSkuCategoryModel)).build();
        SkuCategoryTitleOpt.Config config = new SkuCategoryTitleOpt.Config();
        config.setSkuCategoryShowingSkuName(Arrays.asList("test"));
        String result = skuCategoryTitleOpt.compute(context, param, config);
        assertEquals("testName", result);
    }
}
