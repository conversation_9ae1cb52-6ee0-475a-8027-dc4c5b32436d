package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SkuListBuiltByScriptExpressionOptTest {

    @InjectMocks
    private SkuListBuiltByScriptExpressionOpt skuListBuiltByScriptExpressionOpt;

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuListAfterProcessingVP.Param param;

    @Mock
    private SkuListBuiltByScriptExpressionOpt.Config config;

    /**
     * Tests the compute method when config.getSkuBuildModels() is null.
     */
    @Test
    public void testComputeWhenSkuBuildModelsIsNull() throws Throwable {
        // Arrange
        when(config.getSkuBuildModels()).thenReturn(null);
        // Act
        List<DealSkuVO> result = skuListBuiltByScriptExpressionOpt.compute(context, param, config);
        // Assert
        assertNull(result);
    }

    /**
     * Tests the compute method when config.getSkuBuildModels() is not empty, but buildDealSkuVoByConfig returns null.
     */
    @Test
    public void testComputeWhenBuildDealSkuVoByConfigReturnsNull() throws Throwable {
        // Arrange
        when(config.getSkuBuildModels()).thenReturn(Collections.emptyList());
        // Act
        List<DealSkuVO> result = skuListBuiltByScriptExpressionOpt.compute(context, param, config);
        // Assert
        assertNull(result);
    }
}
