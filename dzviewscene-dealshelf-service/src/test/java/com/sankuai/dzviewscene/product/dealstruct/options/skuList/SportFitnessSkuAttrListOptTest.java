package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import static org.junit.Assert.*;
import org.junit.*;
import org.testng.collections.Lists;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SportFitnessSkuAttrListOptTest {

    @InjectMocks
    SportFitnessSkuAttrListOpt sportFitnessSkuAttrListOpt;

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuAttrListVP.Param param;

    @Mock
    private SportFitnessSkuAttrListOpt.Config config;

    @Mock
    private SportFitnessSkuAttrListOpt.SkuDisplayModel mockSkuDisplayModel;

    // Helper method to create an instance of SkuDisplayModel using reflection
    private Object createSkuDisplayModelInstance() throws Exception {
        Class<?> clazz = Class.forName("com.sankuai.dzviewscene.product.dealstruct.options.skuList.SportFitnessSkuAttrListOpt$SkuDisplayModel");
        Constructor<?> constructor = clazz.getDeclaredConstructors()[0];
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    // Helper method to inject SkuDisplayModel instances into Config mock using reflection
    private void injectSkuDisplayModelsIntoConfig(Map<Long, List<?>> mockMap) throws Exception {
        Field field = config.getClass().getDeclaredField("category2SkuDisplayModelList");
        field.setAccessible(true);
        field.set(config, mockMap);
    }

    @Test
    public void testComputeEmptySkuDisplayModels() throws Throwable {
        SportFitnessSkuAttrListOpt opt = new SportFitnessSkuAttrListOpt();
        when(param.getSkuItemDto()).thenReturn(new SkuItemDto());
        when(param.getProductCategories()).thenReturn(Collections.emptyList());
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNull("Result should be null when configurations are empty", result);
    }

    @Test
    public void testComputeBuildDealSkuItemByDisplayModelReturnsNull() throws Throwable {
        SportFitnessSkuAttrListOpt opt = new SportFitnessSkuAttrListOpt();
        when(param.getSkuItemDto()).thenReturn(new SkuItemDto());
        when(param.getProductCategories()).thenReturn(Collections.emptyList());
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNull("Result should be null when there are no display models", result);
    }

    /**
     * 测试 compute 方法在 skuItemDto 为 null 的情况下
     */
    @Test
    public void testComputeNullSkuItemDto() throws Throwable {
        SportFitnessSkuAttrListOpt opt = new SportFitnessSkuAttrListOpt();
        when(param.getSkuItemDto()).thenReturn(null);
        List<DealSkuItemVO> result = opt.compute(context, param, config);
        assertNull("Result should be null when skuItemDto is null", result);
    }

    /**
     * 测试dealAttrs为空的情况
     */
    @Test
    public void testBuildAvailableTimeDealSkuItemWithEmptyDealAttrs() {
        List<AttrM> dealAttrs = new ArrayList<>();

        DealSkuItemVO result = sportFitnessSkuAttrListOpt.buildAvailableTimeDealSkuItem(dealAttrs, mockSkuDisplayModel);

        assertNull(result);
    }

    /**
     * 测试dealAttrs中没有available_time属性的情况
     */
    @Test
    public void testBuildAvailableTimeDealSkuItemWithNoAvailableTime() {
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("name", "value"));

        DealSkuItemVO result = sportFitnessSkuAttrListOpt.buildAvailableTimeDealSkuItem(dealAttrs, mockSkuDisplayModel);

        assertNull(result);
    }

    /**
     * 测试dealAttrs中有available_time属性且正常返回DealSkuItemVO的情况
     */
    @Test
    public void testBuildAvailableTimeDealSkuItemWithAvailableTime() {
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("available_time", "10:00-20:00"));
        Mockito.when(mockSkuDisplayModel.getSkuAttrValueFormat()).thenReturn("可使用时间：%s");
        Mockito.when(mockSkuDisplayModel.getSkuTitle()).thenReturn("可使用时间");

        DealSkuItemVO result = sportFitnessSkuAttrListOpt.buildAvailableTimeDealSkuItem(dealAttrs, mockSkuDisplayModel);

        assertNotNull(result);
        assertEquals("可使用时间", result.getName());
        assertEquals("可使用时间：10:00-20:00", result.getValue());
    }

    /**
     * 测试适用球桌全部适用情况
     */
    @Test
    public void testBuildApplicableTableDealSkuItem_AllApplicable() {
        SkuAttrItemDto item = Mockito.mock(SkuAttrItemDto.class);
        Mockito.when(item.getAttrName()).thenReturn("applicableTable");
        Mockito.when(item.getAttrValue()).thenReturn("全部适用");
        List<SkuAttrItemDto> skuAttrItems = Lists.newArrayList();
        skuAttrItems.add(item);

        SportFitnessSkuAttrListOpt.SkuDisplayModel skuDisplayModel = new SportFitnessSkuAttrListOpt.SkuDisplayModel();
        skuDisplayModel.setSkuTitle("适用球桌");
        skuDisplayModel.setSkuAttrValueFormat("%s");

        DealSkuItemVO result = sportFitnessSkuAttrListOpt.buildApplicableTableDealSkuItem(skuAttrItems, skuDisplayModel);

        assertNotNull(result);
        assertEquals("全部", result.getValue());
    }

    /**
     * 测试适用球桌部分适用情况
     */
    @Test
    public void testBuildApplicableTableDealSkuItem_PartialApplicable() {
        SkuAttrItemDto applicableTableItem = Mockito.mock(SkuAttrItemDto.class);
        Mockito.when(applicableTableItem.getAttrName()).thenReturn("applicableTable");
        Mockito.when(applicableTableItem.getAttrValue()).thenReturn("部分适用");
        List<SkuAttrItemDto> skuAttrItems = Lists.newArrayList();
        skuAttrItems.add(applicableTableItem);

        SkuAttrItemDto applicableTableNameItem = Mockito.mock(SkuAttrItemDto.class);
        Mockito.when(applicableTableNameItem.getAttrName()).thenReturn("applicableTableName");
        Mockito.when(applicableTableNameItem.getAttrValue()).thenReturn("适用名称");
        skuAttrItems.add(applicableTableNameItem);

        SportFitnessSkuAttrListOpt.SkuDisplayModel skuDisplayModel = new SportFitnessSkuAttrListOpt.SkuDisplayModel();
        skuDisplayModel.setSkuTitle("适用球桌");
        skuDisplayModel.setSkuAttrValueFormat("%s");

        DealSkuItemVO result = sportFitnessSkuAttrListOpt.buildApplicableTableDealSkuItem(skuAttrItems, skuDisplayModel);

        assertNotNull(result);
        assertEquals("适用名称", result.getValue());
    }

    /**
     * 测试适用球桌为空情况
     */
    @Test
    public void testBuildApplicableTableDealSkuItem_NullApplicableTable() {
        SkuAttrItemDto item = Mockito.mock(SkuAttrItemDto.class);
        Mockito.when(item.getAttrName()).thenReturn("applicableTable");
        Mockito.when(item.getAttrValue()).thenReturn("");
        List<SkuAttrItemDto> skuAttrItems = Lists.newArrayList();
        skuAttrItems.add(item);

        SportFitnessSkuAttrListOpt.SkuDisplayModel skuDisplayModel = new SportFitnessSkuAttrListOpt.SkuDisplayModel();
        skuDisplayModel.setSkuTitle("适用球桌");
        skuDisplayModel.setSkuAttrValueFormat("%s");

        DealSkuItemVO result = sportFitnessSkuAttrListOpt.buildApplicableTableDealSkuItem(skuAttrItems, skuDisplayModel);

        assertNull(result);
    }
}
