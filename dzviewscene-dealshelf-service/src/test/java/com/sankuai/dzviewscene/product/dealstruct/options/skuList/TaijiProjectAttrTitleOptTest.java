package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.tpfun.product.api.sku.aggregate.dto.AttrDTO;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.TaiJiProjectTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaijiProjectAttrTitleOptTest {
    @Mock
    private ActivityCxt context;

    @Mock
    private TaiJiProjectTitleVP.Param param;

    @Mock
    private TaijiProjectAttrTitleOpt.Config config;

    private TaijiProjectAttrTitleOpt.Config buildTitleConfigs(String serviceType,String titleAttrKey,int fetchSource) {
        TaijiProjectAttrTitleOpt.Config config = new TaijiProjectAttrTitleOpt.Config();

        TaijiProjectAttrTitleOpt.TitleConfig titleConfig = new TaijiProjectAttrTitleOpt.TitleConfig();
        titleConfig.setServiceType(serviceType);
        titleConfig.setTitleAttrKey(titleAttrKey);
        titleConfig.setFetchSource(fetchSource);


        HashMap<String, String> map = Maps.newHashMap();
        map.put("发发发","蕊蕊蕊");
        config.setCateIdToShowNameMap(map);
        config.setTitleConfigs(Collections.singletonList(titleConfig));

        return config;
    }

    @Test
    public void testComputeDealAttrsIsEmpty() {
        // arrange
        TaijiProjectAttrTitleOpt taijiProjectAttrTitleOpt = new TaijiProjectAttrTitleOpt();
        when(param.getDealAttrs()).thenReturn(null);
        // act
        String result = taijiProjectAttrTitleOpt.compute(context, param, config);
        // assert
       assertNull(result);
    }

    @Test
    public void testComputeServiceTypeIsEmpty() {
        // arrange
        TaijiProjectAttrTitleOpt taijiProjectAttrTitleOpt = new TaijiProjectAttrTitleOpt();
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM()));
        when(param.getTaiJiProjectItem()).thenReturn(new StandardServiceProjectItemDTO());

        TaijiProjectAttrTitleOpt.Config testConfig = buildTitleConfigs("serviceType", "titleAttrKey", 1);

        // act
        String result = taijiProjectAttrTitleOpt.compute(context, param, testConfig);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeNormal_1_ServiceType() {
        // arrange
        TaijiProjectAttrTitleOpt taijiProjectAttrTitleOpt = new TaijiProjectAttrTitleOpt();
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("serviceType", "serviceType")));

        StandardServiceProjectItemDTO standardServiceProjectItemDTO = buildStandardServiceProjectItemDTO("skuCateId","项目分类", Lists.newArrayList("发发发"));
        when(param.getTaiJiProjectItem()).thenReturn(standardServiceProjectItemDTO);

        TaijiProjectAttrTitleOpt.Config testConfig = buildTitleConfigs("serviceType", "titleAttrKey", 1);

        // act
        String result = taijiProjectAttrTitleOpt.compute(context, param, testConfig);
        // assert
        assertNotNull(result);
    }


    private StandardServiceProjectItemDTO buildStandardServiceProjectItemDTO(String attrName,String attrCnName,List<String> values) {
        StandardServiceProjectItemDTO standardServiceProjectItemDTO = new StandardServiceProjectItemDTO();
        StandardAttributeDTO standardAttributeDTO = new StandardAttributeDTO();
        StandardAttributeItemDTO standardAttributeItemDTO = new StandardAttributeItemDTO();
        standardAttributeItemDTO.setAttrName(attrName);
        standardAttributeItemDTO.setAttrCnName(attrCnName);
        StandardAttributeValueDTO standardAttributeValueDTO = new StandardAttributeValueDTO();
        standardAttributeValueDTO.setSimpleValues(values);
        standardAttributeItemDTO.setAttrValues(Lists.newArrayList(standardAttributeValueDTO));
        standardAttributeDTO.setAttrs(Lists.newArrayList(standardAttributeItemDTO));
        standardServiceProjectItemDTO.setStandardAttribute(standardAttributeDTO);
        return standardServiceProjectItemDTO;
    }

    @Test
    public void testComputeNormal_1_Attr() {
        // arrange
        TaijiProjectAttrTitleOpt taijiProjectAttrTitleOpt = new TaijiProjectAttrTitleOpt();
        AttrM attrM = new AttrM("serviceType", "serviceType");
        AttrM attrM1 = new AttrM("titleAttrKey", "titleAttrKey");
        ArrayList<AttrM> attrs = Lists.newArrayList();
        attrs.add(attrM);
        attrs.add(attrM1);

        when(param.getDealAttrs()).thenReturn(attrs);
        when(param.getTaiJiProjectItem()).thenReturn(new StandardServiceProjectItemDTO());

        TaijiProjectAttrTitleOpt.Config testConfig = buildTitleConfigs("serviceType", "titleAttrKey", 1);

        // act
        String result = taijiProjectAttrTitleOpt.compute(context, param, testConfig);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testComputeNormal_1_ZongHe() {
        // arrange
        TaijiProjectAttrTitleOpt taijiProjectAttrTitleOpt = new TaijiProjectAttrTitleOpt();
        AttrM attrM = new AttrM("serviceType", "综合");
        AttrM attrM1 = new AttrM("titleAttrKey", "titleAttrKey");
        ArrayList<AttrM> attrs = Lists.newArrayList();
        attrs.add(attrM);
        attrs.add(attrM1);

        when(param.getDealAttrs()).thenReturn(attrs);
        StandardServiceProjectItemDTO standardServiceProjectItemDTO = buildStandardServiceProjectItemDTO("skuCateId","项目分类", Lists.newArrayList("发发"));
        when(param.getTaiJiProjectItem()).thenReturn(standardServiceProjectItemDTO);

        TaijiProjectAttrTitleOpt.Config testConfig = buildTitleConfigs("综合", "titleAttrKey", 1);

        // act
        String result = taijiProjectAttrTitleOpt.compute(context, param, testConfig);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testComputeNormal_2_TaiJi() {
        // arrange
        TaijiProjectAttrTitleOpt taijiProjectAttrTitleOpt = new TaijiProjectAttrTitleOpt();
        AttrM attrM = new AttrM("serviceType", "serviceType");
        AttrM attrM1 = new AttrM("titleAttrKey", "titleAttrKey");
        ArrayList<AttrM> attrs = Lists.newArrayList();
        attrs.add(attrM);
        attrs.add(attrM1);

        when(param.getDealAttrs()).thenReturn(attrs);

        StandardServiceProjectItemDTO standardServiceProjectItemDTO = new StandardServiceProjectItemDTO();
        StandardAttributeDTO standardAttributeDTO = new StandardAttributeDTO();
        StandardAttributeItemDTO standardAttributeItemDTO = new StandardAttributeItemDTO();
        standardAttributeItemDTO.setAttrName("titleAttrKey");
        StandardAttributeValueDTO standardAttributeValueDTO = new StandardAttributeValueDTO();
        standardAttributeValueDTO.setType(0);
        standardAttributeValueDTO.setSimpleValues(Lists.newArrayList("titleAttrKey"));

        standardAttributeItemDTO.setAttrValues(Lists.newArrayList(standardAttributeValueDTO));

        standardAttributeDTO.setAttrs(Lists.newArrayList(standardAttributeItemDTO));

        standardServiceProjectItemDTO.setStandardAttribute(standardAttributeDTO);
        when(param.getTaiJiProjectItem()).thenReturn(standardServiceProjectItemDTO);

        TaijiProjectAttrTitleOpt.Config testConfig = buildTitleConfigs("serviceType", "titleAttrKey", 2);

        // act
        String result = taijiProjectAttrTitleOpt.compute(context, param, testConfig);
        // assert
        assertNotNull(result);
    }



}
