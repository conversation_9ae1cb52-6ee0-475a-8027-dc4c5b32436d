package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VaccineSkuListAfterPricessingOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private VaccineSkuListAfterPricessingOpt.Param param;

    @Mock
    private VaccineSkuListAfterPricessingOpt.Config config;

    private static String originalVaccineExtraCostPriceAttrName;

    @After
    public void resetStaticFields() throws NoSuchFieldException, IllegalAccessException {
        if (originalVaccineExtraCostPriceAttrName != null) {
            setPrivateStaticFieldName("VACCINE_EXTRA_COST_PRICE_ATTR_NAME", originalVaccineExtraCostPriceAttrName);
        }
    }

    private static void setPrivateStaticFieldName(String fieldName, String value) throws NoSuchFieldException, IllegalAccessException {
        Field field = VaccineSkuListAfterPricessingOpt.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    private static String getPrivateStaticFieldName() throws NoSuchFieldException, IllegalAccessException {
        Field field = VaccineSkuListAfterPricessingOpt.class.getDeclaredField("VACCINE_EXTRA_COST_PRICE_ATTR_NAME");
        field.setAccessible(true);
        return (String) field.get(null);
    }

    @Test
    public void testComputeDealSkuVOSIsNull() throws Throwable {
        when(param.getDealSkuVOS()).thenReturn(null);
        VaccineSkuListAfterPricessingOpt opt = new VaccineSkuListAfterPricessingOpt();
        List<DealSkuVO> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeDealAttrsIsNull() throws Throwable {
        when(param.getDealSkuVOS()).thenReturn(Collections.singletonList(new DealSkuVO()));
        when(param.getDealAttrs()).thenReturn(null);
        VaccineSkuListAfterPricessingOpt opt = new VaccineSkuListAfterPricessingOpt();
        List<DealSkuVO> result = opt.compute(context, param, config);
        assertEquals(1, result.size());
    }

    @Test
    public void testComputeDealAttrsNotContainsExtraCostPrice() throws Throwable {
        when(param.getDealSkuVOS()).thenReturn(Collections.singletonList(new DealSkuVO()));
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("other", "otherValue")));
        VaccineSkuListAfterPricessingOpt opt = new VaccineSkuListAfterPricessingOpt();
        List<DealSkuVO> result = opt.compute(context, param, config);
        assertEquals(1, result.size());
    }

    @Test
    public void testComputeBuildSupportingProjectDealSkuVOIsNull() throws Throwable {
        when(param.getDealSkuVOS()).thenReturn(Collections.singletonList(new DealSkuVO()));
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM(getPrivateStaticFieldName(), "extraCostPriceValue")));
        VaccineSkuListAfterPricessingOpt opt = new VaccineSkuListAfterPricessingOpt();
        List<DealSkuVO> result = opt.compute(context, param, config);
        assertEquals(1, result.size());
    }
}
