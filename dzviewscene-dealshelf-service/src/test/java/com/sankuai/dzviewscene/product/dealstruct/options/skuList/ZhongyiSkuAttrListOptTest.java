package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ZhongyiSkuAttrListOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ZhongyiSkuAttrListOpt.Config config;

    @Mock
    private ZhongyiSkuAttrListOpt.Param param;

    private ZhongyiSkuAttrListOpt zhongyiSkuAttrListOpt = new ZhongyiSkuAttrListOpt();

    private StandardAttributeItemDTO buildStandardAttributeItemDTOComplexValues(String key, String keyname, String value) {
        StandardAttributeItemDTO standardAttributeItemDTO = new StandardAttributeItemDTO();
        standardAttributeItemDTO.setAttrName(key);
        standardAttributeItemDTO.setAttrCnName(keyname);
        StandardAttributeValueDTO standardAttributeValueDTO = new StandardAttributeValueDTO();
        standardAttributeValueDTO.setType(1);
        standardAttributeValueDTO.setComplexValues(value);
        standardAttributeItemDTO.setAttrValues(Lists.newArrayList(standardAttributeValueDTO));
        return standardAttributeItemDTO;
    }

    private StandardAttributeItemDTO buildStandardAttributeItemDTOSimpleValues(String key, String keyname, String value) {
        StandardAttributeItemDTO standardAttributeItemDTO = new StandardAttributeItemDTO();
        standardAttributeItemDTO.setAttrName(key);
        standardAttributeItemDTO.setAttrCnName(keyname);
        StandardAttributeValueDTO standardAttributeValueDTO = new StandardAttributeValueDTO();
        standardAttributeValueDTO.setType(0);
        standardAttributeValueDTO.setSimpleValues(Lists.newArrayList(value));
        standardAttributeItemDTO.setAttrValues(Lists.newArrayList(standardAttributeValueDTO));
        return standardAttributeItemDTO;
    }

    private ZhongyiSkuAttrListOpt.DisplayRuleCfg getDisplayRuleCfg(String displayName, String strategy, String key, int type, Map<String, String> displayAlias) {
        ZhongyiSkuAttrListOpt.DisplayRuleCfg displayRuleCfg = new ZhongyiSkuAttrListOpt.DisplayRuleCfg();
        displayRuleCfg.setDisplayName(displayName);
        displayRuleCfg.setStrategy(strategy);
        displayRuleCfg.setKey(key);
        displayRuleCfg.setType(type);
        displayRuleCfg.setDisplayAlias(displayAlias);
        return displayRuleCfg;
    }

    @Test
    public void testComputeConfigIsNull() {
        ZhongyiSkuAttrListOpt opt = new ZhongyiSkuAttrListOpt();
        assertNull(opt.compute(activityCxt, param, null));
    }

    @Test
    public void testComputeStandardAttributeIsNull() {
        ZhongyiSkuAttrListOpt opt = new ZhongyiSkuAttrListOpt();
        StandardServiceProjectItemDTO taiJiProjectItem = new StandardServiceProjectItemDTO();
        when(param.getTaiJiProjectItem()).thenReturn(taiJiProjectItem);
        config = new ZhongyiSkuAttrListOpt.Config();
        ZhongyiSkuAttrListOpt.DisplayRuleCfg displayRuleCfg = getDisplayRuleCfg("displayName1", "showDirect", "key1", 0, null);
        config.setConfigs(Lists.newArrayList(displayRuleCfg));
        assertNull(opt.compute(activityCxt, param, config));
    }

    @Test
    public void testComputeNormal_DealSkuItem() {
        ZhongyiSkuAttrListOpt opt = new ZhongyiSkuAttrListOpt();
        StandardServiceProjectItemDTO taiJiProjectItem = new StandardServiceProjectItemDTO();
        StandardAttributeDTO standardAttributeDTO = new StandardAttributeDTO();
        StandardAttributeItemDTO standardAttributeItemDTO1 = buildStandardAttributeItemDTOSimpleValues("key1", "key1name", "value1");
        StandardAttributeItemDTO standardAttributeItemDTO2 = buildStandardAttributeItemDTOSimpleValues("key2", "key2name", "[\"value2\"]");
        StandardAttributeItemDTO standardAttributeItemDTO3 = buildStandardAttributeItemDTOSimpleValues("key3", "key3name", "value");
        StandardAttributeItemDTO standardAttributeItemDTO4 = buildStandardAttributeItemDTOSimpleValues("Number_of_parts", "key4name", "全身");
        List<StandardAttributeItemDTO> attrs = Lists.newArrayList();
        attrs.add(standardAttributeItemDTO1);
        attrs.add(standardAttributeItemDTO2);
        attrs.add(standardAttributeItemDTO3);
        attrs.add(standardAttributeItemDTO4);
        standardAttributeDTO.setAttrs(attrs);
        taiJiProjectItem.setStandardAttribute(standardAttributeDTO);
        when(param.getTaiJiProjectItem()).thenReturn(taiJiProjectItem);
        config = new ZhongyiSkuAttrListOpt.Config();
        ZhongyiSkuAttrListOpt.DisplayRuleCfg displayRuleCfg1 = getDisplayRuleCfg("displayName1", "showDirect", "key1", 0, null);
        ZhongyiSkuAttrListOpt.DisplayRuleCfg displayRuleCfg2 = getDisplayRuleCfg("displayName2", "", "key2", 0, null);
        ZhongyiSkuAttrListOpt.DisplayRuleCfg displayRuleCfg3 = getDisplayRuleCfg("服务部位", "", "Number_of_parts", 0, null);
        Map<String, String> displayAlias = Maps.newHashMap();
        displayAlias.put("key", "alias");
        ZhongyiSkuAttrListOpt.DisplayRuleCfg displayRuleCfg4 = getDisplayRuleCfg("displayName2", "showAlias", "key3", 0, displayAlias);
        List<ZhongyiSkuAttrListOpt.DisplayRuleCfg> displayRuleCfgs = Lists.newArrayList();
        displayRuleCfgs.add(displayRuleCfg1);
        displayRuleCfgs.add(displayRuleCfg2);
        displayRuleCfgs.add(displayRuleCfg3);
        displayRuleCfgs.add(displayRuleCfg4);
        config.setConfigs(displayRuleCfgs);
        List<DealSkuItemVO> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeNormal_ProjectItem() {
        ZhongyiSkuAttrListOpt opt = new ZhongyiSkuAttrListOpt();
        StandardServiceProjectItemDTO taiJiProjectItem = new StandardServiceProjectItemDTO();
        StandardAttributeDTO standardAttributeDTO = new StandardAttributeDTO();
        String processValue = "[{\"attrs\":[{\"attrName\":\"Introd_service\",\"attrCnName\":\"服务简介\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"11\"]}]},{\"attrName\":\"Service_Content\",\"attrCnName\":\"服务内容\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"挂号\"]}]}],\"cpvObjectId\":21386110},{\"attrs\":[{\"attrName\":\"Introd_service\",\"attrCnName\":\"服务简介\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"\"]}]},{\"attrName\":\"Service_Content\",\"attrCnName\":\"服务内容\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"体质辩证\"]}]}],\"cpvObjectId\":21386110}]";
        StandardAttributeItemDTO standardAttributeItemDTO = buildStandardAttributeItemDTOComplexValues("serviceProcessArrayNew", "key4name", processValue);
        List<StandardAttributeItemDTO> attrs = Lists.newArrayList();
        attrs.add(standardAttributeItemDTO);
        standardAttributeDTO.setAttrs(attrs);
        taiJiProjectItem.setStandardAttribute(standardAttributeDTO);
        when(param.getTaiJiProjectItem()).thenReturn(taiJiProjectItem);
        config = new ZhongyiSkuAttrListOpt.Config();
        ZhongyiSkuAttrListOpt.DisplayRuleCfg displayRuleCfg = getDisplayRuleCfg("服务流程", "", "serviceProcessArrayNew", 2, null);
        ZhongyiSkuAttrListOpt.ProjectProcessCfg projectProcessCfg = new ZhongyiSkuAttrListOpt.ProjectProcessCfg();
        projectProcessCfg.setContentKey("Service_Content");
        projectProcessCfg.setIntroKey("Introd_service");
        displayRuleCfg.setProjectProcessCfg(projectProcessCfg);
        List<ZhongyiSkuAttrListOpt.DisplayRuleCfg> displayRuleCfgs = Lists.newArrayList();
        displayRuleCfgs.add(displayRuleCfg);
        config.setConfigs(displayRuleCfgs);
        List<DealSkuItemVO> result = opt.compute(activityCxt, param, config);
        assertNotNull(result);
    }

    /**
     * 测试输入的数字小于0时，应抛出 IllegalArgumentException 异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNumberConvertLessThanZero() throws Throwable {
        zhongyiSkuAttrListOpt.numberConvert(-1);
    }

    /**
     * 测试输入的数字大于等于100时，应抛出 IllegalArgumentException 异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNumberConvertGreaterThanHundred() throws Throwable {
        zhongyiSkuAttrListOpt.numberConvert(100);
    }

    /**
     * 测试输入的数字小于10时，应返回对应的中文数字
     */
    @Test
    public void testNumberConvertLessThanTen() throws Throwable {
        assertEquals("零", zhongyiSkuAttrListOpt.numberConvert(0));
        assertEquals("一", zhongyiSkuAttrListOpt.numberConvert(1));
        assertEquals("二", zhongyiSkuAttrListOpt.numberConvert(2));
        assertEquals("三", zhongyiSkuAttrListOpt.numberConvert(3));
        assertEquals("四", zhongyiSkuAttrListOpt.numberConvert(4));
        assertEquals("五", zhongyiSkuAttrListOpt.numberConvert(5));
        assertEquals("六", zhongyiSkuAttrListOpt.numberConvert(6));
        assertEquals("七", zhongyiSkuAttrListOpt.numberConvert(7));
        assertEquals("八", zhongyiSkuAttrListOpt.numberConvert(8));
        assertEquals("九", zhongyiSkuAttrListOpt.numberConvert(9));
    }

    /**
     * 测试输入的数字在10到19之间时，应返回 "十" + 数字对应的中文数字
     */
    @Test
    public void testNumberConvertBetweenTenAndTwenty() throws Throwable {
        assertEquals("十零", zhongyiSkuAttrListOpt.numberConvert(10));
        assertEquals("十一", zhongyiSkuAttrListOpt.numberConvert(11));
        assertEquals("十二", zhongyiSkuAttrListOpt.numberConvert(12));
        assertEquals("十三", zhongyiSkuAttrListOpt.numberConvert(13));
        assertEquals("十四", zhongyiSkuAttrListOpt.numberConvert(14));
        assertEquals("十五", zhongyiSkuAttrListOpt.numberConvert(15));
        assertEquals("十六", zhongyiSkuAttrListOpt.numberConvert(16));
        assertEquals("十七", zhongyiSkuAttrListOpt.numberConvert(17));
        assertEquals("十八", zhongyiSkuAttrListOpt.numberConvert(18));
        assertEquals("十九", zhongyiSkuAttrListOpt.numberConvert(19));
    }

    /**
     * 测试输入的数字大于19时，应返回数字除以10对应的中文数字 + "十"，如果数字除以10的余数不为0，再添加数字除以10的余数对应的中文数字
     */
    @Test
    public void testNumberConvertGreaterThanTwenty() throws Throwable {
        assertEquals("二十", zhongyiSkuAttrListOpt.numberConvert(20));
        assertEquals("二十一", zhongyiSkuAttrListOpt.numberConvert(21));
        assertEquals("二十二", zhongyiSkuAttrListOpt.numberConvert(22));
        assertEquals("二十三", zhongyiSkuAttrListOpt.numberConvert(23));
        assertEquals("二十四", zhongyiSkuAttrListOpt.numberConvert(24));
        assertEquals("二十五", zhongyiSkuAttrListOpt.numberConvert(25));
        assertEquals("二十六", zhongyiSkuAttrListOpt.numberConvert(26));
        assertEquals("二十七", zhongyiSkuAttrListOpt.numberConvert(27));
        assertEquals("二十八", zhongyiSkuAttrListOpt.numberConvert(28));
        assertEquals("二十九", zhongyiSkuAttrListOpt.numberConvert(29));
        assertEquals("三十", zhongyiSkuAttrListOpt.numberConvert(30));
        assertEquals("三十一", zhongyiSkuAttrListOpt.numberConvert(31));
        assertEquals("三十三", zhongyiSkuAttrListOpt.numberConvert(33));
        assertEquals("三十四", zhongyiSkuAttrListOpt.numberConvert(34));
        assertEquals("三十五", zhongyiSkuAttrListOpt.numberConvert(35));
        assertEquals("三十六", zhongyiSkuAttrListOpt.numberConvert(36));
        assertEquals("三十七", zhongyiSkuAttrListOpt.numberConvert(37));
        assertEquals("三十八", zhongyiSkuAttrListOpt.numberConvert(38));
        assertEquals("三十九", zhongyiSkuAttrListOpt.numberConvert(39));
        assertEquals("四十", zhongyiSkuAttrListOpt.numberConvert(40));
        assertEquals("四十一", zhongyiSkuAttrListOpt.numberConvert(41));
        assertEquals("四十三", zhongyiSkuAttrListOpt.numberConvert(43));
        assertEquals("四十四", zhongyiSkuAttrListOpt.numberConvert(44));
        assertEquals("四十五", zhongyiSkuAttrListOpt.numberConvert(45));
        assertEquals("四十六", zhongyiSkuAttrListOpt.numberConvert(46));
        assertEquals("四十七", zhongyiSkuAttrListOpt.numberConvert(47));
        assertEquals("四十八", zhongyiSkuAttrListOpt.numberConvert(48));
        assertEquals("四十九", zhongyiSkuAttrListOpt.numberConvert(49));
        assertEquals("五十", zhongyiSkuAttrListOpt.numberConvert(50));
        assertEquals("五十一", zhongyiSkuAttrListOpt.numberConvert(51));
        assertEquals("五十三", zhongyiSkuAttrListOpt.numberConvert(53));
        assertEquals("五十四", zhongyiSkuAttrListOpt.numberConvert(54));
        assertEquals("五十五", zhongyiSkuAttrListOpt.numberConvert(55));
        assertEquals("五十六", zhongyiSkuAttrListOpt.numberConvert(56));
        assertEquals("五十七", zhongyiSkuAttrListOpt.numberConvert(57));
        assertEquals("五十八", zhongyiSkuAttrListOpt.numberConvert(58));
        assertEquals("五十九", zhongyiSkuAttrListOpt.numberConvert(59));
    }
}
