package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopUpWindowVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractBarSkuCreatorBuildDealSkuVOTest {

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private BarDealDetailSkuListModuleOpt.Config config;

    @Mock
    private LinkedHashMap<Double, String> alcoholByVolumeRange2DocMap;

    @Mock
    private List<BarDealDetailSkuListModuleOpt.AttrConfigModel> popupAttrConfigModels;

    private AbstractBarSkuCreator abstractBarSkuCreator;

    // Concrete subclass of AbstractBarSkuCreator for testing
    private static class TestableBarSkuCreator extends AbstractBarSkuCreator {

        @Override
        public boolean ideantify(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
            // Implementing a dummy method for demonstration
            return false;
        }

        @Override
        protected String buildIcon(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isSameCategorySku) {
            // Implementing a dummy method for demonstration
            return null;
        }

        @Override
        protected Map<String, String> getSubtitlesAttrName2FormatMap(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
            // Implementing a dummy method for demonstration
            return new HashMap<>();
        }

        @Override
        protected PopUpWindowVO buildPopUpWindowVO(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
            // Implementing a dummy method for demonstration
            return null;
        }

        @Override
        protected String extractCopies(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
            // Implementing a dummy method for demonstration
            return null;
        }
    }

    @Before
    public void setUp() {
        abstractBarSkuCreator = mock(AbstractBarSkuCreator.class, CALLS_REAL_METHODS);
    }

    @Test
    public void testBuildDealSkuVONullSkuItemDto() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = new TestableBarSkuCreator();
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(null, true, config, true);
        assertNull(result);
    }

    @Test
    public void testBuildDealSkuVONullMarketPrice() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = new TestableBarSkuCreator();
        when(skuItemDto.getMarketPrice()).thenReturn(null);
        when(skuItemDto.getName()).thenReturn("Test Product");
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(skuItemDto, true, config, true);
        assertNotNull(result);
        assertNull(result.getPrice());
    }

    @Test
    public void testBuildDealSkuVONonNullMarketPrice() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = new TestableBarSkuCreator();
        when(skuItemDto.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        when(skuItemDto.getName()).thenReturn("Test Product");
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(skuItemDto, true, config, true);
        assertNotNull(result);
        assertEquals("¥100", result.getPrice());
    }

    @Test
    public void testBuildDealSkuVOIsSameCategorySkuTrue() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = new TestableBarSkuCreator();
        when(skuItemDto.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        when(skuItemDto.getName()).thenReturn("Test Product");
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(skuItemDto, true, config, true);
        assertNotNull(result);
    }

    @Test
    public void testBuildDealSkuVOIsSameCategorySkuFalse() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = new TestableBarSkuCreator();
        when(skuItemDto.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        when(skuItemDto.getName()).thenReturn("Test Product");
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(skuItemDto, false, config, true);
        assertNotNull(result);
    }

    @Test
    public void testBuildDealSkuVOIsHitDouhuTrue() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = new TestableBarSkuCreator();
        when(skuItemDto.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        when(skuItemDto.getName()).thenReturn("Test Product");
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(skuItemDto, true, config, true);
        assertNotNull(result);
    }

    @Test
    public void testBuildDealSkuVOIsHitDouhuFalse() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = new TestableBarSkuCreator();
        when(skuItemDto.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        when(skuItemDto.getName()).thenReturn("Test Product");
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(skuItemDto, true, config, false);
        assertNotNull(result);
    }

    @Test
    public void testBuildPopUpWindowVOForDrinks_SkuItemDtoIsNull() {
        PopUpWindowVO result = abstractBarSkuCreator.buildPopUpWindowVOForDrinks(null, alcoholByVolumeRange2DocMap, popupAttrConfigModels);
        assertNull(result);
    }

    @Test
    public void testBuildPopUpWindowVOForDrinks_AttrItemsIsEmpty() {
        when(skuItemDto.getAttrItems()).thenReturn(new ArrayList<>());
        PopUpWindowVO result = abstractBarSkuCreator.buildPopUpWindowVOForDrinks(skuItemDto, alcoholByVolumeRange2DocMap, popupAttrConfigModels);
        assertNull(result);
    }

    @Test
    public void testBuildPopUpWindowVOForDrinks_AlcoholByVolumeRange2DocMapIsNull() {
        PopUpWindowVO result = abstractBarSkuCreator.buildPopUpWindowVOForDrinks(skuItemDto, null, popupAttrConfigModels);
        assertNull(result);
    }

    @Test
    public void testBuildPopUpWindowVOForDrinks_PopupAttrConfigModelsIsNull() {
        PopUpWindowVO result = abstractBarSkuCreator.buildPopUpWindowVOForDrinks(skuItemDto, alcoholByVolumeRange2DocMap, null);
        assertNull(result);
    }

    @Test
    public void testBuildPopUpWindowVOForDrinks_AllParamsAreNotNull() {
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        attrItems.add(new SkuAttrItemDto());
        when(skuItemDto.getAttrItems()).thenReturn(attrItems);
        PopUpWindowVO result = abstractBarSkuCreator.buildPopUpWindowVOForDrinks(skuItemDto, alcoholByVolumeRange2DocMap, popupAttrConfigModels);
        assertNotNull(result);
    }
}
