package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopUpWindowVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class AbstractBarSkuCreator_BuildDealSkuVOTest {

    @Mock
    private AbstractBarSkuCreator abstractBarSkuCreator;

    private void setupMockBehavior() {
    }

    @Test
    public void testBuildDealSkuVONullSkuItemDto() throws Throwable {
        setupMockBehavior();
        SkuItemDto skuItemDto = null;
        boolean isSameCategorySku = true;
        BarDealDetailSkuListModuleOpt.Config config = mock(BarDealDetailSkuListModuleOpt.Config.class);
        boolean isHitDouhu = true;
        DealSkuVO result = abstractBarSkuCreator.buildDealSkuVO(skuItemDto, isSameCategorySku, config, isHitDouhu);
        assertNull(result);
    }
}
