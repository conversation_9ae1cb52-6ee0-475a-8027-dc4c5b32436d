package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class BarSkuCreatorFactoryTest {

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private BarDealDetailSkuListModuleOpt.Config config;

    @Before
    public void setUp() throws Exception {
        resetBarSkuBuilders();
    }

    @After
    public void tearDown() throws Exception {
        resetBarSkuBuilders();
    }

    private void resetBarSkuBuilders() throws Exception {
        Field field = BarSkuCreatorFactory.class.getDeclaredField("barSkuBuilders");
        field.setAccessible(true);
        List<AbstractBarSkuCreator> barSkuBuilders = (List<AbstractBarSkuCreator>) field.get(null);
        barSkuBuilders.clear();
    }

    private void addMockCreatorToBarSkuBuilders(AbstractBarSkuCreator mockCreator) throws Exception {
        Field field = BarSkuCreatorFactory.class.getDeclaredField("barSkuBuilders");
        field.setAccessible(true);
        List<AbstractBarSkuCreator> barSkuBuilders = (List<AbstractBarSkuCreator>) field.get(null);
        barSkuBuilders.add(mockCreator);
    }

    @Test
    public void testCreadSkuBuilderWhenBarSkuBuildersIsEmpty() throws Throwable {
        AbstractBarSkuCreator result = BarSkuCreatorFactory.creadSkuBuilder(skuItemDto, config);
        assertNull(result);
    }

    @Test
    public void testCreadSkuBuilderWhenNoIdeantifyReturnsTrue() throws Throwable {
        AbstractBarSkuCreator mockCreator = mock(AbstractBarSkuCreator.class);
        when(mockCreator.ideantify(skuItemDto, config)).thenReturn(false);
        addMockCreatorToBarSkuBuilders(mockCreator);
        AbstractBarSkuCreator result = BarSkuCreatorFactory.creadSkuBuilder(skuItemDto, config);
        assertNull(result);
    }

    @Test
    public void testCreadSkuBuilderWhenIdeantifyReturnsTrue() throws Throwable {
        AbstractBarSkuCreator mockCreator = mock(AbstractBarSkuCreator.class);
        when(mockCreator.ideantify(skuItemDto, config)).thenReturn(true);
        addMockCreatorToBarSkuBuilders(mockCreator);
        AbstractBarSkuCreator result = BarSkuCreatorFactory.creadSkuBuilder(skuItemDto, config);
        assertNotNull(result);
        assertEquals(mockCreator, result);
    }
}
