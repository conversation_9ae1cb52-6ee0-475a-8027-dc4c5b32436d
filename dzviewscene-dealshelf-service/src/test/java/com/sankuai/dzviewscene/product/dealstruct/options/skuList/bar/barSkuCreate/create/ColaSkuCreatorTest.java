package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class ColaSkuCreatorTest {

    private ColaSkuCreator colaSkuCreator = new ColaSkuCreator();

    /**
     * 测试 skuItemDto 为 null 的情况
     */
    @Test
    public void testIdeantifySkuItemDtoIsNull() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setColaSkuCateIds(Arrays.asList(1L, 2L, 3L));
        assertFalse(colaSkuCreator.ideantify(null, config));
    }

    /**
     * 测试 skuItemDto 不为 null，但 config 中的 colaSkuCateIds 不包含 skuItemDto 的 productCategory 的情况
     */
    @Test
    public void testIdeantifyProductCategoryNotInColaSkuCateIds() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4L);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setColaSkuCateIds(Arrays.asList(1L, 2L, 3L));
        assertFalse(colaSkuCreator.ideantify(skuItemDto, config));
    }

    /**
     * 测试 skuItemDto 不为 null，且 config 中的 colaSkuCateIds 包含 skuItemDto 的 productCategory 的情况
     */
    @Test
    public void testIdeantifyProductCategoryInColaSkuCateIds() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setColaSkuCateIds(Arrays.asList(1L, 2L, 3L));
        assertTrue(colaSkuCreator.ideantify(skuItemDto, config));
    }
}
