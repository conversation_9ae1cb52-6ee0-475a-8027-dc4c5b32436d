package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class ColaSkuCreator_BuildIconTest {

    private ColaSkuCreator colaSkuCreator = new ColaSkuCreator();

    // Test case when SkuItemDto is null
    @Test
    public void testBuildIconSkuItemDtoIsNull() throws Throwable {
        String result = colaSkuCreator.buildIcon(null, null, false);
        assertNull(result);
    }

    // Test case when attribute items list is empty
    @Test
    public void testBuildIconAttrItemsIsEmpty() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Collections.emptyList());
        String result = colaSkuCreator.buildIcon(skuItemDto, null, false);
        assertNull(result);
    }

    // Test case when no attribute item matches the expected attribute name
    @Test
    public void testBuildIconNoMatchedAttrItem() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("other");
        skuAttrItemDto.setAttrValue("other");
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        String result = colaSkuCreator.buildIcon(skuItemDto, null, false);
        assertNull(result);
    }
}
