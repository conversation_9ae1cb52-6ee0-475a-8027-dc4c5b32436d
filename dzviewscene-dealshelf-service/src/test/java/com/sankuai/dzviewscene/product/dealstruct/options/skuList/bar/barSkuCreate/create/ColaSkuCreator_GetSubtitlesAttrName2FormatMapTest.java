package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ColaSkuCreator_GetSubtitlesAttrName2FormatMapTest {

    @Mock
    private BarDealDetailSkuListModuleOpt.Config config;

    @Test
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuAndConfigNotNull() throws Throwable {
        // arrange
        ColaSkuCreator colaSkuCreator = new ColaSkuCreator();
        Map<String, String> expected = new HashMap<>();
        expected.put("volume", "%sL");
        expected.put("taste", "%s");
        expected.put("alcoholByVolume", "%s%%Vol");
        when(config.getColaSubtitlesAttrName2FormatMap()).thenReturn(expected);
        // act
        Map<String, String> result = colaSkuCreator.getSubtitlesAttrName2FormatMap(new SkuItemDto(), config, true);
        // assert
        assertEquals(expected, result);
    }

    @Test(expected = NullPointerException.class)
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuAndConfigNull() throws Throwable {
        // arrange
        ColaSkuCreator colaSkuCreator = new ColaSkuCreator();
        // act
        colaSkuCreator.getSubtitlesAttrName2FormatMap(new SkuItemDto(), null, true);
        // The method is expected to throw NullPointerException
    }

    @Test
    public void testGetSubtitlesAttrName2FormatMapNotHitDouhu() throws Throwable {
        // arrange
        ColaSkuCreator colaSkuCreator = new ColaSkuCreator();
        Map<String, String> expected = new HashMap<>();
        expected.put("volume", "%sL");
        expected.put("taste", "%s");
        expected.put("alcoholByVolume", "%s%%Vol");
        // act
        Map<String, String> result = colaSkuCreator.getSubtitlesAttrName2FormatMap(new SkuItemDto(), config, false);
        // assert
        assertEquals(expected, result);
    }
}
