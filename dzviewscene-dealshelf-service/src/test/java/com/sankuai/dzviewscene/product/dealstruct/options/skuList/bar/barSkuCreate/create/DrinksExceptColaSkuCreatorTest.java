package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DrinksExceptColaSkuCreatorTest {

    private DrinksExceptColaSkuCreator drinksExceptColaSkuCreator = new DrinksExceptColaSkuCreator();

    // Assuming the actual constant value is "healPic"
    private static final String HEAL_PIC_SKU_ATTR_NAME = "healPic";

    /**
     * 测试SkuItemDto对象为null的情况
     */
    @Test
    public void testIdeantifySkuItemDtoIsNull() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setColaSkuCateIds(Arrays.asList(1L, 2L, 3L));
        config.setDrinksSkuCateIds(Arrays.asList(4L, 5L, 6L));
        assertFalse(drinksExceptColaSkuCreator.ideantify(null, config));
    }

    /**
     * 测试SkuItemDto对象的productCategory属性在config的colaSkuCateIds列表中的情况
     */
    @Test
    public void testIdeantifyProductCategoryInColaSkuCateIds() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setColaSkuCateIds(Arrays.asList(1L, 2L, 3L));
        config.setDrinksSkuCateIds(Arrays.asList(4L, 5L, 6L));
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        assertFalse(drinksExceptColaSkuCreator.ideantify(skuItemDto, config));
    }

    /**
     * 测试SkuItemDto对象的productCategory属性在config的drinksSkuCateIds列表中的情况
     */
    @Test
    public void testIdeantifyProductCategoryInDrinksSkuCateIds() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setColaSkuCateIds(Arrays.asList(1L, 2L, 3L));
        config.setDrinksSkuCateIds(Arrays.asList(4L, 5L, 6L));
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4L);
        assertTrue(drinksExceptColaSkuCreator.ideantify(skuItemDto, config));
    }

    /**
     * 测试SkuItemDto对象的productCategory属性既不在config的colaSkuCateIds列表，也不在config的drinksSkuCateIds列表中的情况
     */
    @Test
    public void testIdeantifyProductCategoryNotInAnyList() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setColaSkuCateIds(Arrays.asList(1L, 2L, 3L));
        config.setDrinksSkuCateIds(Arrays.asList(4L, 5L, 6L));
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(7L);
        assertFalse(drinksExceptColaSkuCreator.ideantify(skuItemDto, config));
    }

    @Test(expected = NullPointerException.class)
    public void testExtractCopiesWhenSkuItemDtoIsNull() throws Throwable {
        // Expecting a NullPointerException to be thrown
        drinksExceptColaSkuCreator.extractCopies(null, null);
    }

    @Test
    public void testExtractCopiesWhenAttrItemsIsNull() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setCopies(2);
        String result = drinksExceptColaSkuCreator.extractCopies(skuItemDto, null);
        assertEquals("(2份)", result);
    }

    @Test
    public void testExtractCopiesWhenAttrItemsNotContainsQuantityAvailableAndQuantityUnit() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setCopies(2);
        skuItemDto.setAttrItems(Arrays.asList(new SkuAttrItemDto()));
        String result = drinksExceptColaSkuCreator.extractCopies(skuItemDto, null);
        assertEquals("(2份)", result);
    }

    @Test
    public void testExtractCopiesWhenQuantityAvailableIsNull() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setCopies(2);
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("quantityUnit");
        skuAttrItemDto.setAttrValue("瓶");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        String result = drinksExceptColaSkuCreator.extractCopies(skuItemDto, null);
        assertEquals("(2瓶)", result);
    }

    @Test
    public void testExtractCopiesWhenQuantityAvailableIsNotNull() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setCopies(2);
        SkuAttrItemDto quantityAvailableAttr = new SkuAttrItemDto();
        quantityAvailableAttr.setAttrName("quantityAvailable");
        quantityAvailableAttr.setAttrValue("3");
        SkuAttrItemDto quantityUnitAttr = new SkuAttrItemDto();
        quantityUnitAttr.setAttrName("quantityUnit");
        quantityUnitAttr.setAttrValue("瓶");
        skuItemDto.setAttrItems(Arrays.asList(quantityAvailableAttr, quantityUnitAttr));
        String result = drinksExceptColaSkuCreator.extractCopies(skuItemDto, null);
        assertEquals("(6瓶)", result);
    }

    @Test
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuTrueAndConfigNotNull() {
        // arrange
        DrinksExceptColaSkuCreator creator = new DrinksExceptColaSkuCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        BarDealDetailSkuListModuleOpt.Config config = mock(BarDealDetailSkuListModuleOpt.Config.class);
        Map<String, String> expectedMap = new HashMap<>();
        expectedMap.put("key", "value");
        when(config.getDrinksSubtitlesAttrName2FormatMap()).thenReturn(expectedMap);
        // act
        Map<String, String> result = creator.getSubtitlesAttrName2FormatMap(skuItemDto, config, true);
        // assert
        assertEquals(expectedMap, result);
    }

    @Test
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuTrueAndConfigNull() {
        // arrange
        DrinksExceptColaSkuCreator creator = new DrinksExceptColaSkuCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        BarDealDetailSkuListModuleOpt.Config config = mock(BarDealDetailSkuListModuleOpt.Config.class);
        when(config.getDrinksSubtitlesAttrName2FormatMap()).thenReturn(null);
        // act
        Map<String, String> result = creator.getSubtitlesAttrName2FormatMap(skuItemDto, config, true);
        // assert
        assertEquals(null, result);
    }

    @Test
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuFalse() {
        // arrange
        DrinksExceptColaSkuCreator creator = new DrinksExceptColaSkuCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        BarDealDetailSkuListModuleOpt.Config config = mock(BarDealDetailSkuListModuleOpt.Config.class);
        // act
        Map<String, String> result = creator.getSubtitlesAttrName2FormatMap(skuItemDto, config, false);
        // assert
        assertEquals("%s", result.get("quantityAvailable"));
        assertEquals("%s", result.get("serviceLimit"));
        assertEquals("%s", result.get("taste"));
        assertEquals("%s%%Vol", result.get("alcoholByVolume"));
        assertEquals("%smL", result.get("volume"));
    }

    @Test
    public void testBuildIconSkuItemDtoIsNull() throws Throwable {
        DrinksExceptColaSkuCreator creator = new DrinksExceptColaSkuCreator();
        String result = creator.buildIcon(null, null, false);
        assertNull(result);
    }

    @Test
    public void testBuildIconAttrItemsIsEmpty() throws Throwable {
        DrinksExceptColaSkuCreator creator = new DrinksExceptColaSkuCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        String result = creator.buildIcon(skuItemDto, null, false);
        assertNull(result);
    }

    @Test
    public void testBuildIconAttrNameIsEmpty() throws Throwable {
        DrinksExceptColaSkuCreator creator = new DrinksExceptColaSkuCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Arrays.asList(new SkuAttrItemDto()));
        String result = creator.buildIcon(skuItemDto, null, false);
        assertNull(result);
    }

    @Test
    public void testBuildIconAttrNotFound() throws Throwable {
        DrinksExceptColaSkuCreator creator = new DrinksExceptColaSkuCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("otherName");
        skuItemDto.setAttrItems(Arrays.asList(attrItem));
        String result = creator.buildIcon(skuItemDto, null, false);
        assertNull(result);
    }
}
