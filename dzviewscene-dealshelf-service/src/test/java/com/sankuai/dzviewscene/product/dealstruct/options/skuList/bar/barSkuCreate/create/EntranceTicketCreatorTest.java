package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import static org.junit.Assert.assertNull;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EntranceTicketCreatorTest {

    /**
     * 测试buildIcon方法是否返回null
     */
    @Test
    public void testBuildIconReturnNull() throws Throwable {
        // arrange
        EntranceTicketCreator entranceTicketCreator = new EntranceTicketCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        boolean isSameCategorySku = false;
        // act
        String result = entranceTicketCreator.buildIcon(skuItemDto, config, isSameCategorySku);
        // assert
        assertNull(result);
    }
}
