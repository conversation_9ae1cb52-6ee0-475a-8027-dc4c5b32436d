package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import static org.junit.Assert.*;

public class EntranceTicketCreator_ExtractCopiesTest {

    /**
     * 测试extractCopies方法，当skuItemDto中的copies字段是一个正整数时
     */
    @Test
    public void testExtractCopiesNormal() throws Throwable {
        // arrange
        EntranceTicketCreator creator = new EntranceTicketCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setCopies(10);
        // act
        String result = creator.extractCopies(skuItemDto, null);
        // assert
        assertEquals("(10份)", result);
    }

    /**
     * 测试extractCopies方法，当skuItemDto中的copies字段是0时
     */
    @Test
    public void testExtractCopiesZero() throws Throwable {
        // arrange
        EntranceTicketCreator creator = new EntranceTicketCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setCopies(0);
        // act
        String result = creator.extractCopies(skuItemDto, null);
        // assert
        assertEquals("(0份)", result);
    }

    /**
     * 测试extractCopies方法，当skuItemDto为null时
     */
    @Test(expected = NullPointerException.class)
    public void testExtractCopiesNull() throws Throwable {
        // arrange
        EntranceTicketCreator creator = new EntranceTicketCreator();
        // act
        creator.extractCopies(null, null);
    }
}
