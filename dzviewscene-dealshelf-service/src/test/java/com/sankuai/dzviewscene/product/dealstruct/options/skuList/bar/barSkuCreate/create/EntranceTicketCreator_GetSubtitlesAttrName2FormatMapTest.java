package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class EntranceTicketCreator_GetSubtitlesAttrName2FormatMapTest {

    /**
     * Test getSubtitlesAttrName2FormatMap when isHitDouhu is false.
     */
    @Test
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuFalse() {
        // arrange
        EntranceTicketCreator creator = new EntranceTicketCreator();
        SkuItemDto skuItemDto = new SkuItemDto();
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        boolean isHitDouhu = false;
        // act
        Map<String, String> result = creator.getSubtitlesAttrName2FormatMap(skuItemDto, config, isHitDouhu);
        // assert
        Assert.assertNull(result);
    }

    /**
     * Test getSubtitlesAttrName2FormatMap when containWine is 否.
     */
    @Test
    public void testGetSubtitlesAttrName2FormatMapContainWineNo() {
        // arrange
        EntranceTicketCreator creator = new EntranceTicketCreator();
        SkuItemDto skuItemDto = Mockito.mock(SkuItemDto.class);
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("containWine");
        attrItem.setAttrValue("否");
        attrItems.add(attrItem);
        Mockito.when(skuItemDto.getAttrItems()).thenReturn(attrItems);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        boolean isHitDouhu = true;
        // act
        Map<String, String> result = creator.getSubtitlesAttrName2FormatMap(skuItemDto, config, isHitDouhu);
        // assert
        Assert.assertNull(result);
    }

    /**
     * Test getSubtitlesAttrName2FormatMap when containWine is 是 and isHitDouhu is true.
     */
    @Test
    public void testGetSubtitlesAttrName2FormatMapContainWineYesAndIsHitDouhuTrue() {
        // arrange
        EntranceTicketCreator creator = new EntranceTicketCreator();
        SkuItemDto skuItemDto = Mockito.mock(SkuItemDto.class);
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("containWine");
        attrItem.setAttrValue("是");
        attrItems.add(attrItem);
        Mockito.when(skuItemDto.getAttrItems()).thenReturn(attrItems);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        boolean isHitDouhu = true;
        // act
        Map<String, String> result = creator.getSubtitlesAttrName2FormatMap(skuItemDto, config, isHitDouhu);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals("含%s酒水", result.get("quantity"));
    }
}
