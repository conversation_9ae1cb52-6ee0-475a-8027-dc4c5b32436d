package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import static org.junit.Assert.*;

public class EntranceTicketCreator_IdeantifyTest {

    private EntranceTicketCreator entranceTicketCreator = new EntranceTicketCreator();

    /**
     * 测试 skuItemDto 为 null 的情况
     */
    @Test
    public void testIidentifySkuItemDtoIsNull() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        assertFalse(entranceTicketCreator.ideantify(null, config));
    }

    /**
     * 测试 skuItemDto 不为 null，但 productCategory 不等于 4050L 的情况
     */
    @Test
    public void testIidentifyProductCategoryNotEqual4050() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4051L);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        assertFalse(entranceTicketCreator.ideantify(skuItemDto, config));
    }

    /**
     * 测试 skuItemDto 不为 null，且 productCategory 等于 4050L 的情况
     */
    @Test
    public void testIidentifyProductCategoryEqual4050() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4050L);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        assertTrue(entranceTicketCreator.ideantify(skuItemDto, config));
    }
}
