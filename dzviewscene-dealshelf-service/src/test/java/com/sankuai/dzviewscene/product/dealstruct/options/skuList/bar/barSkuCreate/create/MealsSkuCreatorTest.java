package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;

@RunWith(MockitoJUnitRunner.class)
public class MealsSkuCreatorTest {

    private MealsSkuCreator mealsSkuCreator = new MealsSkuCreator();

    // Constants representing the attribute names expected by the buildIcon method
    // Assuming these constants are correctly defined and accessible
    private static final String DETAIL_PIC_SKU_ATTR_NAME = "detailPic";

    private static final String NEW_DETAIL_PIC_SKU_ATTR_NAME = "newDetailPic";

    private static final String HEAL_PIC_SKU_ATTR_NAME = "healPic";

    @Test
    public void testBuildIconSkuItemDtoIsNull() throws Throwable {
        String result = mealsSkuCreator.buildIcon(null, null, false);
        assertNull("Expected null when SkuItemDto is null", result);
    }

    @Test
    public void testBuildIconIsSameCategorySkuIsTrueAndAttrItemsNotContainsExpectedDetailPicSkuAttrNames() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("UNEXPECTED_ATTR_NAME");
        skuAttrItemDto.setAttrValue("unexpectedValue");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        String result = mealsSkuCreator.buildIcon(skuItemDto, null, true);
        assertNull("Expected null when attribute names do not match expected detail picture attribute names", result);
    }
}
