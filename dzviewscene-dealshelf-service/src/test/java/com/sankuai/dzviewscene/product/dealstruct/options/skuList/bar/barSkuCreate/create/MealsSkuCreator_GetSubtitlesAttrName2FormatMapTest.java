package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class MealsSkuCreator_GetSubtitlesAttrName2FormatMapTest {

    private MealsSkuCreator mealsSkuCreator = new MealsSkuCreator();

    /**
     * 测试isHitDouhu为true时，返回null
     */
    @Test
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuTrue() {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        boolean isHitDouhu = true;
        // act
        Map<String, String> result = mealsSkuCreator.getSubtitlesAttrName2FormatMap(skuItemDto, config, isHitDouhu);
        // assert
        assertNull(result);
    }

    /**
     * 测试isHitDouhu为false时，返回mealsSubtitlesAttrName2FormatMap
     */
    @Test
    public void testGetSubtitlesAttrName2FormatMapIsHitDouhuFalse() {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        Map<String, String> expectedMap = new HashMap<>();
        expectedMap.put("key", "value");
        config.setMealsSubtitlesAttrName2FormatMap(expectedMap);
        boolean isHitDouhu = false;
        // act
        Map<String, String> result = mealsSkuCreator.getSubtitlesAttrName2FormatMap(skuItemDto, config, isHitDouhu);
        // assert
        assertEquals(expectedMap, result);
    }
}
