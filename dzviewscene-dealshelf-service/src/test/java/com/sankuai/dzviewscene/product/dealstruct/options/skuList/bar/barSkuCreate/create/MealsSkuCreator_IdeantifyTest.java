package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class MealsSkuCreator_IdeantifyTest {

    private MealsSkuCreator mealsSkuCreator = new MealsSkuCreator();

    /**
     * 测试 skuItemDto 为 null 的情况
     */
    @Test
    public void testIdeantifySkuItemDtoIsNull() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setDrinksSkuCateIds(Arrays.asList(1L, 2L, 3L));
        assertFalse(mealsSkuCreator.ideantify(null, config));
    }

    /**
     * 测试 skuItemDto 不为 null，但 productCategory 在 drinksSkuCateIds 中的情况
     */
    @Test
    public void testIdeantifyProductCategoryInDrinksSkuCateIds() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(1L);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setDrinksSkuCateIds(Arrays.asList(1L, 2L, 3L));
        assertFalse(mealsSkuCreator.ideantify(skuItemDto, config));
    }

    /**
     * 测试 skuItemDto 不为 null，且 productCategory 不在 drinksSkuCateIds 中的情况
     */
    @Test
    public void testIdeantifyProductCategoryNotInDrinksSkuCateIds() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4L);
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        config.setDrinksSkuCateIds(Arrays.asList(1L, 2L, 3L));
        assertTrue(mealsSkuCreator.ideantify(skuItemDto, config));
    }
}
