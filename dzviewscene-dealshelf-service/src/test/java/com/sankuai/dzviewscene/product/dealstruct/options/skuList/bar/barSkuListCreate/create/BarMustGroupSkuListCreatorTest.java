package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class BarMustGroupSkuListCreatorTest {

    private BarMustGroupSkuListCreator barMustGroupSkuListCreator = new BarMustGroupSkuListCreator();

    /**
     * 测试 isMustGroupSku 为 true 的情况
     */
    @Test
    public void testIdeantifyIsMustGroupSkuTrue() {
        // arrange
        boolean isMustGroupSku = true;
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        // act
        boolean result = barMustGroupSkuListCreator.ideantify(isMustGroupSku, Collections.emptyList(), config);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isMustGroupSku 为 false 的情况
     */
    @Test
    public void testIdeantifyIsMustGroupSkuFalse() {
        // arrange
        boolean isMustGroupSku = false;
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        // act
        boolean result = barMustGroupSkuListCreator.ideantify(isMustGroupSku, Collections.emptyList(), config);
        // assert
        assertFalse(result);
    }
}
