package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BarMustGroupSkuListCreator_BuildSkuListModulesTest {

    private BarMustGroupSkuListCreator barMustGroupSkuListCreator;

    @Before
    public void setUp() {
        barMustGroupSkuListCreator = new BarMustGroupSkuListCreator();
    }

    private BarDealDetailSkuListModuleOpt.Config createConfig() {
        BarDealDetailSkuListModuleOpt.Config config = new BarDealDetailSkuListModuleOpt.Config();
        List<Long> drinksSkuCateIds = new ArrayList<>();
        // Example category ID that matches the test case
        drinksSkuCateIds.add(1L);
        config.setDrinksSkuCateIds(drinksSkuCateIds);
        config.setMealsSkuCateIds(new ArrayList<>());
        config.setMustDrinksSkusGroupNameFormat("Drinks %s");
        config.setMustMealsSkusGroupNameFormat("Meals %s");
        return config;
    }

    private List<SkuItemDto> createSkuItemDtosWithCategoryAndCopies(Long category, int copies) {
        List<SkuItemDto> skuItemDtos = new ArrayList<>();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(category);
        skuItemDto.setCopies(copies);
        skuItemDtos.add(skuItemDto);
        return skuItemDtos;
    }

    @Test
    public void testBuildSkuListModulesWhenSkuItemDtosAndConfigAreNull() throws Throwable {
        BarDealDetailSkuListModuleOpt.Config config = createConfig();
        List<DealSkuGroupModuleVO> result = barMustGroupSkuListCreator.buildSkuListModules(null, config, 0, false);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildSkuListModulesWhenSkuItemDtosIsNotNullButConfigIsNull() throws Throwable {
        List<SkuItemDto> skuItemDtos = new ArrayList<>();
        skuItemDtos.add(new SkuItemDto());
        BarDealDetailSkuListModuleOpt.Config config = createConfig();
        List<DealSkuGroupModuleVO> result = barMustGroupSkuListCreator.buildSkuListModules(skuItemDtos, config, 0, false);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildSkuListModulesWhenSkuItemDtosIsNotNullAndConfigIsNotNullButNoMatchingSku() throws Throwable {
        List<SkuItemDto> skuItemDtos = new ArrayList<>();
        skuItemDtos.add(new SkuItemDto());
        BarDealDetailSkuListModuleOpt.Config config = createConfig();
        List<DealSkuGroupModuleVO> result = barMustGroupSkuListCreator.buildSkuListModules(skuItemDtos, config, 0, false);
        assertTrue(result.isEmpty());
    }
}
