package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class BarOptionalGroupDrinksSkuListCreatorTest {

    private BarOptionalGroupDrinksSkuListCreator creator;

    private BarDealDetailSkuListModuleOpt.Config config;

    private SkuItemDto skuItemDto;

    @Before
    public void setUp() {
        creator = new BarOptionalGroupDrinksSkuListCreator();
        config = new BarDealDetailSkuListModuleOpt.Config();
        skuItemDto = new SkuItemDto();
    }

    @Test
    public void testIidentifyIsMustGroupSkuTrue() {
        assertFalse(creator.ideantify(true, Collections.singletonList(skuItemDto), config));
    }

    @Test
    public void testIidentifySkuListEmpty() {
        assertFalse(creator.ideantify(false, Collections.emptyList(), config));
    }

    @Test
    public void testIidentifyDrinksSkuCateIdsEmpty() {
        assertFalse(creator.ideantify(false, Collections.singletonList(skuItemDto), config));
    }

    @Test
    public void testIidentifyAllSkusInDrinksSkuCateIds() {
        config.setDrinksSkuCateIds(Collections.singletonList(1L));
        skuItemDto.setProductCategory(1L);
        assertTrue(creator.ideantify(false, Collections.singletonList(skuItemDto), config));
    }

    @Test
    public void testIidentifySomeSkusInDrinksSkuCateIds() {
        config.setDrinksSkuCateIds(Collections.singletonList(1L));
        skuItemDto.setProductCategory(2L);
        assertFalse(creator.ideantify(false, Collections.singletonList(skuItemDto), config));
    }

    @Test
    public void testIidentifyNoSkusInDrinksSkuCateIds() {
        config.setDrinksSkuCateIds(Collections.singletonList(1L));
        skuItemDto.setProductCategory(2L);
        assertFalse(creator.ideantify(false, Collections.singletonList(skuItemDto), config));
    }

    /**
     * Test when hitDouHu=false or config.optionalFormat is empty
     */
    @Test
    public void testBuildSkuListModules_UseDefaultGroupName() throws Throwable {
        // arrange
        BarOptionalGroupDrinksSkuListCreator creator = new BarOptionalGroupDrinksSkuListCreator();
        BarDealDetailSkuListModuleOpt.Config config = Mockito.mock(BarDealDetailSkuListModuleOpt.Config.class);
        // Correctly mock to return a String that matches the format string expectation
        Mockito.when(config.getOptionalDrinksSkusWithSameNumGroupNameFormat()).thenReturn("Default Group Name %s");
        List<SkuItemDto> skuItemDtos = new ArrayList<>();
        skuItemDtos.add(new SkuItemDto());
        // act
        List<DealSkuGroupModuleVO> result = creator.buildSkuListModules(skuItemDtos, config, 2, false);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * Test when hitDouHu=true and config.optionalFormat is not empty
     */
    @Test
    public void testBuildSkuListModules_HitDouHuAndOptionalFormatNotEmpty() throws Throwable {
        // arrange
        BarOptionalGroupDrinksSkuListCreator creator = new BarOptionalGroupDrinksSkuListCreator();
        BarDealDetailSkuListModuleOpt.Config config = Mockito.mock(BarDealDetailSkuListModuleOpt.Config.class);
        Mockito.when(config.getOptionalFormat()).thenReturn("Select %d from %d");
        List<SkuItemDto> skuItemDtos = new ArrayList<>();
        skuItemDtos.add(new SkuItemDto());
        // act
        List<DealSkuGroupModuleVO> result = creator.buildSkuListModules(skuItemDtos, config, 2, true);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        // Adjusted to expect 2 invocations due to the logic in the method under test
        Mockito.verify(config, Mockito.times(2)).getOptionalFormat();
    }

    /**
     * Test when skuItemDtos is empty
     */
    @Test
    public void testBuildSkuListModules_EmptySkuList() throws Throwable {
        // arrange
        BarOptionalGroupDrinksSkuListCreator creator = new BarOptionalGroupDrinksSkuListCreator();
        BarDealDetailSkuListModuleOpt.Config config = Mockito.mock(BarDealDetailSkuListModuleOpt.Config.class);
        // act
        List<DealSkuGroupModuleVO> result = creator.buildSkuListModules(new ArrayList<>(), config, 2, false);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
