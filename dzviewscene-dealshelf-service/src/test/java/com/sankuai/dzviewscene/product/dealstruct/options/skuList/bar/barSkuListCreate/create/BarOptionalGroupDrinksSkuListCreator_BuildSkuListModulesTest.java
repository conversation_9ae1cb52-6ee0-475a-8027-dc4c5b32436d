package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertTrue;
import org.junit.Before;
import static org.junit.Assert.assertEquals;

public class BarOptionalGroupDrinksSkuListCreator_BuildSkuListModulesTest {

    private BarOptionalGroupDrinksSkuListCreator barOptionalGroupDrinksSkuListCreator;

    private List<SkuItemDto> skuItemDtos;

    private BarDealDetailSkuListModuleOpt.Config config;

    /**
     * Test case when skuItemDtos is null.
     */
    @Test
    public void testBuildSkuListModulesSkuItemDtosIsNull() throws Throwable {
        BarOptionalGroupDrinksSkuListCreator creator = new BarOptionalGroupDrinksSkuListCreator();
        BarDealDetailSkuListModuleOpt.Config config = Mockito.mock(BarDealDetailSkuListModuleOpt.Config.class);
        List<DealSkuGroupModuleVO> result = creator.buildSkuListModules(null, config, 2, false);
        assertTrue(result.isEmpty());
    }
}
