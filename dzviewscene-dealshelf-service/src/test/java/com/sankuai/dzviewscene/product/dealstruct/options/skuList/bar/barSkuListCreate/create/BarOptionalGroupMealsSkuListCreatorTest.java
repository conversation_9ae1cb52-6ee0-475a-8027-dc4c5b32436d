package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertEquals;

public class BarOptionalGroupMealsSkuListCreatorTest {

    private BarOptionalGroupMealsSkuListCreator barOptionalGroupMealsSkuListCreator;

    private List<SkuItemDto> skuItemDtos;

    private BarDealDetailSkuListModuleOpt.Config config;

    @Before
    public void setUp() {
        barOptionalGroupMealsSkuListCreator = new BarOptionalGroupMealsSkuListCreator();
        skuItemDtos = new ArrayList<>();
        config = Mockito.mock(BarDealDetailSkuListModuleOpt.Config.class);
    }

    /**
     * 测试skuItemDtos为空的情况
     */
    @Test
    public void testBuildSkuListModulesSkuItemDtosIsNull() throws Throwable {
        List<DealSkuGroupModuleVO> result = barOptionalGroupMealsSkuListCreator.buildSkuListModules(null, config, 1, false);
        assertTrue(result.isEmpty());
    }
}
