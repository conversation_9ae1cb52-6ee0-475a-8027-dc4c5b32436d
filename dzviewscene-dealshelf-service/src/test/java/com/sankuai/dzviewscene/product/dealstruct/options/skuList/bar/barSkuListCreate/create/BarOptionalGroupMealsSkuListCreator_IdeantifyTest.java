package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Before;
import org.junit.Test;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class BarOptionalGroupMealsSkuListCreator_IdeantifyTest {

    private BarOptionalGroupMealsSkuListCreator creator;

    private BarDealDetailSkuListModuleOpt.Config config;

    private SkuItemDto sku1;

    private SkuItemDto sku2;

    @Before
    public void setUp() {
        creator = new BarOptionalGroupMealsSkuListCreator();
        config = new BarDealDetailSkuListModuleOpt.Config();
        sku1 = new SkuItemDto();
        sku1.setProductCategory(1L);
        sku2 = new SkuItemDto();
        sku2.setProductCategory(2L);
    }

    @Test
    public void testIdeantifyIsMustGroupSkuTrue() {
        assertFalse(creator.ideantify(true, Arrays.asList(sku1, sku2), config));
    }

    @Test
    public void testIdeantifySkuListEmpty() {
        assertFalse(creator.ideantify(false, Collections.emptyList(), config));
    }

    @Test
    public void testIdeantifyMealsSkuCateIdsEmpty() {
        assertFalse(creator.ideantify(false, Arrays.asList(sku1, sku2), config));
    }

    @Test
    public void testIdeantifyAllSkusInMealsSkuCateIds() {
        config.setMealsSkuCateIds(Arrays.asList(1L, 2L));
        assertTrue(creator.ideantify(false, Arrays.asList(sku1, sku2), config));
    }

    @Test
    public void testIdeantifySomeSkusInMealsSkuCateIds() {
        config.setMealsSkuCateIds(Collections.singletonList(1L));
        assertFalse(creator.ideantify(false, Arrays.asList(sku1, sku2), config));
    }

    @Test
    public void testIdeantifyNoSkusInMealsSkuCateIds() {
        config.setMealsSkuCateIds(Collections.singletonList(3L));
        assertFalse(creator.ideantify(false, Arrays.asList(sku1, sku2), config));
    }
}
