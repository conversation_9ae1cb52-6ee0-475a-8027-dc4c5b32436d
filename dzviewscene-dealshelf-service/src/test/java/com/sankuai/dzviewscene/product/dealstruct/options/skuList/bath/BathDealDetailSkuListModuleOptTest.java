package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath;
import com.google.common.collect.Maps;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealAdditionalProjectM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class BathDealDetailSkuListModuleOptTest {

    private BathDealDetailSkuListModuleOpt bathDealDetailSkuListModuleOpt;
    private BathDealDetailSkuListModuleOpt.Config config;
    private ActivityCxt context;
    private BathDealDetailSkuListModuleOpt.Param param;
    private DealDetailInfoModel dealDetailInfoModel;

    @Before
    public void setUp() {
        bathDealDetailSkuListModuleOpt = new BathDealDetailSkuListModuleOpt();
        config = new BathDealDetailSkuListModuleOpt.Config();
        config.setTagFormatMap(Collections.singletonMap("标签1", "格式化后的值：%s"));
        config.setTitleReplaceList(Collections.singletonList("去除的字符"));

        context = mock(ActivityCxt.class);
        param = mock(BathDealDetailSkuListModuleOpt.Param.class);
        dealDetailInfoModel = mock(DealDetailInfoModel.class);

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
    }

    @Test
    public void testComputeWithEmptyData() {
        // 模拟空数据情况
        when(dealDetailInfoModel.getAdditionalProjectList()).thenReturn(Collections.emptyList());
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Collections.emptyList());

        List<DealDetailSkuListModuleGroupModel> result = bathDealDetailSkuListModuleOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty() || result.stream().allMatch(Objects::nonNull));
    }

    @Test
    public void testComputeWithFullData() {
        // 模拟具有完整数据的情况
        when(dealDetailInfoModel.getAdditionalProjectList()).thenReturn(mockData());
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Arrays.asList(mock(AttrM.class)));

        List<DealDetailSkuListModuleGroupModel> result = bathDealDetailSkuListModuleOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.stream().anyMatch(module -> "服务流程模块".equals(module.getGroupName()) || "服务设施模块".equals(module.getGroupName())));
    }

    private List<DealAdditionalProjectM> mockData() {
        List<DealAdditionalProjectM> result = Lists.newArrayList();
        DealAdditionalProjectM a1 = new DealAdditionalProjectM();
        a1.setProductId(1L);
        a1.setClassification("按摩");
        a1.setSkuId(1L);
        a1.setProductType(1);
        a1.setItemName("");
        a1.setProductTagMap(Maps.newHashMap());
        a1.setMarketPrice(new BigDecimal("0"));
        a1.setSalePrice(new BigDecimal("0"));
        a1.setSalesCnt(0);
        result.add(a1);

        DealAdditionalProjectM a2 = new DealAdditionalProjectM();
        a2.setProductId(2L);
        a2.setClassification("足疗");
        a2.setSkuId(0L);
        a2.setProductType(0);
        a2.setItemName("");
        a2.setProductTagMap(Maps.newHashMap());
        a2.setMarketPrice(new BigDecimal("0"));
        a2.setSalePrice(new BigDecimal("0"));
        a2.setSalesCnt(0);
        return result;
    }

}
