package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath;

import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.BathDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class BathDealDetailSkuListModuleOpt_BuildDealDetailSkuListModuleGroupModelTest {

    private BathDealDetailSkuListModuleOpt bathDealDetailSkuListModuleOpt;

    @Before
    public void setUp() {
        bathDealDetailSkuListModuleOpt = new BathDealDetailSkuListModuleOpt();
    }

    /**
     * 测试buildDealDetailSkuListModuleGroupModel方法，当dealDetailInfoModel为null时
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelWhenDealDetailInfoModelIsNull() {
        DealDetailSkuListModuleGroupModel result = bathDealDetailSkuListModuleOpt.buildDealDetailSkuListModuleGroupModel("groupName", "subTitle", null);
        Assert.assertNotNull(result);
        Assert.assertNull(result.getGroupTitle());
    }

    /**
     * 测试buildDealDetailSkuListModuleGroupModel方法，当dealDetailInfoModel不为null，但不是团购次卡时
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelWhenDealDetailInfoModelIsNotNullButNotTimesDeal() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setTradeType(18);
        DealDetailSkuListModuleGroupModel result = bathDealDetailSkuListModuleOpt.buildDealDetailSkuListModuleGroupModel("groupName", "subTitle", dealDetailInfoModel);
        Assert.assertNotNull(result);
        Assert.assertNull(result.getGroupTitle());
    }

    /**
     * 测试buildDealDetailSkuListModuleGroupModel方法，当dealDetailInfoModel不为null，是团购次卡时
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelWhenDealDetailInfoModelIsNotNullAndIsTimesDeal() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setTradeType(19);
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("sys_multi_sale_number", "10"));
        dealDetailInfoModel.setDealAttrs(dealAttrs);
        DealDetailSkuListModuleGroupModel result = bathDealDetailSkuListModuleOpt.buildDealDetailSkuListModuleGroupModel("groupName", "subTitle", dealDetailInfoModel);
        Assert.assertNotNull(result);
        Assert.assertEquals("每次套餐详情（共10次）", result.getGroupTitle());
    }
}
