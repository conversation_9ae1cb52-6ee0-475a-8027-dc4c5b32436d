package com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractBeautyYogaModuleOptTest {

    /**
     * Tests the buildDealSkuItemVO method under normal conditions.
     */
    @Test
    public void testBuildDealSkuItemVONormal() throws Throwable {
        // Arrange
        AbstractBeautyYogaModuleOpt<Object> abstractBeautyYogaModuleOpt = new AbstractBeautyYogaModuleOpt<Object>() {

            @Override
            protected List<com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO> buildServiceItemsByServiceType(String serviceType, java.util.List<com.sankuai.dzviewscene.shelf.platform.common.model.AttrM> dealAttrs, com.alibaba.fastjson.JSONObject unitJSON, String optionStr, BeautyYogaDealModuleOpt.Config config) {
                return null;
            }

            @Override
            protected com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String subTitle, java.util.List<com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO> dealSkuList) {
                return null;
            }

            @Override
            protected com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String groupTitle, String subTitle, java.util.List<com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO> dealSkuList) {
                return null;
            }

            @Override
            protected DealSkuItemVO buildDealSkuItemVO(String name, int type, List<SkuAttrAttrItemVO> skuAttrAttrItemVOS, String value) {
                DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
                dealSkuItemVO.setName(name);
                dealSkuItemVO.setType(type);
                dealSkuItemVO.setValueAttrs(skuAttrAttrItemVOS);
                dealSkuItemVO.setValue(value);
                return dealSkuItemVO;
            }

            @Override
            public List<com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel> compute(com.sankuai.athena.viewscene.framework.ActivityCxt context, com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP.Param param, Object config) {
                // Mock implementation for the abstract method
                return null;
            }
        };
        String name = "testName";
        int type = 1;
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        skuAttrAttrItemVO.setInfo(Arrays.asList("testInfo"));
        String value = "testValue";
        // Act
        DealSkuItemVO result = abstractBeautyYogaModuleOpt.buildDealSkuItemVO(name, type, Arrays.asList(skuAttrAttrItemVO), value);
        // Assert
        assertNotNull(result);
        assertEquals(name, result.getName());
        assertEquals(type, result.getType());
        assertEquals(value, result.getValue());
        assertEquals(skuAttrAttrItemVO.getInfo(), result.getValueAttrs().get(0).getInfo());
    }

    /**
     * Tests the buildDealSkuItemVO method with null parameters.
     */
    @Test
    public void testBuildDealSkuItemVONull() throws Throwable {
        // Arrange
        AbstractBeautyYogaModuleOpt<Object> abstractBeautyYogaModuleOpt = new AbstractBeautyYogaModuleOpt<Object>() {

            @Override
            protected List<com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO> buildServiceItemsByServiceType(String serviceType, java.util.List<com.sankuai.dzviewscene.shelf.platform.common.model.AttrM> dealAttrs, com.alibaba.fastjson.JSONObject unitJSON, String optionStr, BeautyYogaDealModuleOpt.Config config) {
                return null;
            }

            @Override
            protected com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String subTitle, java.util.List<com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO> dealSkuList) {
                return null;
            }

            @Override
            protected com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String groupTitle, String subTitle, java.util.List<com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO> dealSkuList) {
                return null;
            }

            @Override
            protected DealSkuItemVO buildDealSkuItemVO(String name, int type, List<SkuAttrAttrItemVO> skuAttrAttrItemVOS, String value) {
                DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
                dealSkuItemVO.setName(name);
                dealSkuItemVO.setType(type);
                dealSkuItemVO.setValueAttrs(skuAttrAttrItemVOS);
                dealSkuItemVO.setValue(value);
                return dealSkuItemVO;
            }

            @Override
            public List<com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel> compute(com.sankuai.athena.viewscene.framework.ActivityCxt context, com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP.Param param, Object config) {
                // Mock implementation for the abstract method
                return null;
            }
        };
        String name = null;
        int type = 0;
        String value = null;
        // Act
        DealSkuItemVO result = abstractBeautyYogaModuleOpt.buildDealSkuItemVO(name, type, null, value);
        // Assert
        assertNotNull(result);
        assertNull(result.getName());
        assertEquals(type, result.getType());
        assertNull(result.getValue());
        assertNull(result.getValueAttrs());
    }
}
