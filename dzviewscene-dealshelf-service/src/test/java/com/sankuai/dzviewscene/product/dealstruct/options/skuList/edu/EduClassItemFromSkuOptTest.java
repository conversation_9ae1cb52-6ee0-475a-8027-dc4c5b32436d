package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.EduSkuUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EduClassItemFromSkuOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @Mock
    private DealDetailDtoModel dealDetailDtoModel;

    @Mock
    private StandardServiceProjectDTO standardServiceProjectDTO;

    private MockedStatic<DealDetailUtils> dealDetailUtilsMockedStatic;

    private MockedStatic<EduSkuUtils> eduSkuUtilsMockedStatic;

    @Before
    public void onBefore() {
        dealDetailUtilsMockedStatic = Mockito.mockStatic(DealDetailUtils.class);
        eduSkuUtilsMockedStatic = Mockito.mockStatic(EduSkuUtils.class);

        when(dealDetailInfoModel.getStandardServiceProjectDTO()).thenReturn(standardServiceProjectDTO);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
    }

    @After
    public void onAfter() {
        dealDetailUtilsMockedStatic.close();
        eduSkuUtilsMockedStatic.close();
    }

    /**
     * 测试compute方法，当入参没有值时
     * 期望返回null
     */
    @Test
    public void testCompute_nullParam() {
        // arrange
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();
        dealDetailDtoModel = new DealDetailDtoModel();
        standardServiceProjectDTO = new StandardServiceProjectDTO();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, new EduClassItemFromSkuOpt.Config());

        // assert
        assertNull(result);
    }

    /**
     * 测试空的attr
     * 期望返回null
     */
    @Test
    public void testCompute_null_attr() {
        // arrange
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                standardServiceProjectDTO, dealDetailDtoModel, "")).thenReturn(null);

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试短期课的课程内容
     * 期望返回课程内容
     */
    @Test
    public void testCompute_buildCourseListItemForSortClass_returnCourseListItem() {
        // arrange
        String classJson = "[{\"educontent\":\"理论课\"},{\"educontent\":\"实践课\"},{\"educontent\":\"实践课\"}]";
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.EDU_SERVICE_ITEM_TABLE))
                .thenReturn(classJson);
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.EDU_COURSE_TYPE))
                .thenReturn(EduClassItemFromSkuOpt.EDU_COURSE_TYPE_SHORT_COURSE);
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, "count"))
                .thenReturn("2");
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");
        DealDetailSkuUniStructuredDto structuredDto = Mockito.mock(DealDetailSkuUniStructuredDto.class);
        when(dealDetailDtoModel.getSkuUniStructuredDto()).thenReturn(structuredDto);
        when(structuredDto.getMarketPrice()).thenReturn("200");

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setShowItems(Lists.newArrayList("课程内容"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue("2节".equals(dealSkuVO.getSubTitle()));
        assertTrue("¥200".equals(dealSkuVO.getPrice()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "课程内容".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValueAttrs().get(0).getName().equals("理论课"));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValueAttrs().get(1).getName().equals("实践课"));
    }

    /**
     * 测试夜校的课程内容
     * 期望返回课程内容
     */
    @Test
    public void testCompute_buildCourseListItemForNightSchool_returnCourseListItem() {
        // arrange
        String classJson = "[{\"educontent\":\"理论课\"},{\"educontent\":\"实践课\"},{\"educontent\":\"实践课\"}]";
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.ATTR_CLASS_CONTENT_NIGHT_SCHOOL))
                .thenReturn(classJson);
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("课程内容"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "课程内容".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValueAttrs().get(0).getName().equals("理论课"));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValueAttrs().get(1).getName().equals("实践课"));
    }

    /**
     * 测试开班时间
     * 期望返回开班时间
     */
    @Test
    public void testCompute_buildOpenClassTimeSkuItem_returnOpenClassTime() {
        // arrange
        DealSkuItemVO openClassSkuItem = Mockito.mock(DealSkuItemVO.class);
        when(openClassSkuItem.getName()).thenReturn("开班时间");
        eduSkuUtilsMockedStatic.when(() -> EduSkuUtils.buildOpenClassTimeSkuItem(dealDetailInfoModel))
                .thenReturn(openClassSkuItem);
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("开班时间"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "开班时间".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).equals(openClassSkuItem));
    }

    /**
     * 测试短期课的课程安排
     * 期望返回课程安排
     */
    @Test
    public void testCompute_buildCoursePlanItemForSortClass_returnCoursePlan() {
        // arrange
        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("课程安排"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();
        // 课程类型
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.EDU_COURSE_TYPE))
                .thenReturn(EduClassItemFromSkuOpt.EDU_COURSE_TYPE_SHORT_COURSE);
        // 班型
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), config.getClassTypeSkuAttrKeys().get(0)))
                .thenReturn("1v1");
        // 授课地点
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.SERVICE_POSITION))
                .thenReturn("网上");
        // 科目
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), config.getClassSubjectAttrKeys().get(0)))
                .thenReturn("钢琴");
        // 课时数
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), config.getClassCountAttrKeys().get(0)))
                .thenReturn("20节");
        // 课时时长
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), config.getClassDurationAttrKeys().get(0)))
                .thenReturn("40");
        // 消课周期
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.COURSE_DURATION_NUMBER))
                .thenReturn("5");
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "课程安排".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "1v1网上钢琴20节课(每节40分钟)，5天内完成课程学习".equals(dealSkuVO.getItems().get(0).getValue()));
    }

    /**
     * 测试夜校的课程安排
     * 期望返回课程安排
     */
    @Test
    public void testCompute_buildCoursePlanItemForNightSchool_returnCoursePlan() {
        // arrange
        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("课程安排"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.CLASS_FREQUENCY))
                .thenReturn("1");
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), config.getClassDurationAttrKeys().get(0)))
                .thenReturn("50");
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "课程安排".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValue().equals("每周1次，每次50分钟"));
    }

    /**
     * 测试课程目标
     * 使用平台统一
     * 期望返回课程目标
     */
    @Test
    public void testCompute_buildCourseTargetItemAsPlatform_returnCourseTarget() {
        // arrange
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.LEARNING_OBJECTIVE))
                .thenReturn(EduClassItemFromSkuOpt.OBJECT_AS_PLATFORM);
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.CLASS_HIGHLIGHTS))
                .thenReturn("课程目标1");
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("课程目标"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "课程目标".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValue().equals("课程目标1"));
    }

    /**
     * 测试课程目标
     * 使用自定义
     * 期望返回课程目标
     */
    @Test
    public void testCompute_buildCourseTargetItemAsSelf_returnCourseTarget() {
        // arrange
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.LEARNING_OBJECTIVE))
                .thenReturn(EduClassItemFromSkuOpt.OBJECT_AS_SELF);
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.CLASS_NATURE))
                .thenReturn("课程目标1");
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("课程目标"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "课程目标".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValue().equals("课程目标1"));
    }

    /**
     * 测试材料费信息，不需要展示
     * 期望返回null
     */
    @Test
    public void testCompute_buildMaterialFeeItemAsNoFee_returnMaterialFee() {
        // arrange
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.MATERIAL_COST_EXPLAIN))
                .thenReturn(EduClassItemFromSkuOpt.NOT_NEED_MATERIAL_FEE);

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("材料费信息"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试材料费信息
     * 使用免费材料
     * 期望返回材料费信息
     */
    @Test
    public void testCompute_buildMaterialFeeItemAsGift_returnMaterialFee() {
        // arrange
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.MATERIAL_COST_EXPLAIN))
                .thenReturn("存在说明");
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.GIFT_FOR_NIGHT_SCHOOL))
                .thenReturn("免费赠送xxx");
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("材料费信息"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "材料费信息".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValue().equals("免费赠送xxx"));
    }

    /**
     * 测试材料费信息
     * 存在额外费用
     * 期望返回材料费信息
     */
    @Test
    public void testCompute_buildMaterialFeeItemAsOtherFee_returnMaterialFee() {
        // arrange
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.MATERIAL_COST_EXPLAIN))
                .thenReturn("存在费用");
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.OTHER_FEE))
                .thenReturn("下面项目收费：xxx");
        when(dealDetailInfoModel.getDealTitle()).thenReturn("title");

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("材料费信息"));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue("title".equals(dealSkuVO.getTitle()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && "材料费信息".equals(dealSkuVO.getItems().get(0).getName()));
        assertTrue(CollectionUtils.isNotEmpty(dealSkuVO.getItems()) && dealSkuVO.getItems().get(0).getValue().equals("下面项目收费：xxx"));
    }


    /**
     * 测试模块标题
     * 使用sku的属性值
     * 期望模块标题
     */
    @Test
    public void testModuleTitle_givenSkuAttr_returnSkuAttr() {
        // arrange
        String skuAttrName = "skuAttrName";
        String skuAttrValue = "skuAttrValue";
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.MATERIAL_COST_EXPLAIN))
                .thenReturn("存在说明");
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.GIFT_FOR_NIGHT_SCHOOL))
                .thenReturn("免费赠送xxx");
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), skuAttrName))
                .thenReturn(skuAttrValue);

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("材料费信息"));
        config.setModelNameFromSkuAttrKeys(Lists.newArrayList(skuAttrName));
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue(skuAttrValue.equals(dealSkuVO.getTitle()));
    }

    /**
     * 测试模块标题
     * 使用sku的name
     * 期望模块标题
     */
    @Test
    public void testModuleTitle_givenSkuAttr_returnSkuName() {
        // arrange
        String skuAttrName = "skuAttrName";
        String skuAttrValue = "skuAttrValue";
        String skuName = "skuName";
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(
                        standardServiceProjectDTO, dealDetailDtoModel, EduClassItemFromSkuOpt.MATERIAL_COST_EXPLAIN))
                .thenReturn("存在说明");
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), EduClassItemFromSkuOpt.GIFT_FOR_NIGHT_SCHOOL))
                .thenReturn("免费赠送xxx");
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                        dealDetailInfoModel.getDealDetailDtoModel(), skuAttrName))
                .thenReturn(skuAttrValue);

        SkuItemDto skuItemDto = Mockito.mock(SkuItemDto.class);
        when(skuItemDto.getName()).thenReturn(skuName);
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.findFirstSkuItemWhoHasAttr(
                        dealDetailInfoModel.getDealDetailDtoModel(), skuAttrName))
                .thenReturn(skuItemDto);

        EduClassItemFromSkuOpt.Config config = new EduClassItemFromSkuOpt.Config();
        config.setShowCoursePrice(true);
        config.setJustShowOnShortCourse(false);
        config.setShowItems(Lists.newArrayList("材料费信息"));
        config.setModelNameFromSkuAttrKeys(Lists.newArrayList("errorAttr"));
        config.setModelNameFromSkuNameShoHasAttrKey(skuAttrName);
        EduClassItemFromSkuOpt opt = new EduClassItemFromSkuOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue(skuName.equals(dealSkuVO.getTitle()));
    }
}
