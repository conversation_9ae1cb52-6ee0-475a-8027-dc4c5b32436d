package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.dzviewscene.product.ability.options.LoadDealTeacherAndTrialClassOpt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.EduSkuUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EduOnlineDealClassItemSkuListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    /**
     * 测试compute方法，当入参没有值时
     * 期望返回null
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsEmpty() {
        // arrange
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, new EduOnlineDealClassItemSkuListOpt.Config());

        // assert
        assertNull(result);
    }

    /**
     * 测试点评侧试听课链接
     * 期望返回点评链接
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForDP() {
        // arrange
        List<EduOnlineDealClassItemSkuListOpt.CoursePlan> coursePlanList = getCoursePlans();

        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));

        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");

        addTeacherClassValue(context, 0);

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));
        when(dealDetailInfoModel.getDealId()).thenReturn(101010);
        when(context.getParam(ProductDetailActivityConstants.Params.platform)).thenReturn(100);
        when(context.getParam(ProductDetailActivityConstants.Params.dpPoiIdL)).thenReturn(100L);
        when(context.getParam(ProductDetailActivityConstants.Params.mtPoiIdL)).thenReturn(200L);
        when(context.getParam(ProductDetailActivityConstants.Params.shopUuid)).thenReturn("shopUuid");

        EduOnlineDealClassItemSkuListOpt.Config config = new EduOnlineDealClassItemSkuListOpt.Config();
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue(dealSkuVO.getJumpUrl() != null && dealSkuVO.getJumpUrl().getUrl().equals("dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=101010&shopid=100&shopuuid=shopUuid"));

        assertTrue(dealSkuVO.getTitle().equals("method"));
        assertTrue(dealSkuVO.getSubTitle().equals("10.5课时"));
        assertTrue(dealSkuVO.getItems().size() == 1 && dealSkuVO.getItems().get(0).getValueAttrs().size() == 2);
        assertTrue(dealSkuVO.getItems().get(0).getName().equals("课程内容"));
        assertTrue(dealSkuVO.getItems().get(0).getValueAttrs().get(0).getName().equals("测试1"));
        assertTrue(dealSkuVO.getItems().get(0).getValueAttrs().get(0).getInfo().get(0).equals("10课时"));
        assertTrue(dealSkuVO.getItems().get(0).getValueAttrs().get(1).getName().equals("测试2"));
        assertTrue(dealSkuVO.getItems().get(0).getValueAttrs().get(1).getInfo().get(0).equals("0.5课时"));
    }

    private void addTeacherClassValue(ActivityCxt context, int status) {
        List<EduTechnicianVideoDTO> technicianVideoDTOList = new ArrayList<>();
        EduTechnicianVideoDTO videoDTO = new EduTechnicianVideoDTO();
        videoDTO.setId(484L);
        videoDTO.setStatus(status);
        technicianVideoDTOList.add(videoDTO);
        when(context.getParam(LoadDealTeacherAndTrialClassOpt.CODE)).thenReturn(technicianVideoDTOList);
    }

    /**
     * 测试美团侧试听课链接
     * 期望返回美团链接
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForMT() {
        // arrange
        List<EduOnlineDealClassItemSkuListOpt.CoursePlan> coursePlanList = getCoursePlans();

        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));

        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");

        addTeacherClassValue(context, 0);

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));
        when(dealDetailInfoModel.getDealId()).thenReturn(101010);
        when(context.getParam(ProductDetailActivityConstants.Params.platform)).thenReturn(201);
        when(context.getParam(ProductDetailActivityConstants.Params.dpPoiIdL)).thenReturn(100L);
        when(context.getParam(ProductDetailActivityConstants.Params.mtPoiIdL)).thenReturn(200L);
        when(context.getParam(ProductDetailActivityConstants.Params.shopUuid)).thenReturn("shopUuid");

        EduOnlineDealClassItemSkuListOpt.Config config = new EduOnlineDealClassItemSkuListOpt.Config();
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue(dealSkuVO.getJumpUrl() != null && dealSkuVO.getJumpUrl().getUrl().equals("imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=101010&shopid=200"));
    }

    /**
     * 测试小程序的试听课链接
     * 期望不返回链接
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForXCX() {
        // arrange
        List<EduOnlineDealClassItemSkuListOpt.CoursePlan> coursePlanList = getCoursePlans();

        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));

        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");

        addTeacherClassValue(context, 0);

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));
        when(context.getParam(ProductDetailActivityConstants.Params.mpSource)).thenReturn("xcx");

        EduOnlineDealClassItemSkuListOpt.Config config = new EduOnlineDealClassItemSkuListOpt.Config();
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue(dealSkuVO.getJumpUrl() == null);
    }

    /**
     * 测试试听课的状态是无效的情况
     * 期望不返回链接
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForMT_noJumpUrl() {
        // arrange
        List<EduOnlineDealClassItemSkuListOpt.CoursePlan> coursePlanList = getCoursePlans();

        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));

        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");

        addTeacherClassValue(context, 1);

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));

        EduOnlineDealClassItemSkuListOpt.Config config = new EduOnlineDealClassItemSkuListOpt.Config();
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);
        assertTrue(dealSkuVO.getJumpUrl() == null);
    }

    @NotNull
    private List<EduOnlineDealClassItemSkuListOpt.CoursePlan> getCoursePlans() {
        List<EduOnlineDealClassItemSkuListOpt.CoursePlan> coursePlanList = new ArrayList<>();
        EduOnlineDealClassItemSkuListOpt.CoursePlan plan1 = new EduOnlineDealClassItemSkuListOpt.CoursePlan();
        plan1.setCourseModule("测试1");
        plan1.setCourseTimeNum(new BigDecimal(10));
        coursePlanList.add(plan1);
        EduOnlineDealClassItemSkuListOpt.CoursePlan plan2 = new EduOnlineDealClassItemSkuListOpt.CoursePlan();
        plan2.setCourseModule("测试2");
        plan2.setCourseTimeNum(new BigDecimal(0.5));
        coursePlanList.add(plan2);
        return coursePlanList;
    }

    /**
     * 测试开课时间：随到随开
     * 期望返回随到随开
     */
    @Test
    public void testOpenClassTimeAsShowAttrValue() {
        // arrange
        List<EduOnlineDealClassItemSkuListOpt.CoursePlan> coursePlanList = getCoursePlans();

        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));

        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");

        addTeacherClassValue(context, 1);

        AttrM openClassAttr = new AttrM();
        openClassAttr.setName(EduSkuUtils.ATTR_OPEN_CLASS_TYPE);
        openClassAttr.setValue("随到随开");

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(openClassAttr, courseAttr, methodAttr));

        EduOnlineDealClassItemSkuListOpt.Config config = new EduOnlineDealClassItemSkuListOpt.Config();
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);

        assertTrue(dealSkuVO.getItems().size() == 2);
        assertTrue(dealSkuVO.getItems().get(0).getName().equals("开班时间"));
        assertTrue(dealSkuVO.getItems().get(0).getValue().equals("随到随开"));
    }

    /**
     * 测试开课时间：指定时间
     * 期望返回指定的时间，按照、分割
     */
    @Test
    public void testOpenClassTimeAsShowTimeList() {
        // arrange
        List<EduOnlineDealClassItemSkuListOpt.CoursePlan> coursePlanList = getCoursePlans();

        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));

        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemSkuListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");

        addTeacherClassValue(context, 1);

        AttrM openClassAttr = new AttrM();
        openClassAttr.setName(EduSkuUtils.ATTR_OPEN_CLASS_TYPE);
        openClassAttr.setValue("指定日期");

        AttrM openClassTimeAttr = new AttrM();
        openClassTimeAttr.setName(EduSkuUtils.ATTR_OPEN_CLASS_TIME);
        openClassTimeAttr.setValue(JsonCodec.encodeWithUTF8(Lists.newArrayList("2024.04.02", "2023.05.03", "2023.09.10")));

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(openClassAttr, openClassTimeAttr, courseAttr, methodAttr));

        EduOnlineDealClassItemSkuListOpt.Config config = new EduOnlineDealClassItemSkuListOpt.Config();
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1
                && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 1);
        DealSkuVO dealSkuVO = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0);

        assertTrue(dealSkuVO.getItems().size() == 2);
        assertTrue(dealSkuVO.getItems().get(0).getName().equals("开班时间"));
        assertTrue(dealSkuVO.getItems().get(0).getValue().equals("2024.04.02、2023.05.03、2023.09.10，多期可选"));
    }

    /**
     * 测试开课时间：没有课程信息吗，只有开课时间
     * 期望返回null
     */
    @Test
    public void testOpenClassTimeAsJustHasOpenClassData() {
        // arrange
        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduSkuUtils.ATTR_OPEN_CLASS_TYPE);
        courseAttr.setValue("随到随开");

        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr));

        EduOnlineDealClassItemSkuListOpt.Config config = new EduOnlineDealClassItemSkuListOpt.Config();
        EduOnlineDealClassItemSkuListOpt opt = new EduOnlineDealClassItemSkuListOpt();

        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);

        // assert
        assertNull(result );
    }

}
