package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP.Param;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu.EduProgrammeDealGiftSkuListOpt.Config;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;
import org.junit.Before;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;

@RunWith(MockitoJUnitRunner.class)
public class EduProgrammeDealGiftSkuListOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param param;

    @Mock
    private Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    private EduProgrammeDealGiftSkuListOpt opt = new EduProgrammeDealGiftSkuListOpt();

    @Test
    public void testComputeDealDetailInfoModelIsNull() throws Throwable {
        when(param.getDealDetailInfoModel()).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        assertNull("Expected null result when DealDetailInfoModel is null", result);
    }
}
