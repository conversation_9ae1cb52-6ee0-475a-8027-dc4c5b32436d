package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class // Other test methods remain unchanged
AbstractFootMessageModuleOptGetServiceFlowDealSkuItemVOTest {

    @Spy
    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = new AbstractFootMessageModuleOpt() {

        @Override
        public DealSkuItemVO compute(ActivityCxt context, Object request, Object config) {
            // Mock implementation for the abstract method
            return new DealSkuItemVO();
        }
    };

    // Test method for when StepTime is greater than zero
    @Test
    public void testGetServiceFlowDealSkuItemVO_WhenStepTimeGreaterThanZero() throws Throwable {
        ServiceFlowParseModel model = new ServiceFlowParseModel();
        model.setBodyPart("body");
        model.setServicemethod("method");
        model.setStepTime(30);
        DealSkuItemVO expectedDealSkuItem = new DealSkuItemVO();
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        // Set the info field directly
        skuAttrAttrItemVO.setInfo(Lists.newArrayList("30分钟"));
        List<SkuAttrAttrItemVO> valueAttrs = Collections.singletonList(skuAttrAttrItemVO);
        expectedDealSkuItem.setValueAttrs(valueAttrs);
        // Correctly mock the buildDealSkuItemVO method to return the expected DealSkuItemVO object
        doReturn(expectedDealSkuItem).when(abstractFootMessageModuleOpt).buildDealSkuItemVO(anyString(), anyInt(), any(), any());
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceFlowDealSkuItemVO(Lists.newArrayList(model));
        assertNotNull(result);
        assertEquals(1, result.size());
        SkuAttrAttrItemVO resultSkuAttrAttrItemVO = result.get(0).getValueAttrs().get(0);
        assertNull(resultSkuAttrAttrItemVO.getName());
        assertEquals(Lists.newArrayList("30分钟"), resultSkuAttrAttrItemVO.getInfo());
    }

    // Test method for when BodyPart and ServiceMethod are null
    @Test
    public void testGetServiceFlowDealSkuItemVO_WhenBodyPartAndServiceMethodAreNull() throws Throwable {
        ServiceFlowParseModel model = new ServiceFlowParseModel();
        model.setBodyPart(null);
        model.setServicemethod(null);
        model.setStepTime(0);
        DealSkuItemVO expectedDealSkuItem = new DealSkuItemVO();
        List<SkuAttrAttrItemVO> valueAttrs = Collections.singletonList(new SkuAttrAttrItemVO());
        expectedDealSkuItem.setValueAttrs(valueAttrs);
        doReturn(expectedDealSkuItem).when(abstractFootMessageModuleOpt).buildDealSkuItemVO(anyString(), anyInt(), any(), any());
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceFlowDealSkuItemVO(Lists.newArrayList(model));
        assertNotNull(result);
        assertEquals(1, result.size());
        SkuAttrAttrItemVO skuAttrAttrItemVO = result.get(0).getValueAttrs().get(0);
        assertNull(skuAttrAttrItemVO.getName());
        assertNull(skuAttrAttrItemVO.getInfo());
    }

    // Test method for when all items are filtered out
    @Test
    public void testGetServiceFlowDealSkuItemVO_WhenAllItemsFilteredOut() throws Throwable {
        List<ServiceFlowParseModel> models = Collections.emptyList();
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceFlowDealSkuItemVO(models);
        assertNull(result);
    }
}
