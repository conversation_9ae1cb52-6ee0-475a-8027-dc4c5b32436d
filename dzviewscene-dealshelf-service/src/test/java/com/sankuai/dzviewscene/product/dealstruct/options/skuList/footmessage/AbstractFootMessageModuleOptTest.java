package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.AbstractFootMessageModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFootMessageModuleOptTest {

    @Mock
    private AbstractFootMessageModuleOpt moduleOpt;

    @Spy
    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt;

    /**
     * 测试buildDealDetailSkuListModuleGroupModel方法，当dealSkuList为空时，应返回null
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelWhenDealSkuListIsNull() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = new AbstractFootMessageModuleOptImpl();
        String groupName = "testGroup";
        String subTitle = "testSubTitle";
        List<DealSkuVO> dealSkuList = null;
        // act
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildDealDetailSkuListModuleGroupModel(groupName, subTitle, dealSkuList);
        // assert
        assertNull(result);
    }

    /**
     * 测试buildDealDetailSkuListModuleGroupModel方法，当dealSkuList不为空时，应返回一个DealDetailSkuListModuleGroupModel对象
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelWhenDealSkuListIsNotNull() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = new AbstractFootMessageModuleOptImpl();
        String groupName = "testGroup";
        String subTitle = "testSubTitle";
        List<DealSkuVO> dealSkuList = new ArrayList<>();
        dealSkuList.add(new DealSkuVO());
        // act
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildDealDetailSkuListModuleGroupModel(groupName, subTitle, dealSkuList);
        // assert
        assertNotNull(result);
        assertEquals(groupName, result.getGroupName());
        assertEquals(subTitle, result.getGroupSubtitle());
        assertEquals(1, result.getDealSkuGroupModuleVOS().size());
    }

    public class AbstractFootMessageModuleOptImpl extends AbstractFootMessageModuleOpt {

        @Override
        public Object compute(ActivityCxt context, Object o, Object o2) {
            return null;
        }
    }

    @Before
    public void setUp() {
        // Ensure the real method is called for the test
        doCallRealMethod().when(abstractFootMessageModuleOpt).buildDealDetailSkuListModuleGroupModel(anyString(), anyString(), anyString(), anyList());
    }

    /**
     * 测试getCombinationServiceType方法，当productCategories列表为空时
     */
    @Test
    public void testGetCombinationServiceTypeWhenProductCategoriesIsEmpty() throws Throwable {
        // arrange
        long productCategory = 1L;
        when(moduleOpt.getCombinationServiceType(productCategory, Collections.emptyList())).thenReturn(null);
        // act
        String result = moduleOpt.getCombinationServiceType(productCategory, Collections.emptyList());
        // assert
        assertNull(result);
    }

    /**
     * 测试getCombinationServiceType方法，当productCategories列表不为空，但没有productCategoryId与输入参数productCategory相同的ProductSkuCategoryModel对象时
     */
    @Test
    public void testGetCombinationServiceTypeWhenNoMatchedProductCategoryId() throws Throwable {
        // arrange
        long productCategory = 1L;
        ProductSkuCategoryModel model = new ProductSkuCategoryModel();
        model.setProductCategoryId(2L);
        model.setCnName("test");
        when(moduleOpt.getCombinationServiceType(productCategory, Arrays.asList(model))).thenReturn(null);
        // act
        String result = moduleOpt.getCombinationServiceType(productCategory, Arrays.asList(model));
        // assert
        assertNull(result);
    }

    /**
     * 测试getCombinationServiceType方法，当productCategories列表不为空，且有productCategoryId与输入参数productCategory相同的ProductSkuCategoryModel对象时
     */
    @Test
    public void testGetCombinationServiceTypeWhenMatchedProductCategoryIdExists() throws Throwable {
        // arrange
        long productCategory = 1L;
        ProductSkuCategoryModel model = new ProductSkuCategoryModel();
        model.setProductCategoryId(productCategory);
        model.setCnName("test");
        when(moduleOpt.getCombinationServiceType(productCategory, Arrays.asList(model))).thenReturn("test");
        // act
        String result = moduleOpt.getCombinationServiceType(productCategory, Arrays.asList(model));
        // assert
        assertEquals("test", result);
    }

    @Test
    public void testBuildDealDetailSkuListModuleGroupModelDealSkuListIsNull() throws Throwable {
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildDealDetailSkuListModuleGroupModel("groupName", "groupTitle", "subTitle", null);
        assertNull(result);
    }

    @Test
    public void testBuildDealDetailSkuListModuleGroupModelDealSkuListIsNotNull() throws Throwable {
        DealSkuVO dealSkuVO = new DealSkuVO();
        List<DealSkuVO> dealSkuList = Arrays.asList(dealSkuVO);
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildDealDetailSkuListModuleGroupModel("groupName", "groupTitle", "subTitle", dealSkuList);
        assertNotNull(result);
        // Additional assertions can be made here based on the expected behavior of the method under test.
    }

    /**
     * Tests getServiceTimeSum method when serviceFlowParseModels is null, should return 0.
     */
    @Test
    public void testGetServiceTimeSumWhenServiceFlowParseModelsIsNull() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

            @Override
            public Object compute(ActivityCxt context, Object input, Object output) {
                // You can throw an exception here if you want, but it's not required for the test.
                throw new RuntimeException();
            }
        };
        // act
        int result = moduleOpt.getServiceTimeSum(null);
        // assert
        assertEquals(0, result);
    }

    /**
     * Tests getServiceTimeSum method when serviceFlowParseModels is not empty, but all ServiceFlowParseModel object's stepTime property is 0, should return 0.
     */
    @Test
    public void testGetServiceTimeSumWhenStepTimeIsZero() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

            @Override
            public Object compute(ActivityCxt context, Object input, Object output) {
                // You can throw an exception here if you want, but it's not required for the test.
                throw new RuntimeException();
            }
        };
        ServiceFlowParseModel model = new ServiceFlowParseModel();
        model.setStepTime(0);
        // act
        int result = moduleOpt.getServiceTimeSum(Collections.singletonList(model));
        // assert
        assertEquals(0, result);
    }

    /**
     * Tests getServiceTimeSum method when serviceFlowParseModels is not empty, and all ServiceFlowParseModel object's stepTime property is not 0, should return the sum of these stepTime properties.
     */
    @Test
    public void testGetServiceTimeSumWhenStepTimeIsNotZero() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

            @Override
            public Object compute(ActivityCxt context, Object input, Object output) {
                // You can throw an exception here if you want, but it's not required for the test.
                throw new RuntimeException();
            }
        };
        ServiceFlowParseModel model1 = new ServiceFlowParseModel();
        model1.setStepTime(10);
        ServiceFlowParseModel model2 = new ServiceFlowParseModel();
        model2.setStepTime(20);
        // act
        int result = moduleOpt.getServiceTimeSum(Arrays.asList(model1, model2));
        // assert
        assertEquals(30, result);
    }
}
