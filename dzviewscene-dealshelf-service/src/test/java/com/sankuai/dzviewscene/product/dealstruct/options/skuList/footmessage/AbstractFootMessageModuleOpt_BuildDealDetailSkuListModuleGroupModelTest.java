package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFootMessageModuleOpt_BuildDealDetailSkuListModuleGroupModelTest {

    private MockedStatic<AbstractFootMessageModuleOpt> mockedStatic;

    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt;

    private SkuAttrItemDto skuAttrItemDto;

    @After
    public void tearDown() {
        if (mockedStatic != null) {
            mockedStatic.close();
        }
    }

    @Before
    public void setUp() {
        abstractFootMessageModuleOpt = Mockito.mock(AbstractFootMessageModuleOpt.class, Mockito.CALLS_REAL_METHODS);
        skuAttrItemDto = new SkuAttrItemDto();
    }

    /**
     * Tests buildDealDetailSkuListModuleGroupModel method when dealSkuList is null, should return null.
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelWhenDealSkuListIsNull() throws Throwable {
        // Arrange
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = mock(AbstractFootMessageModuleOpt.class);
        String groupName = "testGroup";
        String subTitle = "testSubTitle";
        List<DealSkuVO> dealSkuList = null;
        // Act
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildDealDetailSkuListModuleGroupModel(groupName, subTitle, dealSkuList);
        // Assert
        assertNull(result);
    }

    /**
     * Tests buildDealDetailSkuListModuleGroupModel method when dealSkuList is not null, should return a DealDetailSkuListModuleGroupModel object.
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelWhenDealSkuListIsNotNull() throws Throwable {
        // Arrange
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = mock(AbstractFootMessageModuleOpt.class);
        String groupName = "testGroup";
        String subTitle = "testSubTitle";
        List<DealSkuVO> dealSkuList = new ArrayList<>();
        dealSkuList.add(new DealSkuVO());
        DealDetailSkuListModuleGroupModel expectedModel = new DealDetailSkuListModuleGroupModel();
        expectedModel.setGroupName(groupName);
        expectedModel.setGroupSubtitle(subTitle);
        expectedModel.setDealSkuGroupModuleVOS(new ArrayList<>());
        when(abstractFootMessageModuleOpt.buildDealDetailSkuListModuleGroupModel(groupName, subTitle, dealSkuList)).thenReturn(expectedModel);
        // Act
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildDealDetailSkuListModuleGroupModel(groupName, subTitle, dealSkuList);
        // Assert
        assertNotNull(result);
        assertEquals(groupName, result.getGroupName());
        assertEquals(subTitle, result.getGroupSubtitle());
        assertNotNull(result.getDealSkuGroupModuleVOS());
    }

    @Test
    public void testGetServiceItemsWhenAllMethodsReturnNull() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(Collections.emptyList(), 1L);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetServiceItemsWhenSkuAttrsIsNull() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(null, 1L);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetServiceItemsWhenProductCategoryIsNull() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(Collections.singletonList(skuAttrItemDto), null);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetServiceItemsWhenProductCategoryNotInSpecialList() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(Collections.singletonList(skuAttrItemDto), 1L);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetServiceItemsWhenProductCategoryInSpecialList() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(Collections.singletonList(skuAttrItemDto), 2104617L);
        assertEquals(0, result.size());
    }
}
