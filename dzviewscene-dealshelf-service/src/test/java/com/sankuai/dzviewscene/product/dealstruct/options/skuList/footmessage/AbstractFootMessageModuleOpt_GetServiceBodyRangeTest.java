package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFootMessageModuleOpt_GetServiceBodyRangeTest {

    private final AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

        @Override
        public Object compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, Object o, Object o2) {
            return null;
        }
    };

    @Spy
    private AbstractFootMessageModuleOpt<Object> abstractFootMessageModuleOpt = new AbstractFootMessageModuleOpt<Object>() {

        @Override
        public List<DealDetailSkuListModuleGroupModel> compute(com.sankuai.athena.viewscene.framework.ActivityCxt context, com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP.Param param, Object data) {
            // This method is required by the PmfVPoint interface, but its implementation is not relevant to the test cases.
            return new ArrayList<>();
        }
    };

    /**
     * 测试 getServiceBodyRange 方法，当 skuAttrs 列表为空时
     */
    @Test
    public void testGetServiceBodyRangeWhenSkuAttrsIsNull() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

            public String getModuleId() {
                return "test";
            }

            public Object compute(ActivityCxt activityCxt, Object o, Object o2) {
                return null;
            }
        };
        List<SkuAttrItemDto> skuAttrs = null;
        // act
        DealSkuItemVO result = moduleOpt.getServiceBodyRange(skuAttrs);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getServiceBodyRange 方法，当 skuAttrs 列表不为空，但没有名为 "serviceBodyRange" 的属性时
     */
    @Test
    public void testGetServiceBodyRangeWhenSkuAttrsHasNoServiceBodyRange() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

            public String getModuleId() {
                return "test";
            }

            public Object compute(ActivityCxt activityCxt, Object o, Object o2) {
                return null;
            }
        };
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        skuAttrs.add(new SkuAttrItemDto());
        // act
        DealSkuItemVO result = moduleOpt.getServiceBodyRange(skuAttrs);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getServiceBodyRange 方法，当 skuAttrs 列表不为空，有名为 "serviceBodyRange" 的属性，但其值为空时
     */
    @Test
    public void testGetServiceBodyRangeWhenServiceBodyRangeValueIsNull() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

            public String getModuleId() {
                return "test";
            }

            public Object compute(ActivityCxt activityCxt, Object o, Object o2) {
                return null;
            }
        };
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        SkuAttrItemDto attr = new SkuAttrItemDto();
        attr.setAttrName("serviceBodyRange");
        attr.setAttrValue(null);
        skuAttrs.add(attr);
        // act
        DealSkuItemVO result = moduleOpt.getServiceBodyRange(skuAttrs);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getServiceBodyRange 方法，当 skuAttrs 列表不为空，有名为 "serviceBodyRange" 的属性，其值不为空时
     */
    @Test
    public void testGetServiceBodyRangeWhenServiceBodyRangeValueIsNotNull() throws Throwable {
        // arrange
        AbstractFootMessageModuleOpt moduleOpt = new AbstractFootMessageModuleOpt() {

            public String getModuleId() {
                return "test";
            }

            public Object compute(ActivityCxt activityCxt, Object o, Object o2) {
                return null;
            }
        };
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        SkuAttrItemDto attr = new SkuAttrItemDto();
        attr.setAttrName("serviceBodyRange");
        attr.setAttrValue("全身");
        skuAttrs.add(attr);
        // act
        DealSkuItemVO result = moduleOpt.getServiceBodyRange(skuAttrs);
        // assert
        assertNotNull(result);
        assertEquals("服务部位", result.getName());
        assertEquals(0, result.getType());
        assertNull(result.getValueAttrs());
        assertEquals("全身", result.getValue());
    }

    @Test
    public void testGetCombinationServiceTypeWhenProductCategoriesIsNull() throws Throwable {
        long productCategory = 1L;
        String result = moduleOpt.getCombinationServiceType(productCategory, null);
        assertNull(result);
    }

    @Test
    public void testGetCombinationServiceTypeWhenNoMatchedProductCategoryIdExists() throws Throwable {
        long productCategory = 1L;
        ProductSkuCategoryModel model = new ProductSkuCategoryModel();
        model.setProductCategoryId(2L);
        model.setCnName("test");
        String result = moduleOpt.getCombinationServiceType(productCategory, Arrays.asList(model));
        assertNull(result);
    }

    @Test
    public void testGetCombinationServiceTypeWhenMatchedProductCategoryIdExists() throws Throwable {
        long productCategory = 1L;
        ProductSkuCategoryModel model = new ProductSkuCategoryModel();
        model.setProductCategoryId(productCategory);
        model.setCnName("test");
        String result = moduleOpt.getCombinationServiceType(productCategory, Arrays.asList(model));
        assertEquals("test", result);
    }

    /**
     * Test case: Successfully add both service body range and service flow list
     * Expected: Both items should be added to the result list
     */
    @Test
    public void testGetServiceItems2_BothServiceBodyRangeAndServiceFlow() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        DealSkuItemVO bodyRange = new DealSkuItemVO();
        bodyRange.setName("服务部位");
        bodyRange.setValue("全身按摩");
        List<SkuAttrAttrItemVO> flowAttrs = new ArrayList<>();
        SkuAttrAttrItemVO flowAttr = new SkuAttrAttrItemVO();
        flowAttr.setName("头部按摩");
        flowAttrs.add(flowAttr);
        DealSkuItemVO serviceFlow = new DealSkuItemVO();
        serviceFlow.setName("服务流程");
        serviceFlow.setValueAttrs(flowAttrs);
        doReturn(bodyRange).when(abstractFootMessageModuleOpt).getServiceBodyRange2(any());
        doReturn(Arrays.asList(serviceFlow)).when(abstractFootMessageModuleOpt).getServiceFlowDealSkuItemVO(any());
        // act
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems2(skuAttrs);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("服务部位", result.get(0).getName());
        assertEquals("服务流程", result.get(1).getName());
    }

    /**
     * Test case: Only service body range is present, service flow list is empty
     * Expected: Only service body range should be added to the result list
     */
    @Test
    public void testGetServiceItems2_OnlyServiceBodyRange() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        DealSkuItemVO bodyRange = new DealSkuItemVO();
        bodyRange.setName("服务部位");
        bodyRange.setValue("全身按摩");
        doReturn(bodyRange).when(abstractFootMessageModuleOpt).getServiceBodyRange2(any());
        doReturn(null).when(abstractFootMessageModuleOpt).getServiceFlowDealSkuItemVO(any());
        // act
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems2(skuAttrs);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("服务部位", result.get(0).getName());
    }

    /**
     * Test case: Only service flow list is present, service body range is null
     * Expected: Only service flow list should be added to the result list
     */
    @Test
    public void testGetServiceItems2_OnlyServiceFlow() throws Throwable {
        // arrange
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        List<SkuAttrAttrItemVO> flowAttrs = new ArrayList<>();
        SkuAttrAttrItemVO flowAttr = new SkuAttrAttrItemVO();
        flowAttr.setName("头部按摩");
        flowAttrs.add(flowAttr);
        DealSkuItemVO serviceFlow = new DealSkuItemVO();
        serviceFlow.setName("服务流程");
        serviceFlow.setValueAttrs(flowAttrs);
        doReturn(null).when(abstractFootMessageModuleOpt).getServiceBodyRange2(any());
        doReturn(Arrays.asList(serviceFlow)).when(abstractFootMessageModuleOpt).getServiceFlowDealSkuItemVO(any());
        // act
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems2(skuAttrs);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("服务流程", result.get(0).getName());
    }
}
