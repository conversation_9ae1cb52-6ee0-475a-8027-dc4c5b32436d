package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.Arrays;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFootMessageModuleOpt_GetServiceDurationIntTest {

    @Mock
    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt;

    @Before
    public void setUp() {
        // Since getServiceDurationInt is a protected method, we need to make it accessible
        // This can be achieved by using <PERSON><PERSON><PERSON>'s spy method and then do the necessary stubbing.
        abstractFootMessageModuleOpt = spy(abstractFootMessageModuleOpt);
        doCallRealMethod().when(abstractFootMessageModuleOpt).getServiceDurationInt(anyList());
    }

    /**
     * 测试 skuAttrs 为空的情况
     */
    @Test
    public void testGetServiceDurationInt_SkuAttrsIsNull() throws Throwable {
        assertNull(abstractFootMessageModuleOpt.getServiceDurationInt(null));
    }

    /**
     * 测试 skuAttrs 不为空，但 serviceDurationInt 为空的情况
     */
    @Test
    public void testGetServiceDurationInt_ServiceDurationIntIsNull() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("serviceDurationInt");
        assertNull(abstractFootMessageModuleOpt.getServiceDurationInt(Collections.singletonList(skuAttrItemDto)));
    }

    /**
     * 测试 skuAttrs 和 serviceDurationInt 都不为空的情况
     */
    @Test
    public void testGetServiceDurationInt_AllNotNull() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("serviceDurationInt");
        skuAttrItemDto.setAttrValue("60");
        DealSkuItemVO dealSkuItemVO = abstractFootMessageModuleOpt.getServiceDurationInt(Collections.singletonList(skuAttrItemDto));
        assertNotNull(dealSkuItemVO);
        assertEquals("服务时长", dealSkuItemVO.getName());
        assertEquals(0, dealSkuItemVO.getType());
        assertEquals("60分钟", dealSkuItemVO.getValue());
        assertNull(dealSkuItemVO.getValueAttrs());
    }
}
