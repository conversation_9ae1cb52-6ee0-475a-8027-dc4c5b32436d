package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class AbstractFootMessageModuleOpt_GetServiceItems_1_Test {

    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt;

    private List<SkuAttrItemDto> skuAttrs;

    @Before
    public void setUp() {
        abstractFootMessageModuleOpt = Mockito.mock(AbstractFootMessageModuleOpt.class, Mockito.CALLS_REAL_METHODS);
    }

    private AbstractFootMessageModuleOpt createTestSubject() {
        return new AbstractFootMessageModuleOpt() {

            @Override
            public Object compute(com.sankuai.athena.viewscene.framework.ActivityCxt context, Object input1, Object input2) {
                return null;
            }
        };
    }

    @Test
    public void testGetServiceItems2WhenSkuAttrsIsNull() throws Throwable {
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = createTestSubject();
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems2(null);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetServiceItems2WhenSkuAttrsWithoutServiceBodyRangeAndServiceProcessArrayNew() throws Throwable {
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = createTestSubject();
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems2(skuAttrs);
        assertEquals(0, result.size());
    }

    /**
     * 测试 getSkuSubTitle 方法，当 skuItemDto 为 null 时，应抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testGetSkuSubTitleWhenSkuItemDtoIsNull() {
        abstractFootMessageModuleOpt.getSkuSubTitle(null);
    }

    /**
     * 测试 getSkuSubTitle 方法，当 skuItemDto 的 attrItems 为 null 时，应返回 null
     */
    @Test
    public void testGetSkuSubTitleWhenAttrItemsIsNull() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(null);
        String result = abstractFootMessageModuleOpt.getSkuSubTitle(skuItemDto);
        assertNull(result);
    }

    /**
     * 测试 getSkuSubTitle 方法，当 skuItemDto 的 attrItems 中不存在 serviceDurationInt 属性时，应返回 null
     */
    @Test
    public void testGetSkuSubTitleWhenAttrItemsNotContainsServiceDurationInt() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Arrays.asList(new SkuAttrItemDto()));
        String result = abstractFootMessageModuleOpt.getSkuSubTitle(skuItemDto);
        assertNull(result);
    }

    /**
     * 测试 getSkuSubTitle 方法，当 skuItemDto 的 attrItems 中存在 serviceDurationInt 属性时，应返回该属性值加上 "分钟" 的字符串
     */
    @Test
    public void testGetSkuSubTitleWhenAttrItemsContainsServiceDurationInt() {
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("serviceDurationInt");
        skuAttrItemDto.setAttrValue("60");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        String result = abstractFootMessageModuleOpt.getSkuSubTitle(skuItemDto);
        assertEquals("60分钟", result);
    }
}
