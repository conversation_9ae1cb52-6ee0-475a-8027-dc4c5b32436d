package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFootMessageModuleOpt_GetServingPostureTest {

    @Mock
    private AbstractFootMessageModuleOpt moduleOpt;

    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt;

    @Before
    public void setUp() {
        abstractFootMessageModuleOpt = Mockito.mock(AbstractFootMessageModuleOpt.class, Mockito.CALLS_REAL_METHODS);
    }

    @Test
    public void testGetServingPostureWhenSkuAttrsIsNull() throws Throwable {
        // Arrange
        when(moduleOpt.getServingPosture(null)).thenReturn(null);
        // Act
        DealSkuItemVO result = moduleOpt.getServingPosture(null);
        // Assert
        assertNull(result);
    }

    @Test
    public void testGetServingPostureWhenSkuAttrsNotContainsServingPosture() throws Throwable {
        // Arrange
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("other");
        skuAttrItemDto.setAttrValue("value");
        when(moduleOpt.getServingPosture(Collections.singletonList(skuAttrItemDto))).thenReturn(null);
        // Act
        DealSkuItemVO result = moduleOpt.getServingPosture(Collections.singletonList(skuAttrItemDto));
        // Assert
        assertNull(result);
    }

    @Test
    public void testGetServingPostureWhenSkuAttrsContainsServingPosture() throws Throwable {
        // Arrange
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("servingPosture");
        skuAttrItemDto.setAttrValue("value");
        DealSkuItemVO expectedVO = new DealSkuItemVO();
        expectedVO.setName("服务姿势");
        expectedVO.setType(0);
        expectedVO.setValue("value");
        when(moduleOpt.getServingPosture(Collections.singletonList(skuAttrItemDto))).thenReturn(expectedVO);
        // Act
        DealSkuItemVO result = moduleOpt.getServingPosture(Collections.singletonList(skuAttrItemDto));
        // Assert
        assertNotNull(result);
        assertEquals("服务姿势", result.getName());
        assertEquals(0, result.getType());
        assertNull(result.getValueAttrs());
        assertEquals("value", result.getValue());
    }

    @Test
    public void testBuildPayServiceModuleDealAttrsIsNull() throws Throwable {
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildPayServiceModule(null, true);
        assertNull(result);
    }

    @Test
    public void testBuildPayServiceModuleDealOverNightRuleIsNull() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildPayServiceModule(dealAttrs, true);
        assertNull(result);
    }

    @Test
    public void testBuildPayServiceModuleIdentityKeyIsNotOverNightService() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealOverNightRule");
        attrM.setValue("{\"identityKey\":\"notOverNightService\"}");
        dealAttrs.add(attrM);
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildPayServiceModule(dealAttrs, true);
        assertNull(result);
    }

    @Test
    public void testBuildPayServiceModuleOverNightRuleMapNotContainsRequiredKeys() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealOverNightRule");
        attrM.setValue("{\"identityKey\":\"overNightService\"}");
        dealAttrs.add(attrM);
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildPayServiceModule(dealAttrs, true);
        assertNull(result);
    }

    @Test
    public void testBuildPayServiceModuleFreeIsTrue() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealOverNightRule");
        attrM.setValue("{\"identityKey\":\"overNightService\", \"free\":true}");
        dealAttrs.add(attrM);
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildPayServiceModule(dealAttrs, true);
        assertNull(result);
    }

    @Test
    public void testBuildPayServiceModuleNormal() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealOverNightRule");
        attrM.setValue("{\"identityKey\":\"overNightService\", \"amount\":\"100\", \"serviceTitle\":\"title\", \"originalPrice\":\"200\", \"free\":false, \"rule\":\"rule\"}");
        dealAttrs.add(attrM);
        DealDetailSkuListModuleGroupModel result = abstractFootMessageModuleOpt.buildPayServiceModule(dealAttrs, true);
        assertNotNull(result);
    }
}
