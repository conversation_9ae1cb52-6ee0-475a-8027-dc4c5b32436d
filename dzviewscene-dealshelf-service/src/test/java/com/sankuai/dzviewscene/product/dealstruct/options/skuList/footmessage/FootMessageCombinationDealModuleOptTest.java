package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP.Param;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import org.junit.*;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;

@RunWith(MockitoJUnitRunner.class)
public class FootMessageCombinationDealModuleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param param;

    @Mock
    private FootMessageCombinationDealModuleOpt.Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @Test
    public void testComputeOverNightSkuListModuleIsNull() throws Throwable {
        FootMessageCombinationDealModuleOpt opt = new FootMessageCombinationDealModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals("Expected no results when deal attributes are null", 0, result.size());
    }

    @Test
    public void testComputeServiceFlowSkuListModuleIsNull() throws Throwable {
        FootMessageCombinationDealModuleOpt opt = new FootMessageCombinationDealModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals("Expected no results when service flow module is not setup", 0, result.size());
    }

    @Test
    public void testComputeOverNightSkuListModuleIsNotNull() throws Throwable {
        FootMessageCombinationDealModuleOpt opt = new FootMessageCombinationDealModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM("dealOverNightRule", "{\"identityKey\":\"overNightService\",\"amount\":\"100\",\"serviceTitle\":\"服务标题\",\"originalPrice\":\"200\",\"free\":\"false\",\"rule\":\"规则\"}");
        dealAttrs.add(attrM);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(dealAttrs);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        assertEquals("Expected one result when overnight module is setup", 1, result.size());
    }
}
