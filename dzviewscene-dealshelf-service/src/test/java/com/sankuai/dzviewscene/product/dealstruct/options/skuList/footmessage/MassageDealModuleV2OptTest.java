package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.MassageDealModuleV2Opt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.MassageDealModuleV2Opt.Config;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MassageDealModuleV2OptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private DouhuResultModel douhuResultModel;

    private MassageDealModuleV2Opt massageDealModuleV2Opt = new MassageDealModuleV2Opt();

    @Test
    public void testComputeWhenDealDetailInfoModelIsNull() throws Throwable {
        when(param.getDealDetailInfoModel()).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result = massageDealModuleV2Opt.compute(context, param, config);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testComputeWhenSkuItemDtoIsNull() throws Throwable {
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(DealDetailUtils.extractFirstMustSkuFromDealDetailInfoModel(dealDetailInfoModel)).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result = massageDealModuleV2Opt.compute(context, param, config);
        assertEquals(new ArrayList<>(), result);
    }
}
