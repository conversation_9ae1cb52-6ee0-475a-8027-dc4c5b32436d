package com.sankuai.dzviewscene.product.dealstruct.options.skuList.gathering;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.TeamBuildingGatheringListOpt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.TeamBuildingGatheringListOpt.splicingTitles;
import static com.sankuai.it.iam.common_base.exception.BizAssert.assertEquals;
import static com.sankuai.it.iam.common_base.exception.BizAssert.assertNull;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class TeamBuildingGatheringListOptTest {

    private static final String minPeople = "minParticipantsCheck";
    private static final String maxPeople = "maxParticipantsCheck";

    @Test
    public void testSplicingTitles_WithEqualParticipants() {
        ProductM productM = new ProductM();
        productM.setAttr(minPeople, "5");
        productM.setAttr(maxPeople, "5");
        String strConfig = "{\n" + "    \"enable\": true,\n" + "    \"unit\": \"人\",\n" + "    \"suffix\": \"套餐\"\n" + "}";
        TeamBuildingGatheringListOpt.Config config1 = JSON.parseObject(strConfig, TeamBuildingGatheringListOpt.Config.class);
        String result = splicingTitles(productM, config1);
        assertEquals("5人套餐", result);
    }

    @Test
    public void testSplicingTitles_WithRangeParticipants() {
        ProductM productM = new ProductM();
        productM.setAttr(minPeople, "2");
        productM.setAttr(maxPeople, "10");
        String strConfig = "{\n" + "    \"enable\": true,\n" + "    \"unit\": \"人\",\n" + "    \"suffix\": \"套餐\"\n" + "}";
        TeamBuildingGatheringListOpt.Config config1 = JSON.parseObject(strConfig, TeamBuildingGatheringListOpt.Config.class);
        String result = splicingTitles(productM, config1);
        assertEquals("2-10人套餐", result);
    }

    @Test
    public void testSplicingTitles_WithNoParticipants() {
        ProductM productM = new ProductM();
        productM.setAttr(minPeople, null);
        productM.setAttr(maxPeople, null);
        String strConfig = "{\n" + "    \"enable\": true,\n" + "    \"unit\": \"人\",\n" + "    \"suffix\": \"套餐\"\n" + "}";
        TeamBuildingGatheringListOpt.Config config1 = JSON.parseObject(strConfig, TeamBuildingGatheringListOpt.Config.class);
        String result = splicingTitles(productM, config1);
        assertNull(result);
    }

    @Test
    public void testSplicingTitles_WithZeroParticipants() {
        ProductM productM = new ProductM();
        productM.setAttr(minPeople, "0");
        productM.setAttr(maxPeople, "0");
        String strConfig = "{\n" + "    \"enable\": true,\n" + "    \"unit\": \"人\",\n" + "    \"suffix\": \"套餐\"\n" + "}";
        TeamBuildingGatheringListOpt.Config config1 = JSON.parseObject(strConfig, TeamBuildingGatheringListOpt.Config.class);
        String result = splicingTitles(productM, config1);
        assertEquals("人套餐", result);
    }

    @Test
    public void testSplicingTitles_WithInvalidNumberFormat() {
        ProductM productM = new ProductM();
        productM.setAttr(minPeople, "abc");
        productM.setAttr(maxPeople, "10");
        String strConfig = "{\n" + "    \"enable\": true,\n" + "    \"unit\": \"人\",\n" + "    \"suffix\": \"套餐\"\n" + "}";
        TeamBuildingGatheringListOpt.Config config1 = JSON.parseObject(strConfig, TeamBuildingGatheringListOpt.Config.class);
        String result = splicingTitles(productM, config1);
        assertNull(result);
    }

    @Test
    public void testSplicingTitles_WithNullProductM() {
        String result = splicingTitles(null, null);
        assertNull(result);
    }

    @Test
    public void testCompute() {
        String str = "\n" + "{\"activityCode\":\"deal_filter_list_activity\",\"activityConfigStep\":{\"abilityNode\":{\"code\":\"DealListResponseAssembler\",\"dependencies\":[\"DealListBuilder\",\"DealListModelAssembler\"],\"strategy\":\"{\\n    \\\"defaultShowNum\\\": 0,\\n    \\\"jumpUrlTemplate\\\": \\\"\\\",\\n    \\\"showType\\\": 0,\\n    \\\"subtitle\\\": \\\"\\\",\\n    \\\"title\\\": \\\"\\\"\\n}\",\"vPoints\":[]}},\"attachments\":{\"productGroup2ProductGroups\":{\"cancelled\":false,\"completedExceptionally\":false,\"done\":true,\"numberOfDependents\":0}},\"executeError\":{},\"extContext\":{},\"parameters\":{\"ctxShop\":{\"category\":50035,\"cityId\":1,\"distanceNum\":0.0,\"lat\":31.218157,\"lng\":121.375264,\"longShopId\":604603521,\"score\":0.0,\"shopId\":604603521,\"shopName\":\"密室+剧本杀\",\"shopType\":30,\"shopUuid\":\"l4wB4uP04NxwyUGj\",\"status\":5,\"useType\":0,\"userEqShop\":false},\"appVersion\":\"12.25.200\",\"reCountProducts\":false,\"channel\":\"dealFilterList\",\"pageSize\":20,\"mtPoiId\":604603521,\"deviceId\":\"000000000000003C1466DE19F4EF99BBECD06093017A2A153834938872403792\",\"mtUserId\":5137472344,\"extPointClassMap\":{\"FilterFetcherExt\":\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultipleConfigFilterFetcherExt\"},\"filterComponent2Group\":{\"团购\":\"团购\"},\"dpCityId\":1,\"clientType\":\"android\",\"dpPoiIdL\":604603521,\"pageNo\":1,\"lat\":0.0,\"productComponent2Group\":{\"团购\":\"团购\"},\"unionId\":\"03c1466de19f4ef99bbecd06093017a2a153834938872403792\",\"lng\":0.0,\"selectedFilterId\":200120579,\"topN\":0,\"recalledFilterId\":200120579,\"sortid\":0,\"reportMagicMember\":true,\"shopType\":30,\"shelfDefaultConvert\":{\"cancelled\":false,\"completedExceptionally\":false,\"done\":true,\"numberOfDependents\":0},\"mtLocationCityId\":0,\"abilityClassMap\":{\"ProductPaddingFetcher\":\"com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher\",\"FilterFetcher\":\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcher\",\"ProductFetcher\":\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher\"},\"wttRegionId\":\"1000110100\",\"platform\":2,\"shopMtCityId\":10,\"mtCityId\":10,\"userAgent\":200,\"entityId\":\"1031754168\",\"mtPoiIdL\":604603521,\"spaceKey\":\"deal_detail_list\",\"dpPoiId\":604603521,\"groupNames\":[\"团购\"],\"groupParams\":{\"团购\":{\"paddingType\":\"dealThemePadding\",\"paddingProductType\":1,\"promoTemplateId\":244,\"attributeKeys\":[\"reservation_is_needed_or_not\",\"service_type\",\"support_home_service\",\"preSaleTag\",\"DealPricePowerTag\",\"minParticipantsCheck\",\"maxParticipantsCheck\"],\"directPromoScene\":400200,\"enablePreSalePromoTag\":true,\"priceDescType\":4,\"querySecKillSceneStrategy\":3,\"planId\":\"10002476\"}},\"shopDpCityId\":1,\"timesDealQueryFlag\":false,\"selectPerFirstLeafNode\":false,\"pageSource\":\"shelf\",\"category\":50035,\"dealCategoryId\":324},\"resource\":{\"abilityNodes\":[{\"code\":\"FilterFirstFetcher\",\"dependencies\":[],\"strategy\":\"{\\n    \\\"oldEngineCfg\\\": {\\n        \\\"abilityClass\\\": \\\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcher\\\",\\n        \\\"extPointClass\\\": \\\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultipleConfigFilterFetcherExt\\\"\\n    },\\n    \\\"filterComponent2Group\\\": {\\n        \\\"团购\\\": \\\"团购\\\"\\n    },\\n    \\\"skipWhenOnlyProducts\\\": true\\n}\",\"vPoints\":[]},{\"code\":\"DealQueryFetcher\",\"dependencies\":[\"FilterFirstFetcher\"],\"strategy\":\"{\\n    \\\"oldEngineCfg\\\": {\\n        \\\"abilityClass\\\": \\\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher\\\"\\n    },\\n    \\\"groupNames\\\": [\\n        \\\"团购\\\"\\n    ],\\n    \\\"productComponent2Group\\\": {\\n        \\\"团购\\\": \\\"团购\\\"\\n    },\\n    \\\"groupParams\\\": {\\n        \\\"团购\\\": {\\n            \\\"paddingType\\\": \\\"dealThemePadding\\\",\\n            \\\"paddingProductType\\\": 1,\\n            \\\"promoTemplateId\\\": 244,\\n            \\\"attributeKeys\\\": [\\n                \\\"reservation_is_needed_or_not\\\",\\n                \\\"service_type\\\",\\n                \\\"support_home_service\\\",\\n                \\\"preSaleTag\\\",\\n                \\\"DealPricePowerTag\\\",\\n                \\\"minParticipantsCheck\\\",\\n                \\\"maxParticipantsCheck\\\"\\n            ],\\n            \\\"directPromoScene\\\": 400200,\\n            \\\"enablePreSalePromoTag\\\": true,\\n            \\\"priceDescType\\\": 4,\\n            \\\"querySecKillSceneStrategy\\\": 3,\\n            \\\"planId\\\": \\\"10002476\\\"\\n        }\\n    }\\n}\",\"vPoints\":[]},{\"code\":\"ProductPaddingFetcher\",\"dependencies\":[\"DealQueryFetcher\"],\"strategy\":\"{\\n    \\\"oldEngineCfg\\\": {\\n        \\\"abilityClass\\\": \\\"com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher\\\"\\n    }\\n}\",\"vPoints\":[]},{\"code\":\"DealListModelAssembler\",\"dependencies\":[\"DealQueryFetcher\",\"ProductPaddingFetcher\",\"FilterFirstFetcher\"],\"strategy\":\"{\\n    \\\"fetcherCode\\\": \\\"\\\"\\n}\",\"vPoints\":[]},{\"code\":\"DealListBuilder\",\"dependencies\":[\"DealListModelAssembler\"],\"strategy\":\"{\\n    \\\"usePostPadding\\\": true\\n}\",\"vPoints\":[{\"code\":\"ProductListVP\",\"options\":[{\"optCode\":\"TeamBuildingGatheringListOpt\",\"rule\":\"\",\"strategy\":\"{\\n    \\\"enable\\\": true\\n}\",\"type\":\"static\"}]}]},{\"$ref\":\"$.activityConfigStep.abilityNode\"}],\"id\":\"11900258\"},\"sceneCode\":\"leisure-entertainment-teambuilding\",\"sourceMap\":{\"DealQueryFetcher\":{\"团购\":{\"hasNext\":true,\"products\":[{\"actProductId\":425930762,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":268.00,\"basePriceTag\":\"¥268\",\"beginDate\":0,\"categoryId\":1802,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团课/训练营\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=425930762&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":425930762,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":6,\"saleTag\":\"年售6\"},\"salePrice\":268.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"商场卡测试-勿动\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024764517,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":1.10,\"basePriceTag\":\"¥1.1\",\"beginDate\":0,\"categoryId\":311,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"运动类门票\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024764517&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024764517,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":22,\"saleTag\":\"年售22\"},\"salePrice\":1.10,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"亲子两大1小单次不限时通用门票\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":428146795,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":69.90,\"basePriceTag\":\"¥69.9\",\"beginDate\":0,\"categoryId\":322,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=428146795&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":428146795,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":11,\"saleTag\":\"年售11\"},\"salePrice\":69.90,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"100枚游戏币套餐\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1028350974,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":198.00,\"basePriceTag\":\"¥198\",\"beginDate\":0,\"categoryId\":301,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"纯欢唱套餐\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1028350974&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1028350974,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":5,\"saleTag\":\"年售5\"},\"salePrice\":198.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"【新店特惠】欢乐无限唱\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1022126418,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":50.00,\"basePriceTag\":\"¥50\",\"beginDate\":0,\"categoryId\":321,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"上网包时类团单\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1022126418&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1022126418,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":3,\"saleTag\":\"年售3\"},\"salePrice\":50.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"包123\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1020867547,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":499.00,\"basePriceTag\":\"¥499\",\"beginDate\":0,\"categoryId\":322,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1020867547&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1020867547,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":3,\"saleTag\":\"年售3\"},\"salePrice\":499.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"1000枚游戏币套餐\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":418241911,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":70.00,\"basePriceTag\":\"¥70\",\"beginDate\":0,\"categoryId\":306,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"true\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=418241911&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":418241911,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":3,\"saleTag\":\"已预售3\"},\"salePrice\":70.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"剧本杀-预售团单测试\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1028617233,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":0.80,\"basePriceTag\":\"¥0.8\",\"beginDate\":0,\"categoryId\":301,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"录音棚\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1028617233&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1028617233,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":1,\"saleTag\":\"年售1\"},\"salePrice\":0.80,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"9968录音棚\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":417894405,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":100.00,\"basePriceTag\":\"¥100\",\"beginDate\":0,\"categoryId\":306,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=417894405&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":417894405,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":1,\"saleTag\":\"年售1\"},\"salePrice\":100.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"AutoCreateByET\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031758006,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031758006&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031758006,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试02\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031756136,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031756136&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031756136,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试03\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031754168,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031754168&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031754168,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试01\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031751329,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031751329&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031751329,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试04\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1030232467,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":98.00,\"basePriceTag\":\"¥98\",\"beginDate\":0,\"categoryId\":303,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"足疗\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1030232467&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1030232467,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":98.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"肩颈按摩30分钟123\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1026036537,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":1800.00,\"basePriceTag\":\"¥1800\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"15\"},{\"name\":\"minParticipantsCheck\",\"value\":\"3\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1026036537&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1026036537,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":1800.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"无服务设施测试测试\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024813994,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":180.00,\"basePriceTag\":\"¥180\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"10\"},{\"name\":\"minParticipantsCheck\",\"value\":\"10\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024813994&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024813994,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":180.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"密室+剧本杀\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024721219,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":79.00,\"basePriceTag\":\"¥79\",\"beginDate\":0,\"categoryId\":322,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024721219&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024721219,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":79.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"游戏币100枚\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024547626,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":100.00,\"basePriceTag\":\"¥100\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"5\"},{\"name\":\"minParticipantsCheck\",\"value\":\"1\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024547626&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024547626,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":100.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"密室团单+\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1023840038,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":499.00,\"basePriceTag\":\"¥499\",\"beginDate\":0,\"categoryId\":315,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1023840038&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1023840038,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":499.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"轰趴全天畅玩套餐\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1023058724,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":300.00,\"basePriceTag\":\"¥300\",\"beginDate\":0,\"categoryId\":306,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1023058724&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1023058724,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":300.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"密室验证111\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false}],\"total\":25},\"1\":{\"$ref\":\"$.sourceMap.DealQueryFetcher.团购\"}},\"ProductPaddingFetcher\":{\"团购\":{\"$ref\":\"$.sourceMap.DealQueryFetcher.团购\"},\"1\":{\"hasNext\":false,\"total\":0}},\"FilterFirstFetcher\":{\"团购\":{\"extra\":{\"shelfTagsFromPlatform\":[\"随时退\",\"过期退\"]},\"filters\":[{\"children\":[],\"depth\":1,\"filterId\":200120579,\"multiSelect\":false,\"selected\":true,\"title\":\"全部\",\"totalCount\":25,\"type\":0},{\"children\":[],\"depth\":1,\"filterId\":200131890,\"multiSelect\":false,\"selected\":false,\"title\":\"聚会团建\",\"totalCount\":7,\"type\":0}],\"id\":0,\"selected\":false,\"type\":0}},\"DealListModelAssembler\":{\"filterMs\":{\"$ref\":\"$.sourceMap.FilterFirstFetcher\"},\"firstProductGroup\":{\"$ref\":\"$.sourceMap.DealQueryFetcher.团购\"},\"productGroupMs\":{\"$ref\":\"$.sourceMap.ProductPaddingFetcher\"}}},\"startTime\":1728903958071,\"success\":true,\"trace\":false,\"traceElements\":[{\"latency\":0,\"name\":\"FilterFirstFetcher\",\"result\":{\"团购\":{\"$ref\":\"$.sourceMap.FilterFirstFetcher.团购\"}},\"type\":\"能力\"},{\"latency\":0,\"name\":\"DealQueryFetcher\",\"result\":{\"团购\":{\"$ref\":\"$.sourceMap.DealQueryFetcher.团购\"},\"1\":{\"$ref\":\"$.sourceMap.DealQueryFetcher.团购\"}},\"type\":\"能力\"},{\"latency\":0,\"name\":\"ProductPaddingFetcher\",\"result\":{\"团购\":{\"$ref\":\"$.sourceMap.DealQueryFetcher.团购\"},\"1\":{\"$ref\":\"$.sourceMap.ProductPaddingFetcher.1\"}},\"type\":\"能力\"},{\"latency\":0,\"name\":\"DealListModelAssembler\",\"result\":{\"$ref\":\"$.sourceMap.DealListModelAssembler\"},\"type\":\"能力\"}]}";
        ActivityCxt mockContext1 = JSON.parseObject(str, ActivityCxt.class);
        String strParam = "\n" + "{\"productMS\":[{\"actProductId\":425930762,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":268.00,\"basePriceTag\":\"¥268\",\"beginDate\":0,\"categoryId\":1802,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团课/训练营\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=425930762&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":425930762,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":6,\"saleTag\":\"年售6\"},\"salePrice\":268.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"商场卡测试-勿动\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024764517,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":1.10,\"basePriceTag\":\"¥1.1\",\"beginDate\":0,\"categoryId\":311,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"运动类门票\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024764517&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024764517,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":22,\"saleTag\":\"年售22\"},\"salePrice\":1.10,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"亲子两大1小单次不限时通用门票\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":428146795,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":69.90,\"basePriceTag\":\"¥69.9\",\"beginDate\":0,\"categoryId\":322,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=428146795&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":428146795,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":11,\"saleTag\":\"年售11\"},\"salePrice\":69.90,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"100枚游戏币套餐\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1028350974,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":198.00,\"basePriceTag\":\"¥198\",\"beginDate\":0,\"categoryId\":301,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"纯欢唱套餐\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1028350974&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1028350974,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":5,\"saleTag\":\"年售5\"},\"salePrice\":198.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"【新店特惠】欢乐无限唱\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1022126418,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":50.00,\"basePriceTag\":\"¥50\",\"beginDate\":0,\"categoryId\":321,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"上网包时类团单\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1022126418&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1022126418,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":3,\"saleTag\":\"年售3\"},\"salePrice\":50.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"包123\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1020867547,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":499.00,\"basePriceTag\":\"¥499\",\"beginDate\":0,\"categoryId\":322,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1020867547&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1020867547,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":3,\"saleTag\":\"年售3\"},\"salePrice\":499.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"1000枚游戏币套餐\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":418241911,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":70.00,\"basePriceTag\":\"¥70\",\"beginDate\":0,\"categoryId\":306,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"true\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=418241911&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":418241911,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":3,\"saleTag\":\"已预售3\"},\"salePrice\":70.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"剧本杀-预售团单测试\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1028617233,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":0.80,\"basePriceTag\":\"¥0.8\",\"beginDate\":0,\"categoryId\":301,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"录音棚\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1028617233&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1028617233,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":1,\"saleTag\":\"年售1\"},\"salePrice\":0.80,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"9968录音棚\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":417894405,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":100.00,\"basePriceTag\":\"¥100\",\"beginDate\":0,\"categoryId\":306,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=417894405&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":417894405,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":1,\"saleTag\":\"年售1\"},\"salePrice\":100.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"AutoCreateByET\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031758006,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031758006&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031758006,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试02\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031756136,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031756136&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031756136,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试03\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031754168,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031754168&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031754168,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试01\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1031751329,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":420.00,\"basePriceTag\":\"¥420\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"6\"},{\"name\":\"minParticipantsCheck\",\"value\":\"2\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1031751329&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1031751329,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":420.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"4~6人团建聚会团详聚合测试04\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1030232467,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":98.00,\"basePriceTag\":\"¥98\",\"beginDate\":0,\"categoryId\":303,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"足疗\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"是\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1030232467&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1030232467,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":98.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"肩颈按摩30分钟123\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1026036537,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":1800.00,\"basePriceTag\":\"¥1800\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"15\"},{\"name\":\"minParticipantsCheck\",\"value\":\"3\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1026036537&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1026036537,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":1800.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"无服务设施测试测试\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024813994,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":180.00,\"basePriceTag\":\"¥180\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"10\"},{\"name\":\"minParticipantsCheck\",\"value\":\"10\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024813994&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024813994,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":180.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"密室+剧本杀\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024721219,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":79.00,\"basePriceTag\":\"¥79\",\"beginDate\":0,\"categoryId\":322,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024721219&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024721219,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":79.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"游戏币100枚\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1024547626,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":100.00,\"basePriceTag\":\"¥100\",\"beginDate\":0,\"categoryId\":324,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"service_type\",\"value\":\"团建/聚会\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"maxParticipantsCheck\",\"value\":\"5\"},{\"name\":\"minParticipantsCheck\",\"value\":\"1\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1024547626&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1024547626,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":100.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"密室团单+\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1023840038,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":499.00,\"basePriceTag\":\"¥499\",\"beginDate\":0,\"categoryId\":315,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1023840038&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1023840038,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":499.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"轰趴全天畅玩套餐\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":1023058724,\"actProductType\":1,\"activities\":[],\"additionalDeal\":false,\"additionalProjectList\":[],\"basePrice\":300.00,\"basePriceTag\":\"¥300\",\"beginDate\":0,\"categoryId\":306,\"compositeScore\":0.0,\"dealSpuId\":0,\"endDate\":0,\"extAttrs\":[{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1023058724&poiid=604603521&pagefrom=poiShelf\",\"materialList\":[],\"orderUsers\":[],\"prePadded\":true,\"productId\":1023058724,\"productTagList\":[],\"productType\":1,\"promoPrices\":[],\"relatedTimesDeal\":[],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":0,\"saleTag\":\"年售0\"},\"salePrice\":300.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"密室验证111\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false}]}";
        ProductListVP.Param param1 = JSON.parseObject(strParam, ProductListVP.Param.class);
        String strConfig = "{\n" + "    \"enable\": true,\n" + "    \"unit\": \"人\",\n" + "    \"suffix\": \"套餐\"\n" + "}";
        TeamBuildingGatheringListOpt.Config config1 = JSON.parseObject(strConfig, TeamBuildingGatheringListOpt.Config.class);
        TeamBuildingGatheringListOpt aa = new TeamBuildingGatheringListOpt();
        List<ProductM> result = aa.compute(mockContext1, param1, config1);
        assertTrue(result.size() > 0);
    }


}
