package com.sankuai.dzviewscene.product.dealstruct.options.skuList.life;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.LifeCleanAttrVOListOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.life.LifeCleanDetailSkuListModuleOpt.Config;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.filterlist.utils.LifeCleanUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP.Param;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

public class LifeCleanDetailSkuListModuleOptNewTest {


    private LifeCleanDetailSkuListModuleOpt lifeCleanDetailSkuListModuleOpt;


    @Test
    public void testComputeWithNullFirstProductCategory() throws Throwable {
        lifeCleanDetailSkuListModuleOpt = new LifeCleanDetailSkuListModuleOpt();
        List<SkuAttrItemDto> skuAttrItemDtos = JSON.parseArray("[\n" +
                "    {\n" +
                "        \"attrName\": \"serviceDuration\",\n" +
                "        \"attrValue\": \"3小时\",\n" +
                "        \"chnName\": \"服务时长\",\n" +
                "        \"metaAttrId\": 1390,\n" +
                "        \"rawAttrValue\": \"3小时\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"area\",\n" +
                "        \"attrValue\": \"≤50㎡\",\n" +
                "        \"chnName\": \"参考适用面积\",\n" +
                "        \"metaAttrId\": 1003,\n" +
                "        \"rawAttrValue\": \"≤50㎡\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanTool\",\n" +
                "        \"attrValue\": \"服务人员携带\",\n" +
                "        \"chnName\": \"清洁工具\",\n" +
                "        \"metaAttrId\": 1393,\n" +
                "        \"rawAttrValue\": \"服务人员携带\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"outCleanScope\",\n" +
                "        \"attrValue\": \"\",\n" +
                "        \"chnName\": \"除外范围\",\n" +
                "        \"metaAttrId\": 1392,\n" +
                "        \"rawAttrValue\": \"\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"unclassifiedTools\",\n" +
                "        \"attrValue\": \"清洁工具包\",\n" +
                "        \"chnName\": \"携带工具\",\n" +
                "        \"metaAttrId\": 3113,\n" +
                "        \"rawAttrValue\": \"清洁工具包\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"product\",\n" +
                "        \"attrValue\": \"服务人员携带\",\n" +
                "        \"chnName\": \"清洁剂\",\n" +
                "        \"metaAttrId\": 976,\n" +
                "        \"rawAttrValue\": \"服务人员携带\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"material\",\n" +
                "        \"attrValue\": \"除油污剂、消毒剂\",\n" +
                "        \"chnName\": \"携带清洁剂\",\n" +
                "        \"metaAttrId\": 750,\n" +
                "        \"rawAttrValue\": \"除油污剂、消毒剂\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanScope\",\n" +
                "        \"attrValue\": \"地面、家具家电表面、台面、瓷砖\",\n" +
                "        \"chnName\": \"全屋清洁\",\n" +
                "        \"metaAttrId\": 1391,\n" +
                "        \"rawAttrValue\": \"地面、家具家电表面、台面、瓷砖\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanContent\",\n" +
                "        \"attrValue\": \"灶台、水槽、油烟机表面\",\n" +
                "        \"chnName\": \"厨房除油污\",\n" +
                "        \"metaAttrId\": 1437,\n" +
                "        \"rawAttrValue\": \"灶台、水槽、油烟机表面\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"trimCleanPart\",\n" +
                "        \"attrValue\": \"台盆、淋浴房/浴缸、马桶、镜面\",\n" +
                "        \"chnName\": \"卫生间除水垢\",\n" +
                "        \"metaAttrId\": 2813,\n" +
                "        \"rawAttrValue\": \"台盆、淋浴房/浴缸、马桶、镜面\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"facadeCleanPart\",\n" +
                "        \"attrValue\": \"\",\n" +
                "        \"chnName\": \"擦窗/玻璃\",\n" +
                "        \"metaAttrId\": 2815,\n" +
                "        \"rawAttrValue\": \"\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    }\n" +
                "]", SkuAttrItemDto.class);

        LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig a1 = JSON.parseObject("{\n" +
                "            \"selfSupport\": false,\n" +
                "            \"skuAttrName2IconList\": [\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/50206acc29d78ccf7adef2fe5522f0b13886.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanScope\"\n" +
                "                    ],\n" +
                "                    \"title\": \"全屋清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/bac147005fa4e7fd1bbc01c1b93d1a5a3511.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanContent\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房除油污\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/d1d414ff587f9a2da209e950ab05d8bf4018.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"trimCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间除水垢\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/8a317e9c53d278087b4fc049398edc223535.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"facadeCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"窗/玻璃清洁\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"subTitle\": \"可选以下区域和服务重点打扫\"\n" +
                "        }", LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig.class);

        Map<String, LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig> skuAttrIconConfigMap1 = new HashMap<>();
        skuAttrIconConfigMap1.put("3145", a1);
        ProductSkuCategoryModel firstProductCategory = JSON.parseObject("{\"cnName\":\"深度保洁\",\"productCategoryId\":3145}", ProductSkuCategoryModel.class);
        Boolean isSelfSupport = true;
        DealSkuGroupModuleVO dealSkuGroupModuleVO = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVO(skuAttrItemDtos, skuAttrIconConfigMap1, firstProductCategory, isSelfSupport);
        assertNull(dealSkuGroupModuleVO);
    }


    @Test
    public void testbuildDealSkuGroupModuleVOs() throws Throwable {
        lifeCleanDetailSkuListModuleOpt = new LifeCleanDetailSkuListModuleOpt();
        List<SkuAttrItemDto> skuAttrItemDtos = JSON.parseArray("[\n" +
                "    {\n" +
                "        \"attrName\": \"serviceDuration\",\n" +
                "        \"attrValue\": \"3小时\",\n" +
                "        \"chnName\": \"服务时长\",\n" +
                "        \"metaAttrId\": 1390,\n" +
                "        \"rawAttrValue\": \"3小时\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"area\",\n" +
                "        \"attrValue\": \"≤50㎡\",\n" +
                "        \"chnName\": \"参考适用面积\",\n" +
                "        \"metaAttrId\": 1003,\n" +
                "        \"rawAttrValue\": \"≤50㎡\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanTool\",\n" +
                "        \"attrValue\": \"服务人员携带\",\n" +
                "        \"chnName\": \"清洁工具\",\n" +
                "        \"metaAttrId\": 1393,\n" +
                "        \"rawAttrValue\": \"服务人员携带\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"outCleanScope\",\n" +
                "        \"attrValue\": \"\",\n" +
                "        \"chnName\": \"除外范围\",\n" +
                "        \"metaAttrId\": 1392,\n" +
                "        \"rawAttrValue\": \"\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"unclassifiedTools\",\n" +
                "        \"attrValue\": \"清洁工具包\",\n" +
                "        \"chnName\": \"携带工具\",\n" +
                "        \"metaAttrId\": 3113,\n" +
                "        \"rawAttrValue\": \"清洁工具包\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"product\",\n" +
                "        \"attrValue\": \"服务人员携带\",\n" +
                "        \"chnName\": \"清洁剂\",\n" +
                "        \"metaAttrId\": 976,\n" +
                "        \"rawAttrValue\": \"服务人员携带\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"material\",\n" +
                "        \"attrValue\": \"除油污剂、消毒剂\",\n" +
                "        \"chnName\": \"携带清洁剂\",\n" +
                "        \"metaAttrId\": 750,\n" +
                "        \"rawAttrValue\": \"除油污剂、消毒剂\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanScope\",\n" +
                "        \"attrValue\": \"地面、家具家电表面、台面、瓷砖\",\n" +
                "        \"chnName\": \"全屋清洁\",\n" +
                "        \"metaAttrId\": 1391,\n" +
                "        \"rawAttrValue\": \"地面、家具家电表面、台面、瓷砖\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanContent\",\n" +
                "        \"attrValue\": \"灶台、水槽、油烟机表面\",\n" +
                "        \"chnName\": \"厨房除油污\",\n" +
                "        \"metaAttrId\": 1437,\n" +
                "        \"rawAttrValue\": \"灶台、水槽、油烟机表面\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"trimCleanPart\",\n" +
                "        \"attrValue\": \"台盆、淋浴房/浴缸、马桶、镜面\",\n" +
                "        \"chnName\": \"卫生间除水垢\",\n" +
                "        \"metaAttrId\": 2813,\n" +
                "        \"rawAttrValue\": \"台盆、淋浴房/浴缸、马桶、镜面\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"facadeCleanPart\",\n" +
                "        \"attrValue\": \"\",\n" +
                "        \"chnName\": \"擦窗/玻璃\",\n" +
                "        \"metaAttrId\": 2815,\n" +
                "        \"rawAttrValue\": \"\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    }\n" +
                "]", SkuAttrItemDto.class);

        LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig a1 = JSON.parseObject("{\n" +
                "            \"selfSupport\": true,\n" +
                "            \"skuAttrName2IconList\": [\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/50206acc29d78ccf7adef2fe5522f0b13886.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanScope\"\n" +
                "                    ],\n" +
                "                    \"title\": \"全屋清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/bac147005fa4e7fd1bbc01c1b93d1a5a3511.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanContent\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房除油污\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/d1d414ff587f9a2da209e950ab05d8bf4018.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"trimCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间除水垢\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/8a317e9c53d278087b4fc049398edc223535.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"facadeCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"窗/玻璃清洁\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"subTitle\": \"可选以下区域和服务重点打扫\"\n" +
                "        }", LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig.class);

        Map<String, LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig> skuAttrIconConfigMap1 = new HashMap<>();
        skuAttrIconConfigMap1.put("3145", a1);
        ProductSkuCategoryModel firstProductCategory = JSON.parseObject("{\"cnName\":\"深度保洁\",\"productCategoryId\":3145}", ProductSkuCategoryModel.class);
        Boolean isSelfSupport = true;
        DealSkuGroupModuleVO dealSkuGroupModuleVO = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVO(skuAttrItemDtos, skuAttrIconConfigMap1, firstProductCategory, isSelfSupport);
        LifeCleanUtils lifeCleanUtils = new LifeCleanUtils();
        assertNotNull(dealSkuGroupModuleVO);
    }


    @Test
    public void testComputeWithNullFirstProductCategoryTrrue() throws Throwable {
        lifeCleanDetailSkuListModuleOpt = new LifeCleanDetailSkuListModuleOpt();
        List<SkuAttrItemDto> skuAttrItemDtos = JSON.parseArray("[\n" +
                "    {\n" +
                "        \"attrName\": \"serviceDuration\",\n" +
                "        \"attrValue\": \"3小时\",\n" +
                "        \"chnName\": \"服务时长\",\n" +
                "        \"metaAttrId\": 1390,\n" +
                "        \"rawAttrValue\": \"3小时\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"area\",\n" +
                "        \"attrValue\": \"≤50㎡\",\n" +
                "        \"chnName\": \"参考适用面积\",\n" +
                "        \"metaAttrId\": 1003,\n" +
                "        \"rawAttrValue\": \"≤50㎡\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanTool\",\n" +
                "        \"attrValue\": \"服务人员携带\",\n" +
                "        \"chnName\": \"清洁工具\",\n" +
                "        \"metaAttrId\": 1393,\n" +
                "        \"rawAttrValue\": \"服务人员携带\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"outCleanScope\",\n" +
                "        \"attrValue\": \"\",\n" +
                "        \"chnName\": \"除外范围\",\n" +
                "        \"metaAttrId\": 1392,\n" +
                "        \"rawAttrValue\": \"\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"unclassifiedTools\",\n" +
                "        \"attrValue\": \"清洁工具包\",\n" +
                "        \"chnName\": \"携带工具\",\n" +
                "        \"metaAttrId\": 3113,\n" +
                "        \"rawAttrValue\": \"清洁工具包\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"product\",\n" +
                "        \"attrValue\": \"服务人员携带\",\n" +
                "        \"chnName\": \"清洁剂\",\n" +
                "        \"metaAttrId\": 976,\n" +
                "        \"rawAttrValue\": \"服务人员携带\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"material\",\n" +
                "        \"attrValue\": \"除油污剂、消毒剂\",\n" +
                "        \"chnName\": \"携带清洁剂\",\n" +
                "        \"metaAttrId\": 750,\n" +
                "        \"rawAttrValue\": \"除油污剂、消毒剂\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanScope\",\n" +
                "        \"attrValue\": \"地面、家具家电表面、台面、瓷砖\",\n" +
                "        \"chnName\": \"全屋清洁\",\n" +
                "        \"metaAttrId\": 1391,\n" +
                "        \"rawAttrValue\": \"地面、家具家电表面、台面、瓷砖\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"cleanContent\",\n" +
                "        \"attrValue\": \"灶台、水槽、油烟机表面\",\n" +
                "        \"chnName\": \"厨房除油污\",\n" +
                "        \"metaAttrId\": 1437,\n" +
                "        \"rawAttrValue\": \"灶台、水槽、油烟机表面\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"trimCleanPart\",\n" +
                "        \"attrValue\": \"台盆、淋浴房/浴缸、马桶、镜面\",\n" +
                "        \"chnName\": \"卫生间除水垢\",\n" +
                "        \"metaAttrId\": 2813,\n" +
                "        \"rawAttrValue\": \"台盆、淋浴房/浴缸、马桶、镜面\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    },\n" +
                "    {\n" +
                "        \"attrName\": \"facadeCleanPart\",\n" +
                "        \"attrValue\": \"\",\n" +
                "        \"chnName\": \"擦窗/玻璃\",\n" +
                "        \"metaAttrId\": 2815,\n" +
                "        \"rawAttrValue\": \"\",\n" +
                "        \"sequence\": 0,\n" +
                "        \"valueType\": 500\n" +
                "    }\n" +
                "]", SkuAttrItemDto.class);

        LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig a1 = JSON.parseObject("{\n" +
                "            \"selfSupport\": true,\n" +
                "            \"skuAttrName2IconList\": [\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/50206acc29d78ccf7adef2fe5522f0b13886.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanScope\"\n" +
                "                    ],\n" +
                "                    \"title\": \"全屋清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/bac147005fa4e7fd1bbc01c1b93d1a5a3511.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanContent\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房除油污\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/d1d414ff587f9a2da209e950ab05d8bf4018.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"trimCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间除水垢\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/8a317e9c53d278087b4fc049398edc223535.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"facadeCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"窗/玻璃清洁\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"subTitle\": \"可选以下区域和服务重点打扫\"\n" +
                "        }", LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig.class);

        Config config = JSON.parseObject("{\n" +
                "    \"groupName\": \"头图展示\",\n" +
                "    \"lifeCleanProductCategory2SkuAttrIconMap\": {\n" +
                "        \"2104941\": {\n" +
                "            \"selfSupport\": false,\n" +
                "            \"skuAttrName2IconList\": [\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/7b316e9e032250e25930a08571e7f2793910.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanScope\"\n" +
                "                    ],\n" +
                "                    \"title\": \"全屋清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/ff0e93d04f8ea89f5e909fac2fe586f83661.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanContent\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房除油污\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/a1453db8c4cf42c865e9709d72fc5f8c3896.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"trimCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间除水垢\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/6636bc501bc4924f027f5095d61ed4c73667.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"facadeCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"窗/玻璃清洁\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"subTitle\": \"包含以下所有服务\"\n" +
                "        },\n" +
                "        \"841\": {\n" +
                "            \"selfSupport\": false\n" +
                "        },\n" +
                "        \"3145\": {\n" +
                "            \"selfSupport\": false,\n" +
                "            \"skuAttrName2IconList\": [\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/50206acc29d78ccf7adef2fe5522f0b13886.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanScope\"\n" +
                "                    ],\n" +
                "                    \"title\": \"全屋清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/bac147005fa4e7fd1bbc01c1b93d1a5a3511.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanContent\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房除油污\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/d1d414ff587f9a2da209e950ab05d8bf4018.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"trimCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间除水垢\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/8a317e9c53d278087b4fc049398edc223535.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"facadeCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"窗/玻璃清洁\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"subTitle\": \"可选以下区域和服务重点打扫\"\n" +
                "        },\n" +
                "        \"2104707\": {\n" +
                "            \"selfSupport\": false,\n" +
                "            \"skuAttrName2IconList\": [\n" +
                "                {\n" +
                "                    \"icon\": \"https://p1.meituan.net/travelcube/e44eea6b4bd15fe05f6e50fe807597745314.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"content\"\n" +
                "                    ],\n" +
                "                    \"title\": \"宠物专区清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/50206acc29d78ccf7adef2fe5522f0b13886.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"productContent\"\n" +
                "                    ],\n" +
                "                    \"title\": \"全屋清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/d18a0ff21e92e73bf9b01a305b59fb703649.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"methanalFeature\"\n" +
                "                    ],\n" +
                "                    \"title\": \"窗/玻璃清洁\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"subTitle\": \"可选以下区域和服务重点打扫\"\n" +
                "        },\n" +
                "        \"1426\": {\n" +
                "            \"selfSupport\": false,\n" +
                "            \"skuAttrName2IconList\": [\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/bac147005fa4e7fd1bbc01c1b93d1a5a3511.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"serviceScope\",\n" +
                "                        \"serviceBodyRange\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房电器及用品清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/1f44499e78555a10aef97c2101c6f8da3538.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"facadeCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房储物区域\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/56868644a474bb69aee6625f91e0d98c3692.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"cleanScope\"\n" +
                "                    ],\n" +
                "                    \"title\": \"厨房窗/玻璃清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/travelcube/d1d414ff587f9a2da209e950ab05d8bf4018.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"applytarget\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫浴与洁具清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/a763eb56815baba5c10a046bc309554d3730.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"applyscene\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间电器清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/901387ef8b805414c384e2e7af6d096f3514.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"suitcourse\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间储物区域\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/56868644a474bb69aee6625f91e0d98c3692.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"applyType\"\n" +
                "                    ],\n" +
                "                    \"title\": \"卫生间窗/玻璃清洁\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"icon\": \"https://p0.meituan.net/ingee/36daf5cc4f90682f7e92c75448ac279c3985.png\",\n" +
                "                    \"skuAttrList\": [\n" +
                "                        \"suitablePlace\",\n" +
                "                        \"trimCleanPart\"\n" +
                "                    ],\n" +
                "                    \"title\": \"其他区域清洁\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"subTitle\": \"包含以下所有服务项\"\n" +
                "        }\n" +
                "    }\n" +
                "}", Config.class);

        Map<String, LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig> skuAttrIconConfigMap1 = new HashMap<>();
        skuAttrIconConfigMap1.put("3145", a1);
        ProductSkuCategoryModel firstProductCategory = JSON.parseObject("{\"cnName\":\"深度保洁\",\"productCategoryId\":3145}", ProductSkuCategoryModel.class);
        Boolean isSelfSupport = true;
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVO = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVOs(skuAttrItemDtos, config, firstProductCategory, isSelfSupport);
        assertNotNull(dealSkuGroupModuleVO);
    }


    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private LifeCleanDetailSkuListModuleOpt.Config config;
    @Mock
    private DealDetailInfoModel dealDetailInfoModel;
    @Mock
    private ProductSkuCategoryModel firstProductCategory;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        lifeCleanDetailSkuListModuleOpt = new LifeCleanDetailSkuListModuleOpt();
    }

    /**
     * 测试compute方法，当skuAttrItemDtos为null时
     */
    @Test
    public void testComputeWithNullSkuAttrItemDtos() throws Throwable {
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result = lifeCleanDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

    /**
     * 测试compute方法，当config为null时
     */
    @Test
    public void testComputeWithNullConfig() throws Throwable {
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getProductCategories()).thenReturn(Collections.singletonList(firstProductCategory));
        List<DealDetailSkuListModuleGroupModel> result = lifeCleanDetailSkuListModuleOpt.compute(activityCxt, param, null);
        assertNull(result);
    }

    /**
     * 测试compute方法，当firstProductCategory为null时
     */
    @Test
    public void testComputeWithNullFirstProductCategory1() throws Throwable {
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getProductCategories()).thenReturn(Collections.emptyList());
        when(config.getLifeCleanProductCategory2SkuAttrIconMap()).thenReturn(new HashMap<>());
        List<DealDetailSkuListModuleGroupModel> result = lifeCleanDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

    /**
     * 测试compute方法，当lifeCleanProductCategory2SkuAttrIconMap为空时
     */
    @Test
    public void testComputeWithEmptyLifeCleanProductCategory2SkuAttrIconMap() throws Throwable {
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getProductCategories()).thenReturn(Collections.singletonList(firstProductCategory));
        when(config.getLifeCleanProductCategory2SkuAttrIconMap()).thenReturn(new HashMap<>());
        List<DealDetailSkuListModuleGroupModel> result = lifeCleanDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

//    /**
//     * 测试compute方法，正常情况
//     */
//    @Test
//    public void testComputeWithValidParameters() throws Throwable {
//        // 此处省略了具体的mock数据设置，因为涉及到的模型和逻辑较为复杂，需要根据实际情况填充mock逻辑
//        // 假设所有的输入参数都是有效的，并且mock相应的方法返回期望的结果
//
//        // arrange
//        // 此处填充mock对象的返回值
//
//        // act
//        List<DealDetailSkuListModuleGroupModel> result = lifeCleanDetailSkuListModuleOpt.compute(activityCxt, param, config);
//
//        // assert
//        // 此处根据期望的结果进行断言
//        assertNotNull(result);
//        // 更多的断言逻辑...
//    }
}
