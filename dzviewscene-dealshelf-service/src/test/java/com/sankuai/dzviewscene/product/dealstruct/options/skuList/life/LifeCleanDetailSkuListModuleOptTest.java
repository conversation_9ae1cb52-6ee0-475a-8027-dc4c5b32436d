package com.sankuai.dzviewscene.product.dealstruct.options.skuList.life;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.LifeCleanAttrVOListOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.life.LifeCleanDetailSkuListModuleOpt.Config;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.filterlist.utils.LifeCleanUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import org.junit.*;

@Ignore
@RunWith(MockitoJUnitRunner.class)
public class LifeCleanDetailSkuListModuleOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @Mock
    private LifeCleanDetailSkuListModuleOpt lifeCleanDetailSkuListModuleOpt;

    @Mock
    private LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt;

    @Mock
    private List<SkuAttrItemDto> mockSkuAttrItemDtos;
    @Mock
    private Map<String, LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig> mockSkuAttrIconConfigMap;
    @Mock
    private ProductSkuCategoryModel mockFirstProductCategory;
    @Mock
    private LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig mockSkuAttrIconConfig;



//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//        lifeCleanDetailSkuListModuleOpt = new LifeCleanDetailSkuListModuleOpt();
//    }




    @Test
    public void testComputeWithNullSkuAttrItemDtos() throws Throwable {
        LifeCleanDetailSkuListModuleOpt lifeCleanDetailSkuListModuleOpt = new LifeCleanDetailSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result=lifeCleanDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWithNullFirstProductCategory() throws Throwable {
        LifeCleanDetailSkuListModuleOpt lifeCleanDetailSkuListModuleOpt = new LifeCleanDetailSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getProductCategories()).thenReturn(Collections.emptyList());
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(null);
        List<DealDetailSkuListModuleGroupModel> result=lifeCleanDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

    /**
     * 测试 skuAttrItemDtos 为空的情况
     */
    @Test
    public void testBuildDealSkuGroupModuleVO_WithEmptySkuAttrItemDtos() {
        // arrange
//        when(mockFirstProductCategory.getProductCategoryId()).thenReturn(1L);

        // act
        DealSkuGroupModuleVO result = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVO(Collections.emptyList(), mockSkuAttrIconConfigMap, mockFirstProductCategory, true);

        // assert
        assertNull(result);
    }

    /**
     * 测试 firstProductCategory 为 null 的情况
     */
    @Test
    public void testBuildDealSkuGroupModuleVO_WithNullFirstProductCategory() {
        // act
        DealSkuGroupModuleVO result = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVO(mockSkuAttrItemDtos, mockSkuAttrIconConfigMap, null, true);
        // assert
        assertNull(result);
    }

    /**
     * 测试 skuAttrIconConfigMap 中没有对应的配置的情况
     */
    @Test
    public void testBuildDealSkuGroupModuleVO_WithNoMatchingConfig() {
        // arrange
//        when(mockFirstProductCategory.getProductCategoryId()).thenReturn(1L);

        DealSkuGroupModuleVO result = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVO(mockSkuAttrItemDtos, new HashMap<>(), mockFirstProductCategory, true);
        // assert
        assertNull(result);
    }

    /**
     * 测试 isSelfSupport 与 configSelfSupport 不匹配的情况
     */
    @Test
    public void testBuildDealSkuGroupModuleVO_WithMismatchingSelfSupport() {
        // arrange
//        when(mockFirstProductCategory.getProductCategoryId()).thenReturn(1L);
//        when(mockSkuAttrIconConfig.isSelfSupport()).thenReturn(false);
        Map<String, LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig> configMap = new HashMap<>();
        configMap.put("1", mockSkuAttrIconConfig);
        DealSkuGroupModuleVO result = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVO(mockSkuAttrItemDtos, configMap, mockFirstProductCategory, true);
        // assert
        assertNull(result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBuildDealSkuGroupModuleVO_WithValidInput() {
        // arrange
//        when(mockFirstProductCategory.getProductCategoryId()).thenReturn(1L);
//        when(mockSkuAttrIconConfig.isSelfSupport()).thenReturn(true);
//        when(mockSkuAttrIconConfig.getSkuAttrName2IconList()).thenReturn(Collections.emptyList());
        Map<String, LifeCleanDetailSkuListModuleOpt.SkuAttrIconConfig> configMap = new HashMap<>();
        configMap.put("1", mockSkuAttrIconConfig);
        LifeCleanUtils lifeCleanUtils = new LifeCleanUtils();
        // act
        DealSkuGroupModuleVO result = lifeCleanDetailSkuListModuleOpt.buildDealSkuGroupModuleVO(mockSkuAttrItemDtos, configMap, mockFirstProductCategory, true);
        assertNull(result);
    }


}
