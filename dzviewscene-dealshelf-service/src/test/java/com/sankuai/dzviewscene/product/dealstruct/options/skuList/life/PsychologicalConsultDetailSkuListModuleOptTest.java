package com.sankuai.dzviewscene.product.dealstruct.options.skuList.life;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.life.PsychologicalConsultDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.life.PsychologicalConsultDetailSkuListModuleOpt.Config;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PsychologicalConsultDetailSkuListModuleOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private PsychologicalConsultDetailSkuListModuleOpt.Param param;

    @Mock
    private Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    /**
     * 测试 buildServiceItemPrice 方法，当 salePrice 为 null 时
     */
    @Test
    public void testBuildServiceItemPriceWhenSalePriceIsNull() {
        // arrange
        PsychologicalConsultDetailSkuListModuleOpt opt = new PsychologicalConsultDetailSkuListModuleOpt();
        Config config = new Config();
        config.setRMB_SIGN("¥");
        // act
        String result = opt.buildServiceItemPrice(null, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试 buildServiceItemPrice 方法，当 salePrice 不为 null 时
     */
    @Test
    public void testBuildServiceItemPriceWhenSalePriceIsNotNull() {
        // arrange
        PsychologicalConsultDetailSkuListModuleOpt opt = new PsychologicalConsultDetailSkuListModuleOpt();
        Config config = new Config();
        config.setRMB_SIGN("¥");
        BigDecimal salePrice = new BigDecimal("10.01");
        // act
        String result = opt.buildServiceItemPrice(salePrice, config);
        // assert
        assertEquals("¥10.01", result);
    }

    /**
     * 测试场景：dealAttrs 列表为空
     */
    @Test
    public void testComputeDealAttrsIsEmpty() throws Throwable {
        PsychologicalConsultDetailSkuListModuleOpt psychologicalConsultDetailSkuListModuleOpt = new PsychologicalConsultDetailSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Collections.emptyList());
        List<DealDetailSkuListModuleGroupModel> result = psychologicalConsultDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertEquals(0, result.size());
    }

    /**
     * 测试场景：dealAttrs 列表不为空，但解析服务内容后的 PsychologicalServiceItem 列表为空
     */
    @Test
    public void testComputePsychologicalServiceItemsIsEmpty() throws Throwable {
        PsychologicalConsultDetailSkuListModuleOpt psychologicalConsultDetailSkuListModuleOpt = new PsychologicalConsultDetailSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("key", "value")));
        List<DealDetailSkuListModuleGroupModel> result = psychologicalConsultDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertEquals(0, result.size());
    }

    /**
     * 测试场景: normal正常心理咨询场景, dealattrs列表不为空, 且解析后的PsychologicalServiceItem不为空
     */
    @Test
    public void testNormalScene() throws Throwable {
        PsychologicalConsultDetailSkuListModuleOpt psychologicalConsultDetailSkuListModuleOpt = new PsychologicalConsultDetailSkuListModuleOpt();
        String json = "{\"additionalProjectList\":[],\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1035320110,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1035320110,\\\\\\\"title\\\\\\\":null,\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"mustGroups\\\\\\\":[],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"html\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1035320110,\\\"title\\\":null,\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":[],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"html\\\"},\\\"productCategories\\\":[],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1035320110,\\\"title\\\":null,\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":null,\\\"marketPrice\\\":null,\\\"mustGroups\\\":null,\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"\",\"value\":\"\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"团购详情\\\",\\\"content\\\":[{\\\"type\\\":\\\"table\\\",\\\"data\\\":{\\\"schema\\\":[\\\"名称\\\",\\\"数量\\\",\\\"价值\\\"],\\\"totalPrice\\\":222.35,\\\"salePrice\\\":111.45,\\\"groups\\\":[{\\\"isRequired\\\":true,\\\"repeatable\\\":false,\\\"values\\\":[[\\\"心理新上单1\\\",\\\"1\\\",\\\"111.117元\\\"],[\\\"心理线上单2\\\",\\\"1\\\",\\\"111.229元\\\"]],\\\"name\\\":\\\"\\\",\\\"fullName\\\":\\\"\\\",\\\"title\\\":\\\"\\\",\\\"optionalCount\\\":0}],\\\"totalPriceDesc\\\":\\\"总价值\\\"}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":222.35,\\\"groups\\\":[{\\\"units\\\":[{\\\"serviceItemName\\\":\\\"心理新上单1\\\",\\\"price\\\":111.117,\\\"serviceItemValue\\\":{\\\"objectVersion\\\":43,\\\"objectValues\\\":{\\\"table_amount\\\":\\\"1\\\",\\\"PsychologicalService\\\":[{\\\"ConsultingServicesContent\\\":\\\"1服务内容\\\",\\\"SingleServiceTime\\\":\\\"111\\\"},{\\\"ConsultingServicesContent\\\":\\\"2服务内容\\\",\\\"SingleServiceTime\\\":\\\"222\\\"},{\\\"ConsultingServicesContent\\\":\\\"3服务内容\\\",\\\"SingleServiceTime\\\":\\\"333\\\"}]},\\\"objectId\\\":20293169}},{\\\"serviceItemName\\\":\\\"心理线上单2\\\",\\\"price\\\":111.229,\\\"serviceItemValue\\\":{\\\"objectVersion\\\":43,\\\"objectValues\\\":{\\\"table_amount\\\":\\\"1\\\",\\\"PsychologicalService\\\":[{\\\"ConsultingServicesContent\\\":\\\"1服务内容\\\",\\\"SingleServiceTime\\\":\\\"111\\\"},{\\\"ConsultingServicesContent\\\":\\\"2服务内容\\\",\\\"SingleServiceTime\\\":\\\"222\\\"},{\\\"ConsultingServicesContent\\\":\\\"3服务内容\\\",\\\"SingleServiceTime\\\":\\\"333\\\"}]},\\\"objectId\\\":20293169}}],\\\"optionalCount\\\":0}]}},{\\\"type\\\":\\\"richtext\\\",\\\"data\\\":\\\"补充信息需显示\\\"}]}\"},{\"name\":\"psychological_consultant_service_mode\",\"value\":\"视频,语音,语音/视频\"},{\"name\":\"ConcernType\",\"value\":\"情绪压力\"},{\"name\":\"ConsultantLevel\",\"value\":\"资深咨询师\"},{\"name\":\"EmotionalStressServiceDirection\",\"value\":\"焦虑抑郁,无意义感,情绪低落,孤独,无价值感,情绪失控,恐惧,心理创伤\"},{\"name\":\"psychological_consultant_service_mode\",\"value\":\"视频\"},{\"name\":\"ConcernType\",\"value\":\"情绪压力\"},{\"name\":\"ConsultantLevel\",\"value\":\"资深咨询师\"},{\"name\":\"EmotionalStressServiceDirection\",\"value\":\"焦虑抑郁\"}],\"dealDetailDtoModel\":{\"dealGroupId\":1035320110,\"skuUniStructuredDto\":{\"mustGroups\":[],\"optionalGroups\":[]},\"structType\":\"html\"},\"dealId\":1035320110,\"dealTitle\":\"[情绪压力] 资深咨询师｜心理新上单\",\"desc\":\"补充信息需显示\",\"marketPrice\":\"222.35\",\"salePrice\":\"111.45\",\"unifyProduct\":false}";
        dealDetailInfoModel = JSON.parseObject(json, DealDetailInfoModel.class);
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
//        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("key", "value")));
        List<DealDetailSkuListModuleGroupModel> result = psychologicalConsultDetailSkuListModuleOpt.compute(activityCxt, param, config);
        assertEquals(1, result.size());
    }
}