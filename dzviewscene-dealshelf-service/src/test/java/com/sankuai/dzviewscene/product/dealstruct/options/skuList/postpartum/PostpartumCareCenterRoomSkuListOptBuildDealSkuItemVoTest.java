package com.sankuai.dzviewscene.product.dealstruct.options.skuList.postpartum;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

/**
 * Test class for PostpartumCareCenterRoomSkuListOpt's buildDealSkuItemVo method
 */
@RunWith(MockitoJUnitRunner.class)
public class PostpartumCareCenterRoomSkuListOptBuildDealSkuItemVoTest {

    @Spy
    private PostpartumCareCenterRoomSkuListOpt postpartumCareCenterRoomSkuListOpt;

    private List<AttrM> dealAttrs;

    private String moduleType;

    @Mock
    private PostpartumCareCenterRoomSkuListOpt.AttrListGroupModel attrListGroupModel;

    public PostpartumCareCenterRoomSkuListOptBuildDealSkuItemVoTest() {
        dealAttrs = new ArrayList<>();
        moduleType = "testModule";
    }

    @Test
    public void testBuildDealSkuItemVo_WhenAttrListGroupModelNull() throws Throwable {
        // arrange
        attrListGroupModel = null;
        // act
        List<DealSkuItemVO> result = postpartumCareCenterRoomSkuListOpt.buildDealSkuItemVo(dealAttrs, attrListGroupModel, moduleType);
        // assert
        assertNull(result);
    }
}
