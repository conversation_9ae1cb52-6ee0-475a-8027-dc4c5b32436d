package com.sankuai.dzviewscene.product.dealstruct.options.skuList.postpartum;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PostpartumCareCenterRoomSkuListOptBuildSkuItemVoTest {

    @Mock
    private PostpartumCareCenterRoomSkuListOpt postpartumCareCenterRoomSkuListOpt;

    @Test
    public void testBuildSkuItemVoDealAttrsIsNull() throws Throwable {
        List<AttrM> dealAttrs = null;
        DealSkuItemVO result = postpartumCareCenterRoomSkuListOpt.buildSkuItemVo(dealAttrs, null, null);
        assertNull(result);
    }

    @Test
    public void testBuildSkuItemVoAttrModelListIsNull() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        DealSkuItemVO result = postpartumCareCenterRoomSkuListOpt.buildSkuItemVo(dealAttrs, null, null);
        assertNull(result);
    }
}
