package com.sankuai.dzviewscene.product.dealstruct.options.skuList.postpartum;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PostpartumCareCenterRoomSkuListOptProcessGroupTest {

    @InjectMocks
    private PostpartumCareCenterRoomSkuListOpt postpartumCareCenterRoomSkuListOpt;

    @Test
    public void testProcessGroupAttrValueNotStartWithBracket() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM("key", "{\"name\":\"value1\"}, {\"name\":\"value2\"}");
        dealAttrs.add(attrM);
        String attrKey = "key";
        Class<String> clazz = String.class;
        @SuppressWarnings("unchecked")
        Function<List<String>, List<SkuAttrAttrItemVO>> buildFunction = mock(Function.class);
        List<SkuAttrAttrItemVO> mockResult = new ArrayList<>();
        mockResult.add(new SkuAttrAttrItemVO());
        when(buildFunction.apply(anyList())).thenReturn(mockResult);
        List<SkuAttrAttrItemVO> valueAttrs = new ArrayList<>();
        // act
        postpartumCareCenterRoomSkuListOpt.processGroup(dealAttrs, attrKey, clazz, buildFunction, valueAttrs);
        // assert
        verify(buildFunction).apply(anyList());
        assertEquals(1, valueAttrs.size());
    }

    @Test
    public void testProcessGroupAttrValueStartWithBracket() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM("key", "[{\"name\":\"value1\"}, {\"name\":\"value2\"}]");
        dealAttrs.add(attrM);
        String attrKey = "key";
        Class<String> clazz = String.class;
        @SuppressWarnings("unchecked")
        Function<List<String>, List<SkuAttrAttrItemVO>> buildFunction = mock(Function.class);
        List<SkuAttrAttrItemVO> mockResult = new ArrayList<>();
        mockResult.add(new SkuAttrAttrItemVO());
        when(buildFunction.apply(anyList())).thenReturn(mockResult);
        List<SkuAttrAttrItemVO> valueAttrs = new ArrayList<>();
        // act
        postpartumCareCenterRoomSkuListOpt.processGroup(dealAttrs, attrKey, clazz, buildFunction, valueAttrs);
        // assert
        verify(buildFunction).apply(anyList());
        assertEquals(1, valueAttrs.size());
    }
}
