package com.sankuai.dzviewscene.product.dealstruct.options.skuList.postpartum;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.BathSkuAttrListOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.*;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PostpartumCareCenterRoomSkuListOptTest {

    private PostpartumCareCenterRoomSkuListOpt opt = new PostpartumCareCenterRoomSkuListOpt();

    private PostpartumCareCenterRoomSkuListOpt postpartumCareCenterRoomSkuListOpt;

    private PostpartumCareCenterRoomSkuListOpt.Config configMock;

    private List<AttrM> dealAttrsMock;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private BathSkuAttrListOpt.Config config;

    @Mock
    private ActivityCxt context;

    @Before
    public void setUp() {
        postpartumCareCenterRoomSkuListOpt = new PostpartumCareCenterRoomSkuListOpt();
        configMock = Mockito.mock(PostpartumCareCenterRoomSkuListOpt.Config.class);
        dealAttrsMock = Mockito.mock(List.class);
    }

    // 辅助方法，用于创建测试对象
    private PostpartumCareCenterRoomSkuListOpt.MomSupplies createMomSupplies(String name, String number, String use) {
        PostpartumCareCenterRoomSkuListOpt.MomSupplies supplies = new PostpartumCareCenterRoomSkuListOpt.MomSupplies();
        supplies.setMomSuppliesName(name);
        supplies.setMomSuppliesNumber(number);
        supplies.setMomInstoreUse(use);
        return supplies;
    }

    private PostpartumCareCenterRoomSkuListOpt.BabySupplies createBabySupplies(String name, String number, String use) {
        PostpartumCareCenterRoomSkuListOpt.BabySupplies supplies = new PostpartumCareCenterRoomSkuListOpt.BabySupplies();
        supplies.setBabySuppliesName(name);
        supplies.setBabySuppliesNumber(number);
        supplies.setBabyInstoreUse(use);
        return supplies;
    }

    private PostpartumCareCenterRoomSkuListOpt.MomCareProject createMomCareProject(String name, String number) {
        PostpartumCareCenterRoomSkuListOpt.MomCareProject project = new PostpartumCareCenterRoomSkuListOpt.MomCareProject();
        project.setMomCareProjectName(name);
        project.setMomProjectNumber(number);
        return project;
    }

    private PostpartumCareCenterRoomSkuListOpt.BabyCareProject createBabyCareProject(String name, String number) {
        PostpartumCareCenterRoomSkuListOpt.BabyCareProject project = new PostpartumCareCenterRoomSkuListOpt.BabyCareProject();
        project.setBabyCareProjectName(name);
        project.setBabyProjectNumber(number);
        return project;
    }

    @Test
    public void testComputeDeaConfigIslNull() {
        String string = "[\n" + "    {\n" + "        \"name\": \"mom_supplies\",\n" + "        \"value\": [\n" + "            {\n" + "                \"mom_supplies_name\": \"xxx\",\n" + "                \"mom_supplies_number\": \"2\",\n" + "                \"mom_instore_use\": \"无使用限制\"\n" + "            },\n" + "            {\n" + "                \"mom_supplies_name\": \"yyy\",\n" + "                \"mom_supplies_number\": \"1\",\n" + "                \"mom_instore_use\": \"无使用限制\"\n" + "            }\n" + "        ]\n" + "    },\n" + "    {\n" + "        \"name\": \"baby_supplies\",\n" + "        \"value\": [\n" + "            {\n" + "                \"baby_supplies_name\": \"xxx\",\n" + "                \"baby_supplies_number\": \"2\",\n" + "                \"baby_instore_use\": \"无使用限制\"\n" + "            },\n" + "            {\n" + "                \"baby_supplies_name\": \"yyy\",\n" + "                \"baby_supplies_number\": \"1\",\n" + "                \"baby_instore_use\": \"无使用限制\"\n" + "            }\n" + "        ]\n" + "    },\n" + "    {\n" + "        \"name\": \"mom_care_project\",\n" + "        \"value\": [\n" + "            {\n" + "                \"mom_care_project_name\": \"xxx\",\n" + "                \"mom_project_number\": \"2\"\n" + "            },\n" + "            {\n" + "                \"mom_care_project_name\": \"zzz\",\n" + "                \"mom_project_number\": \"3\"\n" + "            }\n" + "        ]\n" + "    },\n" + "    {\n" + "        \"name\": \"baby_care_project\",\n" + "        \"value\": [\n" + "            {\n" + "                \"baby_care_project_name\": \"xxx\",\n" + "                \"baby_project_number\": \"2\"\n" + "            },\n" + "            {\n" + "                \"baby_care_project_name\": \"zzz\",\n" + "                \"baby_project_number\": \"1\"\n" + "            }\n" + "        ]\n" + "    }\n" + "]\n";
        String configString = "{\n" + "    \"attrListGroupModels\": [\n" + "        {\n" + "            \"groupName\": \"护理服务\",\n" + "            \"attrModelList\": [\n" + "                {\n" + "                    \"displayName\": \"妈妈护理\"\n" + "                },\n" + "                {\n" + "                    \"displayName\": \"宝宝护理\"\n" + "                }\n" + "            ]\n" + "        },\n" + "        {\n" + "            \"groupName\": \"提供用品\",\n" + "            \"attrModelList\": [\n" + "                {\n" + "                    \"displayName\": \"妈妈用品\"\n" + "                },\n" + "                {\n" + "                    \"displayName\": \"宝宝用品\"\n" + "                }\n" + "            ]\n" + "        }\n" + "    ]\n" + "}";
        List<AttrM> testAttrM = JSON.parseArray(string, AttrM.class);
        PostpartumCareCenterRoomSkuListOpt.Config testConfig = JSON.parseObject(configString, PostpartumCareCenterRoomSkuListOpt.Config.class);
        List<DealDetailSkuListModuleGroupModel> result = postpartumCareCenterRoomSkuListOpt.compute(null, param, testConfig);
        assertNull("当 attrListGroupModels 为空时，应返回 null", result);
    }

    /**
     * 测试配置为结构化属性，模块样式为standard_facility_v1_2layer，属性值返回为,分隔属性
     */
    @Test
    public void test_Compute_StructAttr_v1_2layer_MultiValue() {
        String configStr = "{\n" + "    \"skuGroupModels\" : [\n" + "        {\n" + "        \"groupName\": \"其他设施\",\n" + "        \"mergeSkuListFlag\": true,\n" + "        \"moduleType\": \"standard_facility_v1_2layer\",\n" + "        \"attrListGroupModels\": [\n" + "            {\n" + "                \"title\": \"清洁服务\",\n" + "                \"dealStructAttrFlag\": true,\n" + "                \"icon\": \"https://p0.meituan.net/ingee/5f3f52a2d9e97d010c04002f02354ab91098.png\",\n" + "                \"attrModelList\": [\n" + "                    {\n" + "                        \"attrNameList\": [\n" + "                        \"roomClean\"\n" + "                        ],\n" + "                        \"icon\": \"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\"\n" + "                    }    \n" + "                ]\n" + "            }\n" + "    ]\n" + "    }\n" + "    ]\n" + "  }";
        PostpartumCareCenterRoomSkuListOpt.Config curConfig = JSON.parseObject(configStr, PostpartumCareCenterRoomSkuListOpt.Config.class);
        AttrM attr1 = new AttrM();
        attr1.setName("roomClean");
        attr1.setValue("每天打扫,隔周清洁");
        List<AttrM> dealAttrs = Lists.newArrayList(attr1);
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealAttrs(dealAttrs);
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, curConfig);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealSkuGroupModuleVOS().size());
        List<DealSkuVO> dealSkuVOS = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList();
        assertEquals("每天打扫", dealSkuVOS.get(0).getItems().get(0).getName());
        assertEquals("隔周清洁", dealSkuVOS.get(0).getItems().get(1).getName());
    }

    /**
     * 测试配置为结构化属性，模块样式为standard_facility_v1_3layer，属性值返回单属性映射FormatModel
     */
    @Test
    public void test_Compute_StructAttr_v1_3layer_SingleAttrFormat() {
        String configStr = "{\n" + "    \"skuGroupModels\" : [\n" + "    {\n" + "        \"groupName\": \"服务内容1\",\n" + "        \"mergeSkuListFlag\": true,\n" + "        \"moduleType\": \"standard_facility_v1_3layer\",\n" + "        \"attrListGroupModels\": [\n" + "            {\n" + "                \"title\": \"月子餐\",\n" + "                \"dealStructAttrFlag\": true,\n" + "                \"attrModelList\": [\n" + "                    {\n" + "                        \"displayName\": \"餐食数量\",\n" + "                        \"attrNameList\": [\n" + "                        \"dinner_number\",\n" + "                        \"dessert_number\"\n" + "                        ],\n" + "                        \"attrFormatModels\": [\n" + "                        {\n" + "                            \"attrName\": \"dinner_number\",\n" + "                            \"displayFormat\": \"%s餐\"\n" + "                        },\n" + "                        {\n" + "                            \"attrName\": \"dessert_number\",\n" + "                            \"displayFormat\": \"%s点\"\n" + "                        }\n" + "                        ],\n" + "                        \"seperator\": \"\"\n" + "                    },\n" + "                    {\n" + "                        \"displayName\": \"每日餐标\",\n" + "                        \"attrNameList\": [\n" + "                        \"day_meal_label\"\n" + "                        ],\n" + "                        \"attrFormatModels\": [\n" + "                        {\n" + "                            \"attrName\": \"day_meal_label\",\n" + "                            \"displayFormat\": \"每日%s元\"\n" + "                        }\n" + "                        ]\n" + "                    }\n" + "                ]\n" + "            }\n" + "    ]\n" + "    }]\n" + "  }\n" + "  ";
        PostpartumCareCenterRoomSkuListOpt.Config curConfig = JSON.parseObject(configStr, PostpartumCareCenterRoomSkuListOpt.Config.class);
        AttrM attr1 = new AttrM();
        attr1.setName("dinner_number");
        attr1.setValue("2");
        AttrM attr2 = new AttrM();
        attr2.setName("dessert_number");
        attr2.setValue("3");
        AttrM attr3 = new AttrM();
        attr3.setName("day_meal_label");
        attr3.setValue("10");
        List<AttrM> dealAttrs = Lists.newArrayList(attr1, attr2, attr3);
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealAttrs(dealAttrs);
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, curConfig);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealSkuGroupModuleVOS().size());
        List<DealSkuVO> dealSkuVOS = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList();
        assertEquals("餐食数量", dealSkuVOS.get(0).getItems().get(0).getName());
        assertEquals("2餐3点", dealSkuVOS.get(0).getItems().get(0).getValue());
        assertEquals("每日餐标", dealSkuVOS.get(0).getItems().get(1).getName());
        assertEquals("每日10元", dealSkuVOS.get(0).getItems().get(1).getValue());
    }

    /**
     * 测试配置为结构化属性，模块样式为standard_facility_v1_3layer，属性值返回单属性映射FormatModel，数据缺失
     */
    @Test
    public void test_Compute_StructAttr_v1_3layer_SingleAttrFormat_Attr_Empty() {
        String configStr = "{\n" + "    \"skuGroupModels\" : [\n" + "    {\n" + "        \"groupName\": \"服务内容1\",\n" + "        \"mergeSkuListFlag\": true,\n" + "        \"moduleType\": \"standard_facility_v1_3layer\",\n" + "        \"attrListGroupModels\": [\n" + "            {\n" + "                \"title\": \"月子餐\",\n" + "                \"dealStructAttrFlag\": true,\n" + "                \"attrModelList\": [\n" + "                    {\n" + "                        \"displayName\": \"餐食数量\",\n" + "                        \"attrNameList\": [\n" + "                        \"dinner_number\",\n" + "                        \"dessert_number\"\n" + "                        ],\n" + "                        \"attrFormatModels\": [\n" + "                        {\n" + "                            \"attrName\": \"dinner_number\",\n" + "                            \"displayFormat\": \"%s餐\"\n" + "                        },\n" + "                        {\n" + "                            \"attrName\": \"dessert_number\",\n" + "                            \"displayFormat\": \"%s点\"\n" + "                        }\n" + "                        ],\n" + "                        \"seperator\": \"\"\n" + "                    },\n" + "                    {\n" + "                        \"displayName\": \"每日餐标\",\n" + "                        \"attrNameList\": [\n" + "                        \"day_meal_label\"\n" + "                        ],\n" + "                        \"attrFormatModels\": [\n" + "                        {\n" + "                            \"attrName\": \"day_meal_label\",\n" + "                            \"displayFormat\": \"每日%s元\"\n" + "                        }\n" + "                        ]\n" + "                    }\n" + "                ]\n" + "            }\n" + "    ]\n" + "    }]\n" + "  }\n" + "  ";
        PostpartumCareCenterRoomSkuListOpt.Config curConfig = JSON.parseObject(configStr, PostpartumCareCenterRoomSkuListOpt.Config.class);
        AttrM attr2 = new AttrM();
        attr2.setName("dessert_number");
        attr2.setValue("3");
        List<AttrM> dealAttrs = Lists.newArrayList(attr2);
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealAttrs(dealAttrs);
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, curConfig);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealSkuGroupModuleVOS().size());
        List<DealSkuVO> dealSkuVOS = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList();
        assertEquals(1, dealSkuVOS.get(0).getItems().size());
        assertEquals("餐食数量", dealSkuVOS.get(0).getItems().get(0).getName());
        assertEquals("3点", dealSkuVOS.get(0).getItems().get(0).getValue());
    }

    /**
     * 测试配置为结构化属性，模块样式为standard_facility_v1_3layer，属性值返回多属性映射FormatModel，支持陪住
     */
    @Test
    public void test_Compute_StructAttr_v1_3layer_MultiAttrFormat_Normal_Support() {
        String configStr = "{\n" + "    \"skuGroupModels\" : [\n" + "    {\n" + "        \"groupName\": \"服务内容1\",\n" + "        \"mergeSkuListFlag\": true,\n" + "        \"moduleType\": \"standard_facility_v1_3layer\",\n" + "        \"attrListGroupModels\": [\n" + "            {\n" + "                \"title\": \"家人陪住\",\n" + "                \"dealStructAttrFlag\": true,\n" + "                \"attrModelList\": [\n" + "                    {\n" + "                        \"displayName\": \"陪住人员\",\n" + "                        \"attrNameList\": [\n" + "                          \"family_accompany\",\n" + "                          \"accompany_number\"\n" + "                        ],\n" + "                        \"attr2MultiSelectFormatModels\": {\n" + "                          \"attrName\": \"family_accompany\",\n" + "                          \"attrValue2MultiFormatModels\": [\n" + "                            {\n" + "                              \"attrValue\": \"支持陪住\",\n" + "                              \"attrFormatModels\": [\n" + "                                {\n" + "                                  \"attrName\": \"accompany_number\",\n" + "                                  \"displayFormat\": \"最多%s人陪住\"\n" + "                                }\n" + "                              ]\n" + "                            },\n" + "                            {\n" + "                              \"attrValue\": \"不支持陪住\",\n" + "                              \"attrFormatModels\": [\n" + "                                {\n" + "                                  \"attrName\": \"accompany_number\",\n" + "                                  \"displayFormat\": \"\"\n" + "                                }\n" + "                              ]\n" + "                            }\n" + "                          ],\n" + "                          \"filterBaseAttr\": true\n" + "                        }\n" + "                    }\n" + "                ]\n" + "            }\n" + "    ]\n" + "    }]\n" + "  }\n" + "  ";
        PostpartumCareCenterRoomSkuListOpt.Config curConfig = JSON.parseObject(configStr, PostpartumCareCenterRoomSkuListOpt.Config.class);
        AttrM attr1 = new AttrM();
        attr1.setName("family_accompany");
        attr1.setValue("支持陪住");
        AttrM attr2 = new AttrM();
        attr2.setName("accompany_number");
        attr2.setValue("3");
        List<AttrM> dealAttrs = Lists.newArrayList(attr1, attr2);
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealAttrs(dealAttrs);
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, curConfig);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getDealSkuGroupModuleVOS().size());
        List<DealSkuVO> dealSkuVOS = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList();
        assertEquals(1, dealSkuVOS.get(0).getItems().size());
        assertEquals("陪住人员", dealSkuVOS.get(0).getItems().get(0).getName());
        assertEquals("最多3人陪住", dealSkuVOS.get(0).getItems().get(0).getValue());
    }

    /**
     * 测试配置为结构化属性，模块样式为standard_facility_v1_3layer，属性值返回多属性映射FormatModel，不支持陪住
     */
    @Test
    public void test_Compute_StructAttr_v1_3layer_MultiAttrFormat_Not_Support() {
        String configStr = "{\n" + "    \"skuGroupModels\" : [\n" + "    {\n" + "        \"groupName\": \"服务内容1\",\n" + "        \"mergeSkuListFlag\": true,\n" + "        \"moduleType\": \"standard_facility_v1_3layer\",\n" + "        \"attrListGroupModels\": [\n" + "            {\n" + "                \"title\": \"家人陪住\",\n" + "                \"dealStructAttrFlag\": true,\n" + "                \"attrModelList\": [\n" + "                    {\n" + "                        \"displayName\": \"陪住人员\",\n" + "                        \"attrNameList\": [\n" + "                          \"family_accompany\",\n" + "                          \"accompany_number\"\n" + "                        ],\n" + "                        \"attr2MultiSelectFormatModels\": {\n" + "                          \"attrName\": \"family_accompany\",\n" + "                          \"attrValue2MultiFormatModels\": [\n" + "                            {\n" + "                              \"attrValue\": \"支持陪住\",\n" + "                              \"attrFormatModels\": [\n" + "                                {\n" + "                                  \"attrName\": \"accompany_number\",\n" + "                                  \"displayFormat\": \"最多%s人陪住\"\n" + "                                }\n" + "                              ]\n" + "                            },\n" + "                            {\n" + "                              \"attrValue\": \"不支持陪住\",\n" + "                              \"attrFormatModels\": [\n" + "                                {\n" + "                                  \"attrName\": \"accompany_number\",\n" + "                                  \"displayFormat\": \"\"\n" + "                                }\n" + "                              ]\n" + "                            }\n" + "                          ],\n" + "                          \"filterBaseAttr\": true\n" + "                        }\n" + "                    }\n" + "                ]\n" + "            }\n" + "    ]\n" + "    }]\n" + "  }\n" + "  ";
        PostpartumCareCenterRoomSkuListOpt.Config curConfig = JSON.parseObject(configStr, PostpartumCareCenterRoomSkuListOpt.Config.class);
        AttrM attr1 = new AttrM();
        attr1.setName("family_accompany");
        attr1.setValue("不支持陪住");
        AttrM attr2 = new AttrM();
        attr2.setName("accompany_number");
        attr2.setValue("3");
        List<AttrM> dealAttrs = Lists.newArrayList(attr1, attr2);
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealAttrs(dealAttrs);
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, curConfig);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testBuildMomSuppliesItemVo() {
        List<PostpartumCareCenterRoomSkuListOpt.MomSupplies> momSupplies = Arrays.asList(createMomSupplies("毛巾", "2", "每天"), createMomSupplies("拖鞋", "1", "无使用限制"));
        List<SkuAttrAttrItemVO> result = opt.buildMomSuppliesItemVo(momSupplies);
        assertEquals(2, result.size());
        assertEquals("毛巾", result.get(0).getName());
        assertEquals(Collections.singletonList("2"), result.get(0).getInfo());
        assertEquals("拖鞋", result.get(1).getName());
        assertEquals(Collections.singletonList("1"), result.get(1).getInfo());
    }

    @Test
    public void testJoinAttrValue() {
        List<String> attrValues = Lists.newArrayList("护士", "月嫂");
        PostpartumCareCenterRoomSkuListOpt.AttrFormatModel attrFormatModel = new PostpartumCareCenterRoomSkuListOpt.AttrFormatModel();
        attrFormatModel.setMultiDisplayFormat("可选%s");
        attrFormatModel.setSeparator("、");
        List<String> joinAttrValue = opt.joinAttrValue(attrValues, attrFormatModel);
        assertEquals(1, joinAttrValue.size());
        assertEquals("可选护士、月嫂", joinAttrValue.get(0));
    }

    @Test
    public void testBuildBabySuppliesItemVo() {
        List<PostpartumCareCenterRoomSkuListOpt.BabySupplies> babySupplies = Arrays.asList(createBabySupplies("奶瓶", "3", "每天"), createBabySupplies("尿布", "999", "无使用限制"));
        List<SkuAttrAttrItemVO> result = opt.buildBabySuppliesItemVo(babySupplies);
        assertEquals(2, result.size());
        assertEquals("奶瓶", result.get(0).getName());
        assertEquals(Collections.singletonList("3"), result.get(0).getInfo());
        assertEquals("尿布", result.get(1).getName());
        assertEquals(Collections.singletonList("按需"), result.get(1).getInfo());
    }

    @Test
    public void testBuildMomCareProjectItemVo() {
        List<PostpartumCareCenterRoomSkuListOpt.MomCareProject> momCareProjects = Arrays.asList(createMomCareProject("按摩", "5"), createMomCareProject("瑜伽", "10"));
        List<SkuAttrAttrItemVO> result = opt.buildMomCareProjectItemVo(momCareProjects);
        assertEquals(2, result.size());
        assertEquals("按摩", result.get(0).getName());
        assertEquals(Collections.singletonList("5次"), result.get(0).getInfo());
        assertEquals("瑜伽", result.get(1).getName());
        assertEquals(Collections.singletonList("10次"), result.get(1).getInfo());
    }

    @Test
    public void testBuildCareProjectsItemVo() {
        List<PostpartumCareCenterRoomSkuListOpt.BabyCareProject> babyCareProjects = Arrays.asList(createBabyCareProject("洗澡", "7"), createBabyCareProject("按摩", "999"));
        List<SkuAttrAttrItemVO> result = opt.buildCareProjectsItemVo(babyCareProjects);
        assertEquals(2, result.size());
        assertEquals("洗澡", result.get(0).getName());
        assertEquals(Collections.singletonList("7次"), result.get(0).getInfo());
        assertEquals("按摩", result.get(1).getName());
        assertEquals(Collections.singletonList("按需"), result.get(1).getInfo());
    }

    @Test
    public void testBuildSkuAttrItem() {
        SkuAttrAttrItemVO result1 = opt.buildSkuAttrItem("毛巾", "2", "每天", true);
        assertEquals("毛巾", result1.getName());
        assertEquals(Collections.singletonList("2"), result1.getInfo());
        SkuAttrAttrItemVO result2 = opt.buildSkuAttrItem("尿布", "999", "无使用限制", true);
        assertEquals("尿布", result2.getName());
        assertEquals(Collections.singletonList("按需"), result2.getInfo());
        SkuAttrAttrItemVO result3 = opt.buildSkuAttrItem("按摩", "5", null, false);
        assertEquals("按摩", result3.getName());
        assertEquals(Collections.singletonList("5次"), result3.getInfo());
    }

    /**
     * 测试输入字符串为空的情况
     */
    @Test
    public void testConvertToJsonArrayEmptyInput() throws Throwable {
        // arrange
        String input = "";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertToJsonArray(input);
        // assert
        assertEquals("[]", result);
    }

    /**
     * 测试输入字符串只包含空格的情况
     */
    @Test
    public void testConvertToJsonArrayBlankInput() throws Throwable {
        // arrange
        String input = " ";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertToJsonArray(input);
        // assert
        assertEquals("[]", result);
    }

    /**
     * 测试输入字符串不包含 JSON 对象的情况
     */
    @Test
    public void testConvertToJsonArrayNoJsonObject() throws Throwable {
        // arrange
        String input = "no json object";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertToJsonArray(input);
        // assert
        assertEquals("[]", result);
    }

    /**
     * 测试输入字符串只包含一个 JSON 对象的情况
     */
    @Test
    public void testConvertToJsonArrayOneJsonObject() throws Throwable {
        // arrange
        String input = "{ \"key\": \"value\" }";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertToJsonArray(input);
        // assert
        assertEquals("[{\"key\":\"value\"}]", result);
    }

    /**
     * 测试输入字符串包含多个 JSON 对象的情况
     */
    @Test
    public void testConvertToJsonArrayMultipleJsonObjects() throws Throwable {
        // arrange
        String input = "{ \"key1\": \"value1\" } { \"key2\": \"value2\" }";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertToJsonArray(input);
        // assert
        // Corrected expectation to match the actual output for multiple JSON objects
        assertEquals("[{\"key1\":\"value1\"},{\"key2\":\"value2\"}]", result);
    }

    /**
     * Test buildModelList when config is null
     */
    @Test
    public void testBuildModelList_WhenConfigIsNull() throws Throwable {
        PostpartumCareCenterRoomSkuListOpt opt = new PostpartumCareCenterRoomSkuListOpt();
        List<AttrM> dealAttrs = new ArrayList<>();
        List<DealDetailSkuListModuleGroupModel> result = opt.buildModelList(dealAttrs, null);
        assertNull(result);
    }

    /**
     * Test buildModelList when config.skuGroupModels is empty
     */
    @Test
    public void testBuildModelList_WhenSkuGroupModelsIsEmpty() throws Throwable {
        PostpartumCareCenterRoomSkuListOpt opt = new PostpartumCareCenterRoomSkuListOpt();
        List<AttrM> dealAttrs = new ArrayList<>();
        PostpartumCareCenterRoomSkuListOpt.Config config = new PostpartumCareCenterRoomSkuListOpt.Config();
        config.setSkuGroupModels(new ArrayList<>());
        List<DealDetailSkuListModuleGroupModel> result = opt.buildModelList(dealAttrs, config);
        assertNull(result);
    }

    /**
     * Test buildModelList when mergeSameGroupNameFlag is true
     */
    @Test
    public void testBuildModelList_WhenMergeSameGroupNameFlagIsTrue() throws Throwable {
        PostpartumCareCenterRoomSkuListOpt opt = new PostpartumCareCenterRoomSkuListOpt();
        List<AttrM> dealAttrs = new ArrayList<>();
        PostpartumCareCenterRoomSkuListOpt.Config config = new PostpartumCareCenterRoomSkuListOpt.Config();
        config.setMergeSameGroupNameFlag(true);
        List<PostpartumCareCenterRoomSkuListOpt.SkuGroupModel> skuGroupModels = new ArrayList<>();
        skuGroupModels.add(new PostpartumCareCenterRoomSkuListOpt.SkuGroupModel());
        config.setSkuGroupModels(skuGroupModels);
        List<DealDetailSkuListModuleGroupModel> result = opt.buildModelList(dealAttrs, config);
        assertNotNull(result);
        // Since the behavior of mergeSameGroupNameModule is private and its effects are internal,
        // we should not verify it directly. Instead, we verify the result of the public method.
    }

    /**
     * Test case to cover the scenario where userName equals NO_USE_LIMIT
     * and isUse is true
     */
    @Test
    public void testBuildSkuAttrItemWithNoUseLimit() {
        // arrange
        String name = "测试商品";
        String numberStr = "100";
        String userName = "仅限店内使用";
        boolean isUse = true;
        // act
        SkuAttrAttrItemVO result = PostpartumCareCenterRoomSkuListOpt.buildSkuAttrItem(name, numberStr, userName, isUse);
        // assert
        assertNotNull(result);
        assertEquals(name, result.getName());
        assertEquals(1, result.getInfo().size());
        assertEquals("100,仅限店内使用", result.getInfo().get(0));
    }

    /**
     * Test case to verify behavior when userName is not NO_USE_LIMIT
     * but isUse is true
     */
    @Test
    public void testBuildSkuAttrItemWithIsUseButNoLimit() {
        // arrange
        String name = "测试商品";
        String numberStr = "100";
        String userName = "其他限制";
        boolean isUse = true;
        // act
        SkuAttrAttrItemVO result = PostpartumCareCenterRoomSkuListOpt.buildSkuAttrItem(name, numberStr, userName, isUse);
        // assert
        assertNotNull(result);
        assertEquals(name, result.getName());
        assertEquals(1, result.getInfo().size());
        assertEquals("100", result.getInfo().get(0));
    }

    /**
     * Test case to verify behavior when isUse is false
     */
    @Test
    public void testBuildSkuAttrItemWithoutIsUse() {
        // arrange
        String name = "测试商品";
        String numberStr = "100";
        String userName = "仅限店内使用";
        boolean isUse = false;
        // act
        SkuAttrAttrItemVO result = PostpartumCareCenterRoomSkuListOpt.buildSkuAttrItem(name, numberStr, userName, isUse);
        // assert
        assertNotNull(result);
        assertEquals(name, result.getName());
        assertEquals(1, result.getInfo().size());
        assertEquals("100次", result.getInfo().get(0));
    }

    /**
     * Test case to verify behavior with large numbers (≥999)
     */
    @Test
    public void testBuildSkuAttrItemWithLargeNumber() {
        // arrange
        String name = "测试商品";
        String numberStr = "999";
        String userName = "仅限店内使用";
        boolean isUse = true;
        // act
        SkuAttrAttrItemVO result = PostpartumCareCenterRoomSkuListOpt.buildSkuAttrItem(name, numberStr, userName, isUse);
        // assert
        assertNotNull(result);
        assertEquals(name, result.getName());
        assertEquals(1, result.getInfo().size());
        assertEquals("按需,仅限店内使用", result.getInfo().get(0));
    }

    @Test
    public void testFindAttrValueDealAttrsIsNull() throws Throwable {
        String result = PostpartumCareCenterRoomSkuListOpt.findAttrValue(null, "name");
        assertNull(result);
    }

    @Test
    public void testFindAttrValueNameIsNull() throws Throwable {
        AttrM attrM = new AttrM("name", "value");
        String result = PostpartumCareCenterRoomSkuListOpt.findAttrValue(Collections.singletonList(attrM), null);
        assertNull(result);
    }

    @Test
    public void testFindAttrValueDealAttrsIsNotEmptyButNotFound() throws Throwable {
        AttrM attrM = new AttrM("otherName", "value");
        String result = PostpartumCareCenterRoomSkuListOpt.findAttrValue(Collections.singletonList(attrM), "name");
        assertNull(result);
    }

    @Test
    public void testFindAttrValueDealAttrsIsNotEmptyAndFound() throws Throwable {
        AttrM attrM = new AttrM("name", "value");
        String result = PostpartumCareCenterRoomSkuListOpt.findAttrValue(Collections.singletonList(attrM), "name");
        assertEquals("value", result);
    }

    /**
     * Test case for null input string
     */
    @Test
    public void testConvertNumberString_NullInput() {
        // arrange
        String numberStr = null;
        String unit = "次";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertNumberString(numberStr, unit);
        // assert
        assertNull(result);
    }

    /**
     * Test case for invalid number format causing exception
     */
    @Test
    public void testConvertNumberString_InvalidNumberFormat() {
        // arrange
        String numberStr = "abc";
        String unit = "次";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertNumberString(numberStr, unit);
        // assert
        assertNull(result);
    }

    /**
     * Test case for number >= 999 with unit
     */
    @Test
    public void testConvertNumberString_LargeNumberWithUnit() {
        // arrange
        String numberStr = "1000";
        String unit = "次";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertNumberString(numberStr, unit);
        // assert
        assertEquals("按需", result);
    }

    /**
     * Test case for number >= 999 without unit
     */
    @Test
    public void testConvertNumberString_LargeNumberWithoutUnit() {
        // arrange
        String numberStr = "999";
        String unit = "";
        // act
        String result = PostpartumCareCenterRoomSkuListOpt.convertNumberString(numberStr, unit);
        // assert
        assertEquals("按需", result);
    }
}
