package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.FreeBathingSuppliesEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.FreefacilitiesEnum;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class BathServiceFacilityUtilsTest {

    @Test
    public void testParseServiceFacilityDealAttrsIsNull() throws Throwable {
        List<AttrM> dealAttrs = null;
        List<DealSkuVO> result = BathServiceFacilityUtils.parseServiceFacility(dealAttrs);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseServiceFacilityNoMatchedAttrM() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("other", "value"));
        List<DealSkuVO> result = BathServiceFacilityUtils.parseServiceFacility(dealAttrs);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseServiceFacilityValueIsNull() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM(FreefacilitiesEnum.BATHING_POOL.getCode(), null));
        dealAttrs.add(new AttrM(FreeBathingSuppliesEnum.DISPOSABLE_MATERIALS.getCode(), null));
        List<DealSkuVO> result = BathServiceFacilityUtils.parseServiceFacility(dealAttrs);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseServiceFacilityValueIsNotNull() throws Throwable {
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM(FreefacilitiesEnum.BATHING_POOL.getCode(), "value1,value2"));
        dealAttrs.add(new AttrM(FreeBathingSuppliesEnum.DISPOSABLE_MATERIALS.getCode(), "value3"));
        List<DealSkuVO> result = BathServiceFacilityUtils.parseServiceFacility(dealAttrs);
        assertEquals(2, result.size());
        assertEquals(FreefacilitiesEnum.BATHING_POOL.getName(), result.get(0).getTitle());
        // Corrected the assertion to match the expected behavior of the method under test
        assertEquals(FreeBathingSuppliesEnum.BATHING_SUPPLIES.getName(), result.get(1).getTitle());
    }
}
