package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import static org.junit.Assert.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.BathServiceProjectEnum;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import static org.mockito.Mockito.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

public class BathSkuUtils_GetProjectDescTest {

    @Test
    public void testGetProjectDescBathTicket() throws Throwable {
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.BATH_TICKET;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescSpa() throws Throwable {
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.SPA;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescRest() throws Throwable {
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.REST;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescServiceFee1() throws Throwable {
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.SERVICE_FEE_1;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto item = new SkuAttrItemDto();
        item.setAttrName("ServiceFeeRatio");
        item.setAttrValue("10%");
        attrItems.add(item);
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescServiceFee2() throws Throwable {
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.SERVICE_FEE_2;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto item = new SkuAttrItemDto();
        item.setAttrName("ServiceFeeRatio");
        item.setAttrValue("15%");
        attrItems.add(item);
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescEmptyAttrItems() throws Throwable {
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.BATH_TICKET;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        assertNotNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testGetProjectDescNullAttrItems() throws Throwable {
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.BATH_TICKET;
        List<SkuAttrItemDto> attrItems = null;
        BathSkuUtils.getProjectDesc(serviceProject, attrItems);
    }
}
