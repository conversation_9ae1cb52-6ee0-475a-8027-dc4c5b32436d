package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.BathServiceProjectEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga.BeautyYogaDealModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BeautyYogaUtilsTest {

    private SkuItemDto skuItemDto;

    @Before
    public void setUp() {
        skuItemDto = new SkuItemDto();
        skuItemDto.setSkuId(1L);
        // Ensure this matches a valid BathServiceProjectEnum value
        skuItemDto.setProductCategory(895L);
        skuItemDto.setName("Test Product");
        skuItemDto.setCopies(1);
        skuItemDto.setMarketPrice(new BigDecimal("10.00"));
        skuItemDto.setStatus(1);
        skuItemDto.setAddTime(new Date());
        skuItemDto.setUpdateTime(new Date());
        SkuAttrItemDto attrItem1 = new SkuAttrItemDto();
        attrItem1.setAttrName("Attribute 1");
        attrItem1.setAttrValue("value1");
        SkuAttrItemDto attrItem2 = new SkuAttrItemDto();
        attrItem2.setAttrName("Attribute 2");
        attrItem2.setAttrValue("value2");
        List<SkuAttrItemDto> attrItems = Arrays.asList(attrItem1, attrItem2);
        skuItemDto.setAttrItems(attrItems);
    }

    @Test
    public void testGetProjectDescBathTicket() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.BATH_TICKET;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    // 其他测试用例类似，修改serviceProject的值和对应的断言即可
    @Test
    public void testGetProjectDescScrub() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.SCRUB;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescMassage() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.MASSAGE;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescFood1() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.FOOD_1;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescFood2() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.FOOD_2;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescFood3() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.FOOD_3;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescJoy() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.JOY;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescSpa() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.SPA;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetProjectDescRest() throws Throwable {
        // arrange
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.REST;
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        // act
        List<DealSkuItemVO> result = BathSkuUtils.getProjectDesc(serviceProject, attrItems);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testBuildSkuInfoWhenSkuItemDtoIsNull() throws Throwable {
        String result = BathSkuUtils.buildSkuInfo(null);
        assertNull("The result should be null when SkuItemDto is null", result);
    }

    /**
     * 测试 buildSkuInfo 方法，当 buildSkuItems 返回的列表为空时
     */
    @Test
    public void testBuildSkuInfoWhenBuildSkuItemsReturnsNull() throws Throwable {
        // arrange
        SkuItemDto skuItemDto = mock(SkuItemDto.class);
        when(BathSkuUtils.buildSkuItems(skuItemDto)).thenReturn(null);
        // act
        String result = BathSkuUtils.buildSkuInfo(skuItemDto);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildSkuItemsNullSkuItemDto() throws Throwable {
        assertNull(BathSkuUtils.buildSkuItems(null));
    }

    @Test
    public void testBuildSkuItemsEmptyAttrItems() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        assertNull(BathSkuUtils.buildSkuItems(skuItemDto));
    }

    @Test
    public void testBuildSkuItemsNullServiceProject() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(new ArrayList<>());
        assertNull(BathSkuUtils.buildSkuItems(skuItemDto));
    }

    @Test
    public void testBuildSkuItemsNameNotInTitleList() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(BathServiceProjectEnum.BATH_TICKET.getServiceProjectId());
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("SomeAttrName");
        attrItem.setAttrValue("SomeValue");
        attrItems.add(attrItem);
        skuItemDto.setAttrItems(attrItems);
        skuItemDto.setName("NonExistingTitle");
        // Expecting non-null because we have valid attributes and a recognized service project
        assertNotNull(BathSkuUtils.buildSkuItems(skuItemDto));
    }

    @Test
    public void testBuildSkuItemsNameInTitleListAndAttrNameNotInNonTicketDisplayFields() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(BathServiceProjectEnum.BATH_TICKET.getServiceProjectId());
        skuItemDto.setName("浴资票");
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("usepeoplenum");
        attrItem.setAttrValue("2");
        attrItems.add(attrItem);
        skuItemDto.setAttrItems(attrItems);
        assertNotNull(BathSkuUtils.buildSkuItems(skuItemDto));
    }

    @Test
    public void testBuildSkuItemsNameInTitleListAndAttrNameInNonTicketDisplayFields() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(BathServiceProjectEnum.BATH_TICKET.getServiceProjectId());
        skuItemDto.setName("浴资票");
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("usepeoplenum");
        attrItem.setAttrValue("2");
        attrItems.add(attrItem);
        SkuAttrItemDto attrItem2 = new SkuAttrItemDto();
        attrItem2.setAttrName("ApplicableDuration");
        attrItem2.setAttrValue("60");
        attrItems.add(attrItem2);
        skuItemDto.setAttrItems(attrItems);
        assertNotNull(BathSkuUtils.buildSkuItems(skuItemDto));
    }

    @Test
    public void testParseServiceItemsByUnit() {
        String serviceType = "";
        List<AttrM> dealAttrs = new ArrayList<>();
        JSONObject unitJSON = null;
        String optionStr = "";
        BeautyYogaDealModuleOpt.Config config = new BeautyYogaDealModuleOpt.Config();

        List<DealSkuVO> result = BeautyYogaUtils.parseServiceItemsByUnit(serviceType, dealAttrs, unitJSON, optionStr, config);

        unitJSON = new JSONObject();
        result = BeautyYogaUtils.parseServiceItemsByUnit(serviceType, dealAttrs, unitJSON, optionStr, config);
        assertNotNull(result);
    }

    @Test
    public void test_getDisplayNameByAttr() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        BeautyYogaUtils beautyYogaUtils = new BeautyYogaUtils();

        Method getDisplayNameByAttr = beautyYogaUtils.getClass().getDeclaredMethod("getDisplayNameByAttr", String.class, Map.class);
        getDisplayNameByAttr.setAccessible(true);

        String string = "specification";

        Map<String, String> attr2ValueMap = new HashMap<>();
        attr2ValueMap.put("specification", "1次");
        String needed = (String) getDisplayNameByAttr.invoke(beautyYogaUtils, string, attr2ValueMap);

        Object o = new Object();
        Assert.assertNotNull(o);
    }

}
