package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealSkuListModuleUtilsTest {

    /**
     * 测试 buildDealSkuVO 方法，所有输入参数都为正常值的情况
     */
    @Test
    public void testBuildDealSkuVONormal() throws Throwable {
        // arrange
        String title = "title";
        String price = "100";
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        String icon = "icon";
        String originalPrice = "200";
        String desc = "desc";
        // act
        DealSkuVO result = DealSkuListModuleUtils.buildDealSkuVO(title, price, Arrays.asList(dealSkuItemVO), icon, originalPrice, desc);
        // assert
        assertNotNull(result);
        assertEquals(title, result.getTitle());
        assertEquals(price, result.getPrice());
        assertEquals(icon, result.getIcon());
        assertEquals(originalPrice, result.getOriginalPrice());
        assertEquals(desc, result.getDesc());
        assertEquals(Arrays.asList(dealSkuItemVO), result.getItems());
    }

    /**
     * 测试 buildDealSkuVO 方法，输入参数为 null 的情况
     */
    @Test
    public void testBuildDealSkuVONull() throws Throwable {
        // arrange
        String title = null;
        String price = null;
        DealSkuItemVO dealSkuItemVO = null;
        String icon = null;
        String originalPrice = null;
        String desc = null;
        // act
        DealSkuVO result = DealSkuListModuleUtils.buildDealSkuVO(title, price, Arrays.asList(dealSkuItemVO), icon, originalPrice, desc);
        // assert
        assertNotNull(result);
        assertEquals(title, result.getTitle());
        assertEquals(price, result.getPrice());
        assertEquals(icon, result.getIcon());
        assertEquals(originalPrice, result.getOriginalPrice());
        assertEquals(desc, result.getDesc());
        assertEquals(Arrays.asList(dealSkuItemVO), result.getItems());
    }

    /**
     * 测试 buildDealSkuVO 方法，输入参数为空字符串的情况
     */
    @Test
    public void testBuildDealSkuVOEmpty() throws Throwable {
        // arrange
        String title = "";
        String price = "";
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        String icon = "";
        String originalPrice = "";
        String desc = "";
        // act
        DealSkuVO result = DealSkuListModuleUtils.buildDealSkuVO(title, price, Arrays.asList(dealSkuItemVO), icon, originalPrice, desc);
        // assert
        assertNotNull(result);
        assertEquals(title, result.getTitle());
        assertEquals(price, result.getPrice());
        assertEquals(icon, result.getIcon());
        assertEquals(originalPrice, result.getOriginalPrice());
        assertEquals(desc, result.getDesc());
        assertEquals(Arrays.asList(dealSkuItemVO), result.getItems());
    }

    /**
     * Test case for invalid JSON parsing
     */
    @Test
    public void testBuildOverNightSkuListModule_InvalidJson() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealOverNightRule");
        attrM.setValue("invalid json");
        dealAttrs.add(attrM);
        // act
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildOverNightSkuListModule(dealAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case for map without identityKey
     */
    @Test
    public void testBuildOverNightSkuListModule_NoIdentityKey() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealOverNightRule");
        attrM.setValue("{\"someKey\":\"someValue\"}");
        dealAttrs.add(attrM);
        // act
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildOverNightSkuListModule(dealAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case for wrong identityKey value
     */
    @Test
    public void testBuildOverNightSkuListModule_WrongIdentityKeyValue() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealOverNightRule");
        attrM.setValue("{\"identityKey\":\"wrongValue\"}");
        dealAttrs.add(attrM);
        // act
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildOverNightSkuListModule(dealAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case for exception scenario
     */
    @Test
    public void testBuildOverNightSkuListModule_Exception() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = null;
        // act
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildOverNightSkuListModule(dealAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealDetailInfoModel is null
     */
    @Test
    public void testStandardExtractMustSkuItemList_NullDealDetailInfoModel() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = null;
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNull(result);
    }

    /**
     * Test when standardServiceProjectDTO is null
     */
    @Test
    public void testStandardExtractMustSkuItemList_NullStandardServiceProjectDTO() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setStandardServiceProjectDTO(null);
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNull(result);
    }

    /**
     * Test when mustGroups is empty
     */
    @Test
    public void testStandardExtractMustSkuItemList_EmptyMustGroups() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
        standardServiceProjectDTO.setMustGroups(new ArrayList<>());
        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNull(result);
    }

    /**
     * Test when mustGroups contains valid items
     */
    @Test
    public void testStandardExtractMustSkuItemList_ValidMustGroups() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
        List<StandardServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        // Create first group with items
        StandardServiceProjectGroupDTO group1 = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> items1 = new ArrayList<>();
        StandardServiceProjectItemDTO item1 = new StandardServiceProjectItemDTO();
        item1.setServiceProjectName("Item1");
        items1.add(item1);
        group1.setServiceProjectItems(items1);
        // Create second group with items
        StandardServiceProjectGroupDTO group2 = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> items2 = new ArrayList<>();
        StandardServiceProjectItemDTO item2 = new StandardServiceProjectItemDTO();
        item2.setServiceProjectName("Item2");
        items2.add(item2);
        group2.setServiceProjectItems(items2);
        mustGroups.add(group1);
        mustGroups.add(group2);
        standardServiceProjectDTO.setMustGroups(mustGroups);
        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Item1", result.get(0).getServiceProjectName());
        assertEquals("Item2", result.get(1).getServiceProjectName());
    }

    /**
     * Test when mustGroups contains mix of valid, null and empty items
     */
    @Test
    public void testStandardExtractMustSkuItemList_MixedMustGroups() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
        List<StandardServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        // Add null group
        mustGroups.add(null);
        // Add group with empty items
        StandardServiceProjectGroupDTO emptyGroup = new StandardServiceProjectGroupDTO();
        emptyGroup.setServiceProjectItems(new ArrayList<>());
        mustGroups.add(emptyGroup);
        // Add group with null items
        StandardServiceProjectGroupDTO nullItemsGroup = new StandardServiceProjectGroupDTO();
        nullItemsGroup.setServiceProjectItems(null);
        mustGroups.add(nullItemsGroup);
        // Add valid group
        StandardServiceProjectGroupDTO validGroup = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> validItems = new ArrayList<>();
        StandardServiceProjectItemDTO validItem = new StandardServiceProjectItemDTO();
        validItem.setServiceProjectName("ValidItem");
        validItems.add(validItem);
        validGroup.setServiceProjectItems(validItems);
        mustGroups.add(validGroup);
        standardServiceProjectDTO.setMustGroups(mustGroups);
        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ValidItem", result.get(0).getServiceProjectName());
    }

    /**
     * Test when all groups in mustGroups are invalid
     */
    @Test
    public void testStandardExtractMustSkuItemList_AllInvalidGroups() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
        List<StandardServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        // Add null group
        mustGroups.add(null);
        // Add group with empty items
        StandardServiceProjectGroupDTO emptyGroup = new StandardServiceProjectGroupDTO();
        emptyGroup.setServiceProjectItems(new ArrayList<>());
        mustGroups.add(emptyGroup);
        // Add group with null items
        StandardServiceProjectGroupDTO nullItemsGroup = new StandardServiceProjectGroupDTO();
        nullItemsGroup.setServiceProjectItems(null);
        mustGroups.add(nullItemsGroup);
        standardServiceProjectDTO.setMustGroups(mustGroups);
        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test standardExtractMustSkuItemList when mustGroups contains valid items
     */
    @Test
    public void testStandardExtractMustSkuItemList_WithValidMustGroups() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
        List<StandardServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        StandardServiceProjectGroupDTO group1 = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> items1 = new ArrayList<>();
        StandardServiceProjectItemDTO item1 = new StandardServiceProjectItemDTO();
        item1.setServiceProjectName("Item1");
        items1.add(item1);
        group1.setServiceProjectItems(items1);
        StandardServiceProjectGroupDTO group2 = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> items2 = new ArrayList<>();
        StandardServiceProjectItemDTO item2 = new StandardServiceProjectItemDTO();
        item2.setServiceProjectName("Item2");
        items2.add(item2);
        group2.setServiceProjectItems(items2);
        mustGroups.add(group1);
        mustGroups.add(group2);
        standardServiceProjectDTO.setMustGroups(mustGroups);
        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Item1", result.get(0).getServiceProjectName());
        assertEquals("Item2", result.get(1).getServiceProjectName());
    }

    /**
     * Test standardExtractMustSkuItemList when mustGroups contains null and empty items
     */
    @Test
    public void testStandardExtractMustSkuItemList_WithNullAndEmptyGroups() {
        // arrange
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
        List<StandardServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        StandardServiceProjectGroupDTO group1 = new StandardServiceProjectGroupDTO();
        group1.setServiceProjectItems(new ArrayList<>());
        StandardServiceProjectGroupDTO group2 = null;
        StandardServiceProjectGroupDTO group3 = new StandardServiceProjectGroupDTO();
        List<StandardServiceProjectItemDTO> items3 = new ArrayList<>();
        StandardServiceProjectItemDTO item3 = new StandardServiceProjectItemDTO();
        item3.setServiceProjectName("Item3");
        items3.add(item3);
        group3.setServiceProjectItems(items3);
        mustGroups.add(group1);
        mustGroups.add(group2);
        mustGroups.add(group3);
        standardServiceProjectDTO.setMustGroups(mustGroups);
        dealDetailInfoModel.setStandardServiceProjectDTO(standardServiceProjectDTO);
        // act
        List<StandardServiceProjectItemDTO> result = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Item3", result.get(0).getServiceProjectName());
    }
}
