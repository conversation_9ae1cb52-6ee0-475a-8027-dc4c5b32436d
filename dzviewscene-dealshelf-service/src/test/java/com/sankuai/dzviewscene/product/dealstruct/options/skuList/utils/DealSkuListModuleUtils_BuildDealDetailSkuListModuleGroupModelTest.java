package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DealSkuListModuleUtils_BuildDealDetailSkuListModuleGroupModelTest {

    /**
     * 测试buildDealDetailSkuListModuleGroupModel方法，当dealSkuList为空时，应返回null
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelDealSkuListIsNull() {
        String groupName = "groupName";
        String title = "title";
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildDealDetailSkuListModuleGroupModel(groupName, title, null);
        assertNull(result);
    }

    /**
     * 测试buildDealDetailSkuListModuleGroupModel方法，当dealSkuList不为空时，应返回一个DealDetailSkuListModuleGroupModel对象
     */
    @Test
    public void testBuildDealDetailSkuListModuleGroupModelDealSkuListIsNotNull() {
        String groupName = "groupName";
        String title = "title";
        DealSkuVO dealSkuVO = new DealSkuVO();
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildDealDetailSkuListModuleGroupModel(groupName, title, Arrays.asList(dealSkuVO));
        assertNotNull(result);
        assertEquals(groupName, result.getGroupName());
        assertEquals(title, result.getDealSkuGroupModuleVOS().get(0).getTitle());
        assertEquals(dealSkuVO, result.getDealSkuGroupModuleVOS().get(0).getDealSkuList().get(0));
    }
}
