package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DealSkuListModuleUtils_BuildOverNightSkuListModuleTest {

    /**
     * 测试dealAttrs为空的情况
     */
    @Test
    public void testBuildOverNightSkuListModuleDealAttrsIsNull() {
        List<AttrM> dealAttrs = null;
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildOverNightSkuListModule(dealAttrs);
        assertNull(result);
    }

    /**
     * 测试dealAttrs中没有"dealOverNightRule"属性的情况
     */
    @Test
    public void testBuildOverNightSkuListModuleDealAttrsNoDealOverNightRule() {
        List<AttrM> dealAttrs = new ArrayList<>();
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildOverNightSkuListModule(dealAttrs);
        assertNull(result);
    }

    /**
     * 测试"dealOverNightRule"属性的值为空的情况
     */
    @Test
    public void testBuildOverNightSkuListModuleDealOverNightRuleValueIsEmpty() {
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("dealOverNightRule", ""));
        DealDetailSkuListModuleGroupModel result = DealSkuListModuleUtils.buildOverNightSkuListModule(dealAttrs);
        assertNull(result);
    }
    // ... 其他测试用例
}
