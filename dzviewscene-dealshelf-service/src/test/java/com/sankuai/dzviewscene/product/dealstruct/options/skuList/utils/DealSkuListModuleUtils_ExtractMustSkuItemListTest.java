package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealSkuListModuleUtils_ExtractMustSkuItemListTest {

    @Test
    public void testExtractMustSkuItemListDealDetailInfoModelIsNull() throws Throwable {
        assertNull(DealSkuListModuleUtils.extractMustSkuItemList(null));
    }

    @Test
    public void testExtractMustSkuItemListDealDetailDtoModelIsNull() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        assertNull(DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel));
    }

    @Test
    public void testExtractMustSkuItemListSkuUniStructuredDtoIsNull() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealDetailDtoModel(new DealDetailDtoModel());
        assertNull(DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel));
    }

    @Test
    public void testExtractMustSkuItemListMustGroupsIsEmpty() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealDetailDtoModel(new DealDetailDtoModel());
        dealDetailInfoModel.getDealDetailDtoModel().setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        assertNull(DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel));
    }

    @Test
    public void testExtractMustSkuItemListMustSkuItemsGroupDtoIsNull() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealDetailDtoModel(new DealDetailDtoModel());
        dealDetailInfoModel.getDealDetailDtoModel().setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().setMustGroups(Collections.singletonList(null));
        assertTrue(DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel).isEmpty());
    }

    @Test
    public void testExtractMustSkuItemListSkuItemsIsEmpty() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealDetailDtoModel(new DealDetailDtoModel());
        dealDetailInfoModel.getDealDetailDtoModel().setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        mustSkuItemsGroupDto.setSkuItems(Collections.emptyList());
        dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().setMustGroups(Collections.singletonList(mustSkuItemsGroupDto));
        assertTrue(DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel).isEmpty());
    }

    @Test
    public void testExtractMustSkuItemListNormalCase() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealDetailDtoModel(new DealDetailDtoModel());
        dealDetailInfoModel.getDealDetailDtoModel().setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        SkuItemDto skuItemDto = new SkuItemDto();
        mustSkuItemsGroupDto.setSkuItems(Collections.singletonList(skuItemDto));
        dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().setMustGroups(Collections.singletonList(mustSkuItemsGroupDto));
        List<SkuItemDto> result = DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel);
        assertEquals(1, result.size());
        assertEquals(skuItemDto, result.get(0));
    }
}
