package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import org.junit.runner.RunWith;
import java.util.Collections;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealSkuListModuleUtils_ExtractOptionalSkuItemsGroupListTest {

    @Test
    public void testExtractOptionalSkuItemsGroupListDealDetailInfoModelIsNull() {
        assertNull(DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(null));
    }

    @Test
    public void testExtractOptionalSkuItemsGroupListDealDetailDtoModelIsNull() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        assertNull(DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(dealDetailInfoModel));
    }

    @Test
    public void testExtractOptionalSkuItemsGroupListSkuUniStructuredDtoIsNull() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealDetailDtoModel(new DealDetailDtoModel());
        assertNull(DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(dealDetailInfoModel));
    }

    @Test
    public void testExtractOptionalSkuItemsGroupListOptionalGroupsIsEmpty() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        dealDetailInfoModel.setDealDetailDtoModel(dealDetailDtoModel);
        assertNull(DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(dealDetailInfoModel));
    }

    @Test
    public void testExtractOptionalSkuItemsGroupListOptionalGroupsIsNotEmpty() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto skuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        skuUniStructuredDto.setOptionalGroups(Collections.singletonList(new OptionalSkuItemsGroupDto()));
        dealDetailDtoModel.setSkuUniStructuredDto(skuUniStructuredDto);
        dealDetailInfoModel.setDealDetailDtoModel(dealDetailDtoModel);
        assertSame(skuUniStructuredDto.getOptionalGroups(), DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(dealDetailInfoModel));
    }
}
