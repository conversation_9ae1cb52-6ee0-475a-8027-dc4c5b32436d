package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * @auther: liweilong06
 * @date: 2024/5/16 2:18 下午
 */
@RunWith(MockitoJUnitRunner.class)
public class EduSkuUtilsTest {

    private DealDetailInfoModel mockModel;

    @Before
    public void setUp() {
        mockModel = Mockito.mock(DealDetailInfoModel.class);
    }

    /**
     * 测试dealDetailInfoModel为null时
     * 期望返回null
     */
    @Test
    public void testBuildOpenClassTimeSkuItem_modelIsNull_returnNull() {
        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(null);
        assertNull(result);
    }

    /**
     * 测试开班类型为空时
     * 期望返回null
     */
    @Test
    public void testBuildOpenClassTimeSkuItem_openClassTypeIsNull_returnNull() {
        when(mockModel.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TYPE, "")));

        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(mockModel);

        assertNull(result);
    }

    /**
     * 测试开班时间为空时
     * 期望返回null
     */
    @Test
    public void testBuildOpenClassTimeSkuIte_openClassTimeIsNull_returnNull() {
        when(mockModel.getDealAttrs()).thenReturn(Arrays.asList(
                new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TYPE, EduSkuUtils.OPEN_CLASS_TYPE_FROM_JSON),
                new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TIME, "")
        ));

        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(mockModel);

        assertNull(result);
    }

    /**
     * 测试开班时间为指定日期，但时间列表为空时
     * 期望返回null
     */
    @Test
    public void testBuildOpenClassTimeSkuItem_openClassTimeIsEmptyList_returnNull() {
        when(mockModel.getDealAttrs()).thenReturn(Arrays.asList(
                new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TYPE, EduSkuUtils.OPEN_CLASS_TYPE_FROM_JSON),
                new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TIME, "[]")
        ));

        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(mockModel);

        assertNull(result);
    }

    /**
     * 测试开班时间为指定日期，且时间列表非空时
     * 期望返回2023-01-01、2023-02-01，多期可选
     */
    @Test
    public void testBuildOpenClassTimeSkuItem_openClassTimeIsNotEmptyList_returnValue() {
        when(mockModel.getDealAttrs()).thenReturn(Arrays.asList(
                new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TYPE, EduSkuUtils.OPEN_CLASS_TYPE_FROM_JSON),
                new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TIME, "[\"2023-01-01\",\"2023-02-01\"]")
        ));

        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(mockModel);

        assertNotNull(result);
        assertEquals(EduSkuUtils.OPEN_CLASS_ITEM_TITLE, result.getName());
        assertEquals("2023-01-01、2023-02-01，多期可选", result.getValue());
    }

    /**
     * 测试开班时间为非指定日期时
     * 期望返回即时开班
     */
    @Test
    public void testBuildOpenClassTimeSkuItem_openClassTypeIsNotJson_returnValue() {
        when(mockModel.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TYPE, "即时开班")));

        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(mockModel);

        assertNotNull(result);
        assertEquals(EduSkuUtils.OPEN_CLASS_ITEM_TITLE, result.getName());
        assertEquals("即时开班", result.getValue());
    }

    /**
     * 测试开班时间为非指定日期时
     * 期望返回即时开班
     */
    @Test
    public void testBuildOpenClassTimeSkuItem_nightSchoolNotNull_returnValue() {
        String json = "{\"dealId\":1024102845,\"dealDetailDtoModel\":{\"dealGroupId\":1024102845,\"title\":\"团购详情\",\"skuUniStructuredDto\":{\"salePrice\":\"100.00\",\"marketPrice\":\"500.00\",\"mustGroups\":[{\"skuItems\":[{\"skuId\":0,\"productCategory\":2106293,\"name\":\"钢琴音乐课\",\"copies\":1,\"marketPrice\":500.0,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[{\"metaAttrId\":549,\"attrName\":\"desc\",\"chnName\":\"课程标题\",\"attrValue\":\"钢琴音乐课\",\"rawAttrValue\":\"钢琴音乐课\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1347,\"attrName\":\"count\",\"chnName\":\"课次数\",\"attrValue\":\"12\",\"rawAttrValue\":\"12\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1004005,\"attrName\":\"kecianpai\",\"chnName\":\"课次安排\",\"attrValue\":\"1\",\"rawAttrValue\":\"1\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":616,\"attrName\":\"classDuration\",\"chnName\":\"课次时长\",\"attrValue\":\"30\",\"rawAttrValue\":\"30\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1037,\"attrName\":\"category1\",\"chnName\":\"科目分类\",\"attrValue\":\"西洋弦乐器\",\"rawAttrValue\":\"西洋弦乐器\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1038,\"attrName\":\"category2\",\"chnName\":\"科目\",\"attrValue\":\"民谣吉他\",\"rawAttrValue\":\"民谣吉他\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":614,\"attrName\":\"classNature\",\"chnName\":\"课程效果\",\"attrValue\":\"学会钢琴课\",\"rawAttrValue\":\"学会钢琴课\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1004006,\"attrName\":\"kechengneirong\",\"chnName\":\"课程内容\",\"attrValue\":\"[{\\\"educontent\\\":\\\"理论课\\\"},{\\\"educontent\\\":\\\"实践课\\\"},{\\\"educontent\\\":\\\"实践课\\\"}]\",\"rawAttrValue\":\"[{\\\"educontent\\\":\\\"理论课\\\"},{\\\"educontent\\\":\\\"实践课\\\"},{\\\"educontent\\\":\\\"实践课\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":978993,\"attrName\":\"kechengkaikeshijian2\",\"chnName\":\"课程开课时间\",\"attrValue\":\"[{\\\"kechengshijian\\\":\\\"18:00\\\",\\\"zhouji\\\":[\\\"周一\\\"]},{\\\"kechengshijian\\\":\\\"19:00\\\",\\\"zhouji\\\":[\\\"周二\\\"]}]\",\"rawAttrValue\":\"[{\\\"kechengshijian\\\":\\\"18:00\\\",\\\"zhouji\\\":[\\\"周一\\\"]},{\\\"kechengshijian\\\":\\\"19:00\\\",\\\"zhouji\\\":[\\\"周二\\\"]}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2695,\"attrName\":\"materialCostExplain\",\"chnName\":\"材料费用说明\",\"attrValue\":\"课程无需材料\",\"rawAttrValue\":\"课程无需材料\",\"unit\":null,\"valueType\":500,\"sequence\":0}]}]}],\"optionalGroups\":[]},\"structType\":\"uniform-structure-table\"},\"productCategories\":[{\"productCategoryId\":2106293,\"cnName\":\"课程\"}],\"desc\":null,\"salePrice\":null,\"marketPrice\":null,\"dealTitle\":null,\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1024102845,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1024102845,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"100.00\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"500.00\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2106293,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\\u94a2\\\\\\\\u7434\\\\\\\\u97f3\\\\\\\\u4e50\\\\\\\\u8bfe\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":500.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":549,\\\\\\\"attrName\\\\\\\":\\\\\\\"desc\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u7a0b\\\\\\\\u6807\\\\\\\\u9898\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u94a2\\\\\\\\u7434\\\\\\\\u97f3\\\\\\\\u4e50\\\\\\\\u8bfe\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u94a2\\\\\\\\u7434\\\\\\\\u97f3\\\\\\\\u4e50\\\\\\\\u8bfe\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1347,\\\\\\\"attrName\\\\\\\":\\\\\\\"count\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u6b21\\\\\\\\u6570\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"12\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"12\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1004005,\\\\\\\"attrName\\\\\\\":\\\\\\\"kecianpai\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u6b21\\\\\\\\u5b89\\\\\\\\u6392\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":616,\\\\\\\"attrName\\\\\\\":\\\\\\\"classDuration\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u6b21\\\\\\\\u65f6\\\\\\\\u957f\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"30\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"30\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1037,\\\\\\\"attrName\\\\\\\":\\\\\\\"category1\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u79d1\\\\\\\\u76ee\\\\\\\\u5206\\\\\\\\u7c7b\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u897f\\\\\\\\u6d0b\\\\\\\\u5f26\\\\\\\\u4e50\\\\\\\\u5668\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u897f\\\\\\\\u6d0b\\\\\\\\u5f26\\\\\\\\u4e50\\\\\\\\u5668\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1038,\\\\\\\"attrName\\\\\\\":\\\\\\\"category2\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u79d1\\\\\\\\u76ee\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u6c11\\\\\\\\u8c23\\\\\\\\u5409\\\\\\\\u4ed6\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u6c11\\\\\\\\u8c23\\\\\\\\u5409\\\\\\\\u4ed6\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":614,\\\\\\\"attrName\\\\\\\":\\\\\\\"classNature\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u7a0b\\\\\\\\u6548\\\\\\\\u679c\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u5b66\\\\\\\\u4f1a\\\\\\\\u94a2\\\\\\\\u7434\\\\\\\\u8bfe\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u5b66\\\\\\\\u4f1a\\\\\\\\u94a2\\\\\\\\u7434\\\\\\\\u8bfe\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":1004006,\\\\\\\"attrName\\\\\\\":\\\\\\\"kechengneirong\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u7a0b\\\\\\\\u5185\\\\\\\\u5bb9\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"educontent\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u7406\\\\\\\\u8bba\\\\\\\\u8bfe\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"educontent\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5b9e\\\\\\\\u8df5\\\\\\\\u8bfe\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"educontent\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5b9e\\\\\\\\u8df5\\\\\\\\u8bfe\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"educontent\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u7406\\\\\\\\u8bba\\\\\\\\u8bfe\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"educontent\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5b9e\\\\\\\\u8df5\\\\\\\\u8bfe\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"educontent\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\u5b9e\\\\\\\\u8df5\\\\\\\\u8bfe\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":978993,\\\\\\\"attrName\\\\\\\":\\\\\\\"kechengkaikeshijian2\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u7a0b\\\\\\\\u5f00\\\\\\\\u8bfe\\\\\\\\u65f6\\\\\\\\u95f4\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"kechengshijian\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"18:00\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"zhouji\\\\\\\\\\\\\\\":[\\\\\\\\\\\\\\\"\\\\\\\\u5468\\\\\\\\u4e00\\\\\\\\\\\\\\\"]},{\\\\\\\\\\\\\\\"kechengshijian\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"19:00\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"zhouji\\\\\\\\\\\\\\\":[\\\\\\\\\\\\\\\"\\\\\\\\u5468\\\\\\\\u4e8c\\\\\\\\\\\\\\\"]}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"kechengshijian\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"18:00\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"zhouji\\\\\\\\\\\\\\\":[\\\\\\\\\\\\\\\"\\\\\\\\u5468\\\\\\\\u4e00\\\\\\\\\\\\\\\"]},{\\\\\\\\\\\\\\\"kechengshijian\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"19:00\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"zhouji\\\\\\\\\\\\\\\":[\\\\\\\\\\\\\\\"\\\\\\\\u5468\\\\\\\\u4e8c\\\\\\\\\\\\\\\"]}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2695,\\\\\\\"attrName\\\\\\\":\\\\\\\"materialCostExplain\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\\\\\u6750\\\\\\\\u6599\\\\\\\\u8d39\\\\\\\\u7528\\\\\\\\u8bf4\\\\\\\\u660e\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u7a0b\\\\\\\\u65e0\\\\\\\\u9700\\\\\\\\u6750\\\\\\\\u6599\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\\\\\u8bfe\\\\\\\\u7a0b\\\\\\\\u65e0\\\\\\\\u9700\\\\\\\\u6750\\\\\\\\u6599\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1024102845,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"100.00\\\",\\\"marketPrice\\\":\\\"500.00\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2106293,\\\"name\\\":\\\"\\\\u94a2\\\\u7434\\\\u97f3\\\\u4e50\\\\u8bfe\\\",\\\"copies\\\":1,\\\"marketPrice\\\":500.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":549,\\\"attrName\\\":\\\"desc\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u6807\\\\u9898\\\",\\\"attrValue\\\":\\\"\\\\u94a2\\\\u7434\\\\u97f3\\\\u4e50\\\\u8bfe\\\",\\\"rawAttrValue\\\":\\\"\\\\u94a2\\\\u7434\\\\u97f3\\\\u4e50\\\\u8bfe\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1347,\\\"attrName\\\":\\\"count\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u6b21\\\\u6570\\\",\\\"attrValue\\\":\\\"12\\\",\\\"rawAttrValue\\\":\\\"12\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1004005,\\\"attrName\\\":\\\"kecianpai\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u6b21\\\\u5b89\\\\u6392\\\",\\\"attrValue\\\":\\\"1\\\",\\\"rawAttrValue\\\":\\\"1\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":616,\\\"attrName\\\":\\\"classDuration\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u6b21\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"30\\\",\\\"rawAttrValue\\\":\\\"30\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1037,\\\"attrName\\\":\\\"category1\\\",\\\"chnName\\\":\\\"\\\\u79d1\\\\u76ee\\\\u5206\\\\u7c7b\\\",\\\"attrValue\\\":\\\"\\\\u897f\\\\u6d0b\\\\u5f26\\\\u4e50\\\\u5668\\\",\\\"rawAttrValue\\\":\\\"\\\\u897f\\\\u6d0b\\\\u5f26\\\\u4e50\\\\u5668\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1038,\\\"attrName\\\":\\\"category2\\\",\\\"chnName\\\":\\\"\\\\u79d1\\\\u76ee\\\",\\\"attrValue\\\":\\\"\\\\u6c11\\\\u8c23\\\\u5409\\\\u4ed6\\\",\\\"rawAttrValue\\\":\\\"\\\\u6c11\\\\u8c23\\\\u5409\\\\u4ed6\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":614,\\\"attrName\\\":\\\"classNature\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u6548\\\\u679c\\\",\\\"attrValue\\\":\\\"\\\\u5b66\\\\u4f1a\\\\u94a2\\\\u7434\\\\u8bfe\\\",\\\"rawAttrValue\\\":\\\"\\\\u5b66\\\\u4f1a\\\\u94a2\\\\u7434\\\\u8bfe\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":1004006,\\\"attrName\\\":\\\"kechengneirong\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u5185\\\\u5bb9\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u7406\\\\u8bba\\\\u8bfe\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u5b9e\\\\u8df5\\\\u8bfe\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u5b9e\\\\u8df5\\\\u8bfe\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u7406\\\\u8bba\\\\u8bfe\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u5b9e\\\\u8df5\\\\u8bfe\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u5b9e\\\\u8df5\\\\u8bfe\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":978993,\\\"attrName\\\":\\\"kechengkaikeshijian2\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u5f00\\\\u8bfe\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"18:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"\\\\u5468\\\\u4e00\\\\\\\"]},{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"19:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"\\\\u5468\\\\u4e8c\\\\\\\"]}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"18:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"\\\\u5468\\\\u4e00\\\\\\\"]},{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"19:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"\\\\u5468\\\\u4e8c\\\\\\\"]}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2695,\\\"attrName\\\":\\\"materialCostExplain\\\",\\\"chnName\\\":\\\"\\\\u6750\\\\u6599\\\\u8d39\\\\u7528\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u65e0\\\\u9700\\\\u6750\\\\u6599\\\",\\\"rawAttrValue\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u65e0\\\\u9700\\\\u6750\\\\u6599\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2106293,\\\"cnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1024102845,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"100.00\\\",\\\"marketPrice\\\":\\\"500.00\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2106293,\\\"name\\\":\\\"\\\\u94a2\\\\u7434\\\\u97f3\\\\u4e50\\\\u8bfe\\\",\\\"copies\\\":1,\\\"marketPrice\\\":500.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":549,\\\"attrName\\\":\\\"desc\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u6807\\\\u9898\\\",\\\"attrValue\\\":\\\"\\\\u94a2\\\\u7434\\\\u97f3\\\\u4e50\\\\u8bfe\\\"},{\\\"metaAttrId\\\":1347,\\\"attrName\\\":\\\"count\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u6b21\\\\u6570\\\",\\\"attrValue\\\":\\\"12\\\"},{\\\"metaAttrId\\\":1004005,\\\"attrName\\\":\\\"kecianpai\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u6b21\\\\u5b89\\\\u6392\\\",\\\"attrValue\\\":\\\"1\\\"},{\\\"metaAttrId\\\":616,\\\"attrName\\\":\\\"classDuration\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u6b21\\\\u65f6\\\\u957f\\\",\\\"attrValue\\\":\\\"30\\\"},{\\\"metaAttrId\\\":1037,\\\"attrName\\\":\\\"category1\\\",\\\"chnName\\\":\\\"\\\\u79d1\\\\u76ee\\\\u5206\\\\u7c7b\\\",\\\"attrValue\\\":\\\"\\\\u897f\\\\u6d0b\\\\u5f26\\\\u4e50\\\\u5668\\\"},{\\\"metaAttrId\\\":1038,\\\"attrName\\\":\\\"category2\\\",\\\"chnName\\\":\\\"\\\\u79d1\\\\u76ee\\\",\\\"attrValue\\\":\\\"\\\\u6c11\\\\u8c23\\\\u5409\\\\u4ed6\\\"},{\\\"metaAttrId\\\":614,\\\"attrName\\\":\\\"classNature\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u6548\\\\u679c\\\",\\\"attrValue\\\":\\\"\\\\u5b66\\\\u4f1a\\\\u94a2\\\\u7434\\\\u8bfe\\\"},{\\\"metaAttrId\\\":1004006,\\\"attrName\\\":\\\"kechengneirong\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u5185\\\\u5bb9\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u7406\\\\u8bba\\\\u8bfe\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u5b9e\\\\u8df5\\\\u8bfe\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"\\\\u5b9e\\\\u8df5\\\\u8bfe\\\\\\\"}]\\\"},{\\\"metaAttrId\\\":978993,\\\"attrName\\\":\\\"kechengkaikeshijian2\\\",\\\"chnName\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u5f00\\\\u8bfe\\\\u65f6\\\\u95f4\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"18:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"\\\\u5468\\\\u4e00\\\\\\\"]},{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"19:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"\\\\u5468\\\\u4e8c\\\\\\\"]}]\\\"},{\\\"metaAttrId\\\":2695,\\\"attrName\\\":\\\"materialCostExplain\\\",\\\"chnName\\\":\\\"\\\\u6750\\\\u6599\\\\u8d39\\\\u7528\\\\u8bf4\\\\u660e\\\",\\\"attrValue\\\":\\\"\\\\u8bfe\\\\u7a0b\\\\u65e0\\\\u9700\\\\u6750\\\\u6599\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"dealDisplayControlCheckStatus\",\"value\":\"1\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":{\\\"totalPrice\\\":500.00,\\\"salePrice\\\":100.00,\\\"groups\\\":[{\\\"units\\\":[{\\\"skuCateId\\\":2106293,\\\"amount\\\":1,\\\"price\\\":500.0,\\\"attrValues\\\":{\\\"category2\\\":\\\"民谣吉他\\\",\\\"classDuration\\\":\\\"30\\\",\\\"materialCostExplain\\\":\\\"课程无需材料\\\",\\\"category1\\\":\\\"西洋弦乐器\\\",\\\"kechengneirong\\\":\\\"[{\\\\\\\"educontent\\\\\\\":\\\\\\\"理论课\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"实践课\\\\\\\"},{\\\\\\\"educontent\\\\\\\":\\\\\\\"实践课\\\\\\\"}]\\\",\\\"count\\\":\\\"12\\\",\\\"classNature\\\":\\\"学会钢琴课\\\",\\\"kecianpai\\\":\\\"1\\\",\\\"kechengkaikeshijian2\\\":\\\"[{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"18:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"周一\\\\\\\"]},{\\\\\\\"kechengshijian\\\\\\\":\\\\\\\"19:00\\\\\\\",\\\\\\\"zhouji\\\\\\\":[\\\\\\\"周二\\\\\\\"]}]\\\",\\\"desc\\\":\\\"钢琴音乐课\\\"},\\\"projectName\\\":\\\"钢琴音乐课\\\",\\\"properties\\\":\\\"课程；课程无需材料；西洋弦乐器；学会钢琴课；钢琴音乐课；民谣...\\\",\\\"skuId\\\":0}],\\\"optionalCount\\\":0}]},\\\"type\\\":\\\"uniform-structure-table\\\"}],\\\"headline\\\":\\\"团购详情\\\",\\\"version\\\":1}\"},{\"name\":\"service_type\",\"value\":\"音乐培训\"},{\"name\":\"service_type_leaf_id\",\"value\":\"138016\"},{\"name\":\"class_type_with_five_select\",\"value\":\"1对1\"}],\"dealModuleAttrs\":null,\"standardServiceProjectDTO\":null,\"tradeType\":null,\"unifyProduct\":false}";
        DealDetailInfoModel dealDetailInfoModel = JsonCodec.decode(json, DealDetailInfoModel.class);

        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(dealDetailInfoModel);

        assertNotNull(result);
        assertEquals(EduSkuUtils.OPEN_CLASS_ITEM_TITLE, result.getName());
        assertEquals("周一18:00、周二19:00", result.getValue());
    }

    /**
     * 测试目标证书
     * 返回null
     */
    @Test
    public void testBuildTargetCertificate_subject1IsNull_returnNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildTargetCertificate(Arrays.asList(new AttrM(EduSkuUtils.ATTR_SUBJECT1, ""), new AttrM(EduSkuUtils.ATTR_SUBJECT1, "1")));
        assertNull(dealSkuItemVO);
    }

    /**
     * 测试目标证书
     */
    @Test
    public void testBuildTargetCertificate_subject1NonNull_returnNonNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildTargetCertificate(Arrays.asList(new AttrM(EduSkuUtils.ATTR_SUBJECT1, "证书"), new AttrM(EduSkuUtils.ATTR_SUBJECT1, "1")));
        assertNotNull(dealSkuItemVO);
    }

    /**
     * 测试学习科目
     * 返回null
     */
    @Test
    public void testBuildSubject_subject3IsNull_returnNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildSubject(Arrays.asList(new AttrM(EduSkuUtils.ATTR_SUBJECT3, "")));
        assertNull(dealSkuItemVO);
    }

    /**
     * 测试学习科目
     */
    @Test
    public void testBuildSubject_subject3NonNull_returnNonNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildSubject(Arrays.asList(new AttrM(EduSkuUtils.ATTR_SUBJECT3, "java")));
        assertNotNull(dealSkuItemVO);
    }

    /**
     * 测试课程效果
     * 返回null
     */
    @Test
    public void testBuildCourseEffect_goalIsNull_returnNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildCourseEffect(Arrays.asList(new AttrM(EduSkuUtils.ATTR_COURSE_EFFECT, "")));
        assertNull(dealSkuItemVO);
    }

    /**
     * 测试课程效果
     */
    @Test
    public void testBuildCourseEffect_goalNonNull_returnNonNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildCourseEffect(Arrays.asList(new AttrM(EduSkuUtils.ATTR_COURSE_EFFECT, "good")));
        assertNotNull(dealSkuItemVO);
    }

    /**
     * 测试课程安排
     * 返回null
     */
    @Test
    public void testBuildClassSchedule_classScheduleIsNull_returnNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildClassSchedule(Arrays.asList(new AttrM(EduSkuUtils.ATTR_COURSE_TIME_DETAIL, ""),
                new AttrM(EduSkuUtils.ATTR_CLASS_ARRANGEMENT_DETAIL, "")));
        assertNull(dealSkuItemVO);
    }

    /**
     * 测试课程安排
     */
    @Test
    public void testBuildClassSchedule_classArrangementDetailNonNull_returnNonNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildClassSchedule(Arrays.asList(new AttrM(EduSkuUtils.ATTR_COURSE_TIME_DETAIL,"23"),
                new AttrM(EduSkuUtils.ATTR_CLASS_ARRANGEMENT_DETAIL, "4"),
                new AttrM(EduSkuUtils.ATTR_CLASS_ARRANGEMENT, "白班")));
        assertNotNull(dealSkuItemVO);
    }

    /**
     * 测试课程安排
     */
    @Test
    public void testBuildClassSchedule_classArrangementDetailIsNull_returnNonNull() {
        DealSkuItemVO dealSkuItemVO = EduSkuUtils.buildClassSchedule(Arrays.asList(new AttrM(EduSkuUtils.ATTR_COURSE_TIME_DETAIL,"23"),
                new AttrM(EduSkuUtils.ATTR_CLASS_ARRANGEMENT, "白班")));
        assertNotNull(dealSkuItemVO);
    }
}
