package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EduSkuUtils_BuildOpenClassTimeSkuItemTest {

    @Test
    public void testBuildOpenClassTimeSkuItem_dealDetailInfoModelIsNull() throws Throwable {
        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(null);
        assertNull(result);
    }

    @Test
    public void testBuildOpenClassTimeSkuItem_openClassTimeIsNull() throws Throwable {
        DealDetailInfoModel mockModel = mock(DealDetailInfoModel.class);
        when(mockModel.getDealAttrs()).thenReturn(null);
        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(mockModel);
        assertNull(result);
    }

    @Test
    public void testBuildOpenClassTimeSkuItem_openClassTimeIsNotNull() throws Throwable {
        DealDetailInfoModel mockModel = mock(DealDetailInfoModel.class);
        // Mocking both ATTR_OPEN_CLASS_TYPE and ATTR_OPEN_CLASS_TIME
        when(mockModel.getDealAttrs()).thenReturn(Arrays.asList(new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TYPE, EduSkuUtils.OPEN_CLASS_TYPE_FROM_JSON), new AttrM(EduSkuUtils.ATTR_OPEN_CLASS_TIME, "[\"2023-04-01\",\"2023-05-01\"]")));
        DealSkuItemVO result = EduSkuUtils.buildOpenClassTimeSkuItem(mockModel);
        assertNotNull(result);
        assertEquals(EduSkuUtils.OPEN_CLASS_ITEM_TITLE, result.getName());
        // The expected value should match the processed JSON string in the readOpenClassTime method
        assertEquals("2023-04-01、2023-05-01，多期可选", result.getValue());
    }
}
