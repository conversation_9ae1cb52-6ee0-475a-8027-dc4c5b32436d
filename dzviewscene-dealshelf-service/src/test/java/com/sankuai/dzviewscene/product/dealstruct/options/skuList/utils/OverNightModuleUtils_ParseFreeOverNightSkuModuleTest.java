package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class OverNightModuleUtils_ParseFreeOverNightSkuModuleTest {

    /**
     * 测试dealAttrs为空的情况
     */
    @Test
    public void testParseFreeOverNightSkuModuleDealAttrsIsNull() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = null;
        boolean hitNewIcon = true;
        // act
        DealSkuVO result = OverNightModuleUtils.parseFreeOverNightSkuModule(dealAttrs, hitNewIcon);
        // assert
        assertNull(result);
    }

    /**
     * 测试dealOverNightRule为空的情况
     */
    @Test
    public void testParseFreeOverNightSkuModuleDealOverNightRuleIsNull() throws Throwable {
        // arrange
        List<AttrM> dealAttrs = new ArrayList<>();
        dealAttrs.add(new AttrM("dealOverNightRule", null));
        boolean hitNewIcon = true;
        // act
        DealSkuVO result = OverNightModuleUtils.parseFreeOverNightSkuModule(dealAttrs, hitNewIcon);
        // assert
        assertNull(result);
    }
    // 其他测试用例类似，这里省略...
}
