package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical;

import com.sankuai.dzviewscene.product.dealstruct.vo.PopUpWindowVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuItemValueConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuPopConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
@RunWith(MockitoJUnitRunner.class)
public class MedicalDealAttrUtilsTest {
    @Test
    public void testBuildSkuPopConfig() {
        SkuPopConfig popConfig = new SkuPopConfig();
        popConfig.setType(1);
        popConfig.setIcon("icon");
        popConfig.setTitle("title");
        SkuItemValueConfig skuItemValueConfig = new SkuItemValueConfig();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setKey("key1");
        valueConfig.setProcessType(5);
        skuItemValueConfig.setValueKeys(Collections.singletonList(valueConfig));
        popConfig.setContentConfig(skuItemValueConfig);
        Map<String, String> map = new HashMap<>();
        map.put("key1", "value1");
        PopUpWindowVO popUpWindowVO = MedicalDealAttrUtils.buildSkuPopConfig(popConfig, map);
        Assert.assertNotNull(popUpWindowVO);
    }

}