package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical;

import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuItemValueConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.*;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.ValueProcessor;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicalDealAttrUtils_BuildCommonAttrValueTest {

    @Mock
    private ValueProcessor valueProcessor;

    /**
     * 测试valueConfig为空的情况
     */
    @Test
    public void testBuildCommonAttrValueValueConfigIsNull() throws Throwable {
        // arrange
        SkuItemValueConfig valueConfig = null;
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = new Object();
        // act
        String result = MedicalDealAttrUtils.buildCommonAttrValue(valueConfig, name2ValueMap, data);
        // assert
        assertNull(result);
    }

    /**
     * 测试name2ValueMap为空的情况
     */
    @Test
    public void testBuildCommonAttrValueName2ValueMapIsNull() throws Throwable {
        // arrange
        SkuItemValueConfig valueConfig = new SkuItemValueConfig();
        Map<String, String> name2ValueMap = null;
        Object data = new Object();
        // act
        String result = MedicalDealAttrUtils.buildCommonAttrValue(valueConfig, name2ValueMap, data);
        // assert
        assertNull(result);
    }

    /**
     * 测试valueKeys为空的情况
     */
    @Test
    public void testBuildCommonAttrValueValueKeysIsNull() throws Throwable {
        // arrange
        SkuItemValueConfig valueConfig = new SkuItemValueConfig();
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = new Object();
        // act
        String result = MedicalDealAttrUtils.buildCommonAttrValue(valueConfig, name2ValueMap, data);
        // assert
        assertNull(result);
    }

    /**
     * 测试processType在processorMap中找不到的情况
     */
    @Test
    public void testBuildCommonAttrValueProcessTypeNotFound() throws Throwable {
        // arrange
        SkuItemValueConfig valueConfig = new SkuItemValueConfig();
        valueConfig.setValueKeys(Collections.singletonList(new ValueConfig()));
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = new Object();
        // act
        String result = MedicalDealAttrUtils.buildCommonAttrValue(valueConfig, name2ValueMap, data);
        // assert
        assertNull(result);
    }

    /**
     * 测试valueList为空的情况
     */
    @Test
    public void testBuildCommonAttrValueValueListIsNull() throws Throwable {
        // arrange
        SkuItemValueConfig valueConfig = new SkuItemValueConfig();
        ValueConfig valueConfig1 = new ValueConfig();
        valueConfig1.setProcessType(1);
        valueConfig.setValueKeys(Collections.singletonList(valueConfig1));
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = new Object();
        // act
        String result = MedicalDealAttrUtils.buildCommonAttrValue(valueConfig, name2ValueMap, data);
        // assert
        assertNull(result);
    }
    // 其他测试用例...
}
