package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical;

import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuGroupModuleConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.junit.Assert;
import org.junit.Test;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MedicalDealAttrUtils_BuildDealSkusByAttrsTest {

    /**
     * 测试buildDealSkusByAttrs方法，当name2ValueMap和skuGroupModuleConfigs都为空时
     */
    @Test
    public void testBuildDealSkusByAttrsBothNull() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = null;
        List<SkuGroupModuleConfig> skuGroupModuleConfigs = null;
        // act
        List<DealSkuGroupModuleVO> result = MedicalDealAttrUtils.buildDealSkusByAttrs(name2ValueMap, skuGroupModuleConfigs);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试buildDealSkusByAttrs方法，当name2ValueMap为空，skuGroupModuleConfigs不为空时
     */
    @Test
    public void testBuildDealSkusByAttrsName2ValueMapNull() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = null;
        List<SkuGroupModuleConfig> skuGroupModuleConfigs = new ArrayList<>();
        // act
        List<DealSkuGroupModuleVO> result = MedicalDealAttrUtils.buildDealSkusByAttrs(name2ValueMap, skuGroupModuleConfigs);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试buildDealSkusByAttrs方法，当name2ValueMap不为空，skuGroupModuleConfigs为空时
     */
    @Test
    public void testBuildDealSkusByAttrsSkuGroupModuleConfigsNull() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        List<SkuGroupModuleConfig> skuGroupModuleConfigs = null;
        // act
        List<DealSkuGroupModuleVO> result = MedicalDealAttrUtils.buildDealSkusByAttrs(name2ValueMap, skuGroupModuleConfigs);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试buildDealSkusByAttrs方法，当name2ValueMap和skuGroupModuleConfigs都不为空，但skuGroupModuleConfigs中的元素无效时
     */
    @Test
    public void testBuildDealSkusByAttrsInvalidSkuGroupModuleConfigs() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        List<SkuGroupModuleConfig> skuGroupModuleConfigs = new ArrayList<>();
        skuGroupModuleConfigs.add(null);
        // act
        List<DealSkuGroupModuleVO> result = MedicalDealAttrUtils.buildDealSkusByAttrs(name2ValueMap, skuGroupModuleConfigs);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试buildDealSkusByAttrs方法，当name2ValueMap和skuGroupModuleConfigs都不为空，且skuGroupModuleConfigs中的元素有效时
     */
    @Test
    public void testBuildDealSkusByAttrsValidInput() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("testKey", "testValue");
        List<SkuGroupModuleConfig> skuGroupModuleConfigs = new ArrayList<>();
        SkuGroupModuleConfig skuGroupModuleConfig = new SkuGroupModuleConfig();
        skuGroupModuleConfigs.add(skuGroupModuleConfig);
        // act
        List<DealSkuGroupModuleVO> result = MedicalDealAttrUtils.buildDealSkusByAttrs(name2ValueMap, skuGroupModuleConfigs);
        // assert
        Assert.assertNotNull(result);
    }
}
