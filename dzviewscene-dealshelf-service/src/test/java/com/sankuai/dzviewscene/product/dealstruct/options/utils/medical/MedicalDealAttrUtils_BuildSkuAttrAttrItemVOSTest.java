package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical;

import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuPopConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuItemValueConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueAttrConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicalDealAttrUtils_BuildSkuAttrAttrItemVOSTest {

    /**
     * 测试name2ValueMap或skuItemValueConfigList为空的情况
     */
    @Test
    public void testBuildSkuAttrAttrItemVOS_InputIsNull() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = null;
        List<ValueAttrConfig> skuItemValueConfigList = null;
        // act
        List<SkuAttrAttrItemVO> result = MedicalDealAttrUtils.buildSkuAttrAttrItemVOS(skuItemValueConfigList, name2ValueMap);
        // assert
        assertNull(result);
    }

    /**
     * 测试valueAttrConfig的nameConfig或valueConfigList为空的情况
     */
    @Test
    public void testBuildSkuAttrAttrItemVOS_ValueAttrConfigIsNull() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        List<ValueAttrConfig> skuItemValueConfigList = Arrays.asList(new ValueAttrConfig());
        // act
        List<SkuAttrAttrItemVO> result = MedicalDealAttrUtils.buildSkuAttrAttrItemVOS(skuItemValueConfigList, name2ValueMap);
        // assert
        assertNull(result);
    }

    /**
     * 测试buildCommonAttrValue或buildCommonAttrs方法返回null的情况
     */
    @Test
    public void testBuildSkuAttrAttrItemVOS_BuildMethodReturnsNull() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        List<ValueAttrConfig> skuItemValueConfigList = Arrays.asList(new ValueAttrConfig());
        // act
        List<SkuAttrAttrItemVO> result = MedicalDealAttrUtils.buildSkuAttrAttrItemVOS(skuItemValueConfigList, name2ValueMap);
        // assert
        assertNull(result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBuildSkuAttrAttrItemVOS_Normal() throws Throwable {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("key1", "value1");
        ValueConfig valueConfig = new ValueConfig();
        // Assuming 1 is a valid process type
        valueConfig.setProcessType(1);
        valueConfig.setKey("key1");
        SkuItemValueConfig skuItemValueConfig = new SkuItemValueConfig();
        skuItemValueConfig.setValueKeys(Arrays.asList(valueConfig));
        skuItemValueConfig.setBeforeText("Before");
        skuItemValueConfig.setMidText("Mid");
        skuItemValueConfig.setNeedRealValues(true);
        ValueAttrConfig valueAttrConfig = new ValueAttrConfig();
        valueAttrConfig.setNameConfig(skuItemValueConfig);
        valueAttrConfig.setValueConfigList(Arrays.asList(skuItemValueConfig));
        List<ValueAttrConfig> skuItemValueConfigList = Arrays.asList(valueAttrConfig);
        // act
        List<SkuAttrAttrItemVO> result = MedicalDealAttrUtils.buildSkuAttrAttrItemVOS(skuItemValueConfigList, name2ValueMap);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
