package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical;

import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuPopConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuItemValueConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopUpWindowVO;
import org.junit.Assert;
import org.junit.Test;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class MedicalDealAttrUtils_BuildSkuPopConfigTest {

    /**
     * 测试 buildSkuPopConfig 方法，当 popConfig 为 null 时，应返回 null
     */
    @Test
    public void testBuildSkuPopConfigPopConfigIsNull() {
        PopUpWindowVO result = MedicalDealAttrUtils.buildSkuPopConfig(null, new HashMap<>());
        Assert.assertNull(result);
    }

    /**
     * 测试 buildSkuPopConfig 方法，当 contentConfig 为空或者 valueKeys 为空时，应返回 null
     */
    @Test
    public void testBuildSkuPopConfigContentConfigOrValueKeysIsNull() {
        SkuPopConfig popConfig = new SkuPopConfig();
        popConfig.setType(1);
        popConfig.setIcon("icon");
        popConfig.setTitle("title");
        SkuItemValueConfig skuItemValueConfig = new SkuItemValueConfig();
        popConfig.setContentConfig(skuItemValueConfig);
        PopUpWindowVO result = MedicalDealAttrUtils.buildSkuPopConfig(popConfig, new HashMap<>());
        Assert.assertNull(result);
    }

    /**
     * 测试 buildSkuPopConfig 方法，当 name2ValueMap 为空时，应返回 null
     */
    @Test
    public void testBuildSkuPopConfigName2ValueMapIsNull() {
        SkuPopConfig popConfig = new SkuPopConfig();
        popConfig.setType(1);
        popConfig.setIcon("icon");
        popConfig.setTitle("title");
        SkuItemValueConfig skuItemValueConfig = new SkuItemValueConfig();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setKey("key1");
        valueConfig.setProcessType(1);
        skuItemValueConfig.setValueKeys(Collections.singletonList(valueConfig));
        popConfig.setContentConfig(skuItemValueConfig);
        PopUpWindowVO result = MedicalDealAttrUtils.buildSkuPopConfig(popConfig, null);
        Assert.assertNull(result);
    }

    /**
     * 测试 buildSkuPopConfig 方法，当 content 为空时，应返回 null
     */
    @Test
    public void testBuildSkuPopConfigContentIsNull() {
        SkuPopConfig popConfig = new SkuPopConfig();
        popConfig.setType(1);
        popConfig.setIcon("icon");
        popConfig.setTitle("title");
        SkuItemValueConfig skuItemValueConfig = new SkuItemValueConfig();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setKey("key1");
        valueConfig.setProcessType(1);
        skuItemValueConfig.setValueKeys(Collections.singletonList(valueConfig));
        popConfig.setContentConfig(skuItemValueConfig);
        Map<String, String> map = new HashMap<>();
        map.put("key1", "");
        PopUpWindowVO result = MedicalDealAttrUtils.buildSkuPopConfig(popConfig, map);
        Assert.assertNull(result);
    }

    /**
     * 测试 buildSkuPopConfig 方法，当 icon 为空时，应返回 null
     */
    @Test
    public void testBuildSkuPopConfigIconIsNull() {
        SkuPopConfig popConfig = new SkuPopConfig();
        popConfig.setType(1);
        popConfig.setIcon("");
        popConfig.setTitle("title");
        SkuItemValueConfig skuItemValueConfig = new SkuItemValueConfig();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setKey("key1");
        valueConfig.setProcessType(1);
        skuItemValueConfig.setValueKeys(Collections.singletonList(valueConfig));
        popConfig.setContentConfig(skuItemValueConfig);
        Map<String, String> map = new HashMap<>();
        map.put("key1", "value1");
        PopUpWindowVO result = MedicalDealAttrUtils.buildSkuPopConfig(popConfig, map);
        Assert.assertNull(result);
    }

    /**
     * 测试 buildSkuPopConfig 方法，当所有参数都不为空时，应返回正确的 PopUpWindowVO 对象
     */
    @Test
    public void testBuildSkuPopConfigAllNotNull() {
        SkuPopConfig popConfig = new SkuPopConfig();
        popConfig.setType(1);
        popConfig.setIcon("icon");
        popConfig.setTitle("title");
        SkuItemValueConfig skuItemValueConfig = new SkuItemValueConfig();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setKey("key1");
        valueConfig.setProcessType(1);
        skuItemValueConfig.setValueKeys(Collections.singletonList(valueConfig));
        popConfig.setContentConfig(skuItemValueConfig);
        Map<String, String> map = new HashMap<>();
        map.put("key1", "value1");
        PopUpWindowVO result = MedicalDealAttrUtils.buildSkuPopConfig(popConfig, map);
        Assert.assertNotNull(result);
        Assert.assertEquals("icon", result.getIcon());
        Assert.assertEquals("title", result.getTitle());
        Assert.assertEquals("value1", result.getContent());
        Assert.assertEquals(1, result.getType());
    }
}
