package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class MedicalDealAttrUtils_ValidateTest {

    /**
     * 测试validate方法，当name2ValueMap和object都不为空时，应返回false
     */
    @Test
    public void testValidateBothNotNull() {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("key", "value");
        Object object = new Object();
        // act
        boolean result = MedicalDealAttrUtils.validate(name2ValueMap, object);
        // assert
        assertFalse(result);
    }

    /**
     * 测试validate方法，当name2ValueMap为空，object不为空时，应返回true
     */
    @Test
    public void testValidateName2ValueMapNull() {
        // arrange
        Map<String, String> name2ValueMap = null;
        Object object = new Object();
        // act
        boolean result = MedicalDealAttrUtils.validate(name2ValueMap, object);
        // assert
        assertTrue(result);
    }

    /**
     * 测试validate方法，当name2ValueMap不为空，object为空时，应返回true
     */
    @Test
    public void testValidateObjectNull() {
        // arrange
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("key", "value");
        Object object = null;
        // act
        boolean result = MedicalDealAttrUtils.validate(name2ValueMap, object);
        // assert
        assertTrue(result);
    }

    /**
     * 测试validate方法，当name2ValueMap和object都为空时，应返回true
     */
    @Test
    public void testValidateBothNull() {
        // arrange
        Map<String, String> name2ValueMap = null;
        Object object = null;
        // act
        boolean result = MedicalDealAttrUtils.validate(name2ValueMap, object);
        // assert
        assertTrue(result);
    }
}
