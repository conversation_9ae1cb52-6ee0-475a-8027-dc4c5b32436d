package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractValueProcessor_ProcessTest {

    @Mock
    private AbstractValueProcessor abstractValueProcessor;

    private AbstractValueProcessor processor = new AbstractValueProcessor() {

        @Override
        public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
            return null;
        }
    };

    @Test
    public void testProcessValueKeyIsNull() throws Throwable {
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = abstractValueProcessor.process(null, name2ValueMap, new Object());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testProcessName2ValueMapIsNull() throws Throwable {
        ValueConfig valueConfig = new ValueConfig();
        List<String> result = abstractValueProcessor.process(valueConfig, null, new Object());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testProcessValueKeyNotInName2ValueMap() throws Throwable {
        ValueConfig valueConfig = new ValueConfig();
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = abstractValueProcessor.process(valueConfig, name2ValueMap, new Object());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testProcessValueIsNull() throws Throwable {
        ValueConfig valueConfig = new ValueConfig();
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("key", null);
        List<String> result = abstractValueProcessor.process(valueConfig, name2ValueMap, new Object());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testProcessValueIsList() throws Throwable {
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setIsList(true);
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("key", "[\"value1\", \"value2\"]");
        List<String> result = abstractValueProcessor.process(valueConfig, name2ValueMap, new Object());
        assertNotNull(result);
    }

    @Test
    public void testProcessValueIsNotList() throws Throwable {
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setIsList(false);
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("key", "value");
        List<String> result = abstractValueProcessor.process(valueConfig, name2ValueMap, new Object());
        assertNotNull(result);
    }

    /**
     * Test process() when valueKey is null
     */
    @Test
    public void testProcessWhenValueKeyIsNull() throws Throwable {
        // arrange
        ValueConfig valueKey = null;
        Map<String, String> name2ValueMap = new HashMap<>();
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, null);
        // assert
        assertNull(result);
    }

    /**
     * Test process() when name2ValueMap is null
     */
    @Test
    public void testProcessWhenName2ValueMapIsNull() throws Throwable {
        // arrange
        ValueConfig valueKey = new ValueConfig();
        Map<String, String> name2ValueMap = null;
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, null);
        // assert
        assertNull(result);
    }

    /**
     * Test process() when both valueKey and name2ValueMap are empty
     */
    @Test
    public void testProcessWhenBothParamsEmpty() throws Throwable {
        // arrange
        ValueConfig valueKey = new ValueConfig();
        Map<String, String> name2ValueMap = new HashMap<>();
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, null);
        // assert
        assertNull(result);
    }
}
