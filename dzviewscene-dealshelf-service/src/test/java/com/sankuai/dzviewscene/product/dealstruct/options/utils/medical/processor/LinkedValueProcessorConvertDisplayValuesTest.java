package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class LinkedValueProcessorConvertDisplayValuesTest {

    @Spy
    @InjectMocks
    private LinkedValueProcessor processor;

    /**
     * Test getting linked value from name2ValueMap
     */
    @Test
    public void testConvertDisplayValuesWithLinkedValue() throws Throwable {
        // arrange
        String value = "testValue";
        ValueConfig valueConfig = new ValueConfig();
        Map<String, ValueConfig> linkedMap = new HashMap<>();
        ValueConfig linkedConfig = new ValueConfig();
        linkedConfig.setKey("linkedKey");
        linkedMap.put(value, linkedConfig);
        valueConfig.setLinkedMap(linkedMap);
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("linkedKey", "linkedValue");
        // act
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("testValuelinkedValue", result.get(0));
    }

    /**
     * Test converting linked value based on isList flag
     */
    @Test
    public void testConvertDisplayValuesWithListFlag() throws Throwable {
        // arrange
        String value = "testValue";
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setIsList(true);
        Map<String, ValueConfig> linkedMap = new HashMap<>();
        ValueConfig linkedConfig = new ValueConfig();
        linkedConfig.setKey("linkedKey");
        linkedMap.put(value, linkedConfig);
        valueConfig.setLinkedMap(linkedMap);
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("linkedKey", "[\"val1\",\"val2\"]");
        doReturn(Lists.newArrayList("val1", "val2")).when(processor).convertListByStr(anyString());
        // act
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        // assert
        assertEquals(2, result.size());
        assertEquals("testValueval1", result.get(0));
        assertEquals("testValueval2", result.get(1));
    }

    /**
     * Test when linked value list is empty
     */
    @Test
    public void testConvertDisplayValuesWithEmptyLinkedValueList() throws Throwable {
        // arrange
        String value = "testValue";
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setIsList(true);
        Map<String, ValueConfig> linkedMap = new HashMap<>();
        ValueConfig linkedConfig = new ValueConfig();
        linkedConfig.setKey("linkedKey");
        linkedMap.put(value, linkedConfig);
        valueConfig.setLinkedMap(linkedMap);
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("linkedKey", "[]");
        doReturn(Lists.newArrayList()).when(processor).convertListByStr(anyString());
        // act
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        // assert
        assertEquals(1, result.size());
        assertEquals(value, result.get(0));
    }

    /**
     * Test value concatenation and format application
     */
    @Test
    public void testConvertDisplayValuesWithFormatting() throws Throwable {
        // arrange
        String value = "testValue";
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setFormat("formatted_%s");
        Map<String, ValueConfig> linkedMap = new HashMap<>();
        ValueConfig linkedConfig = new ValueConfig();
        linkedConfig.setKey("linkedKey");
        linkedMap.put(value, linkedConfig);
        valueConfig.setLinkedMap(linkedMap);
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("linkedKey", "linkedValue");
        // act
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("formatted_testValuelinkedValue", result.get(0));
    }

    /**
     * Test filtering empty values
     */
    @Test
    public void testConvertDisplayValuesFilterEmptyValues() throws Throwable {
        // arrange
        String value = "";
        ValueConfig valueConfig = new ValueConfig();
        Map<String, ValueConfig> linkedMap = new HashMap<>();
        ValueConfig linkedConfig = new ValueConfig();
        linkedConfig.setKey("linkedKey");
        linkedMap.put(value, linkedConfig);
        valueConfig.setLinkedMap(linkedMap);
        Map<String, String> name2ValueMap = new HashMap<>();
        name2ValueMap.put("linkedKey", "");
        // act
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("", result.get(0));
    }
}
