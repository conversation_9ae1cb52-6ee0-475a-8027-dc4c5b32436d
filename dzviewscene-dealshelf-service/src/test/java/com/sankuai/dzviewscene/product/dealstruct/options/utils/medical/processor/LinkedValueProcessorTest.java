package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;

@RunWith(MockitoJUnitRunner.class)
public class LinkedValueProcessorTest {

    private LinkedValueProcessor processor = new LinkedValueProcessor();

    @Test
    public void testConvertDisplayValuesWhenValueIsNull() throws Throwable {
        String value = null;
        ValueConfig valueConfig = mock(ValueConfig.class);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(1, result.size());
        assertNull(result.get(0));
    }

    @Test
    public void testConvertDisplayValuesWhenValueConfigIsNull() throws Throwable {
        String value = "value";
        ValueConfig valueConfig = null;
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(1, result.size());
        assertEquals(value, result.get(0));
    }

    @Test
    public void testConvertDisplayValuesWhenName2ValueMapIsNull() throws Throwable {
        String value = "value";
        ValueConfig valueConfig = mock(ValueConfig.class);
        Map<String, String> name2ValueMap = null;
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(1, result.size());
        assertEquals(value, result.get(0));
    }

    @Test
    public void testConvertDisplayValuesWhenLinkedMapIsNull() throws Throwable {
        String value = "value";
        ValueConfig valueConfig = mock(ValueConfig.class);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(1, result.size());
        assertEquals(value, result.get(0));
    }

    @Test
    public void testConvertDisplayValuesWhenLinkedValueIsNull() throws Throwable {
        String value = "value";
        ValueConfig valueConfig = mock(ValueConfig.class);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(1, result.size());
        assertEquals(value, result.get(0));
    }

    @Test
    public void testConvertDisplayValuesWhenKeyIsNull() throws Throwable {
        String value = "value";
        ValueConfig valueConfig = mock(ValueConfig.class);
        Map<String, ValueConfig> linkedMap = new HashMap<>();
        ValueConfig linkedValueConfig = mock(ValueConfig.class);
        linkedMap.put(null, linkedValueConfig);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = processor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(1, result.size());
        assertEquals(value, result.get(0));
    }
}
