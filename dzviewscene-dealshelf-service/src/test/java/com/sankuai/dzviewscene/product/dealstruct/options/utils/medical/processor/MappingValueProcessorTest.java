package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MappingValueProcessorTest {

    private MappingValueProcessor mappingValueProcessor = new MappingValueProcessor();

    @Test
    public void testConvertDisplayValuesValueOrValueConfigIsNull() throws Throwable {
        String value = null;
        ValueConfig valueConfig = null;
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = mappingValueProcessor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testConvertDisplayValuesMappingIsNull() throws Throwable {
        String value = "value";
        ValueConfig valueConfig = mock(ValueConfig.class);
        when(valueConfig.getMapping()).thenReturn(null);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = mappingValueProcessor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testConvertDisplayValuesValueIsNull() throws Throwable {
        String value = null;
        ValueConfig valueConfig = mock(ValueConfig.class);
        Map<String, String> mapping = new HashMap<>();
        mapping.put("key", "value");
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = mappingValueProcessor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testConvertDisplayValuesFormatIsNull() throws Throwable {
        // Adjusted to match a key in the mapping
        String value = "key";
        ValueConfig valueConfig = mock(ValueConfig.class);
        Map<String, String> mapping = new HashMap<>();
        mapping.put("key", "value");
        when(valueConfig.getMapping()).thenReturn(mapping);
        when(valueConfig.getFormat()).thenReturn(null);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<String> result = mappingValueProcessor.convertDisplayValues(value, valueConfig, name2ValueMap);
        assertEquals(1, result.size());
        // Expecting the mapped value
        assertEquals("value", result.get(0));
    }

    @Test
    public void testConvertDisplayValues_EmptyValueAfterMapping() throws Throwable {
        MappingValueProcessor processor = new MappingValueProcessor();
        ValueConfig valueConfig = new ValueConfig();
        Map<String, String> mapping = new HashMap<>();
        mapping.put("input", "");
        valueConfig.setMapping(mapping);
        List<String> result = processor.convertDisplayValues("input", valueConfig, new HashMap<>());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertDisplayValues_WithFormat() throws Throwable {
        MappingValueProcessor processor = new MappingValueProcessor();
        ValueConfig valueConfig = new ValueConfig();
        Map<String, String> mapping = new HashMap<>();
        mapping.put("input", "100");
        valueConfig.setMapping(mapping);
        valueConfig.setFormat("%s元");
        List<String> result = processor.convertDisplayValues("input", valueConfig, new HashMap<>());
        assertEquals(1, result.size());
        assertEquals("100元", result.get(0));
    }

    @Test
    public void testConvertDisplayValues_EmptyValueAfterFormat() throws Throwable {
        MappingValueProcessor processor = new MappingValueProcessor();
        ValueConfig valueConfig = new ValueConfig();
        Map<String, String> mapping = new HashMap<>();
        mapping.put("input", "value");
        valueConfig.setMapping(mapping);
        valueConfig.setFormat("");
        List<String> result = processor.convertDisplayValues("input", valueConfig, new HashMap<>());
        assertEquals(1, result.size());
        // Corrected the expected value to match the actual behavior of the method
        assertEquals("value", result.get(0));
    }
}
