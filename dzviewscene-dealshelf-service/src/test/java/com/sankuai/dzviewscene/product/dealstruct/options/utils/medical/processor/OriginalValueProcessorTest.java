package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OriginalValueProcessorTest {

    private OriginalValueProcessor originalValueProcessor = new OriginalValueProcessor();

    /**
     * 测试process方法，当valueKey为空时
     */
    @Test
    public void testProcessValueKeyIsNull() throws Throwable {
        // arrange
        ValueConfig valueKey = null;
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = new Object();
        // act
        List<String> result = originalValueProcessor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试process方法，当valueKey的key为空时
     */
    @Test
    public void testProcessValueKeyKeyIsNull() throws Throwable {
        // arrange
        ValueConfig valueKey = new ValueConfig();
        valueKey.setKey(null);
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = new Object();
        // act
        List<String> result = originalValueProcessor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试process方法，当valueKey和valueKey的key都不为空时
     */
    @Test
    public void testProcessValueKeyAndKeyNotNull() throws Throwable {
        // arrange
        ValueConfig valueKey = new ValueConfig();
        valueKey.setKey("testKey");
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = new Object();
        // act
        List<String> result = originalValueProcessor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(Collections.singletonList("testKey"), result);
    }

    /**
     * 测试当传入null时，方法的行为
     */
    @Test
    public void testConvertDisplayValuesWithNull() throws Throwable {
        // arrange
        String value = null;
        ValueConfig valueConfig = null;
        Map<String, String> name2ValueMap = null;
        // act
        List<String> result = originalValueProcessor.convertDisplayValues(value, valueConfig, name2ValueMap);
        // assert
        assertNull(result);
    }

    /**
     * 测试当传入的参数不满足方法的预期类型时，方法的行为
     */
    @Test
    public void testConvertDisplayValuesWithInvalidType() throws Throwable {
        // arrange
        String value = "invalid";
        ValueConfig valueConfig = new ValueConfig();
        Map<String, String> name2ValueMap = new HashMap<>();
        // act
        List<String> result = originalValueProcessor.convertDisplayValues(value, valueConfig, name2ValueMap);
        // assert
        assertNull(result);
    }
}
