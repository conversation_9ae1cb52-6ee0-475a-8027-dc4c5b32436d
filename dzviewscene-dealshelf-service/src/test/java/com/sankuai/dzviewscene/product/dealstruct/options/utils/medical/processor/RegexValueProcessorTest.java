package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import org.junit.Before;

@RunWith(MockitoJUnitRunner.class)
public class RegexValueProcessorTest {

    private RegexValueProcessor regexValueProcessor;

    /**
     * Tests when value or valueConfig is null.
     */
    @Test
    public void testConvertDisplayValuesValueOrValueConfigIsNull() throws Throwable {
        RegexValueProcessor regexValueProcessor = new RegexValueProcessor();
        List<String> result = regexValueProcessor.convertDisplayValues(null, null, null);
        assertTrue(result.isEmpty());
    }

    /**
     * Tests when the regex in valueConfig does not match the value.
     */
    @Test
    public void testConvertDisplayValuesRegexMatchFailed() throws Throwable {
        RegexValueProcessor regexValueProcessor = new RegexValueProcessor();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setRegex("\\d+");
        List<String> result = regexValueProcessor.convertDisplayValues("test", valueConfig, null);
        assertTrue(result.isEmpty());
    }

    /**
     * Tests when the format in valueConfig is null.
     */
    @Test
    public void testConvertDisplayValuesFormatIsNull() throws Throwable {
        RegexValueProcessor regexValueProcessor = new RegexValueProcessor();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setRegex("(\\d+)");
        List<String> result = regexValueProcessor.convertDisplayValues("123", valueConfig, null);
        assertEquals(1, result.size());
        assertEquals("123", result.get(0));
    }

    /**
     * Tests when the format in valueConfig is not null.
     */
    @Test
    public void testConvertDisplayValuesFormatIsNotNull() throws Throwable {
        RegexValueProcessor regexValueProcessor = new RegexValueProcessor();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setRegex("(\\d+)");
        // Corrected the format to match the number of arguments being passed
        valueConfig.setFormat("%s");
        List<String> result = regexValueProcessor.convertDisplayValues("123", valueConfig, null);
        assertEquals(1, result.size());
        assertEquals("123", result.get(0));
    }
}
