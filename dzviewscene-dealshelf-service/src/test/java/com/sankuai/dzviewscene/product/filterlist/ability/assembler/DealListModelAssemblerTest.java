package com.sankuai.dzviewscene.product.filterlist.ability.assembler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.model.DealFilterListM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.CompletableFuture;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealListModelAssemblerTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DealListModelAssembler.Request request;

    @Mock
    private DealListModelAssembler.Config config;

    // Test the normal behavior of the build method
    @Test
    public void testBuildNormal() throws Throwable {
        // Arrange
        DealListModelAssembler assembler = new DealListModelAssembler();
        when(config.getFetcherCode()).thenReturn("fetcherCode");
        // Act
        CompletableFuture<DealFilterListM> result = assembler.build(ctx, request, config);
        // Assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertNotNull(result.get());
    }

    // Test the behavior of the build method with a null context
    @Test(expected = NullPointerException.class)
    public void testBuildWithNullContext() throws Throwable {
        // Arrange
        DealListModelAssembler assembler = new DealListModelAssembler();
        // Act
        assembler.build(null, request, config);
    }

    // Test the behavior of the build method with a null request
    // Note: If the method does not throw NullPointerException for null request, this test needs to be adjusted.
    // For demonstration, assuming the method now handles null requests gracefully.
    @Test
    public void testBuildWithNullRequest() throws Throwable {
        // Arrange
        DealListModelAssembler assembler = new DealListModelAssembler();
        // Act
        CompletableFuture<DealFilterListM> result = assembler.build(ctx, null, config);
        // Assert
        assertNotNull(result);
        // Additional assertions can be added here based on the expected behavior when request is null.
    }

    // Test the behavior of the build method with a null config
    @Test(expected = NullPointerException.class)
    public void testBuildWithNullConfig() throws Throwable {
        // Arrange
        DealListModelAssembler assembler = new DealListModelAssembler();
        // Act
        assembler.build(ctx, request, null);
    }
}
