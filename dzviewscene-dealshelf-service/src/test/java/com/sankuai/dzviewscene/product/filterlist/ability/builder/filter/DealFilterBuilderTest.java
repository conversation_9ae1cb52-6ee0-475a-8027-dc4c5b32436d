package com.sankuai.dzviewscene.product.filterlist.ability.builder.filter;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.DealFilterBuilder;
import com.sankuai.dzviewscene.product.filterlist.model.DealFilterListM;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFilterBuilderTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DealFilterBuilder.Request request;

    @Mock
    private DealFilterBuilder.Config config;

    @Test
    public void testBuildDealFilterListMIsNull() throws Throwable {
        // arrange
        DealFilterBuilder dealFilterBuilder = new DealFilterBuilder();
        when(ctx.getSource(anyString())).thenReturn(null);
        // act
        CompletableFuture<List<DzFilterVO>> result = dealFilterBuilder.build(ctx, request, config);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildFilterMapIsEmpty() throws Throwable {
        // arrange
        DealFilterBuilder dealFilterBuilder = new DealFilterBuilder();
        DealFilterListM dealFilterListM = new DealFilterListM();
        when(ctx.getSource(anyString())).thenReturn(dealFilterListM);
        // act
        CompletableFuture<List<DzFilterVO>> result = dealFilterBuilder.build(ctx, request, config);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildFilterMIsNull() throws Throwable {
        // arrange
        DealFilterBuilder dealFilterBuilder = new DealFilterBuilder();
        DealFilterListM dealFilterListM = new DealFilterListM();
        Map<String, FilterM> filterMap = new HashMap<>();
        filterMap.put("key", null);
        dealFilterListM.setFilterMs(filterMap);
        when(ctx.getSource(anyString())).thenReturn(dealFilterListM);
        // act
        CompletableFuture<List<DzFilterVO>> result = dealFilterBuilder.build(ctx, request, config);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildFilterMIsNotNull() throws Throwable {
        // arrange
        DealFilterBuilder dealFilterBuilder = new DealFilterBuilder();
        DealFilterListM dealFilterListM = new DealFilterListM();
        FilterM filterM = new FilterM();
        Map<String, FilterM> filterMap = new HashMap<>();
        filterMap.put("key", filterM);
        dealFilterListM.setFilterMs(filterMap);
        when(ctx.getSource(anyString())).thenReturn(dealFilterListM);
        // act
        CompletableFuture<List<DzFilterVO>> result = dealFilterBuilder.build(ctx, request, config);
        // assert
        assertFalse(result.get().isEmpty());
    }
}
