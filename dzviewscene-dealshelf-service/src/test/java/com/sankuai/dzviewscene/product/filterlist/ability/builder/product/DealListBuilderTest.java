package com.sankuai.dzviewscene.product.filterlist.ability.builder.product;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.compress.utils.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/3/13 15:39
 */
@RunWith(MockitoJUnitRunner.class)
public class DealListBuilderTest {

    @InjectMocks
    private DealListBuilder dealListBuilder;

    @Test
    public void testBuildHasTimesDeal() {
        List<ProductM> products = Lists.newArrayList();
        products.add(new ProductM());
        boolean result = dealListBuilder.buildTimesDealQueryFlag(products);
        Assert.assertFalse(result);
    }

}