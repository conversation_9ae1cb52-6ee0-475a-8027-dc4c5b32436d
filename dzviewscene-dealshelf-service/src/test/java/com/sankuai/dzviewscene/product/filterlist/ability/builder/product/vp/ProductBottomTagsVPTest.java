package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.List;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBottomTagsVPTest {

    @Mock
    private ProductBottomTagsVP productBottomTagsVP;

    /**
     * 测试filterEmptyTags方法，当输入列表为null时
     */
    @Test
    public void testFilterEmptyTagsWhenInputIsNull() throws Throwable {
        List<RichLabelVO> beforeTags = null;
        when(productBottomTagsVP.filterEmptyTags(beforeTags)).thenReturn(null);
        List<RichLabelVO> result = productBottomTagsVP.filterEmptyTags(beforeTags);
        assertNull(result);
    }

    /**
     * 测试filterEmptyTags方法，当输入列表为空时
     */
    @Test
    public void testFilterEmptyTagsWhenInputIsEmpty() throws Throwable {
        List<RichLabelVO> beforeTags = Arrays.asList();
        when(productBottomTagsVP.filterEmptyTags(beforeTags)).thenReturn(null);
        List<RichLabelVO> result = productBottomTagsVP.filterEmptyTags(beforeTags);
        assertNull(result);
    }

    /**
     * null元素时
     */
    @Test
    public void testFilterEmptyTagsWhenInputContainsNull() throws Throwable {
        List<RichLabelVO> beforeTags = Arrays.asList(null, new RichLabelVO());
        when(productBottomTagsVP.filterEmptyTags(beforeTags)).thenReturn(Arrays.asList(new RichLabelVO()));
        List<RichLabelVO> result = productBottomTagsVP.filterEmptyTags(beforeTags);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * 测试filterEmptyTags方法，当输入列表中的元素全部非空时
     */
    @Test
    public void testFilterEmptyTagsWhenInputContainsNonNull() throws Throwable {
        List<RichLabelVO> beforeTags = Arrays.asList(new RichLabelVO(), new RichLabelVO());
        when(productBottomTagsVP.filterEmptyTags(beforeTags)).thenReturn(beforeTags);
        List<RichLabelVO> result = productBottomTagsVP.filterEmptyTags(beforeTags);
        assertNotNull(result);
        assertEquals(2, result.size());
    }
}
