package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBottomTagsVP_BuildRichLabelTest {

    @Test
    public void testBuildRichLabelProductPriceMIsNull() throws Throwable {
        // arrange
        ProductBottomTagsVP productBottomTagsVP = mock(ProductBottomTagsVP.class, CALLS_REAL_METHODS);
        ProductPriceM productPriceM = null;
        String salePrice = "100";
        String promoPreDesc = "promoPreDesc";
        // act
        List<RichLabelVO> result = productBottomTagsVP.buildRichLabel(productPriceM, salePrice, promoPreDesc);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelPriceTagIsEmpty() throws Throwable {
        // arrange
        ProductBottomTagsVP productBottomTagsVP = mock(ProductBottomTagsVP.class, CALLS_REAL_METHODS);
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("");
        String salePrice = "100";
        String promoPreDesc = "promoPreDesc";
        // act
        List<RichLabelVO> result = productBottomTagsVP.buildRichLabel(productPriceM, salePrice, promoPreDesc);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelPromoDescRichLabelVOIsNull() throws Throwable {
        // arrange
        ProductBottomTagsVP productBottomTagsVP = mock(ProductBottomTagsVP.class, CALLS_REAL_METHODS);
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("100");
        String salePrice = "100";
        String promoPreDesc = "promoPreDesc";
        when(productBottomTagsVP.buildPromoDescRichLabel(anyString(), anyString())).thenReturn(null);
        // act
        List<RichLabelVO> result = productBottomTagsVP.buildRichLabel(productPriceM, salePrice, promoPreDesc);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelPriceDescLengthLessThan2() throws Throwable {
        // arrange
        ProductBottomTagsVP productBottomTagsVP = mock(ProductBottomTagsVP.class, CALLS_REAL_METHODS);
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("100");
        String salePrice = "100";
        String promoPreDesc = "promoPreDesc";
        when(productBottomTagsVP.buildPromoDescRichLabel(anyString(), anyString())).thenReturn(new RichLabelVO());
        // act
        List<RichLabelVO> result = productBottomTagsVP.buildRichLabel(productPriceM, salePrice, promoPreDesc);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testBuildRichLabelPriceDescLengthGreaterThan2() throws Throwable {
        // arrange
        ProductBottomTagsVP productBottomTagsVP = mock(ProductBottomTagsVP.class, CALLS_REAL_METHODS);
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("100/200");
        String salePrice = "100";
        String promoPreDesc = "promoPreDesc";
        when(productBottomTagsVP.buildPromoDescRichLabel(anyString(), anyString())).thenReturn(new RichLabelVO());
        // act
        List<RichLabelVO> result = productBottomTagsVP.buildRichLabel(productPriceM, salePrice, promoPreDesc);
        // assert
        assertNotNull(result);
    }
}
