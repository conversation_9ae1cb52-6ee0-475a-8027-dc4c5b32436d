package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import org.junit.runner.RunWith;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBottomTagsVP_GetPriceTagTest {

    private ProductBottomTagsVP createTestSubject() {
        return new ProductBottomTagsVP() {

            @Override
            public Object compute(ActivityCxt context, Object input1, Object input2) {
                // Mock or implement necessary behavior here if needed for the test
                return null;
            }
        };
    }

    @Test
    public void testGetPriceTagWhenSalePriceIsNull() throws Throwable {
        ProductBottomTagsVP productBottomTagsVP = createTestSubject();
        String salePrice = null;
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("￥100");
        String[] priceDesc = new String[] { "50" };
        String result = productBottomTagsVP.getPriceTag(priceDesc, productPriceM, salePrice);
        assertEquals("", result);
    }

    @Test
    public void testGetPriceTagWhenPriceDescLengthLessThan2() throws Throwable {
        ProductBottomTagsVP productBottomTagsVP = createTestSubject();
        String salePrice = "100";
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("￥100");
        String[] priceDesc = new String[] { "50" };
        String result = productBottomTagsVP.getPriceTag(priceDesc, productPriceM, salePrice);
        assertEquals("", result);
    }

    @Test
    public void testGetPriceTagWhenPriceDescLengthGreaterThan2() throws Throwable {
        ProductBottomTagsVP productBottomTagsVP = createTestSubject();
        String salePrice = "150";
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("￥100");
        String[] priceDesc = new String[] { "￥100", "10" };
        String result = productBottomTagsVP.getPriceTag(priceDesc, productPriceM, salePrice);
        assertEquals("￥50", result);
    }

    @Test
    public void testGetPriceTagWhenSalePriceLessThanOrEqualToPriceTag() throws Throwable {
        ProductBottomTagsVP productBottomTagsVP = createTestSubject();
        String salePrice = "50";
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("￥100");
        String[] priceDesc = new String[] { "50" };
        String result = productBottomTagsVP.getPriceTag(priceDesc, productPriceM, salePrice);
        assertEquals("", result);
    }

    @Test
    public void testGetPriceTagWhenSalePriceGreaterThanPriceTag() throws Throwable {
        ProductBottomTagsVP productBottomTagsVP = createTestSubject();
        String salePrice = "150";
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag("￥100");
        String[] priceDesc = new String[] { "50" };
        String result = productBottomTagsVP.getPriceTag(priceDesc, productPriceM, salePrice);
        assertEquals("￥50", result);
    }
}
