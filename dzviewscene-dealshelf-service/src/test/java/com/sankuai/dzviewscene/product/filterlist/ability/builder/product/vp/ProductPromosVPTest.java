package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.List;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductPromosVPTest {

    @Mock
    private ProductPromosVP productPromosVP;

    /**
     * 测试 buildDzPromoVOS 方法，当 productPromoPriceM 的 promoTag 为 null 时
     */
    @Test
    public void testBuildDzPromoVOSWhenPromoTagIsNull() throws Throwable {
        // arrange
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoTag(null);
        when(productPromosVP.buildDzPromoVOS(productPromoPriceM)).thenCallRealMethod();
        // act
        List<DzPromoVO> result = productPromosVP.buildDzPromoVOS(productPromoPriceM);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getPromo());
    }

    /**
     * 测试 buildDzPromoVOS 方法，当 productPromoPriceM 的 promoTag 为非 null 时
     */
    @Test
    public void testBuildDzPromoVOSWhenPromoTagIsNotNull() throws Throwable {
        // arrange
        String promoTag = "test";
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoTag(promoTag);
        when(productPromosVP.buildDzPromoVOS(productPromoPriceM)).thenCallRealMethod();
        // act
        List<DzPromoVO> result = productPromosVP.buildDzPromoVOS(productPromoPriceM);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(promoTag, result.get(0).getPromo());
    }
}
