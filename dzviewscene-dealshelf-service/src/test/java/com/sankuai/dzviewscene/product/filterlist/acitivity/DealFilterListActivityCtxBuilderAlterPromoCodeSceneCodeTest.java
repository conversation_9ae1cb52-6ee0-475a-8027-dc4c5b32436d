package com.sankuai.dzviewscene.product.filterlist.acitivity;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheCompositeAtomService;
import com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

public class DealFilterListActivityCtxBuilderAlterPromoCodeSceneCodeTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private CacheCompositeAtomService cacheCompositeAtomService;

    private MockedStatic<AthenaBeanFactory> mockedAthenaBeanFactory;

    private AutoCloseable closeable;

    @InjectMocks
    private DealFilterListActivityCtxBuilder ctxBuilder;

    @Before
    public void setUp() {
        // Initialize mocks
        closeable = MockitoAnnotations.openMocks(this);
        // Set up static mock for AthenaBeanFactory
        mockedAthenaBeanFactory = Mockito.mockStatic(AthenaBeanFactory.class);
        mockedAthenaBeanFactory.when(() -> AthenaBeanFactory.getBean(CacheCompositeAtomService.class)).thenReturn(cacheCompositeAtomService);
    }

    @After
    public void tearDown() throws Exception {
        // Close the static mock
        if (mockedAthenaBeanFactory != null) {
            mockedAthenaBeanFactory.close();
        }
        // Close the mocks
        if (closeable != null) {
            closeable.close();
        }
    }

    private void invokeAlterPromoCodeSceneCode(ActivityCxt activityContext) throws Exception {
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("alterPromoCodeSceneCode", ActivityCxt.class);
        method.setAccessible(true);
        method.invoke(ctxBuilder, activityContext);
    }

    /**
     * Test case: Non-unified promo code shelf should return early
     */
    @Test
    public void testAlterPromoCodeSceneCode_NonUnifiedShelf() throws Throwable {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.sceneCode, "some_other_scene");
        // Use integer value for userAgent (MT_APP code)
        activityContext.addParam(ShelfActivityConstants.Params.userAgent, VCClientTypeEnum.MT_APP.getCode());
        // Mock the behavior of cacheCompositeAtomService since it's used in fillMtShopId()
        when(cacheCompositeAtomService.getMtByDpPoiIdL(anyLong())).thenReturn(CompletableFuture.completedFuture(456L));
        invokeAlterPromoCodeSceneCode(activityContext);
        verify(compositeAtomService, never()).queryPromoCodeShopShelfConfig(any());
    }

    /**
     * Test case: Unified shop shelf with default config
     */
    @Test
    public void testAlterPromoCodeSceneCode_UnifiedShopShelf_DefaultConfig() throws Throwable {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.sceneCode, "promocode_unified_shop_shelf");
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, 123L);
        // Use integer value for userAgent (MT_APP code)
        activityContext.addParam(ShelfActivityConstants.Params.userAgent, VCClientTypeEnum.MT_APP.getCode());
        ShelfLoadConfig config = new ShelfLoadConfig();
        config.setLoadCustomizeGroupPurchase(false);
        // Mock the service calls
        when(compositeAtomService.queryPromoCodeShopShelfConfig(any())).thenReturn(CompletableFuture.completedFuture(config));
        when(cacheCompositeAtomService.getMtByDpPoiIdL(anyLong())).thenReturn(CompletableFuture.completedFuture(456L));
        invokeAlterPromoCodeSceneCode(activityContext);
        // Verify the interactions
        verify(compositeAtomService).queryPromoCodeShopShelfConfig(any());
        String newSceneCode = activityContext.getParam(ShelfActivityConstants.Params.sceneCode);
        assertEquals(PromoCodeUtils.PROMO_CODE_SHOP_SCENE, newSceneCode);
    }
}
