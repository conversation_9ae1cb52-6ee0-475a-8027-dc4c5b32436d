package com.sankuai.dzviewscene.product.filterlist.acitivity;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.mockito.InjectMocks;

import java.lang.reflect.InvocationTargetException;

@RunWith(MockitoJUnitRunner.class)
public class DealFilterListActivityCtxBuilderDefaultListNeedShopRouteTest {

    private DealFilterListActivityCtxBuilder builder = new DealFilterListActivityCtxBuilder();

    @Mock
    private ActivityCxt mockActivityContext;

    @InjectMocks
    private DealFilterListActivityCtxBuilder ctxBuilder;

    private ActivityCxt activityContext;

    @Test
    public void testDefaultListNeedShopRoute_PromocodeUnifiedShopShelf() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("promocode_unified_shop_shelf");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertTrue(result);
    }

    @Test
    public void testDefaultListNeedShopRoute_PromocodeUnifiedStaffShelf() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("promocode_unified_staff_shelf");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertTrue(result);
    }

    @Test
    public void testDefaultListNeedShopRoute_ActivityBeautyMedicalCouponShelf() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("activity_beauty_medical_coupon_shelf");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertTrue(result);
    }

    @Test
    public void testDefaultListNeedShopRoute_ActivityBeautyMedicalCouponSearchShelf() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("activity_beauty_medical_coupon_search_shelf");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertTrue(result);
    }

    @Test
    public void testDefaultListNeedShopRoute_ActivityBeautyMedicalCustomizeCouponShelf() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("activity_beauty_medical_customize_coupon_shelf");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertTrue(result);
    }

    @Test
    public void testDefaultListNeedShopRoute_ActivityLeTradeGuaranteeShelf() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("activity_le_trade_guarantee_shelf");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertTrue(result);
    }

    @Test
    public void testDefaultListNeedShopRoute_ActivityBeautyMedicalCouponStaffNavShelf() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("activity_beauty_medical_coupon_staff_nav_shelf");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertTrue(result);
    }

    @Test
    public void testDefaultListNeedShopRoute_OtherSceneCode() throws Throwable {
        when(mockActivityContext.getParam("sceneCode")).thenReturn("other_scene_code");
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("defaultListNeedShopRoute", ActivityCxt.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(builder, mockActivityContext);
        assertFalse(result);
    }

    private boolean invokePrivateMethod(String methodName, String activityContext) throws Exception {
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod(methodName, String.class);
        method.setAccessible(true);
        return (boolean) method.invoke(ctxBuilder, activityContext);
    }

    @Test
    public void testIsUnifiedPromoCodeShelf_WithUnifiedShopShelf_ReturnsTrue() throws Throwable {
        // arrange// act
        boolean result = invokePrivateMethod("isUnifiedPromoCodeShelf", "promocode_unified_shop_shelf");
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsUnifiedPromoCodeShelf_WithUnifiedStaffShelf_ReturnsTrue() throws Throwable {
        // act
        boolean result = invokePrivateMethod("isUnifiedPromoCodeShelf", "promocode_unified_staff_shelf");
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsUnifiedPromoCodeShelf_WithNullSceneCode_ReturnsFalse() throws Throwable {
        // act
        boolean result = invokePrivateMethod("isUnifiedPromoCodeShelf", null);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsUnifiedPromoCodeShelf_WithDifferentSceneCode_ReturnsFalse() throws Throwable {
        // act
        boolean result = invokePrivateMethod("isUnifiedPromoCodeShelf", "other_scene_code");
        // assert
        assertFalse(result);
    }

    @Before
    public void setUp() {
        activityContext = mock(ActivityCxt.class);
    }

    @Test
    public void testAddPromoShelfParamsNormal() throws Throwable {
        // Arrange
        DealFilterListActivityCtxBuilder builder = new DealFilterListActivityCtxBuilder();
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("addPromoShelfParams", ActivityCxt.class);
        method.setAccessible(true);
        // Mock required parameters
        when(activityContext.getParam(anyString())).thenReturn(null);
        // Act
        method.invoke(builder, activityContext);
        // Assert
        verify(activityContext, atLeastOnce()).getParam(anyString());
    }

    @Test
    public void testAddPromoShelfParamsWithNullContext() throws Throwable {
        // Arrange
        DealFilterListActivityCtxBuilder builder = new DealFilterListActivityCtxBuilder();
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("addPromoShelfParams", ActivityCxt.class);
        method.setAccessible(true);
        try {
            // Act
            method.invoke(builder, new Object[] { null });
            fail("Expected NullPointerException");
        } catch (InvocationTargetException e) {
            // Assert
            assertTrue("Expected NullPointerException but got " + e.getCause().getClass(), e.getCause() instanceof NullPointerException);
        }
    }
}
