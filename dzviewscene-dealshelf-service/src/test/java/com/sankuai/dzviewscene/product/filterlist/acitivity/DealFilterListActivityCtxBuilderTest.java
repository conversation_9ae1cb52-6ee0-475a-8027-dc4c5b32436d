package com.sankuai.dzviewscene.product.filterlist.acitivity;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.MagicMemberCouponCheckResult;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import com.dianping.gmkt.event.api.promoqrcode.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingInfoRequest;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.dzviewscene.nr.atom.cache.CacheCompositeAtomService;
import org.mockito.MockedStatic;

/**
 * Test cases for DealFilterListActivityCtxBuilder.addPromoShelfExtraParams
 */
@RunWith(MockitoJUnitRunner.class)
public class DealFilterListActivityCtxBuilderTest {

    @InjectMocks
    private DealFilterListActivityCtxBuilder ctxBuilder;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private AtomFacadeService facadeService;

    @Mock
    private ActivityCxt mockActivityContext;

    private String originalAppName;

    @Before
    public void setUp() throws Exception {
        Field appNameField = Environment.class.getDeclaredField("appName");
        appNameField.setAccessible(true);
        originalAppName = (String) appNameField.get(null);
        appNameField.set(null, "testApp");
    }

    @After
    public void tearDown() throws Exception {
        Field appNameField = Environment.class.getDeclaredField("appName");
        appNameField.setAccessible(true);
        appNameField.set(null, originalAppName);
    }

    /**
     * Test case: extra parameter is blank
     * Expected: should not add any parameters
     */
    @Test
    public void testAddPromoShelfExtraParams_ExtraBlank() throws Throwable {
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn("");
        ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
        verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.orderTrafficFlag), any());
        verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), any());
    }

    /**
     * Test case: extra parameter is null
     * Expected: should not add any parameters
     */
    @Test
    public void testAddPromoShelfExtraParams_ExtraNull() throws Throwable {
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(null);
        ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
        verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.orderTrafficFlag), any());
        verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), any());
    }

    /**
     * Test case: extra parameter decodes to empty map
     * Expected: should add orderTrafficFlag but not promoCodeExtraInfo
     */
    @Test
    public void testAddPromoShelfExtraParams_ExtraMapEmpty() throws Throwable {
        String extraJson = "{}";
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        try (MockedStatic<JsonCodec> jsonCodecStatic = mockStatic(JsonCodec.class)) {
            jsonCodecStatic.when(() -> JsonCodec.decode(anyString(), any(TypeReference.class))).thenReturn(new HashMap<>());
            ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
            verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), any());
        }
    }

    /**
     * Test case: Lion switch is true and valid extra parameters
     * Expected: should attach magicMemberCouponSwitch and add all parameters
     */
    @Test
    public void testAddPromoShelfExtraParams_LionSwitchTrue() throws Throwable {
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("source", 1);
        extraMap.put("codeKey", "code123");
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.dpPoiIdL)).thenReturn(123L);
        try (MockedStatic<JsonCodec> jsonCodecStatic = mockStatic(JsonCodec.class);
            MockedStatic<Lion> lionStatic = mockStatic(Lion.class)) {
            jsonCodecStatic.when(() -> JsonCodec.decode(anyString(), any(TypeReference.class))).thenReturn(extraMap);
            lionStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(true);
            MagicMemberCouponCheckResult checkResult = mock(MagicMemberCouponCheckResult.class);
            RemoteResponse<MagicMemberCouponCheckResult> response = RemoteResponse.success(checkResult);
            CompletableFuture<RemoteResponse<MagicMemberCouponCheckResult>> future = CompletableFuture.completedFuture(response);
            when(compositeAtomService.queryMagicMemberCouponSwitch(123L)).thenReturn(future);
            ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
            verify(mockActivityContext).attach(eq("magicMemberCouponSwitch"), eq(future));
            verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), argThat(map -> {
                Map<String, Object> promoMap = (Map<String, Object>) map;
                assertEquals(1, promoMap.get("source"));
                assertTrue(promoMap.containsKey("pass_param"));
                assertEquals("code123", promoMap.get("codeKey"));
                return true;
            }));
        }
    }

    /**
     * Test case: Lion switch is false with mmc fields
     * Expected: should add all mmc fields to promoCodeExtraInfo
     */
    @Test
    public void testAddPromoShelfExtraParams_LionSwitchFalse_MmcFields() throws Throwable {
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("source", 2);
        extraMap.put("codeKey", "code456");
        extraMap.put("mmcinflate", "inflate");
        extraMap.put("mmcuse", "use");
        extraMap.put("mmcbuy", "buy");
        extraMap.put("mmcfree", "free");
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);
        try (MockedStatic<JsonCodec> jsonCodecStatic = mockStatic(JsonCodec.class);
            MockedStatic<Lion> lionStatic = mockStatic(Lion.class)) {
            jsonCodecStatic.when(() -> JsonCodec.decode(anyString(), any(TypeReference.class))).thenReturn(extraMap);
            lionStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(false);
            ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
            verify(mockActivityContext, never()).attach(eq("magicMemberCouponSwitch"), any());
            verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), argThat(map -> {
                Map<String, Object> promoMap = (Map<String, Object>) map;
                assertEquals(2, promoMap.get("source"));
                assertEquals("inflate", promoMap.get("mmcinflate"));
                assertEquals("use", promoMap.get("mmcuse"));
                assertEquals("buy", promoMap.get("mmcbuy"));
                assertEquals("free", promoMap.get("mmcfree"));
                assertEquals("code456", promoMap.get("codeKey"));
                assertTrue(promoMap.containsKey("pass_param"));
                return true;
            }));
        }
    }

    /**
     * Test case: Lion switch is false without mmc fields and codeKey
     * Expected: should add basic parameters without mmc fields
     */
    @Test
    public void testAddPromoShelfExtraParams_LionSwitchFalse_NoMmcFields_NoCodeKey() throws Throwable {
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("source", 3);
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);
        try (MockedStatic<JsonCodec> jsonCodecStatic = mockStatic(JsonCodec.class);
            MockedStatic<Lion> lionStatic = mockStatic(Lion.class)) {
            jsonCodecStatic.when(() -> JsonCodec.decode(anyString(), any(TypeReference.class))).thenReturn(extraMap);
            lionStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(false);
            ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
            verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), argThat(map -> {
                Map<String, Object> promoMap = (Map<String, Object>) map;
                assertEquals(3, promoMap.get("source"));
                assertEquals("", promoMap.get("codeKey"));
                assertTrue(promoMap.containsKey("pass_param"));
                assertFalse(promoMap.containsKey("mmcinflate"));
                assertFalse(promoMap.containsKey("mmcuse"));
                assertFalse(promoMap.containsKey("mmcbuy"));
                assertFalse(promoMap.containsKey("mmcfree"));
                return true;
            }));
        }
    }

    /**
     * Test case: MT_XCX user agent with version
     * Expected: should add appVersion parameter
     */
    @Test
    public void testAddPromoShelfExtraParams_UserAgentMtXcx_WithVersion() throws Throwable {
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("source", 4);
        extraMap.put("codeKey", "code789");
        extraMap.put("version", "1.2.3");
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_XCX.getCode());
        try (MockedStatic<JsonCodec> jsonCodecStatic = mockStatic(JsonCodec.class);
            MockedStatic<Lion> lionStatic = mockStatic(Lion.class)) {
            jsonCodecStatic.when(() -> JsonCodec.decode(anyString(), any(TypeReference.class))).thenReturn(extraMap);
            lionStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(false);
            ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
            verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), anyMap());
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.appVersion, "1.2.3");
        }
    }

    /**
     * Test case: MT_XCX user agent without version
     * Expected: should not add appVersion parameter
     */
    @Test
    public void testAddPromoShelfExtraParams_UserAgentMtXcx_NoVersion() throws Throwable {
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("source", 5);
        extraMap.put("codeKey", "code000");
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_XCX.getCode());
        try (MockedStatic<JsonCodec> jsonCodecStatic = mockStatic(JsonCodec.class);
            MockedStatic<Lion> lionStatic = mockStatic(Lion.class)) {
            jsonCodecStatic.when(() -> JsonCodec.decode(anyString(), any(TypeReference.class))).thenReturn(extraMap);
            lionStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(false);
            ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
            verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), anyMap());
            verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.appVersion), any());
        }
    }

    /**
     * Test case: JsonCodec.decode returns null
     * Expected: should only add orderTrafficFlag
     */
    @Test
    public void testAddPromoShelfExtraParams_JsonCodecDecodeNull() throws Throwable {
        String extraJson = "validUrlEncoded";
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        try (MockedStatic<JsonCodec> jsonCodecStatic = mockStatic(JsonCodec.class)) {
            jsonCodecStatic.when(() -> JsonCodec.decode(anyString(), any(TypeReference.class))).thenReturn(null);
            ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
            verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
            verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), any());
        }
    }

    /**
     * Test case: URL decode throws exception
     * Expected: should only add orderTrafficFlag
     */
    @Test
    public void testAddPromoShelfExtraParams_DecodeExtraParamThrows() throws Throwable {
        // Invalid URL encoded string
        String extraJson = "%";
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        ctxBuilder.addPromoShelfExtraParams(mockActivityContext);
        verify(mockActivityContext).addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
        verify(mockActivityContext, never()).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), any());
    }

    private LandingInfoRequest invokeBuildShelfLandInfoReq(ActivityCxt activityContext, long mtShopId, Integer codeType, String sourceId) throws Exception {
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod("buildShelfLandInfoReq", ActivityCxt.class, long.class, Integer.class, String.class);
        method.setAccessible(true);
        return (LandingInfoRequest) method.invoke(null, activityContext, mtShopId, codeType, sourceId);
    }

    @Test
    public void testBuildShelfLandInfoReqWithNonAppUserAgent() throws Throwable {
        // arrange
        ActivityCxt activityContext = mock(ActivityCxt.class);
        // DP_WX code
        when(activityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(103);
        // act
        LandingInfoRequest result = invokeBuildShelfLandInfoReq(activityContext, 12345L, 1, "sourceId123");
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(12345L), result.getShopId());
        assertEquals(Integer.valueOf(1), result.getCodeType());
        assertEquals("sourceId123", result.getSourceIdentifier());
        assertEquals(Integer.valueOf(QRClientType.MT_WE_CHAT_APPLET.code), result.getQrClientType());
    }

    @Test
    public void testBuildShelfLandInfoReqWithMTAppUserAgent() throws Throwable {
        // arrange
        ActivityCxt activityContext = mock(ActivityCxt.class);
        // MT_APP code
        when(activityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(200);
        // act
        LandingInfoRequest result = invokeBuildShelfLandInfoReq(activityContext, 12345L, 1, "sourceId123");
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(12345L), result.getShopId());
        assertEquals(Integer.valueOf(1), result.getCodeType());
        assertEquals("sourceId123", result.getSourceIdentifier());
        assertEquals(Integer.valueOf(QRClientType.MT_APP.code), result.getQrClientType());
    }

    @Test
    public void testBuildShelfLandInfoReqWithDPAppUserAgent() throws Throwable {
        // arrange
        ActivityCxt activityContext = mock(ActivityCxt.class);
        // DP_APP code
        when(activityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);
        // act
        LandingInfoRequest result = invokeBuildShelfLandInfoReq(activityContext, 12345L, 1, "sourceId123");
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(12345L), result.getShopId());
        assertEquals(Integer.valueOf(1), result.getCodeType());
        assertEquals("sourceId123", result.getSourceIdentifier());
        assertEquals(Integer.valueOf(QRClientType.DP_APP.code), result.getQrClientType());
    }

    @Test
    public void testBuildShelfLandInfoReqWithNullUserAgent() throws Throwable {
        // arrange
        ActivityCxt activityContext = mock(ActivityCxt.class);
        when(activityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(null);
        try {
            // act
            invokeBuildShelfLandInfoReq(activityContext, 12345L, 1, "sourceId123");
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e instanceof InvocationTargetException);
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    @Test
    public void testBuildShelfLandInfoReqWithNullCodeType() throws Throwable {
        // arrange
        ActivityCxt activityContext = mock(ActivityCxt.class);
        when(activityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);
        // act
        LandingInfoRequest result = invokeBuildShelfLandInfoReq(activityContext, 12345L, null, "sourceId123");
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(12345L), result.getShopId());
        assertNull(result.getCodeType());
        assertEquals("sourceId123", result.getSourceIdentifier());
        assertEquals(Integer.valueOf(QRClientType.DP_APP.code), result.getQrClientType());
    }

    private Long invokePrivateMethod(String methodName, ActivityCxt activityContext) throws Exception {
        Method method = DealFilterListActivityCtxBuilder.class.getDeclaredMethod(methodName, ActivityCxt.class);
        method.setAccessible(true);
        return (Long) method.invoke(null, activityContext);
    }

    @Test
    public void testFillMtShopId_WithValidMtPoiId() throws Throwable {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiIdL, 12345L);
        Long result = invokePrivateMethod("fillMtShopId", activityContext);
        assertEquals(Long.valueOf(12345L), result);
    }

    @Test
    public void testFillMtShopId_FetchFromCache() throws Throwable {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiIdL, "0");
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, 67890L);
        CacheCompositeAtomService mockCacheService = mock(CacheCompositeAtomService.class);
        when(mockCacheService.getMtByDpPoiIdL(67890L)).thenReturn(CompletableFuture.completedFuture(54321L));
        try (MockedStatic<AthenaBeanFactory> mockedStatic = mockStatic(AthenaBeanFactory.class)) {
            mockedStatic.when(() -> AthenaBeanFactory.getBean(CacheCompositeAtomService.class)).thenReturn(mockCacheService);
            Long result = invokePrivateMethod("fillMtShopId", activityContext);
            assertEquals(Long.valueOf(54321L), result);
            assertEquals(Long.valueOf(54321L), activityContext.getParam(ShelfActivityConstants.Params.mtPoiIdL));
        }
    }

    @Test
    public void testFillMtShopId_BothIdsZero() throws Throwable {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiIdL, "0");
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, "0");
        CacheCompositeAtomService mockCacheService = mock(CacheCompositeAtomService.class);
        when(mockCacheService.getMtByDpPoiIdL(0L)).thenReturn(CompletableFuture.completedFuture(null));
        try (MockedStatic<AthenaBeanFactory> mockedStatic = mockStatic(AthenaBeanFactory.class)) {
            mockedStatic.when(() -> AthenaBeanFactory.getBean(CacheCompositeAtomService.class)).thenReturn(mockCacheService);
            Long result = invokePrivateMethod("fillMtShopId", activityContext);
            assertEquals(null, result);
            assertEquals(null, activityContext.getParam(ShelfActivityConstants.Params.mtPoiIdL));
        }
    }

    @Test(expected = InvocationTargetException.class)
    public void testFillMtShopId_CacheServiceException() throws Throwable {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiIdL, "0");
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, 67890L);
        CacheCompositeAtomService mockCacheService = mock(CacheCompositeAtomService.class);
        when(mockCacheService.getMtByDpPoiIdL(67890L)).thenThrow(new RuntimeException("Cache service error"));
        try (MockedStatic<AthenaBeanFactory> mockedStatic = mockStatic(AthenaBeanFactory.class)) {
            mockedStatic.when(() -> AthenaBeanFactory.getBean(CacheCompositeAtomService.class)).thenReturn(mockCacheService);
            invokePrivateMethod("fillMtShopId", activityContext);
        }
    }
}
