package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.defaultShowNum;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListDefaultShowNumVP;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefaultShowNumOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DefaultShowNumOpt.Param param;

    @Mock
    private DefaultShowNumOpt.Config config;

    /**
     * 测试 compute 方法，当 config.isUseConfigDefaultShowNum() 返回 false 时
     */
    @Test
    public void testComputeWhenUseConfigDefaultShowNumIsFalse() {
        // arrange
        DefaultShowNumOpt defaultShowNumOpt = new DefaultShowNumOpt();
        when(config.isUseConfigDefaultShowNum()).thenReturn(false);
        when(param.getDefaultShowNum()).thenReturn(10);
        // act
        Integer result = defaultShowNumOpt.compute(activityCxt, param, config);
        // assert
        assertEquals(Integer.valueOf(10), result);
    }

    /**
     * 测试 compute 方法，当 config.isUseConfigDefaultShowNum() 返回 true 时
     */
    @Test
    public void testComputeWhenUseConfigDefaultShowNumIsTrue() {
        // arrange
        DefaultShowNumOpt defaultShowNumOpt = new DefaultShowNumOpt();
        when(config.isUseConfigDefaultShowNum()).thenReturn(true);
        when(config.getDefaultShowNum()).thenReturn(20);
        // act
        Integer result = defaultShowNumOpt.compute(activityCxt, param, config);
        // assert
        assertEquals(Integer.valueOf(20), result);
    }
}
