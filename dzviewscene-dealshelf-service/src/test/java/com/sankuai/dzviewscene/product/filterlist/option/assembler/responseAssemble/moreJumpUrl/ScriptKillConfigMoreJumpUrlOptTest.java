package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;

@RunWith(MockitoJUnitRunner.class)
public class ScriptKillConfigMoreJumpUrlOptTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private ScriptKillConfigMoreJumpUrlOpt.Config config;

    private ScriptKillConfigMoreJumpUrlOpt scriptKillConfigMoreJumpUrlOpt;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void setUp() {
        scriptKillConfigMoreJumpUrlOpt = new ScriptKillConfigMoreJumpUrlOpt();
        when(config.getDefaultDisplayCount()).thenReturn(3);
    }

    private String serializeMap(Map<String, Object> map) {
        try {
            return objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }

    @Test
    public void testComputeLessThanDefaultCount() throws Throwable {
        try (MockedStatic<ContextParamBuildUtils> mockedStatic = mockStatic(ContextParamBuildUtils.class)) {
            mockedStatic.when(() -> ContextParamBuildUtils.lessThanDefaultCount(anyInt(), any(ActivityCxt.class))).thenReturn(true);
            String result = scriptKillConfigMoreJumpUrlOpt.compute(ctx, null, config);
            assertNull(result);
        }
    }
}
