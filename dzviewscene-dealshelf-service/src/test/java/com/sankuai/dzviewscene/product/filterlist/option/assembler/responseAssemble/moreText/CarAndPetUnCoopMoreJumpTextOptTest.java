package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreText;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.CarAndPetUnCoopCommonInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreTextVP;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CarAndPetUnCoopMoreJumpTextOptTest {

    @Mock
    private ActivityCxt activityCxt;

    private CarAndPetUnCoopMoreJumpTextOpt opt;
    private CarAndPetUnCoopMoreJumpTextOpt.Config config;
    @Mock
    private DealFilterListMoreTextVP.Param param;

    @Before
    public void setUp() {
        opt = new CarAndPetUnCoopMoreJumpTextOpt();
        config = new CarAndPetUnCoopMoreJumpTextOpt.Config();
    }

    @Test
    public void compute_whenHasJumpDesc_thenReturnDesc() {
        // given
        String expectedDesc = "查看更多";
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setJumpDesc(expectedDesc);
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(expectedDesc, result);
    }

    @Test
    public void compute_whenAttrIsNull_thenReturnDefaultText() {
        // given
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(null);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(config.getDefaultText(), result);
    }

    @Test
    public void compute_whenJumpDescIsEmpty_thenReturnDefaultText() {
        // given
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setJumpDesc("");
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(config.getDefaultText(), result);
    }

    @Test
    public void compute_whenJumpDescIsBlank_thenReturnDefaultText() {
        // given
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setJumpDesc("   ");
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(config.getDefaultText(), result);
    }
}
