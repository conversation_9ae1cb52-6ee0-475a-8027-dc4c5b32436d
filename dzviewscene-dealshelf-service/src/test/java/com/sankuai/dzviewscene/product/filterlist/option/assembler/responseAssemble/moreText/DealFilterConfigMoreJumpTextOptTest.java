package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreText;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreTextVP;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.Constructor;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFilterConfigMoreJumpTextOptTest {

    private DealFilterListMoreTextVP.Param createParam(int dealCount) throws Exception {
        Constructor<DealFilterListMoreTextVP.Param> constructor = DealFilterListMoreTextVP.Param.class.getDeclaredConstructor(int.class);
        constructor.setAccessible(true);
        return constructor.newInstance(dealCount);
    }

    /**
     * 测试 compute 方法，当 param.getDealCount() 小于等于 config.getDefaultDisplayCount() 时，应返回 null.
     */
    @Test
    public void testComputeDealCountLessThanOrEqualToDefaultDisplayCount() throws Throwable {
        // arrange
        DealFilterConfigMoreJumpTextOpt opt = new DealFilterConfigMoreJumpTextOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        DealFilterListMoreTextVP.Param param = createParam(3);
        DealFilterConfigMoreJumpTextOpt.Config config = new DealFilterConfigMoreJumpTextOpt.Config();
        config.setDefaultDisplayCount(3);
        // act
        String result = opt.compute(activityCxt, param, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试 compute 方法，当 param.getDealCount() 大于 config.getDefaultDisplayCount() 时，应返回一个格式化字符串.
     */
    @Test
    public void testComputeDealCountGreaterThanDefaultDisplayCount() throws Throwable {
        // arrange
        DealFilterConfigMoreJumpTextOpt opt = new DealFilterConfigMoreJumpTextOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        DealFilterListMoreTextVP.Param param = createParam(4);
        DealFilterConfigMoreJumpTextOpt.Config config = new DealFilterConfigMoreJumpTextOpt.Config();
        config.setDefaultDisplayCount(3);
        config.setMoreJumpText("More (%d)");
        // act
        String result = opt.compute(activityCxt, param, config);
        // assert
        assertEquals("More (1)", result);
    }
}
