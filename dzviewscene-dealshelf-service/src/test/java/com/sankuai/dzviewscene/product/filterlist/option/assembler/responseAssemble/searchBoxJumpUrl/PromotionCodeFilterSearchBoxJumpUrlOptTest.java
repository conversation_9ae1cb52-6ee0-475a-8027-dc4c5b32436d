package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.searchBoxJumpUrl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.searchBoxJumpUrl.PromotionCodeFilterSearchBoxJumpUrlOpt.Config;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PromotionCodeFilterSearchBoxJumpUrlOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private PromotionCodeFilterSearchBoxJumpUrlOpt.Param param;

    @Mock
    private PromotionCodeFilterSearchBoxJumpUrlOpt.Config config;

    /**
     * Tests the compute method when buildJumpUrl returns normally.
     */
    @Test
    public void testComputeNormal() throws Throwable {
        // Arrange
        PromotionCodeFilterSearchBoxJumpUrlOpt opt = new PromotionCodeFilterSearchBoxJumpUrlOpt();
        // Mocking config to return a non-null dpJumpUrl
        when(config.getDpJumpUrl()).thenReturn("http://example.com/jumpUrl");
        // Act
        String result = opt.compute(context, param, config);
        // Assert
        assertNotNull(result);
    }

    /**
     * Tests the compute method when buildJumpUrl throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testComputeException() throws Throwable {
        // Arrange
        PromotionCodeFilterSearchBoxJumpUrlOpt opt = new PromotionCodeFilterSearchBoxJumpUrlOpt();
        // Similar to the normal test case, we arrange the setup to lead to the exception through the public API.
        // This might involve configuring the mocks to return values that would lead to the exception being thrown.
        // Act
        opt.compute(context, param, config);
        // Assert is in the annotation
    }
}
