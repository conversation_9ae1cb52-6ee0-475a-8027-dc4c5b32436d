package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.CarAndPetUnCoopCommonInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListTitleVP;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CarAndPetUnCoopShopDealListTitleOptTest {

    @Mock
    private ActivityCxt activityCxt;

    private CarAndPetUnCoopShopDealListTitleOpt opt;
    private CarAndPetUnCoopShopDealListTitleOpt.Config config;
    @Mock
    private DealFilterListTitleVP.Param param;

    @Before
    public void setUp() {
        opt = new CarAndPetUnCoopShopDealListTitleOpt();
        config = new CarAndPetUnCoopShopDealListTitleOpt.Config();
        config.setDefaultTitle("默认标题");
    }

    @Test
    public void compute_whenHasModuleName_thenReturnModuleName() {
        // given
        String expectedTitle = "测试标题";
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setModuleName(expectedTitle);
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(expectedTitle, result);
    }

    @Test
    public void compute_whenAttrIsNull_thenReturnDefaultTitle() {
        // given
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(null);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(config.getDefaultTitle(), result);
    }

    @Test
    public void compute_whenModuleNameIsEmpty_thenReturnDefaultTitle() {
        // given
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setModuleName("");
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(config.getDefaultTitle(), result);
    }

    @Test
    public void compute_whenModuleNameIsBlank_thenReturnDefaultTitle() {
        // given
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setModuleName("   ");
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(config.getDefaultTitle(), result);
    }
}
