package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListTitleVP;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFilterTitleWithStatisticsOptTest {

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        // arrange
        DealFilterTitleWithStatisticsOpt opt = new DealFilterTitleWithStatisticsOpt();
        ActivityCxt context = new ActivityCxt();
        // Using builder pattern to instantiate Param
        DealFilterListTitleVP.Param param = DealFilterListTitleVP.Param.builder().dealCount(1).scriptKillTitle("someString").build();
        // act
        String result = opt.compute(context, param, null);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testComputeTitleIsEmpty() throws Throwable {
        // arrange
        DealFilterTitleWithStatisticsOpt opt = new DealFilterTitleWithStatisticsOpt();
        ActivityCxt context = new ActivityCxt();
        // Using builder pattern to instantiate Param
        DealFilterListTitleVP.Param param = DealFilterListTitleVP.Param.builder().dealCount(1).scriptKillTitle("someString").build();
        DealFilterTitleWithStatisticsOpt.Config config = new DealFilterTitleWithStatisticsOpt.Config();
        config.setTitle("");
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testComputeDealCountIsZero() throws Throwable {
        // arrange
        DealFilterTitleWithStatisticsOpt opt = new DealFilterTitleWithStatisticsOpt();
        ActivityCxt context = new ActivityCxt();
        // Using builder pattern to instantiate Param
        DealFilterListTitleVP.Param param = DealFilterListTitleVP.Param.builder().dealCount(0).scriptKillTitle("someString").build();
        DealFilterTitleWithStatisticsOpt.Config config = new DealFilterTitleWithStatisticsOpt.Config();
        config.setTitle("title");
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertEquals("title", result);
    }

    @Test
    public void testComputeHideCountIsTrue() throws Throwable {
        // arrange
        DealFilterTitleWithStatisticsOpt opt = new DealFilterTitleWithStatisticsOpt();
        ActivityCxt context = new ActivityCxt();
        // Using builder pattern to instantiate Param
        DealFilterListTitleVP.Param param = DealFilterListTitleVP.Param.builder().dealCount(1).scriptKillTitle("someString").build();
        DealFilterTitleWithStatisticsOpt.Config config = new DealFilterTitleWithStatisticsOpt.Config();
        config.setTitle("title");
        config.setHideCount(true);
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertEquals("title", result);
    }

    @Test
    public void testComputeHideCountIsFalse() throws Throwable {
        // arrange
        DealFilterTitleWithStatisticsOpt opt = new DealFilterTitleWithStatisticsOpt();
        ActivityCxt context = new ActivityCxt();
        // Using builder pattern to instantiate Param
        DealFilterListTitleVP.Param param = DealFilterListTitleVP.Param.builder().dealCount(1).scriptKillTitle("someString").build();
        DealFilterTitleWithStatisticsOpt.Config config = new DealFilterTitleWithStatisticsOpt.Config();
        config.setTitle("title");
        config.setHideCount(false);
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertEquals("title(1)", result);
    }
}
