package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.model.LeUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.LeUnCoopShopUniverseInfoOpt;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * @author: wuwenqiang
 * @create: 2024-07-23
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class LEUnCoopShopDealListTitleOptTest {

    @Mock
    private ActivityCxt activityCxt;

    private LEUnCoopShopDealListTitleOpt.Config config = new LEUnCoopShopDealListTitleOpt.Config();
    @InjectMocks
    private LEUnCoopShopDealListTitleOpt leUnCoopShopDealListTitleOpt;

    /**
     * 测试使用配置标题
     */
    @Test
    public void testComputeUseConfigTitle() {
        config.setUseConfigTitle(true);
        config.setTitle("Config Title");
        String result = leUnCoopShopDealListTitleOpt.compute(activityCxt, null, config);
        assertEquals("Config Title", result);
    }

    /**
     * 测试返回LeUncoopShopShelfAttrM的mainTitle
     */
    @Test
    public void testComputeReturnMainTitle() {
        LeUncoopShopShelfAttrM leUncoopShopShelfAttrM = new LeUncoopShopShelfAttrM();
        leUncoopShopShelfAttrM.setMainTitle("Main Title");
        when(activityCxt.getParam(LeUnCoopShopUniverseInfoOpt.CODE)).thenReturn(leUncoopShopShelfAttrM);
        String result = leUnCoopShopDealListTitleOpt.compute(activityCxt, null, config);
        assertEquals("Main Title", result);
    }

    /**
     * 测试返回空字符串
     */
    @Test
    public void testComputeReturnEmpty() {
        String result = leUnCoopShopDealListTitleOpt.compute(activityCxt, null, config);
        assertEquals(StringUtils.EMPTY, result);
    }
}
