package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListTitleVP;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ScriptKillTitleOptTest {

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        ScriptKillTitleOpt scriptKillTitleOpt = new ScriptKillTitleOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealFilterListTitleVP.Param param = mock(DealFilterListTitleVP.Param.class);
        String result = scriptKillTitleOpt.compute(context, param, null);
        assertEquals("", result);
    }

    @Test
    public void testComputeDefaultTitleIsEmpty() throws Throwable {
        ScriptKillTitleOpt scriptKillTitleOpt = new ScriptKillTitleOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealFilterListTitleVP.Param param = mock(DealFilterListTitleVP.Param.class);
        ScriptKillTitleOpt.Config config = mock(ScriptKillTitleOpt.Config.class);
        when(config.getDefaultTitle()).thenReturn("");
        String result = scriptKillTitleOpt.compute(context, param, config);
        assertEquals("", result);
    }

    @Test
    public void testComputeScriptTitleIsEmpty() throws Throwable {
        ScriptKillTitleOpt scriptKillTitleOpt = new ScriptKillTitleOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealFilterListTitleVP.Param param = mock(DealFilterListTitleVP.Param.class);
        ScriptKillTitleOpt.Config config = mock(ScriptKillTitleOpt.Config.class);
        when(param.getScriptKillTitle()).thenReturn("");
        when(config.getDefaultTitle()).thenReturn("defaultTitle");
        String result = scriptKillTitleOpt.compute(context, param, config);
        assertEquals("defaultTitle", result);
    }

    @Test
    public void testComputeScriptTitleIsNotEmptyAndPrefixAndSuffixAreEmpty() throws Throwable {
        ScriptKillTitleOpt scriptKillTitleOpt = new ScriptKillTitleOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DealFilterListTitleVP.Param param = mock(DealFilterListTitleVP.Param.class);
        ScriptKillTitleOpt.Config config = mock(ScriptKillTitleOpt.Config.class);
        String result = scriptKillTitleOpt.compute(context, param, config);
        assertEquals("", result);
    }
}
