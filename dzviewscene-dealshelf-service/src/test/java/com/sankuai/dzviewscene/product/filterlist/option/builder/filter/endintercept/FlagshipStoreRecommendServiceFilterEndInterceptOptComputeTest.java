package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FlagshipStoreRecommendServiceFilterEndInterceptOptComputeTest {

    @InjectMocks
    private FlagshipStoreRecommendServiceFilterEndInterceptOpt filterEndInterceptOpt;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private FlagshipStoreRecommendServiceFilterEndInterceptOpt.Param param;

    @Mock
    private FlagshipStoreRecommendServiceFilterEndInterceptOpt.Config config;

    @Test
    public void testComputeWhenFilterListIsNull() throws Throwable {
        when(param.getFilterList()).thenReturn(null);
        Void result = filterEndInterceptOpt.compute(activityCxt, param, config);
        verify(param, atLeastOnce()).getFilterList();
        assertNull(result);
    }

    @Test
    public void testComputeWhenFilterListIsEmpty() throws Throwable {
        List<DzFilterVO> emptyList = new ArrayList<>();
        when(param.getFilterList()).thenReturn(emptyList);
        Void result = filterEndInterceptOpt.compute(activityCxt, param, config);
        verify(param, atLeastOnce()).getFilterList();
        assertNull(result);
    }

    @Test
    public void testComputeWhenFirstFilterVOIsNull() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        filterList.add(null);
        when(param.getFilterList()).thenReturn(filterList);
        Void result = filterEndInterceptOpt.compute(activityCxt, param, config);
        verify(param, atLeastOnce()).getFilterList();
        assertNull(result);
    }

    @Test
    public void testComputeWhenFilterVOChildrenIsNull() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        filterVO.setChildren(null);
        filterList.add(filterVO);
        when(param.getFilterList()).thenReturn(filterList);
        Void result = filterEndInterceptOpt.compute(activityCxt, param, config);
        verify(param, atLeastOnce()).getFilterList();
        assertNull(result);
    }

    @Test
    public void testComputeWhenFilterVOChildrenIsEmpty() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        filterVO.setChildren(new ArrayList<>());
        filterList.add(filterVO);
        when(param.getFilterList()).thenReturn(filterList);
        Void result = filterEndInterceptOpt.compute(activityCxt, param, config);
        verify(param, atLeastOnce()).getFilterList();
        assertNull(result);
    }

    @Test
    public void testComputeWithValidFilterVOAndChildren() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btnVO = new DzFilterBtnVO();
        children.add(btnVO);
        filterVO.setChildren(children);
        filterList.add(filterVO);
        when(param.getFilterList()).thenReturn(filterList);
        Void result = filterEndInterceptOpt.compute(activityCxt, param, config);
        verify(param, atLeastOnce()).getFilterList();
        assertNull(result);
    }
}
