package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept.PromoCodeFilterEndInterceptOpt.Config;
import com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept.PromoCodeFilterEndInterceptOpt.TabConfig;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import java.lang.reflect.Method;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeFilterEndInterceptOptGetEntireStaffTabTest {

    @Mock
    private Config config;

    @Mock
    private TabConfig staffTabCfg;

    /**
     * Tests getEntireStaffTab method when Config object is null.
     */
    @Test
    public void testGetEntireStaffTabWhenConfigIsNull() throws Throwable {
        // arrange
        PromoCodeFilterEndInterceptOpt opt = new PromoCodeFilterEndInterceptOpt();
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("getEntireStaffTab", Config.class);
        method.setAccessible(true);
        DzFilterBtnVO result = (DzFilterBtnVO) method.invoke(opt, (Config) null);
        // assert
        assertNotNull(result);
        assertEquals("Ta的服务", result.getName());
        assertEquals(PromoCodeFilterEndInterceptOpt.ENTIRE_STAFF_TAB_ID, result.getFilterId());
        assertTrue(result.isSelectable());
    }

    /**
     * Tests getEntireStaffTab method when Config object is not null, but shopTabCfg property is null.
     */
    @Test
    public void testGetEntireStaffTabWhenShopTabCfgIsNull() throws Throwable {
        // arrange
        PromoCodeFilterEndInterceptOpt opt = new PromoCodeFilterEndInterceptOpt();
        when(config.getShopTabCfg()).thenReturn(null);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("getEntireStaffTab", Config.class);
        method.setAccessible(true);
        DzFilterBtnVO result = (DzFilterBtnVO) method.invoke(opt, config);
        // assert
        assertNotNull(result);
        assertEquals("Ta的服务", result.getName());
        assertEquals(PromoCodeFilterEndInterceptOpt.ENTIRE_STAFF_TAB_ID, result.getFilterId());
        assertTrue(result.isSelectable());
    }

    /**
     * Tests getEntireStaffTab method when Config object is not null, and shopTabCfg property is not null.
     */
    @Test
    public void testGetEntireStaffTabWhenShopTabCfgIsNotNull() throws Throwable {
        // arrange
        PromoCodeFilterEndInterceptOpt opt = new PromoCodeFilterEndInterceptOpt();
        // Mock shopTabCfg
        when(config.getShopTabCfg()).thenReturn(mock(TabConfig.class));
        when(config.getStaffTabCfg()).thenReturn(staffTabCfg);
        when(staffTabCfg.getFilterTabName()).thenReturn("Custom Staff Tab");
        when(staffTabCfg.isSelectable()).thenReturn(true);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("getEntireStaffTab", Config.class);
        method.setAccessible(true);
        DzFilterBtnVO result = (DzFilterBtnVO) method.invoke(opt, config);
        // assert
        assertNotNull(result);
        assertEquals("Custom Staff Tab", result.getName());
        assertEquals(PromoCodeFilterEndInterceptOpt.ENTIRE_STAFF_TAB_ID, result.getFilterId());
        assertTrue(result.isSelectable());
    }
}
