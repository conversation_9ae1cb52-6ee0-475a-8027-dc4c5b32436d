package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeFilterEndInterceptOptStaffShelfHandlerTest {

    @Mock
    private PromoCodeFilterEndInterceptOpt.Config config;

    @Mock
    private DzFilterVO dzFilterVO;

    @Mock
    private ShelfLoadConfig shelfLoadConfig;

    private PromoCodeFilterEndInterceptOpt promoCodeFilterEndInterceptOpt;

    @Mock
    private ActivityCxt activityCxt;

    @Before
    public void setUp() {
        promoCodeFilterEndInterceptOpt = new PromoCodeFilterEndInterceptOpt();
        Map<String, Object> params = new HashMap<>();
        when(activityCxt.getParameters()).thenReturn(params);
    }

    /**
     * 测试场景代码不匹配的情况
     */
    @Test
    public void testStaffShelfHandlerSceneCodeNotMatch() throws Throwable {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.sceneCode)).thenReturn("other");
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("staffShelfHandler", ActivityCxt.class, PromoCodeFilterEndInterceptOpt.Config.class, DzFilterVO.class);
        method.setAccessible(true);
        method.invoke(promoCodeFilterEndInterceptOpt, activityCxt, config, dzFilterVO);
        // assert
        verify(activityCxt, times(1)).getParam(ShelfActivityConstants.Params.sceneCode);
        verify(activityCxt, never()).getAttachment(anyString());
        verify(dzFilterVO, never()).setChildren(any());
    }

    /**
     * 测试promoCodeShelfConfig为空的情况
     */
    @Test
    public void testStaffShelfHandlerPromoCodeShelfConfigNull() throws Throwable {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.sceneCode)).thenReturn(PromoCodeUtils.PROMO_CODE_STAFF_SCENE);
        when(activityCxt.getAttachment(ShelfActivityConstants.Attachments.promoCodeShelfConfig)).thenReturn(null);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("staffShelfHandler", ActivityCxt.class, PromoCodeFilterEndInterceptOpt.Config.class, DzFilterVO.class);
        method.setAccessible(true);
        method.invoke(promoCodeFilterEndInterceptOpt, activityCxt, config, dzFilterVO);
        // assert
        verify(activityCxt, times(1)).getParam(ShelfActivityConstants.Params.sceneCode);
        verify(activityCxt, times(1)).getAttachment(ShelfActivityConstants.Attachments.promoCodeShelfConfig);
        verify(dzFilterVO, never()).setChildren(any());
    }

    /**
     * 测试ShelfLoadConfig为空的情况
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testStaffShelfHandlerShelfLoadConfigNull() throws Throwable {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.sceneCode)).thenReturn(PromoCodeUtils.PROMO_CODE_STAFF_SCENE);
        CompletableFuture<Object> future = CompletableFuture.completedFuture(null);
        when(activityCxt.getAttachment(anyString())).thenReturn(future);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("staffShelfHandler", ActivityCxt.class, PromoCodeFilterEndInterceptOpt.Config.class, DzFilterVO.class);
        method.setAccessible(true);
        method.invoke(promoCodeFilterEndInterceptOpt, activityCxt, config, dzFilterVO);
        // assert
        verify(activityCxt, times(2)).getAttachment(ShelfActivityConstants.Attachments.promoCodeShelfConfig);
        verify(dzFilterVO, times(1)).setChildren(any());
    }

    /**
     * 测试LoadCustomizeGroupPurchase为true的情况
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testStaffShelfHandlerLoadCustomizeGroupPurchaseTrue() throws Throwable {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.sceneCode)).thenReturn(PromoCodeUtils.PROMO_CODE_STAFF_SCENE);
        when(shelfLoadConfig.getLoadCustomizeGroupPurchase()).thenReturn(true);
        CompletableFuture<Object> future = CompletableFuture.completedFuture(shelfLoadConfig);
        when(activityCxt.getAttachment(anyString())).thenReturn(future);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("staffShelfHandler", ActivityCxt.class, PromoCodeFilterEndInterceptOpt.Config.class, DzFilterVO.class);
        method.setAccessible(true);
        method.invoke(promoCodeFilterEndInterceptOpt, activityCxt, config, dzFilterVO);
        // assert
        verify(activityCxt, times(2)).getAttachment(ShelfActivityConstants.Attachments.promoCodeShelfConfig);
        verify(dzFilterVO, times(1)).setChildren(any());
    }

    /**
     * 测试LoadStaffBindPurchase为true且staffShelfDegrade为false的情况
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testStaffShelfHandlerLoadStaffBindPurchaseTrue() throws Throwable {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.sceneCode)).thenReturn(PromoCodeUtils.PROMO_CODE_STAFF_SCENE);
        when(shelfLoadConfig.getLoadCustomizeGroupPurchase()).thenReturn(false);
        when(shelfLoadConfig.getLoadStaffBindPurchase()).thenReturn(true);
        CompletableFuture<Object> future = CompletableFuture.completedFuture(shelfLoadConfig);
        when(activityCxt.getAttachment(anyString())).thenReturn(future);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("staffShelfHandler", ActivityCxt.class, PromoCodeFilterEndInterceptOpt.Config.class, DzFilterVO.class);
        method.setAccessible(true);
        method.invoke(promoCodeFilterEndInterceptOpt, activityCxt, config, dzFilterVO);
        // assert
        verify(activityCxt, times(2)).getAttachment(ShelfActivityConstants.Attachments.promoCodeShelfConfig);
        verify(dzFilterVO, times(1)).setChildren(any());
        verify(dzFilterVO, times(2)).getChildren();
    }

    /**
     * 测试LoadStaffBindPurchase为false的情况
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testStaffShelfHandlerLoadStaffBindPurchaseFalse() throws Throwable {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.sceneCode)).thenReturn(PromoCodeUtils.PROMO_CODE_STAFF_SCENE);
        when(shelfLoadConfig.getLoadCustomizeGroupPurchase()).thenReturn(false);
        when(shelfLoadConfig.getLoadStaffBindPurchase()).thenReturn(false);
        CompletableFuture<Object> future = CompletableFuture.completedFuture(shelfLoadConfig);
        when(activityCxt.getAttachment(anyString())).thenReturn(future);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("staffShelfHandler", ActivityCxt.class, PromoCodeFilterEndInterceptOpt.Config.class, DzFilterVO.class);
        method.setAccessible(true);
        method.invoke(promoCodeFilterEndInterceptOpt, activityCxt, config, dzFilterVO);
        // assert
        verify(activityCxt, times(2)).getAttachment(ShelfActivityConstants.Attachments.promoCodeShelfConfig);
        verify(dzFilterVO, times(1)).setChildren(any());
        verify(dzFilterVO, never()).getChildren();
    }

    /**
     * 测试category场景的情况
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testStaffShelfHandlerWithCategory() throws Throwable {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.sceneCode)).thenReturn(PromoCodeUtils.PROMO_CODE_STAFF_SCENE);
        when(shelfLoadConfig.getLoadCustomizeGroupPurchase()).thenReturn(false);
        CompletableFuture<Object> future = CompletableFuture.completedFuture(shelfLoadConfig);
        when(activityCxt.getAttachment(anyString())).thenReturn(future);
        Map<String, Object> params = new HashMap<>();
        params.put("category", 1);
        when(activityCxt.getParameters()).thenReturn(params);
        // act
        Method method = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("staffShelfHandler", ActivityCxt.class, PromoCodeFilterEndInterceptOpt.Config.class, DzFilterVO.class);
        method.setAccessible(true);
        method.invoke(promoCodeFilterEndInterceptOpt, activityCxt, config, dzFilterVO);
        // assert
        verify(activityCxt, times(2)).getAttachment(ShelfActivityConstants.Attachments.promoCodeShelfConfig);
        verify(dzFilterVO, times(1)).setChildren(any());
    }
}
