package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * PromoCodeFilterEndInterceptOpt 类的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class PromoCodeFilterEndInterceptOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @InjectMocks
    private PromoCodeFilterEndInterceptOpt promoCodeFilterEndInterceptOpt;

    @Mock
    private PromoCodeFilterEndInterceptOpt.Config config;

    @Mock
    private PromoCodeFilterEndInterceptOpt.TabConfig shopTabConfig;

    private Method getEntireShopTabMethod;

    /**
     * 测试当配置的过滤Tab名称列表为空时
     */
    @Test
    public void testComputeWhenFilterTabNamesIsEmpty() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Collections.emptyList());

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当过滤列表为空时
     */
    @Test
    public void testComputeWhenFilterListIsEmpty() {
        // 准备测试数据
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(Collections.emptyList()).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当过滤列表为null时
     */
    @Test
    public void testComputeWhenFilterListIsNull() {
        // 准备测试数据
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(null).build();


        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当DzFilterVO为null时
     */
    @Test
    public void testComputeWhenDzFilterVOIsNull() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        filterList.add(null);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();


        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当DzFilterVO的children为空时
     */
    @Test
    public void testComputeWhenChildrenIsEmpty() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();
        dzFilterVO.setChildren(Collections.emptyList());
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();


        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试正常情况下过滤Tab
     */
    @Test
    public void testComputeNormalCase() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();

        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btn1 = new DzFilterBtnVO();
        btn1.setName("神券");
        DzFilterBtnVO btn2 = new DzFilterBtnVO();
        btn2.setName("秒杀");
        DzFilterBtnVO btn3 = new DzFilterBtnVO();
        btn3.setName("特团");
        DzFilterBtnVO btn4 = new DzFilterBtnVO();
        btn4.setName("其他");

        children.add(btn1);
        children.add(btn2);
        children.add(btn3);
        children.add(btn4);

        dzFilterVO.setChildren(children);
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
        assertEquals(1, dzFilterVO.getChildren().size());
        assertEquals("其他", dzFilterVO.getChildren().get(0).getName());
    }

    /**
     * 测试嵌套子Tab的过滤
     */
    @Test
    public void testComputeWithNestedChildren() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();

        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btn1 = new DzFilterBtnVO();
        btn1.setName("父Tab");

        List<DzFilterBtnVO> nestedChildren = new ArrayList<>();
        DzFilterBtnVO nestedBtn1 = new DzFilterBtnVO();
        nestedBtn1.setName("神券");
        DzFilterBtnVO nestedBtn2 = new DzFilterBtnVO();
        nestedBtn2.setName("其他子Tab");

        nestedChildren.add(nestedBtn1);
        nestedChildren.add(nestedBtn2);
        btn1.setChildren(nestedChildren);

        children.add(btn1);
        dzFilterVO.setChildren(children);
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
        assertEquals(1, dzFilterVO.getChildren().size());
        assertEquals("父Tab", dzFilterVO.getChildren().get(0).getName());
        assertEquals(1, dzFilterVO.getChildren().get(0).getChildren().size());
        assertEquals("其他子Tab", dzFilterVO.getChildren().get(0).getChildren().get(0).getName());
    }

    /**
     * 测试员工货架tab填充
     */
    @Test
    public void testComputeWhenStaffShelfTabComplete() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();

        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btn1 = new DzFilterBtnVO();
        btn1.setName("父Tab");

        List<DzFilterBtnVO> nestedChildren = new ArrayList<>();
        DzFilterBtnVO nestedBtn1 = new DzFilterBtnVO();
        nestedBtn1.setName("神券");
        DzFilterBtnVO nestedBtn2 = new DzFilterBtnVO();
        nestedBtn2.setName("其他子Tab");

        nestedChildren.add(nestedBtn1);
        nestedChildren.add(nestedBtn2);
        btn1.setChildren(nestedChildren);

        children.add(btn1);
        dzFilterVO.setChildren(children);
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));
        ActivityCxt activityCxt = new ActivityCxt();
        activityCxt.addParam(ShelfActivityConstants.Params.sceneCode, PromoCodeUtils.PROMO_CODE_STAFF_SCENE);
        ShelfLoadConfig shelfLoadConfig = new ShelfLoadConfig();
        shelfLoadConfig.setLoadStaffBindPurchase(true);
        activityCxt.attach(ShelfActivityConstants.Attachments.promoCodeShelfConfig, CompletableFuture.completedFuture(shelfLoadConfig));
        activityCxt.addParam("category", 1);
        try (MockedStatic<PromoCodeUtils> mockedStatic = Mockito.mockStatic(PromoCodeUtils.class)) {
            mockedStatic.when(() -> PromoCodeUtils.judgePromoShopScene(Mockito.any())).thenReturn("default");
            Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);
            assertEquals(2, dzFilterVO.getChildren().size());
            assertEquals("Ta的服务", dzFilterVO.getChildren().get(0).getName());
            assertEquals("全部", dzFilterVO.getChildren().get(1).getName());
        }
    }


    /**
     * 测试当配置为null时
     */
    @Test
    public void testComputeWhenConfigIsNull() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btn = new DzFilterBtnVO();
        btn.setName("测试");
        children.add(btn);
        dzFilterVO.setChildren(children);
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, null);

        // 验证结果
        assertNull(result);
        assertEquals(1, dzFilterVO.getChildren().size()); // 不应该有任何变化
    }

    private DzFilterBtnVO invokeGetEntireShopTab(PromoCodeFilterEndInterceptOpt.Config config) throws Exception {
        if (getEntireShopTabMethod == null) {
            getEntireShopTabMethod = PromoCodeFilterEndInterceptOpt.class.getDeclaredMethod("getEntireShopTab", PromoCodeFilterEndInterceptOpt.Config.class);
            getEntireShopTabMethod.setAccessible(true);
        }
        return (DzFilterBtnVO) getEntireShopTabMethod.invoke(promoCodeFilterEndInterceptOpt, config);
    }

    @Test
    public void testGetEntireShopTab_WhenConfigIsNull() throws Throwable {
        // arrange
        // act
        DzFilterBtnVO result = invokeGetEntireShopTab(null);
        // assert
        assertEquals("全部", result.getName());
        assertEquals(PromoCodeFilterEndInterceptOpt.ENTIRE_SHOP_TAB_ID, result.getFilterId());
        assertTrue(result.isSelectable());
    }

    @Test
    public void testGetEntireShopTab_WhenShopTabCfgIsNull() throws Throwable {
        // arrange
        when(config.getShopTabCfg()).thenReturn(null);
        // act
        DzFilterBtnVO result = invokeGetEntireShopTab(config);
        // assert
        assertEquals("全部", result.getName());
        assertEquals(PromoCodeFilterEndInterceptOpt.ENTIRE_SHOP_TAB_ID, result.getFilterId());
        assertTrue(result.isSelectable());
    }

    @Test
    public void testGetEntireShopTab_WhenConfigValid() throws Throwable {
        // arrange
        String customTabName = "Custom Tab Name";
        when(config.getShopTabCfg()).thenReturn(shopTabConfig);
        when(shopTabConfig.getFilterTabName()).thenReturn(customTabName);
        when(shopTabConfig.isSelectable()).thenReturn(true);
        // act
        DzFilterBtnVO result = invokeGetEntireShopTab(config);
        // assert
        assertEquals(customTabName, result.getName());
        assertEquals(PromoCodeFilterEndInterceptOpt.ENTIRE_SHOP_TAB_ID, result.getFilterId());
        assertTrue(result.isSelectable());
    }

    @Test
    public void testGetEntireShopTab_WhenTabNameEmpty() throws Throwable {
        // arrange
        when(config.getShopTabCfg()).thenReturn(shopTabConfig);
        when(shopTabConfig.getFilterTabName()).thenReturn("");
        when(shopTabConfig.isSelectable()).thenReturn(false);
        // act
        DzFilterBtnVO result = invokeGetEntireShopTab(config);
        // assert
        assertEquals("", result.getName());
        assertEquals(PromoCodeFilterEndInterceptOpt.ENTIRE_SHOP_TAB_ID, result.getFilterId());
        assertFalse(result.isSelectable());
    }
}
