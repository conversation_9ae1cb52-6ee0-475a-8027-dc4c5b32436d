package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class filterIdMapFilterBtnIconOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Test
    public void testComputeFilterListIsNull() throws Throwable {
        // arrange
        filterIdMapFilterBtnIconOpt filterIdMapFilterBtnIconOpt = new filterIdMapFilterBtnIconOpt();
        FilterEndInterceptVP.Param param = mock(FilterEndInterceptVP.Param.class);
        when(param.getFilterList()).thenReturn(null);
        // act
        Void result = filterIdMapFilterBtnIconOpt.compute(activityCxt, param, null);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        // arrange
        filterIdMapFilterBtnIconOpt filterIdMapFilterBtnIconOpt = new filterIdMapFilterBtnIconOpt();
        FilterEndInterceptVP.Param param = mock(FilterEndInterceptVP.Param.class);
        when(param.getFilterList()).thenReturn(Arrays.asList(new DzFilterVO()));
        // act
        Void result = filterIdMapFilterBtnIconOpt.compute(activityCxt, param, null);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeChildrenFilterId2ImageCfgIsNull() throws Throwable {
        // arrange
        filterIdMapFilterBtnIconOpt filterIdMapFilterBtnIconOpt = new filterIdMapFilterBtnIconOpt();
        filterIdMapFilterBtnIconOpt.Config config = mock(filterIdMapFilterBtnIconOpt.Config.class);
        when(config.getChildrenFilterId2ImageCfg()).thenReturn(null);
        FilterEndInterceptVP.Param param = mock(FilterEndInterceptVP.Param.class);
        when(param.getFilterList()).thenReturn(Arrays.asList(new DzFilterVO()));
        // act
        Void result = filterIdMapFilterBtnIconOpt.compute(activityCxt, param, config);
        // assert
        assertNull(result);
    }
    // ... other test cases
}
