package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BarProductActivityTagsOpt_GetPerfectBottomTagWithFilterTest {

    @Mock
    private ProductM productM;

    @Mock
    private FloatTagVO floatTagVO;

    private BarProductActivityTagsOpt barProductActivityTagsOpt;

    @Before
    public void setUp() {
        barProductActivityTagsOpt = new BarProductActivityTagsOpt();
    }

    /**
     * Test case when ProductM object is null.
     */
    @Test
    public void testGetPerfectBottomTagWithFilterProductMIsNull() throws Throwable {
        FloatTagVO result = barProductActivityTagsOpt.getPerfectBottomTagWithFilter(null);
        assertNull("Expected null when ProductM is null", result);
    }

    /**
     * Test case when the FloatTagVO list is empty.
     */
    @Test
    public void testGetPerfectBottomTagWithFilterFloatTagVOListIsEmpty() throws Throwable {
        when(productM.getActivities()).thenReturn(Collections.emptyList());
        FloatTagVO result = barProductActivityTagsOpt.getPerfectBottomTagWithFilter(productM);
        assertNull("Expected null when FloatTagVO list is empty", result);
    }
}
