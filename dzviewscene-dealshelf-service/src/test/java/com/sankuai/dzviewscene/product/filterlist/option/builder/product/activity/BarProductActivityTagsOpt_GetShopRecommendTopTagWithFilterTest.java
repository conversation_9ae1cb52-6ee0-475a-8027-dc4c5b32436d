package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityTagsVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.BarProductActivityTagsOpt.Config;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BarProductActivityTagsOpt_GetShopRecommendTopTagWithFilterTest {

    // Assuming this is the attribute key used in isShopRecommend
    private static final String SHOP_RECOMMEND = "shopRecommend";

    private BarProductActivityTagsOpt opt;

    @InjectMocks
    private BarProductActivityTagsOpt barProductActivityTagsOpt;

    @Mock
    private ProductM productM;

    @Mock
    private Config config;

    @Before
    public void setUp() {
        opt = new BarProductActivityTagsOpt();
    }

    @Test
    public void testGetShopRecommendTopTagWithFilter_ProductIsNotShopRecommend() throws Throwable {
        ProductM productM = mock(ProductM.class);
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, 0);
        assertNull("Expected null FloatTagVO for non-shop recommend product", result);
    }

    @Test(expected = NullPointerException.class)
    public void testGetShopRecommendTopTagWithFilter_ProductIsNull() throws Throwable {
        opt.getShopRecommendTopTagWithFilter(null, 0);
    }

    /**
     * Test getPerfectBottomTagWithFilter when FloatTagVO list has elements
     *
     * Expected: Should return the first FloatTagVO from the list
     */
    @Test
    public void testGetPerfectBottomTagWithFilterWithNonEmptyList() throws Throwable {
        // arrange
        FloatTagVO expectedTag = new FloatTagVO();
        try (MockedStatic<PerfectActivityBuildUtils> perfectActivityBuildUtilsMockedStatic = mockStatic(PerfectActivityBuildUtils.class)) {
            perfectActivityBuildUtilsMockedStatic.when(() -> PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM)).thenReturn(Arrays.asList(expectedTag));
            // act
            FloatTagVO result = barProductActivityTagsOpt.getPerfectBottomTagWithFilter(productM);
            // assert
            assertEquals("Should return the first FloatTagVO from the list", expectedTag, result);
        }
    }

    /**
     * Test getPerfectBottomTagWithFilter when FloatTagVO list has multiple elements
     *
     * Expected: Should return the first FloatTagVO from the list
     */
    @Test
    public void testGetPerfectBottomTagWithFilterWithMultipleElements() throws Throwable {
        // arrange
        FloatTagVO expectedTag = new FloatTagVO();
        FloatTagVO secondTag = new FloatTagVO();
        List<FloatTagVO> tags = Arrays.asList(expectedTag, secondTag);
        try (MockedStatic<PerfectActivityBuildUtils> perfectActivityBuildUtilsMockedStatic = mockStatic(PerfectActivityBuildUtils.class)) {
            perfectActivityBuildUtilsMockedStatic.when(() -> PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM)).thenReturn(tags);
            // act
            FloatTagVO result = barProductActivityTagsOpt.getPerfectBottomTagWithFilter(productM);
            // assert
            assertEquals("Should return the first FloatTagVO from multiple elements", expectedTag, result);
        }
    }

    /**
     * Test getPerfectBottomTagWithFilter when FloatTagVO list is empty
     *
     * Expected: Should return null
     */
    @Test
    public void testGetPerfectBottomTagWithFilterWithEmptyList() throws Throwable {
        // arrange
        try (MockedStatic<PerfectActivityBuildUtils> perfectActivityBuildUtilsMockedStatic = mockStatic(PerfectActivityBuildUtils.class)) {
            perfectActivityBuildUtilsMockedStatic.when(() -> PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM)).thenReturn(Collections.emptyList());
            // act
            FloatTagVO result = barProductActivityTagsOpt.getPerfectBottomTagWithFilter(productM);
            // assert
            assertNull("Should return null when list is empty", result);
        }
    }

    /**
     * Test getPerfectBottomTagWithFilter when FloatTagVO list is null
     *
     * Expected: Should return null
     */
    @Test
    public void testGetPerfectBottomTagWithFilterWithNullList() throws Throwable {
        // arrange
        try (MockedStatic<PerfectActivityBuildUtils> perfectActivityBuildUtilsMockedStatic = mockStatic(PerfectActivityBuildUtils.class)) {
            perfectActivityBuildUtilsMockedStatic.when(() -> PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM)).thenReturn(null);
            // act
            FloatTagVO result = barProductActivityTagsOpt.getPerfectBottomTagWithFilter(productM);
            // assert
            assertNull("Should return null when list is null", result);
        }
    }

    @Test
    public void testGetActivityFloatTag_WhenPlatformPromoTagExists() throws Throwable {
        when(productM.getAttr("DealPricePowerTag")).thenReturn("{\"dealPricePowerTagList\":[{\"tagType\":10, \"picUrl\":\"https://p0.meituan.net/travelcube/856bb1d8dfb114d2caf5d7e2ae6987dd9154.png\"}]}");
        FloatTagVO result = barProductActivityTagsOpt.getActivityFloatTag(productM, 0L, config);
        assertNotNull(result);
        assertEquals("https://p0.meituan.net/travelcube/856bb1d8dfb114d2caf5d7e2ae6987dd9154.png", result.getIcon().getPicUrl());
    }

    @Test
    public void testGetActivityFloatTag_WhenNoTagsExist() throws Throwable {
        when(productM.getAttr("DealPricePowerTag")).thenReturn(null);
        when(barProductActivityTagsOpt.getPerfectBottomTagWithFilter(productM)).thenReturn(null);
        FloatTagVO result = barProductActivityTagsOpt.getActivityFloatTag(productM, 0L, config);
        assertNull(result);
    }
}
