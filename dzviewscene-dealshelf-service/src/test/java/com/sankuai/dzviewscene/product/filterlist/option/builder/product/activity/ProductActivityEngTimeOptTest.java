package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.dto.enums.DealActivityTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityEndTimeVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.ProductActivityEngTimeOpt.Config;
import com.sankuai.dzviewscene.product.shelf.utils.JuHuaSuanUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.CountdownLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductActivityEngTimeOptTest {

    @Mock
    private ProductM productM;

    @Mock
    private ProductActivityEngTimeOpt.Config config;

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductActivityEngTimeOpt.Param param;

    @InjectMocks
    private ProductActivityEngTimeOpt opt;

    private void setCheckOnlyRainbowSecKill(boolean value) throws Exception {
        Field field = config.getClass().getDeclaredField("checkOnlyRainbowSecKill");
        field.setAccessible(true);
        field.set(config, value);
    }

    @Test
    public void testComputeAllDisabled() throws Throwable {
        // Given
        when(config.isEnableExposureSecKill()).thenReturn(false);
        when(config.isEnableJuHuaSuan()).thenReturn(false);
        when(config.isEnableRainbowSecKill()).thenReturn(false);
        // When
        CountdownLabelVO result = opt.compute(context, param, config);
        // Then
        assertNull(result);
    }

    @Test
    public void testComputeWithRainbowSecKillAndCheckOnly() throws Throwable {
        ProductActivityEngTimeOpt opt = new ProductActivityEngTimeOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        ProductActivityM activityM = new ProductActivityM();
        // COST_EFFECTIVE_SECKILL
        activityM.setShelfActivityType(8);
        activityM.setRemainingTime(100L);
        productM.setActivities(Collections.singletonList(activityM));
        // Set up promo prices for hasAnyDirectPromo check
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        PromoItemM promoItem = new PromoItemM();
        // NORMAL_PROMO type
        promoItem.setPromoTypeCode(1);
        promoPriceM.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        ProductActivityEngTimeOpt.Param param = ProductActivityEngTimeOpt.Param.builder().productM(productM).build();
        ProductActivityEngTimeOpt.Config config = new ProductActivityEngTimeOpt.Config();
        config.setEnableRainbowSecKill(true);
        config.setCheckOnlyRainbowSecKill(true);
        CountdownLabelVO result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("仅剩", result.getTimePrefix());
        assertEquals(100000L, result.getTimestamp());
    }

    @Test
    public void testComputeWithExposureSecKill() throws Throwable {
        ProductActivityEngTimeOpt opt = new ProductActivityEngTimeOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setAttr("attr_is_price_activity_seckill_deal", "true");
        // Set up promo prices for hasAnyShopDirectPromo check
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        PromoItemM promoItem = new PromoItemM();
        // NORMAL_PROMO type
        promoItem.setPromoTypeCode(1);
        // SHOP source type
        promoItem.setSourceType(2);
        // Set end time 1 hour from now
        promoItem.setEndTime(System.currentTimeMillis() + 3600000);
        promoPriceM.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        ProductActivityEngTimeOpt.Param param = ProductActivityEngTimeOpt.Param.builder().productM(productM).build();
        ProductActivityEngTimeOpt.Config config = new ProductActivityEngTimeOpt.Config();
        config.setEnableExposureSecKill(true);
        config.setEnablePeriodReduction(false);
        CountdownLabelVO result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("仅剩", result.getTimePrefix());
    }

    @Test
    public void testComputeWithRainbowSecKillWithoutCheckOnly() throws Throwable {
        ProductActivityEngTimeOpt opt = new ProductActivityEngTimeOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        ProductActivityM activityM = new ProductActivityM();
        // SEC_KILL type
        activityM.setShelfActivityType(2);
        activityM.setActivityExtraAttrs(new HashMap<String, Object>() {

            {
                put("RainbowSecKillSource", true);
            }
        });
        activityM.setRemainingTime(100L);
        productM.setActivities(Collections.singletonList(activityM));
        // Set up promo prices for hasAnyDirectPromo check
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        PromoItemM promoItem = new PromoItemM();
        // NORMAL_PROMO type
        promoItem.setPromoTypeCode(1);
        promoPriceM.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        ProductActivityEngTimeOpt.Param param = ProductActivityEngTimeOpt.Param.builder().productM(productM).build();
        ProductActivityEngTimeOpt.Config config = new ProductActivityEngTimeOpt.Config();
        config.setEnableRainbowSecKill(true);
        config.setCheckOnlyRainbowSecKill(false);
        CountdownLabelVO result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("仅剩", result.getTimePrefix());
        assertEquals(100000L, result.getTimestamp());
    }
}
