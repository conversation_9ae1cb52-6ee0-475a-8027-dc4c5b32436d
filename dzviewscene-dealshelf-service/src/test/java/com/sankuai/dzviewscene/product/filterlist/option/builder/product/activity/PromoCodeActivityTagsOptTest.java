package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityTagsVP.Param;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class PromoCodeActivityTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    private PromoCodeActivityTagsOpt promoCodeActivityTagsOpt;
    private PromoCodeActivityTagsOpt.Config config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        promoCodeActivityTagsOpt = new PromoCodeActivityTagsOpt();
        config = new PromoCodeActivityTagsOpt.Config();
    }

    /**
     * 测试compute方法，当productM为null时
     */
    @Test
    public void testComputeProductMIsNull() {
        when(param.getProductM()).thenReturn(null);

        List<DzActivityTagVO> result = promoCodeActivityTagsOpt.compute(activityCxt, param, config);

        assertNull("当productM为null时，应返回null", result);
    }

    /**
     * 测试compute方法，当productTagId2TagCfg为空时
     */
    @Test
    public void testComputeProductTagId2TagCfgIsEmpty() {
        when(param.getProductM()).thenReturn(new ProductM());
        config.setProductTagId2TagCfg(new HashMap<>());

        List<DzActivityTagVO> result = promoCodeActivityTagsOpt.compute(activityCxt, param, config);

        assertNull("当productTagId2TagCfg为空时，应返回null", result);
    }

    /**
     * 测试compute方法，当productTagList为空时
     */
    @Test
    public void testComputeProductTagListIsEmpty() {
        ProductM productM = new ProductM();
        productM.setProductTagList(Arrays.asList());
        when(param.getProductM()).thenReturn(productM);
        Map<String, PromoCodeActivityTagsOpt.TagCfg> productTagId2TagCfg = new HashMap<>();
        productTagId2TagCfg.put("1", new PromoCodeActivityTagsOpt.TagCfg());
        config.setProductTagId2TagCfg(productTagId2TagCfg);

        List<DzActivityTagVO> result = promoCodeActivityTagsOpt.compute(activityCxt, param, config);

        assertNull("当productTagList为空时，应返回null", result);
    }

    /**
     * 测试compute方法，当存在匹配的标签配置时
     */
    @Test
    public void testComputeWithMatchedTagCfg() {
        ProductM productM = new ProductM();
        TagM tagM = new TagM(1, "1", "TestTag");
        productM.setProductTagList(Arrays.asList(tagM));
        when(param.getProductM()).thenReturn(productM);

        Map<String, PromoCodeActivityTagsOpt.TagCfg> productTagId2TagCfg = new HashMap<>();
        PromoCodeActivityTagsOpt.TagCfg tagCfg = new PromoCodeActivityTagsOpt.TagCfg();
        tagCfg.setPicUrl("http://example.com/pic");
        tagCfg.setLabel("TestLabel");
        tagCfg.setBackgroundImg("http://example.com/bg");
        tagCfg.setPosition(2);
        productTagId2TagCfg.put("1", tagCfg);
        config.setProductTagId2TagCfg(productTagId2TagCfg);

        List<DzActivityTagVO> result = promoCodeActivityTagsOpt.compute(activityCxt, param, config);

        assertNotNull("当存在匹配的标签配置时，不应返回null", result);
        assertEquals("结果列表应包含一个元素", 1, result.size());
        DzActivityTagVO tagVO = result.get(0);
        assertEquals("标签图片URL应匹配", "http://example.com/pic", tagVO.getImgUrl());
        assertEquals("标签文本应匹配", "TestLabel", tagVO.getLabel());
        assertEquals("背景图片URL应匹配", "http://example.com/bg", tagVO.getBackgroundImg());
        assertEquals("标签位置应匹配", 2, tagVO.getPosition());
    }
}