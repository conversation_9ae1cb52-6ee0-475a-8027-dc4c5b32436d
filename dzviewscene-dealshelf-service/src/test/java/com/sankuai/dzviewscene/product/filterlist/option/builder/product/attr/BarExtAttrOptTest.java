package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BarExtAttrOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private BarExtAttrOpt.Param param;

    @Mock
    private ProductM productM;

    /**
     * 测试 barProductStandardAttrValue 为空的情况
     */
    @Test
    public void testComputeBarProductStandardAttrValueIsNull() throws Throwable {
        // arrange
        BarExtAttrOpt barExtAttrOpt = new BarExtAttrOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("barProductStandardAttr")).thenReturn(null);
        // act
        String result = barExtAttrOpt.compute(context, param, null);
        // assert
        assertEquals("{\"deal_type\":0}", result);
    }

    /**
     * 测试 barProductStandardAttrValue 不为空，但不等于 Boolean.TRUE.toString() 的情况
     */
    @Test
    public void testComputeBarProductStandardAttrValueIsNotTrue() throws Throwable {
        // arrange
        BarExtAttrOpt barExtAttrOpt = new BarExtAttrOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("barProductStandardAttr")).thenReturn("false");
        // act
        String result = barExtAttrOpt.compute(context, param, null);
        // assert
        assertEquals("{\"deal_type\":0}", result);
    }

    /**
     * 测试 barProductStandardAttrValue 不为空，且等于 Boolean.TRUE.toString() 的情况
     */
    @Test
    public void testComputeBarProductStandardAttrValueIsTrue() throws Throwable {
        // arrange
        BarExtAttrOpt barExtAttrOpt = new BarExtAttrOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("barProductStandardAttr")).thenReturn("true");
        // act
        String result = barExtAttrOpt.compute(context, param, null);
        // assert
        assertEquals("{\"deal_type\":1}", result);
    }
}
