package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.core.config.Config;
import com.dianping.lion.client.api.annotation.Param;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class ConfigExtAttrOptTest {

    @InjectMocks
    private ConfigExtAttrOpt configExtAttrOpt;

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductExtAttrVP.Param param;

    @Mock
    private ConfigExtAttrOpt.Config config;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(param.getProductM()).thenReturn(productM);
    }

    @Test
    public void testCompute_BothMapsNotEmpty_ProductMGetAttrReturnsNull() throws Throwable {
        Map<String, String> fixAttrMap = new HashMap<>();
        fixAttrMap.put("key1", "value1");
        when(config.getFixAttrAndValueMap()).thenReturn(fixAttrMap);
        List<String> dealAttrKeys = Lists.newArrayList("attr1");
        when(config.getDealAttrKeys()).thenReturn(dealAttrKeys);
        when(productM.getAttr("attr1")).thenReturn(null);
        String result = configExtAttrOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("{\"key1\":\"value1\"}", result);
    }

    @Test
    public void testCompute_BothMapsNotEmpty_ProductMGetAttrReturnsEmptyString() throws Throwable {
        Map<String, String> fixAttrMap = new HashMap<>();
        fixAttrMap.put("key1", "value1");
        when(config.getFixAttrAndValueMap()).thenReturn(fixAttrMap);
        List<String> dealAttrKeys = Lists.newArrayList("attr1");
        when(config.getDealAttrKeys()).thenReturn(dealAttrKeys);
        when(productM.getAttr("attr1")).thenReturn("");
        String result = configExtAttrOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("{\"key1\":\"value1\"}", result);
    }

    @Test
    public void testCompute_BothMapsEmpty() throws Throwable {
        when(config.getFixAttrAndValueMap()).thenReturn(new HashMap<>());
        when(config.getDealAttrKeys()).thenReturn(Lists.newArrayList());
        String result = configExtAttrOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testCompute_FixAttrMapNotEmpty_DealAttrKeysEmpty() throws Throwable {
        Map<String, String> fixAttrMap = new HashMap<>();
        fixAttrMap.put("key1", "value1");
        when(config.getFixAttrAndValueMap()).thenReturn(fixAttrMap);
        when(config.getDealAttrKeys()).thenReturn(Lists.newArrayList());
        String result = configExtAttrOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("{\"key1\":\"value1\"}", result);
    }

    @Test
    public void testCompute_FixAttrMapEmpty_DealAttrKeysNotEmpty() throws Throwable {
        when(config.getFixAttrAndValueMap()).thenReturn(new HashMap<>());
        List<String> dealAttrKeys = Lists.newArrayList("attr1");
        when(config.getDealAttrKeys()).thenReturn(dealAttrKeys);
        when(productM.getAttr("attr1")).thenReturn("value1");
        String result = configExtAttrOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("{\"attr1\":\"value1\"}", result);
    }

    @Test
    public void testCompute_BothMapsNotEmpty() throws Throwable {
        Map<String, String> fixAttrMap = new HashMap<>();
        fixAttrMap.put("key1", "value1");
        when(config.getFixAttrAndValueMap()).thenReturn(fixAttrMap);
        List<String> dealAttrKeys = Lists.newArrayList("attr1");
        when(config.getDealAttrKeys()).thenReturn(dealAttrKeys);
        when(productM.getAttr("attr1")).thenReturn("value2");
        String result = configExtAttrOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("{\"key1\":\"value1\",\"attr1\":\"value2\"}", result);
    }
}
