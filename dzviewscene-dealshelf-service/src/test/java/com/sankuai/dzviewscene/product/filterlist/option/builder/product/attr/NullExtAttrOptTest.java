package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import java.lang.reflect.Constructor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NullExtAttrOptTest {

    /**
     * 测试 compute 方法，期望返回 null
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        NullExtAttrOpt nullExtAttrOpt = new NullExtAttrOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        // Use reflection to create an instance of ProductExtAttrVP.Param
        Class<?> paramClass = Class.forName("com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP$Param");
        Constructor<?> constructor = paramClass.getDeclaredConstructor(ProductM.class);
        // Bypasses access checking
        constructor.setAccessible(true);
        ProductExtAttrVP.Param param = (ProductExtAttrVP.Param) constructor.newInstance(productM);
        // act
        String result = nullExtAttrOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }
}
