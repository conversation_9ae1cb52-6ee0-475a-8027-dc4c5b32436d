package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP.Param;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr.PressOnNailExtAttrOpt;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailExtAttrOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private ProductM mockProductM;

    private MockedStatic<PressOnNailUtils> pressOnNailUtilsMockedStatic;

    private PressOnNailExtAttrOpt pressOnNailExtAttrOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailExtAttrOpt = new PressOnNailExtAttrOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
        pressOnNailUtilsMockedStatic = Mockito.mockStatic(PressOnNailUtils.class);
    }

    @After
    public void tearDown(){
        pressOnNailUtilsMockedStatic.close();
    }

    /**
     * 测试compute方法，正常情况
     */
    @Test
    public void testComputeNormalCase() {
        // arrange
        pressOnNailUtilsMockedStatic.when(()->PressOnNailUtils.buildItemOceanMap(any(), any())).thenReturn(new HashMap<>());

        // act
        String result = pressOnNailExtAttrOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertNotNull("结果不应为null", result);
        assertTrue("结果应包含customize_array关键字", result.contains("customize_array"));
    }

    /**
     * 测试compute方法，当buildItemOceanMap返回null时
     */
    @Test
    public void testComputeWhenBuildItemOceanMapReturnsNull() {
        // arrange
        pressOnNailUtilsMockedStatic.when(()->PressOnNailUtils.buildItemOceanMap(any(), any())).thenReturn(null);

        // act
        String result = pressOnNailExtAttrOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertNotNull("结果不应为null", result);
        assertTrue("结果应包含customize_array关键字", result.contains("customize_array"));
        assertTrue("当buildItemOceanMap返回null时，customize_array应为空数组", result.contains("customize_array\":[null]"));
    }
}
