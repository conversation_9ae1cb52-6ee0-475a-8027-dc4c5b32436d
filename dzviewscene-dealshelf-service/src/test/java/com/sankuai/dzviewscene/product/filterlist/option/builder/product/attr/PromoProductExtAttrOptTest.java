package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class PromoProductExtAttrOptTest {
    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private ProductM mockProductM;

    private PromoProductExtAttrOpt promoProductExtAttrOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        promoProductExtAttrOpt = new PromoProductExtAttrOpt();
    }

    /**
     * 测试商品为null时返回null
     */
    @Test
    public void testComputeProductIsNull() {

        ProductExtAttrVP.Param mockParam = ProductExtAttrVP.Param.builder()
                .productM(null)
                .build();
        String result = promoProductExtAttrOpt.compute(mockActivityCxt, mockParam, null);

        assertNull(result);
    }

    /**
     * 测试商品类型为1时返回团单商品类型信息
     */
    @Test
    public void testComputeDealProductType() {
        ProductM mockProductM = new ProductM();
        mockProductM.setProductType(1);
        mockProductM.setBasePriceTag("100");
        ProductExtAttrVP.Param mockParam = ProductExtAttrVP.Param.builder()
                .productM(mockProductM)
                .build();

        String expected = "{\"productType\":1,\"basePrice\":\"100\",\"tradeType\":\"null\"}";
        String result = promoProductExtAttrOpt.compute(mockActivityCxt, mockParam, null);

        assertEquals(expected, result);
    }

    /**
     * 测试商品类型为5时返回次卡商品类型信息
     */
    @Test
    public void testComputeTimeCardProductType() {
        ProductM mockProductM = new ProductM();
        mockProductM.setProductType(5);
        mockProductM.setBasePriceTag("200");
        ProductExtAttrVP.Param mockParam = ProductExtAttrVP.Param.builder()
                .productM(mockProductM)
                .build();

        String expected = "{\"productType\":5,\"basePrice\":\"200\"}";
        String result = promoProductExtAttrOpt.compute(mockActivityCxt, mockParam, null);

        assertEquals(expected, result);
    }

    /**
     * 测试商品类型未知时返回团单商品类型信息
     * 为了防止影响其他商品货架，不能修改商品类型，所以这个未知商品就是返回团单
     */
    @Test
    public void testComputeUnknownProductType() {
        ProductM mockProductM = new ProductM();
        mockProductM.setProductType(999);
        mockProductM.setBasePriceTag("300");
        ProductExtAttrVP.Param mockParam = ProductExtAttrVP.Param.builder()
                .productM(mockProductM)
                .build();

        String expected = "{\"productType\":1,\"basePrice\":\"300\",\"tradeType\":\"null\"}";
        String result = promoProductExtAttrOpt.compute(mockActivityCxt, mockParam, null);

        assertEquals(expected, result);
    }
}