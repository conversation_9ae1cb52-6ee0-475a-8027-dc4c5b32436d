package com.sankuai.dzviewscene.product.filterlist.option.builder.product.bottomtag;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembottomtags.ItemBottomTagsWithPromoOpt;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealListBottomTagsWithPriorityPromoOptTest {

    @InjectMocks
    private DealListBottomTagsWithPriorityPromoOpt dealListBottomTagsWithPriorityPromoOpt;

    @Mock
    private ItemBottomTagsWithPromoOpt itemBottomTagsWithPromoOpt;

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductBottomTagsVP.Param param;

    @Mock
    private ProductM productM;

    @Test
    public void testComputeWithDiscount() throws Throwable {
        // Setup
        RichLabelVO expectedLabel = new RichLabelVO();
        when(param.getProductM()).thenReturn(productM);
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(productM, 1)).thenReturn(expectedLabel);
        // Act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // Assert
        assertEquals(Collections.singletonList(expectedLabel), result);
    }

    @Test
    public void testComputeWithNoDiscountAndNoPriceLabels() throws Throwable {
        // Setup
        when(param.getProductM()).thenReturn(productM);
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(productM, 1)).thenReturn(null);
        // Assuming the internal logic of compute method does not find any labels to add
        // This is a limitation since we cannot mock protected methods directly
        // and are focusing on the behavior of the compute method itself.
        // Act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // Assert
        assertEquals(null, result);
    }

    /**
     * Test when timeCard exists and has valid price difference
     */
    @Test
    public void testComputeWithTimeCard() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.platform, 1);
        ProductM productM = new ProductM();
        ProductPriceM cardPrice = new ProductPriceM();
        cardPrice.setPriceTag("￥100/次");
        cardPrice.setPriceDesc("单次价");
        productM.setCardPrice(cardPrice);
        DealListBottomTagsWithPriorityPromoOpt.Param param = DealListBottomTagsWithPriorityPromoOpt.Param.builder().productM(productM).salePrice("120").build();
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(any(), anyInt())).thenReturn(null);
        // act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * Test when pinTuan exists and has valid price difference
     */
    @Test
    public void testComputeWithPinTuan() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.platform, 1);
        ProductM productM = new ProductM();
        ProductPriceM pinPrice = new ProductPriceM();
        pinPrice.setPriceTag("￥80/人");
        pinPrice.setPriceDesc("拼团价");
        productM.setPinPrice(pinPrice);
        DealListBottomTagsWithPriorityPromoOpt.Param param = DealListBottomTagsWithPriorityPromoOpt.Param.builder().productM(productM).salePrice("100").build();
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(any(), anyInt())).thenReturn(null);
        // act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    // Additional tests can be added here following a similar pattern.
    // Each test should focus on a specific behavior of the compute method
    // and use mocking to simulate the necessary conditions for that behavior.
    /**
     * Test when no valid price options exist
     */
    @Test
    public void testComputeWithNoValidOptions() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.platform, 1);
        ProductM productM = new ProductM();
        DealListBottomTagsWithPriorityPromoOpt.Param param = DealListBottomTagsWithPriorityPromoOpt.Param.builder().productM(productM).salePrice("100").build();
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(any(), anyInt())).thenReturn(null);
        // act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }

    /**
     * Test when platform parameter is not set
     */
    @Test
    public void testComputeWithNoPlatform() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        DealListBottomTagsWithPriorityPromoOpt.Param param = DealListBottomTagsWithPriorityPromoOpt.Param.builder().productM(productM).salePrice("100").build();
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(any(), anyInt())).thenReturn(null);
        // act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }

    /**
     * Test when timeCard price is invalid (lower than sale price)
     */
    @Test
    public void testComputeWithInvalidTimeCardPrice() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.platform, 1);
        ProductM productM = new ProductM();
        ProductPriceM cardPrice = new ProductPriceM();
        cardPrice.setPriceTag("￥120/次");
        cardPrice.setPriceDesc("单次价");
        productM.setCardPrice(cardPrice);
        DealListBottomTagsWithPriorityPromoOpt.Param param = DealListBottomTagsWithPriorityPromoOpt.Param.builder().productM(productM).salePrice("100").build();
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(any(), anyInt())).thenReturn(null);
        // act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }

    /**
     * Test when pinTuan price is invalid (lower than sale price)
     */
    @Test
    public void testComputeWithInvalidPinTuanPrice() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.platform, 1);
        ProductM productM = new ProductM();
        ProductPriceM pinPrice = new ProductPriceM();
        pinPrice.setPriceTag("￥120/人");
        pinPrice.setPriceDesc("拼团价");
        productM.setPinPrice(pinPrice);
        DealListBottomTagsWithPriorityPromoOpt.Param param = DealListBottomTagsWithPriorityPromoOpt.Param.builder().productM(productM).salePrice("100").build();
        when(itemBottomTagsWithPromoOpt.buildDiscountByNum(any(), anyInt())).thenReturn(null);
        // act
        List<RichLabelVO> result = dealListBottomTagsWithPriorityPromoOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }
}
