package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import io.swagger.util.Json;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import static org.junit.Assert.*;
import com.dianping.lion.common.util.JsonUtils;

/**
 * <AUTHOR>
 * @since 2024/4/28 10:08
 */
public class ConfigureButtonOptTest {
    private ConfigureButtonOpt configureButtonOpt;
    private ConfigureButtonOpt.Config config;
    private ProductM productM;
    private MockedStatic<StringUtils> mockedStringUtils;
    private MockedStatic<JsonUtils> mockedJsonUtils;
    private ActivityCxt mockContext;

    @Before
    public void setUp() {
        configureButtonOpt = new ConfigureButtonOpt();
        config = new ConfigureButtonOpt.Config();
        productM = Mockito.mock(ProductM.class);
        mockedStringUtils = Mockito.mockStatic(StringUtils.class);
        mockedJsonUtils = Mockito.mockStatic(JsonUtils.class);
        mockContext = Mockito.mock(ActivityCxt.class);
    }

    @After
    public void tearDown() {
        mockedStringUtils.close();
        mockedJsonUtils.close();
    }

    @Test
    public void testGetJumpUrl_UseJumpUrl() {
        config.setUseJumpUrl(true);
        Mockito.when(productM.getJumpUrl()).thenReturn("团详页链接");

        String result = configureButtonOpt.getJumpUrl(productM, config);

        assertNotNull(result);
        assertEquals("团详页链接", result);
    }

    @Test
    public void testGetJumpUrl_UseOrderUrl() {
        config.setUseJumpUrl(false);
        Mockito.when(productM.getOrderUrl()).thenReturn("提单页链接");
        Mockito.when(productM.getJumpUrl()).thenReturn("团详页链接");
        mockedStringUtils.when(() -> StringUtils.isNotEmpty(Mockito.anyString())).thenReturn(true);

        String result = configureButtonOpt.getJumpUrl(productM, config);

        assertNotNull(result);
        assertEquals("提单页链接", result);
    }

    @Test
    public void testGetJumpUrl_DefaultToJumpUrl() {
        config.setUseJumpUrl(false);
        Mockito.when(productM.getJumpUrl()).thenReturn("团详页链接");
        mockedStringUtils.when(() -> StringUtils.isNotEmpty(Mockito.anyString())).thenReturn(false);

        String result = configureButtonOpt.getJumpUrl(productM, config);

        assertNotNull(result);
        assertEquals("团详页链接", result);
    }

    @Test
    public void testLog() {
        config.setUseJumpUrl(true);
        Mockito.when(productM.getJumpUrl()).thenReturn("团详页链接");
        ShopM shopM = new ShopM();
        shopM.setLongShopId(123);
        Mockito.when(mockContext.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);
        Mockito.when(mockContext.getSceneCode()).thenReturn("edu_crosscat_deal_list");
        mockedJsonUtils.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("edu");

        String result = configureButtonOpt.getJumpUrl(productM, config);

        assertNotNull(result);
        assertEquals("团详页链接", result);
    }
}