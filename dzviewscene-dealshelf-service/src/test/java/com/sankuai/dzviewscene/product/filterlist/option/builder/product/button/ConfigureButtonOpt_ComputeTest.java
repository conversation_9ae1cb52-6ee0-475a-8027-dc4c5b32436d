package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ConfigureButtonOpt_ComputeTest {

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        // Assuming getJumpUrl() depends on some conditions related to productM, we mock those conditions here
        when(productM.getOrderUrl()).thenReturn("someUrl");
    }

    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        ConfigureButtonOpt configureButtonOpt = new ConfigureButtonOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductButtonVP.Param param = mock(ProductButtonVP.Param.class);
        ConfigureButtonOpt.Config config = mock(ConfigureButtonOpt.Config.class);
        when(param.getProductM()).thenReturn(productM);
        // Corrected to match the mocked behavior
        when(config.getName()).thenReturn("name");
        when(config.getType()).thenReturn(1);
        when(config.isShow()).thenReturn(true);
        // act
        DzSimpleButtonVO result = configureButtonOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        // Corrected to match the mocked behavior
        assertEquals("someUrl", result.getJumpUrl());
        assertEquals("name", result.getName());
        assertEquals(1, result.getType());
        assertTrue(result.isShow());
    }

    @Test(expected = NullPointerException.class)
    public void testComputeParamNull() throws Throwable {
        // arrange
        ConfigureButtonOpt configureButtonOpt = new ConfigureButtonOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ConfigureButtonOpt.Config config = mock(ConfigureButtonOpt.Config.class);
        // act
        configureButtonOpt.compute(context, null, config);
    }

    @Test(expected = NullPointerException.class)
    public void testComputeConfigNull() throws Throwable {
        // arrange
        ConfigureButtonOpt configureButtonOpt = new ConfigureButtonOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductButtonVP.Param param = mock(ProductButtonVP.Param.class);
        // act
        configureButtonOpt.compute(context, param, null);
    }
}
