package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class ConfigureButtonOpt_GetJumpUrlTest {

    private ConfigureButtonOpt configureButtonOpt;

    private ProductM productM;

    private ConfigureButtonOpt.Config config;

    @Before
    public void setUp() {
        configureButtonOpt = new ConfigureButtonOpt();
        productM = Mockito.mock(ProductM.class);
        config = Mockito.mock(ConfigureButtonOpt.Config.class);
    }

    /**
     * 测试config.useJumpUrl为true，productM.jumpUrl不为空的情况
     */
    @Test
    public void testGetJumpUrl_UseJumpUrlAndJumpUrlNotEmpty() {
        Mockito.when(config.isUseJumpUrl()).thenReturn(true);
        Mockito.when(productM.getJumpUrl()).thenReturn("团详页链接");
        String result = configureButtonOpt.getJumpUrl(productM, config);
        assertNotNull(result);
        assertEquals("团详页链接", result);
    }

    /**
     * 测试config.useJumpUrl为false，productM.orderUrl不为空的情况
     */
    @Test
    public void testGetJumpUrl_NotUseJumpUrlAndOrderUrlNotEmpty() {
        Mockito.when(config.isUseJumpUrl()).thenReturn(false);
        Mockito.when(productM.getOrderUrl()).thenReturn("提单页链接");
        String result = configureButtonOpt.getJumpUrl(productM, config);
        assertNotNull(result);
        assertEquals("提单页链接", result);
    }

    /**
     * 测试config.useJumpUrl为false，productM.orderUrl为空，productM.jumpUrl不为空的情况
     */
    @Test
    public void testGetJumpUrl_NotUseJumpUrlAndOrderUrlEmptyAndJumpUrlNotEmpty() {
        Mockito.when(config.isUseJumpUrl()).thenReturn(false);
        Mockito.when(productM.getOrderUrl()).thenReturn("");
        Mockito.when(productM.getJumpUrl()).thenReturn("团详页链接");
        String result = configureButtonOpt.getJumpUrl(productM, config);
        assertNotNull(result);
        assertEquals("团详页链接", result);
    }

    /**
     * 测试config.useJumpUrl为true，productM.jumpUrl为空的情况
     */
    @Test
    public void testGetJumpUrl_UseJumpUrlAndJumpUrlEmpty() {
        Mockito.when(config.isUseJumpUrl()).thenReturn(true);
        Mockito.when(productM.getJumpUrl()).thenReturn("");
        String result = configureButtonOpt.getJumpUrl(productM, config);
        assertNotNull(result);
        assertEquals("", result);
    }

    /**
     * 测试config.useJumpUrl为false，productM.orderUrl为空，productM.jumpUrl为空的情况
     */
    @Test
    public void testGetJumpUrl_NotUseJumpUrlAndOrderUrlEmptyAndJumpUrlEmpty() {
        Mockito.when(config.isUseJumpUrl()).thenReturn(false);
        Mockito.when(productM.getOrderUrl()).thenReturn("");
        Mockito.when(productM.getJumpUrl()).thenReturn("");
        String result = configureButtonOpt.getJumpUrl(productM, config);
        assertNotNull(result);
        assertEquals("", result);
    }
}
