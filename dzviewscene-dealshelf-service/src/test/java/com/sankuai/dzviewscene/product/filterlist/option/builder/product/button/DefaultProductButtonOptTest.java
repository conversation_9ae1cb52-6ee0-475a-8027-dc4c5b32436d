package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP.Param;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import java.lang.reflect.Constructor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductButtonOptTest {

    /**
     * Tests whether the compute method returns null.
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        DefaultProductButtonOpt defaultProductButtonOpt = new DefaultProductButtonOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        // Use reflection to create Param instance
        Constructor<Param> constructor = Param.class.getDeclaredConstructor(ProductM.class);
        constructor.setAccessible(true);
        Param param = constructor.newInstance(productM);
        Void unused = null;
        // act
        DzSimpleButtonVO result = defaultProductButtonOpt.compute(context, param, unused);
        // assert
        assertNull(result);
    }
}
