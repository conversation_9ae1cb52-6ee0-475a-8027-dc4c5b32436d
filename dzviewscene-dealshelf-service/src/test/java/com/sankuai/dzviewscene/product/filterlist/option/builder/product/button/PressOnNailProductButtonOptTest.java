package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP.Param;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.button.PressOnNailProductButtonOpt.Config;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.ButtonTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * PressOnNailProductButtonOpt测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class PressOnNailProductButtonOptTest {

    @Mock
    private ActivityCxt mockContext;

    @Mock
    private Param mockParam;

    @Mock
    private Config mockConfig;

    @Mock
    private ProductM mockProductM;

    private PressOnNailProductButtonOpt pressOnNailProductButtonOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailProductButtonOpt = new PressOnNailProductButtonOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    private PressOnNailProductButtonOpt.Param createParam() throws Exception {
        Constructor<PressOnNailProductButtonOpt.Param> constructor = PressOnNailProductButtonOpt.Param.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    /**
     * 测试compute方法，当用户为MT_APP且产品具有独家标识时
     */
    @Test
    public void testCompute_MtAppWithExclusive() {
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_APP.getCode());
        //        when(mockContext.getParam(ShelfActivityConstants.Params.mtPoiIdL)).thenReturn(12345L);
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList("sku123"));
        //        when(mockProductM.getProductId()).thenReturn(67890);
        DzSimpleButtonVO result = pressOnNailProductButtonOpt.compute(mockContext, mockParam, mockConfig);
        assertTrue(result.getAvailable());
        assertTrue(result.isShow());
        assertEquals(null, result.getJumpUrl());
    }

    /**
     * 测试compute方法，当用户非MT_APP且产品不具有独家标识时
     */
    @Test
    public void testCompute_NonMtAppWithoutExclusive() {
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_WX.getCode());
        when(mockProductM.getJumpUrl()).thenReturn("defaultJumpUrl");
        DzSimpleButtonVO result = pressOnNailProductButtonOpt.compute(mockContext, mockParam, mockConfig);
        assertTrue(result.getAvailable());
        assertTrue(result.isShow());
        assertEquals("defaultJumpUrl", result.getJumpUrl());
        assertEquals("抢购", result.getName());
    }

    /**
     * 测试compute方法，当产品SKU列表为空时
     */
    @Test
    public void testCompute_EmptySkuList() {
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_APP.getCode());
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList());
        DzSimpleButtonVO result = pressOnNailProductButtonOpt.compute(mockContext, mockParam, mockConfig);
        assertTrue(result.getAvailable());
        assertTrue(result.isShow());
        assertEquals("抢购", result.getName());
    }

    /**
     * 测试compute方法，当context中缺少必要参数时
     */
    @Test
    public void testCompute_MissingContextParams() {
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(null);
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList("sku123"));
        when(mockProductM.getJumpUrl()).thenReturn("defaultJumpUrl");
        DzSimpleButtonVO result = pressOnNailProductButtonOpt.compute(mockContext, mockParam, mockConfig);
        assertTrue(result.getAvailable());
        assertTrue(result.isShow());
        assertEquals("defaultJumpUrl", result.getJumpUrl());
        assertEquals("抢购", result.getName());
    }

    @Test
    public void testComputeWithMTAppAndValidSkuList() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.userAgent, VCClientTypeEnum.MT_APP.getCode());
        context.addParam(ShelfActivityConstants.Params.mtPoiIdL, 12345L);
        ProductM productM = mock(ProductM.class);
        when(productM.getProductId()).thenReturn(67890);
        when(productM.getSkuIdList()).thenReturn(Collections.singletonList("SKU001"));
        PressOnNailProductButtonOpt.Param param = createParam();
        param.setProductM(productM);
        try (MockedStatic<PressOnNailUtils> utilities = Mockito.mockStatic(PressOnNailUtils.class)) {
            utilities.when(() -> PressOnNailUtils.checkExclusive(any())).thenReturn(true);
            DzSimpleButtonVO result = new PressOnNailProductButtonOpt().compute(context, param, new PressOnNailProductButtonOpt.Config());
            assertEquals(ButtonTypeEnums.CART_BUTTON.getType(), result.getType());
            assertTrue(result.isShow());
            assertEquals("https://p1.meituan.net/travelcube/9ded5074e473b49a4f4acc80ac3afb44731.png", result.getImgUrl());
            assertEquals(String.format("imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=addtocartpage-popup&mrn_min_version=0.0.697&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&exitAnim=mrn_anim_exit_from_top&&overridePendingTransition=1&enterAnim=mrn_anim_enter_from_bottom&pagesource=poiShelf&autoadd=true&dealid=%s&shopid=%s&skuid=%s", 67890, 12345L, "SKU001"), result.getJumpUrl());
        }
    }

    @Test
    public void testComputeWithNonMTAppClient() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.userAgent, VCClientTypeEnum.DP_APP.getCode());
        ProductM productM = mock(ProductM.class);
        when(productM.getJumpUrl()).thenReturn("https://example.com");
        PressOnNailProductButtonOpt.Param param = createParam();
        param.setProductM(productM);
        DzSimpleButtonVO result = new PressOnNailProductButtonOpt().compute(context, param, new PressOnNailProductButtonOpt.Config());
        assertEquals(ButtonTypeEnums.BUTTON.getType(), result.getType());
        assertTrue(result.isShow());
        assertEquals("抢购", result.getName());
        assertEquals("https://example.com", result.getJumpUrl());
    }
}
