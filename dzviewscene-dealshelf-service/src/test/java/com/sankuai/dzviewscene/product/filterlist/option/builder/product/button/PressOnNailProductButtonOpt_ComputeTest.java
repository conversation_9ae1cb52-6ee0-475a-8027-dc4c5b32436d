package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.mockito.Mock;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Before;

public class PressOnNailProductButtonOpt_ComputeTest {

    @Mock
    private ActivityCxt mockContext;

    @Mock
    private ProductButtonVP.Param mockParam;

    @Mock
    private PressOnNailProductButtonOpt.Config mockConfig;

    @Mock
    private ProductM mockProductM;

    private PressOnNailProductButtonOpt pressOnNailProductButtonOpt = new PressOnNailProductButtonOpt();

    private void initializeMocks() {
        MockitoAnnotations.initMocks(this);
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    private void setupContextForExclusive(boolean exclusive, int ua) {
        initializeMocks();
        when(mockContext.getParam("userAgent")).thenReturn(ua);
        if (exclusive) {
            when(mockContext.getParam("ZdcTagIdFetcherOpt.CODE")).thenReturn(Collections.singletonList(1L));
        } else {
            when(mockContext.getParam("ZdcTagIdFetcherOpt.CODE")).thenReturn(Collections.emptyList());
        }
    }

    @Test
    public void testCompute_MtAppWithoutExclusive() throws Throwable {
        // Assuming 1 represents MT_APP
        setupContextForExclusive(false, 1);
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList("sku123"));
        DzSimpleButtonVO result = pressOnNailProductButtonOpt.compute(mockContext, mockParam, mockConfig);
        assertNotNull(result);
        assertTrue(result.getAvailable());
        assertTrue(result.isShow());
        assertNull(result.getJumpUrl());
    }

    @Test
    public void testCompute_NotMtAppWithoutExclusive() throws Throwable {
        // Assuming 2 represents non-MT_APP
        setupContextForExclusive(false, 2);
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList("sku123"));
        DzSimpleButtonVO result = pressOnNailProductButtonOpt.compute(mockContext, mockParam, mockConfig);
        assertNotNull(result);
        assertTrue(result.getAvailable());
        assertTrue(result.isShow());
        assertNull(result.getJumpUrl());
    }
}
