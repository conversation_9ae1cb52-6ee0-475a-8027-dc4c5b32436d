package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.runner.RunWith;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopMerchantMemberProductWarmUpButtonOptTest {

    @Test
    public void testComputeIsValidWarmUpStageIsFalse() throws Throwable {
        // arrange
        ShopMerchantMemberProductWarmUpButtonOpt opt = new ShopMerchantMemberProductWarmUpButtonOpt();
        ActivityCxt activityCxt = Mockito.mock(ActivityCxt.class);
        ProductButtonVP.Param param = Mockito.mock(ProductButtonVP.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getOrderUrl()).thenReturn("orderUrl");
        WarmUpStageEnum.WarmUpStageResult warmUpStageResult = Mockito.mock(WarmUpStageEnum.WarmUpStageResult.class);
        // act
        DzSimpleButtonVO result = opt.compute(activityCxt, param, null);
        // assert
        Assert.assertEquals(result.getJumpUrl(), "orderUrl");
        Assert.assertEquals(result.getName(), "抢购");
    }
    // Additional test cases can be added here...
}
