package com.sankuai.dzviewscene.product.filterlist.option.builder.product.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductJumpUrlVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailProductJumpUrlOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private Param param;
    @Mock
    private PressOnNailProductJumpUrlOpt.Config config;
    @Mock
    private ProductM productM;

    private PressOnNailProductJumpUrlOpt pressOnNailProductJumpUrlOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailProductJumpUrlOpt = new PressOnNailProductJumpUrlOpt();
    }

    /**
     * 测试产品M为null时返回空字符串
     */
    @Test
    public void testCompute_ProductMIsNull_ReturnEmptyString() {
        when(param.getProductM()).thenReturn(null);

        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);

        assertEquals("", result);
    }

    /**
     * 测试产品M的JumpUrl为空时返回空字符串
     */
    @Test
    public void testCompute_JumpUrlIsEmpty_ReturnEmptyString() {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("");

        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);

        assertEquals("", result);
    }

    /**
     * 测试产品M的SkuIdList为空时返回JumpUrl
     */
    @Test
    public void testCompute_SkuIdListIsEmpty_ReturnJumpUrl() {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(null);

        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);

        assertEquals("http://example.com", result);
    }

    /**
     * 测试产品M的SkuIdList不为空时返回JumpUrl与skuinitindex
     */
    @Test
    public void testCompute_SkuIdListIsNotEmpty_ReturnJumpUrlWithSkuInitIndex() {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(Arrays.asList("SKU123"));

        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);

        assertEquals("http://example.com&skuinitindex=SKU123", result);
    }
}
