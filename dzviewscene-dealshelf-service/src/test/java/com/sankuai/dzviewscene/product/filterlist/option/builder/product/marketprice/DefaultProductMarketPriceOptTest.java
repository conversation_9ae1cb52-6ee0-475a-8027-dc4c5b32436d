package com.sankuai.dzviewscene.product.filterlist.option.builder.product.marketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductMarketPriceVP;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Arrays;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductMarketPriceOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductMarketPriceVP.Param param;

    @Mock
    private ProductM productM;

    @Mock
    private ProductPromoPriceM promoPriceM;

    private DefaultProductMarketPriceOpt defaultProductMarketPriceOpt;

    @Before
    public void setUp() {
        defaultProductMarketPriceOpt = new DefaultProductMarketPriceOpt();
    }

    @Test
    public void testComputeWithDirectPromo() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(promoPriceM);
        String result = defaultProductMarketPriceOpt.compute(context, param, null);
        assertEquals(FloorsBuilderExtAdapter.EMPTY_VALUE, result);
    }

    @Test
    public void testComputeWithCardPromo() throws Throwable {
        try (MockedStatic<CardPromoUtils> mockedStatic = mockStatic(CardPromoUtils.class)) {
            when(param.getProductM()).thenReturn(productM);
            when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(null);
            when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPriceM));
            mockedStatic.when(() -> CardPromoUtils.hasCardPromo(Arrays.asList(promoPriceM))).thenReturn(true);
            String result = defaultProductMarketPriceOpt.compute(context, param, null);
            assertEquals(FloorsBuilderExtAdapter.EMPTY_VALUE, result);
        }
    }

    @Test
    public void testComputeWithoutPromo() throws Throwable {
        try (MockedStatic<CardPromoUtils> mockedStatic = mockStatic(CardPromoUtils.class)) {
            when(param.getProductM()).thenReturn(productM);
            when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(null);
            when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPriceM));
            mockedStatic.when(() -> CardPromoUtils.hasCardPromo(Arrays.asList(promoPriceM))).thenReturn(false);
            when(productM.getMarketPrice()).thenReturn("100");
            String result = defaultProductMarketPriceOpt.compute(context, param, null);
            assertEquals("100", result);
        }
    }
}
