package com.sankuai.dzviewscene.product.filterlist.option.builder.product.marketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductMarketPriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HideMarketPriceWhenEqualsSalePriceOptUnitTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductMarketPriceVP.Param param;

    @Mock
    private ProductM productM;

    /**
     * 没有优惠，返回空白
     */
    @Test
    public void testComputeWhenPromoIsNull() {
        // arrange
        HideMarketPriceWhenEqualsSalePriceOpt opt = new HideMarketPriceWhenEqualsSalePriceOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(null);

        // act
        String result = opt.compute(activityCxt, param, null);

        // assert
        assertEquals(FloorsBuilderExtAdapter.EMPTY_VALUE, result);
    }

    /**
     * 有优惠，返回划线价
     */
    @Test
    public void testComputeWhenPromoIsNotNull() {
        // arrange
        HideMarketPriceWhenEqualsSalePriceOpt opt = new HideMarketPriceWhenEqualsSalePriceOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(new ProductPromoPriceM());
        when(productM.getMarketPrice()).thenReturn("100");

        // act
        String result = opt.compute(activityCxt, param, null);

        // assert
        assertEquals("100", result);
    }
}
