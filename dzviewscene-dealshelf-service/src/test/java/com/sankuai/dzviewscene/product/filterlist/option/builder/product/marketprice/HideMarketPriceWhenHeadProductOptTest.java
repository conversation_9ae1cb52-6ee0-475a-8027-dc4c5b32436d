package com.sankuai.dzviewscene.product.filterlist.option.builder.product.marketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductMarketPriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HideMarketPriceWhenHeadProductOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    @Mock
    private ProductMarketPriceVP.Param param;

    @Mock
    private HideMarketPriceWhenHeadProductOpt.Config config;


    @Before
    public void setUp() {
    }

    /**
     * 没有优惠，返回空白
     */
    @Test
    public void testComputeWhenPromoIsNull() {
        // arrange
        HideMarketPriceWhenHeadProductOpt opt = new HideMarketPriceWhenHeadProductOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(null);

        // act
        String result = opt.compute(activityCxt, param, null);

        // assert
        assertEquals(FloorsBuilderExtAdapter.EMPTY_VALUE, result);
    }

    /**
     * 需要展示置顶商品价格
     */
    @Test
    public void testComputeWhenNeedShowHeadProductMarketPriceIsTrue() {
        // arrange
        HideMarketPriceWhenHeadProductOpt opt = new HideMarketPriceWhenHeadProductOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(new ProductPromoPriceM());
        when(productM.getMarketPrice()).thenReturn("100");

        when(config.isNeedShowHeadProductMarketPrice()).thenReturn(true);

        // act
        String result = opt.compute(activityCxt, param, config);

        // assert
        assertEquals("100", result);
    }

    /**
     * 不需要展示置顶商品价格
     */
    @Test
    public void testComputeWhenNeedShowHeadProductMarketPriceIsFalse() {
        // arrange
        HideMarketPriceWhenHeadProductOpt opt = new HideMarketPriceWhenHeadProductOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(new ProductPromoPriceM());
        when(productM.getAttr("from_group")).thenReturn("置顶");

        when(config.isNeedShowHeadProductMarketPrice()).thenReturn(false);

        // act
        String result = opt.compute(activityCxt, param, config);

        // assert
        assertEquals(FloorsBuilderExtAdapter.EMPTY_VALUE, result);
    }
}