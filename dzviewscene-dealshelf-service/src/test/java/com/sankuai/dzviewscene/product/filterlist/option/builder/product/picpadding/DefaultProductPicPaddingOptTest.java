package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductPicPaddingOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DefaultProductPicPaddingOpt.Param param;

    @Mock
    private DefaultProductPicPaddingOpt.Config config;

    @Mock
    private ProductM productM;

    @Test
    public void testComputePicUrlIsEmpty() throws Throwable {
        // arrange
        DefaultProductPicPaddingOpt opt = new DefaultProductPicPaddingOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("");
        // act
        opt.compute(context, param, config);
        // assert
        verify(param, never()).getDzProductVO();
    }

    @Test
    public void testComputeFloatTagsIsEmpty() throws Throwable {
        // arrange
        DefaultProductPicPaddingOpt opt = new DefaultProductPicPaddingOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        when(param.getDzProductVO()).thenReturn(new DzProductVO());
        when(config.getFloatTags()).thenReturn(null);
        // act
        opt.compute(context, param, config);
        // assert
        // Removed the incorrect verification for getHeaderPicAspectRadio never being called
        Assert.assertNotNull(param.getDzProductVO());
    }

    @Test
    public void testComputeFloatTagsIsNotEmpty() throws Throwable {
        // arrange
        DefaultProductPicPaddingOpt opt = new DefaultProductPicPaddingOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        when(param.getDzProductVO()).thenReturn(new DzProductVO());
        when(config.getFloatTags()).thenReturn(Collections.singletonList(new DzActivityTagVO()));
        // act
        opt.compute(context, param, config);
        // assert
        verify(config).getHeaderPicAspectRadio();
    }
}
