package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicPaddingVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;


@RunWith(MockitoJUnitRunner.class)
public class PressOnNailProductPicPaddingOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private ProductPicPaddingVP.Param mockParam;
    @Mock
    private PressOnNailProductPicPaddingOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;
    @Mock
    private DzProductVO mockDzProductVO;

    private PressOnNailProductPicPaddingOpt pressOnNailProductPicPaddingOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailProductPicPaddingOpt = new PressOnNailProductPicPaddingOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getDzProductVO()).thenReturn(mockDzProductVO);
    }

    /**
     * 测试场景：当产品材料列表为空时，不应该修改图片URL和角标
     */
    @Test
    public void testCompute_WhenMaterialListIsEmpty() {
        // arrange
        when(mockProductM.getMaterialList()).thenReturn(new ArrayList<>());
//        when(mockConfig.getPicWidth()).thenReturn(300);
//        when(mockConfig.getPicHeight()).thenReturn(300);
//        when(mockConfig.getHeaderPicAspectRadio()).thenReturn(1.0);

        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        verify(mockDzProductVO, never()).setPic(anyString());
        verify(mockDzProductVO, never()).setActivityTags(anyList());
    }

    /**
     * 测试场景：当产品材料列表不为空，但所有材料名称为空时，不应该修改图片URL和角标
     */
    @Test
    public void testCompute_WhenMaterialListIsNotEmptyButNameIsEmpty() {
        // arrange
        List<DealProductMaterialM> materialList = new ArrayList<>();
        materialList.add(new DealProductMaterialM());
        when(mockProductM.getMaterialList()).thenReturn(materialList);

        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        verify(mockDzProductVO, never()).setPic(anyString());
        verify(mockDzProductVO, never()).setActivityTags(anyList());
    }

    /**
     * 测试场景：当产品材料列表不为空，且至少有一个材料的名称不为空，应该修改图片URL
     */
    @Test
    public void testCompute_WhenMaterialListIsNotEmptyAndNameIsNotEmpty() {
        // arrange
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("Material Name");
        materialM.setPic("https://example.com/pic.jpg");
        materialM.setRecommended(1);
        materialList.add(materialM);
        when(mockProductM.getMaterialList()).thenReturn(materialList);
        when(mockConfig.getPicWidth()).thenReturn(300);
        when(mockConfig.getPicHeight()).thenReturn(300);
        when(mockConfig.getHeaderPicAspectRadio()).thenReturn(1.0);

        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        verify(mockDzProductVO).setPic(anyString());
        verify(mockDzProductVO).setActivityTags(anyList());
    }

    /**
     * 测试场景：当产品图片URL为空时，不应该修改图片URL和角标
     */
    @Test
    public void testCompute_WhenPicUrlIsEmpty() {
        // arrange
        when(mockProductM.getPicUrl()).thenReturn("");
        when(mockProductM.getMaterialList()).thenReturn(new ArrayList<>());

        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        verify(mockDzProductVO, never()).setPic(anyString());
        verify(mockDzProductVO, never()).setActivityTags(anyList());
    }
}
