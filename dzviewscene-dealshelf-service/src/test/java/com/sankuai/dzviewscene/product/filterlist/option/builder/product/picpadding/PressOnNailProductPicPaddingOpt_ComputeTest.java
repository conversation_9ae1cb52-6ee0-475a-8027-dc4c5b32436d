package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicPaddingVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.junit.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailProductPicPaddingOpt_ComputeTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private ProductPicPaddingVP.Param mockParam;

    @Mock
    private PressOnNailProductPicPaddingOpt.Config mockConfig;

    @Mock
    private ProductM mockProductM;

    @Mock
    private DzProductVO mockDzProductVO;

    private PressOnNailProductPicPaddingOpt pressOnNailProductPicPaddingOpt;

    @Before
    public void setUp() {
        pressOnNailProductPicPaddingOpt = new PressOnNailProductPicPaddingOpt();
        when(mockParam.getDzProductVO()).thenReturn(mockDzProductVO);
    }

    @Test
    public void testCompute_WhenMaterialListIsEmptyAndPicUrlIsEmpty() throws Throwable {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("");
        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        verify(mockDzProductVO, never()).setPicScale(anyDouble());
        verify(mockDzProductVO, never()).setPic(anyString());
        verify(mockDzProductVO, never()).setActivityTags(anyList());
    }

    @Test
    public void testCompute_WhenMaterialListIsNotEmptyAndNameIsNotEmptyAndPicUrlIsEmpty() throws Throwable {
        // arrange
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("Material Name");
        materialList.add(materialM);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("");
        when(mockProductM.getMaterialList()).thenReturn(materialList);
        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        verify(mockDzProductVO, never()).setPicScale(anyDouble());
        verify(mockDzProductVO, never()).setPic(anyString());
        verify(mockDzProductVO, never()).setActivityTags(anyList());
    }

    @Test
    public void testCompute_WhenMaterialListIsNotEmptyAndNameIsNotEmptyAndPicUrlIsNotEmptyAndIsShopRecIsFalse() throws Throwable {
        // arrange
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("Material Name");
        materialM.setPic("https://example.com/pic.jpg");
        materialM.setRecommended(0);
        materialList.add(materialM);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("https://example.com/pic.jpg");
        when(mockProductM.getMaterialList()).thenReturn(materialList);
        when(mockConfig.getPicWidth()).thenReturn(300);
        when(mockConfig.getPicHeight()).thenReturn(300);
        when(mockConfig.getHeaderPicAspectRadio()).thenReturn(1.0);
        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        verify(mockDzProductVO).setPicScale(anyDouble());
        verify(mockDzProductVO).setPic(anyString());
        verify(mockDzProductVO, never()).setActivityTags(anyList());
    }

    @Test
    public void testCompute_WhenMaterialListIsNotEmptyAndNameIsNotEmptyAndPicUrlIsNotEmptyAndIsShopRecIsTrue() throws Throwable {
        // arrange
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("Material Name");
        materialM.setPic("https://example.com/pic.jpg");
        materialM.setRecommended(1);
        materialList.add(materialM);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("https://example.com/pic.jpg");
        when(mockProductM.getMaterialList()).thenReturn(materialList);
        when(mockConfig.getPicWidth()).thenReturn(300);
        when(mockConfig.getPicHeight()).thenReturn(300);
        when(mockConfig.getHeaderPicAspectRadio()).thenReturn(1.0);
        // act
        pressOnNailProductPicPaddingOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        verify(mockDzProductVO).setPicScale(anyDouble());
        verify(mockDzProductVO).setPic(anyString());
        verify(mockDzProductVO).setActivityTags(anyList());
    }
}
