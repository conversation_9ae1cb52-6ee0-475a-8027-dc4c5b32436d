package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductIdM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * @author: wuwenqiang
 * @create: 2024-06-25
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class NearestShopPriceAboveTagsOptTest {

    @Mock
    ProductPriceAboveTagVP.Param param;

    @InjectMocks
    private NearestShopPriceAboveTagsOpt opt;

    private ActivityCxt activityCxt;
    private NearestShopPriceAboveTagsOpt.Config config;
    private static final String EDU_UNCOOP_SCENE_CODE = "edu_uncoolshop_deal_list";
    private static final String WED_PHOTO_UNCOOP_SCENE_CODE = "wedphoto_uncoopshop_deal_spu_list";
    private static final int HOT_SPU_PRODUCT_ID_TYPE = 10003;

    private static final String PHOTO_UNCOOP_SPU_LIST_SCENE = "photo_uncoolshop_deal_list";

    private static final String BABY_UNCOOP_SCENE_CODE = "baby_uncoolshop_deal_list";

    @Before
    public void setUp() {
        activityCxt = new ActivityCxt();
        config = new NearestShopPriceAboveTagsOpt.Config();
    }

    /**
     * 测试ProductM为null时
     */
    @Test
    public void testComputeProductMNull() {
        when(param.getProductM()).thenReturn(null);
        List<DzTagVO> result = opt.compute(activityCxt, param, config);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试教育-非标品非团单情况
     */
    @Test
    public void testComputeInvalidProductType() {
        activityCxt.setSceneCode(EDU_UNCOOP_SCENE_CODE);
        ProductM productM = new ProductM();
        productM.setProductType(-1);
        when(param.getProductM()).thenReturn(productM);
        List<DzTagVO> result = opt.compute(activityCxt, param, config);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试教育-标品情况下生成标签
     */
    @Test
    public void testComputeSpu() {
        activityCxt.setSceneCode(EDU_UNCOOP_SCENE_CODE);
        ProductM productM = new ProductM();
        productM.setProductType(4);
        productM.setShopNum(10);
        productM.setNearestShopDesc("1km");

        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals(2, result.size());
        assertEquals("10家门店通用", result.get(0).getText());
        assertEquals("1km", result.get(1).getText());
    }

    /**
     * 测试教育-团单情况下生成标签
     */
    @Test
    public void testComputeDeal() {
        activityCxt.setSceneCode(EDU_UNCOOP_SCENE_CODE);
        ShopM shopM = new ShopM();
        shopM.setShopName("测试门店");
        shopM.setDistance("500m");

        ProductM productM = new ProductM();
        productM.setProductType(1);
        productM.setShopMs(java.util.Collections.singletonList(shopM));

        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals(2, result.size());
        assertEquals("测试门店", result.get(0).getText());
        assertEquals("500m", result.get(1).getText());
    }

    /**
     * 测试教育-团单情况下带Icon生成标签
     */
    @Test
    public void testComputeDealWithIcon() {
        activityCxt.setSceneCode(EDU_UNCOOP_SCENE_CODE);
        ShopM shopM = new ShopM();
        shopM.setShopName("测试门店");
        shopM.setDistance("500m");

        ProductM productM = new ProductM();
        productM.setProductType(1);
        productM.setShopMs(java.util.Collections.singletonList(shopM));

        config.setPreIconUrl("test");
        config.setPrePicHeight(1);
        config.setPreAspectRadio(1);

        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals(2, result.size());
        assertEquals("测试门店", result.get(0).getText());
        assertEquals("test", result.get(0).getPrePic().getPicUrl());
        assertEquals("500m", result.get(1).getText());
    }

    /**
     * 测试教育-标品不同值applyShopNum情况下生成标签
     */
    @Test
    public void testComputeWithDifferentApplyShopNum() {
        activityCxt.setSceneCode(EDU_UNCOOP_SCENE_CODE);
        ProductM productM = new ProductM();
        productM.setProductType(4);
        productM.setNearestShopDesc("1km");

        NearestShopPriceAboveTagsOpt.Config config = new NearestShopPriceAboveTagsOpt.Config();
        config.setApplyShopNumTemplate("%s家门店通用");

        // 测试不同的applyShopNum值
        int[] shopNums = {5, 50, 100, 1000};
        String[] expectedTags = {"5家门店通用", "50+家门店通用", "100+家门店通用", "1000+家门店通用"};

        for (int i = 0; i < shopNums.length; i++) {
            productM.setShopNum(shopNums[i]);
            when(param.getProductM()).thenReturn(productM);

            List<DzTagVO> result = opt.compute(activityCxt, param, config);
            assertEquals(2, result.size());
            assertEquals(expectedTags[i], result.get(0).getText());
        }
    }

    /**
     * 测试教育-标品适用门店数量模板不同的情况
     */
    @Test
    public void testComputeWithDifferentTemplates() {
        activityCxt.setSceneCode(EDU_UNCOOP_SCENE_CODE);
        ProductM productM = new ProductM();
        productM.setProductType(4);
        productM.setShopNum(10);
        productM.setNearestShopDesc("1km");

        when(param.getProductM()).thenReturn(productM);

        // 测试不同的模板
        String[] templates = {"%s家门店可用", "%s个门店适用"};
        String[] expectedTags = {"10家门店可用", "10个门店适用"};

        for (int i = 0; i < templates.length; i++) {
            NearestShopPriceAboveTagsOpt.Config config = new NearestShopPriceAboveTagsOpt.Config();
            config.setApplyShopNumTemplate(templates[i]);

            List<DzTagVO> result = opt.compute(activityCxt, param, config);
            assertEquals("应该生成2个标签", 2, result.size());
            assertEquals("门店数量模板应用错误", expectedTags[i], result.get(0).getText());
        }
    }

    /**
     * 测试婚摄-爆品生成标签
     */
    @Test
    public void testComputeHotSpu() {
        activityCxt.setSceneCode(WED_PHOTO_UNCOOP_SCENE_CODE);
        ProductM productM = new ProductM();
        productM.setProductType(4); // 假设4代表GENERAL_SPU类型
        ProductIdM productIdM = new ProductIdM();
        productIdM.setId(123);
        productIdM.setType(HOT_SPU_PRODUCT_ID_TYPE);
        productM.setId(productIdM); // 设置为爆品ID类型
        productM.setShopNum(20);
        productM.setNearestShopDesc("2km");
        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals("应该生成2个标签", 2, result.size());
        assertEquals("20家门店通用", result.get(0).getText());
        assertEquals("2km", result.get(1).getText());
    }

    /**
     * 测试婚摄-泛商品情况下生成标签
     */
    @Test
    public void testComputeGeneralSpuWithIcon() {
        activityCxt.setSceneCode(WED_PHOTO_UNCOOP_SCENE_CODE);
        ShopM shopM = new ShopM();
        shopM.setShopName("特色门店");
        shopM.setDistance("1.2km");

        ProductM productM = new ProductM();
        productM.setProductType(ProductTypeEnum.SPU.getType()); // 假设4代表GENERAL_SPU类型
        productM.setShopMs(java.util.Collections.singletonList(shopM));

        config.setPreIconUrl("icon_url");
        config.setPrePicHeight(1);
        config.setPreAspectRadio(1);

        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals("应该生成2个标签", 2, result.size());
        assertEquals("特色门店", result.get(0).getText());
        assertEquals("icon_url", result.get(0).getPrePic().getPicUrl());
        assertEquals("1.2km", result.get(1).getText());
    }

    @Test
    public void testPhotoNearShop() {
        activityCxt.setSceneCode(PHOTO_UNCOOP_SPU_LIST_SCENE);
        ShopM shopM = new ShopM();
        shopM.setShopName("测试门店");
        shopM.setDistance("500m");

        ProductM productM = new ProductM();
        productM.setProductType(1);
        productM.setShopMs(java.util.Collections.singletonList(shopM));

        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);
        System.out.println(JSON.toJSONString(result));

        assertEquals(2, result.size());
        assertEquals("测试门店", result.get(0).getText());
        assertEquals("500m", result.get(1).getText());
    }

    @Test
    public void testPhotoNearShopSpu() {
        activityCxt.setSceneCode(PHOTO_UNCOOP_SPU_LIST_SCENE);
        ProductM productM = new ProductM();
        productM.setProductType(4);
        ProductIdM productIdM = new ProductIdM();
        productIdM.setId(123);
        productM.setId(productIdM);
        productM.setShopNum(20);
        productM.setNearestShopDesc("2km");
        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals("应该生成2个标签", 2, result.size());
        assertEquals("20家门店通用", result.get(0).getText());
        assertEquals("2km", result.get(1).getText());
    }

    @Test
    public void testBabyHeadProduct() {
        activityCxt.setSceneCode(BABY_UNCOOP_SCENE_CODE);
        ShopM shopM = new ShopM();
        shopM.setShopName("测试门店");
        shopM.setDistance("500m");

        ProductM productM = new ProductM();
        productM.setProductType(1);
        productM.setShopMs(java.util.Collections.singletonList(shopM));
        productM.setAttr("from_group","置顶");

        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals(1, result.size());
        assertEquals("最近可用门店距离500m", result.get(0).getText());
    }

    @Test
    public void testBabyNotHeadProduct() {
        activityCxt.setSceneCode(BABY_UNCOOP_SCENE_CODE);
        ShopM shopM = new ShopM();
        shopM.setShopName("测试门店");
        shopM.setDistance("500m");

        ProductM productM = new ProductM();
        productM.setProductType(1);
        productM.setShopMs(java.util.Collections.singletonList(shopM));
        productM.setAttr("from_group","推荐");

        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals(2, result.size());
        assertEquals("测试门店", result.get(0).getText());
        assertEquals("500m", result.get(1).getText());
    }

    @Test
    public void testBabyNotDeal() {
        activityCxt.setSceneCode(BABY_UNCOOP_SCENE_CODE);

        ProductM productM = new ProductM();
        productM.setProductType(2);
        when(param.getProductM()).thenReturn(productM);

        List<DzTagVO> result = opt.compute(activityCxt, param, config);

        assertEquals(0, result.size());

    }
}
