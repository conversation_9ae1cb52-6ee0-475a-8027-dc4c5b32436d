package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu.EduOnlineDealClassItemSkuListOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductListConfigPriceAboveTagsOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductPriceAboveTagVP.Param param;

    @Mock
    private ProductM productM;

    /**
     * 测试compute方法，当入参没有值时
     * 期望返回空的列表
     */
    @Test
    public void testProductIsEmpty() {
        // arrange
        ProductListConfigPriceAboveTagsOpt opt = new ProductListConfigPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        // act
        List<DzTagVO> result = opt.compute(context, param, new ProductListConfigPriceAboveTagsOpt.Config());

        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试compute方法，没有指定的attr时
     * 期望返回空的列表
     */
    @Test
    public void testAttrNotExist() {
        // arrange
        ProductListConfigPriceAboveTagsOpt opt = new ProductListConfigPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("test")).thenReturn(null);

        ProductListConfigPriceAboveTagsOpt.Config config = new ProductListConfigPriceAboveTagsOpt.Config();
        config.setAttrKeys(Lists.newArrayList("test"));

        // act
        List<DzTagVO> result = opt.compute(context, param, config);

        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试compute方法，存在指定的attr时
     * 期望返回attr的值
     */
    @Test
    public void testAttrExistAsNoConfig() {
        // arrange
        ProductListConfigPriceAboveTagsOpt opt = new ProductListConfigPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("attr_eduOnlineTrialVideoNum")).thenReturn("测试的值");

        ProductListConfigPriceAboveTagsOpt.Config config = new ProductListConfigPriceAboveTagsOpt.Config();
        config.setAttrKeys(Lists.newArrayList("attr_eduOnlineTrialVideoNum"));
        Map<String, ProductListConfigPriceAboveTagsOpt.TagCfg> attrKey2Cfg = new HashMap<>();
        config.setAttrKey2Cfg(attrKey2Cfg);
        // act
        List<DzTagVO> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getText().equals("测试的值"));

    }

    /**
     * 测试compute方法，存在指定的attr时
     * 期望返回attr的值
     */
    @Test
    public void testAttrExistAsHasConfig() {
        // arrange
        ProductListConfigPriceAboveTagsOpt opt = new ProductListConfigPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("attr_eduOnlineTrialVideoNum")).thenReturn("测试的值");

        ProductListConfigPriceAboveTagsOpt.Config config = new ProductListConfigPriceAboveTagsOpt.Config();
        config.setAttrKeys(Lists.newArrayList("attr_eduOnlineTrialVideoNum"));
        Map<String, ProductListConfigPriceAboveTagsOpt.TagCfg> attrKey2Cfg = new HashMap<>();
        ProductListConfigPriceAboveTagsOpt.TagCfg tagCfg = new ProductListConfigPriceAboveTagsOpt.TagCfg();
        tagCfg.setTextColor("#FF4B10");
        tagCfg.setBackground("#FFF1EC");
        tagCfg.setAfterPicUrl("AfterPicUrl");
        tagCfg.setAfterPicHeight(100);
        tagCfg.setAfterAspectRadio(1.0);
        tagCfg.setPrePicUrl("PrePicUrl");
        tagCfg.setPreAspectRadio(1.0);
        tagCfg.setPrePicHeight(100);
        attrKey2Cfg.put("attr_eduOnlineTrialVideoNum", tagCfg);
        config.setAttrKey2Cfg(attrKey2Cfg);
        // act
        List<DzTagVO> result = opt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getText().equals("测试的值"));
        assertTrue(result.get(0).getBackground().equals("#FFF1EC"));
        assertTrue(result.get(0).getTextColor().equals("#FF4B10"));

    }

}
