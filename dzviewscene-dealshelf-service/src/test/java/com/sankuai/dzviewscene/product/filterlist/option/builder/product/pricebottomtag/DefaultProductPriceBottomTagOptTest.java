package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import static org.junit.Assert.assertNull;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductPriceBottomTagOptTest {

    /**
     * 测试 compute 方法是否返回 null
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        DefaultProductPriceBottomTagOpt defaultProductPriceBottomTagOpt = new DefaultProductPriceBottomTagOpt();
        ActivityCxt context = null;
        ProductPriceBottomTagVP.Param param = null;
        // act
        List<DzTagVO> result = defaultProductPriceBottomTagOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }
}
