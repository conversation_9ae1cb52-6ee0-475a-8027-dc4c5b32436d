package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DiscountPromoTagsOpt_GetTimesDealPromoTagTest {

    private DiscountPromoTagsOpt discountPromoTagsOpt;

    private ProductPriceBottomTagVP.Param param;

    private ProductM productM;

    private String timesDealTagPrefix;

    /**
     * Tests the scenario where getSinglePrice returns null and timesDealTagPrefix is null.
     */
    @Test
    public void testGetTimesDealPromoTag_SinglePriceIsNull_TimesDealTagPrefixIsNull() throws Throwable {
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        String timesDealTagPrefix = null;
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn(null);
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, timesDealTagPrefix);
        assertNull(result);
    }

    /**
     * Tests the scenario where getSinglePrice returns non-null and timesDealTagPrefix is null.
     */
    @Test
    public void testGetTimesDealPromoTag_SinglePriceIsNotNull_TimesDealTagPrefixIsNull() throws Throwable {
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        String timesDealTagPrefix = null;
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn("100");
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, timesDealTagPrefix);
        assertNull(result);
    }

    /**
     * Tests the scenario where getSinglePrice returns null and timesDealTagPrefix is non-null.
     */
    @Test
    public void testGetTimesDealPromoTag_SinglePriceIsNull_TimesDealTagPrefixIsNotNull() throws Throwable {
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        String timesDealTagPrefix = "test";
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn(null);
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, timesDealTagPrefix);
        assertNull(result);
    }

    /**
     * Test case for successful creation of DzTagVO with valid inputs
     */
    @Test
    public void testGetTimesDealPromoTag_ValidInputs_ReturnFormattedDzTagVO() throws Throwable {
        // arrange
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        String timesDealTagPrefix = "单次¥%s";
        String salePrice = "100";
        String expectedSinglePrice = "50";
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn(salePrice);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("2");
        // act
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, timesDealTagPrefix);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DzTagVO dzTagVO = result.get(0);
        assertEquals(String.format(timesDealTagPrefix, expectedSinglePrice), dzTagVO.getName());
        assertEquals(true, dzTagVO.isHasBorder());
        assertEquals(ColorUtils.colorFF4B10, dzTagVO.getBorderColor());
        assertEquals(String.format(timesDealTagPrefix, expectedSinglePrice), dzTagVO.getText());
        assertEquals(ColorUtils.colorFF4B10, dzTagVO.getTextColor());
    }

    /**
     * Test case when singlePrice is null
     */
    @Test
    public void testGetTimesDealPromoTag_SinglePriceNull_ReturnNull() throws Throwable {
        // arrange
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        String timesDealTagPrefix = "单次¥%s";
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn(null);
        // act
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, timesDealTagPrefix);
        // assert
        assertNull(result);
    }

    /**
     * Test case when timesDealTagPrefix is null
     */
    @Test
    public void testGetTimesDealPromoTag_TagPrefixNull_ReturnNull() throws Throwable {
        // arrange
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn("100");
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("2");
        // act
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, null);
        // assert
        assertNull(result);
    }

    /**
     * Test case when timesDealTagPrefix is empty
     */
    @Test
    public void testGetTimesDealPromoTag_TagPrefixEmpty_ReturnNull() throws Throwable {
        // arrange
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn("100");
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("2");
        // act
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, "");
        // assert
        assertNull(result);
    }

    /**
     * Test case when param is null
     */
    @Test
    public void testGetTimesDealPromoTag_ParamNull_ReturnNull() throws Throwable {
        // arrange
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        String timesDealTagPrefix = "单次¥%s";
        when(param.getProductM()).thenReturn(null);
        when(param.getSalePrice()).thenReturn(null);
        // act
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, timesDealTagPrefix);
        // assert
        assertNull(result);
    }

    /**
     * Test case with decimal single price
     */
    @Test
    public void testGetTimesDealPromoTag_DecimalSinglePrice_ReturnFormattedDzTagVO() throws Throwable {
        // arrange
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        ProductM productM = mock(ProductM.class);
        String timesDealTagPrefix = "单次¥%s";
        String salePrice = "100";
        String expectedSinglePrice = "33.34";
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn(salePrice);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("3");
        // act
        List<DzTagVO> result = discountPromoTagsOpt.getTimesDealPromoTag(param, timesDealTagPrefix);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DzTagVO dzTagVO = result.get(0);
        assertEquals(String.format(timesDealTagPrefix, expectedSinglePrice), dzTagVO.getName());
        assertEquals(true, dzTagVO.isHasBorder());
        assertEquals(ColorUtils.colorFF4B10, dzTagVO.getBorderColor());
        assertEquals(String.format(timesDealTagPrefix, expectedSinglePrice), dzTagVO.getText());
        assertEquals(ColorUtils.colorFF4B10, dzTagVO.getTextColor());
    }
}
