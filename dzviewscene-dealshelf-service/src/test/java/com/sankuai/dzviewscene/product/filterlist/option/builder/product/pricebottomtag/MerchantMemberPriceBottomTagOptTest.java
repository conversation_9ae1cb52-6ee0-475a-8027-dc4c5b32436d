package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag.MerchantMemberPriceBottomTagOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import java.lang.reflect.Method;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MerchantMemberPriceBottomTagOptTest {

    @InjectMocks
    private MerchantMemberPriceBottomTagOpt merchantMemberPriceBottomTagOpt;

    @Mock
    private ActivityCxt activityCxt;

    private MerchantMemberPriceBottomTagOpt.Param param;

    private MerchantMemberPriceBottomTagOpt.Config config;

    @Before
    public void setUp() {
        // Initialize your test data here
        param = MerchantMemberPriceBottomTagOpt.Param.builder().build();
        config = new MerchantMemberPriceBottomTagOpt.Config();
    }

    @Test
    public void testComputeWithNullConfig() throws Throwable {
        // Arrange
        config = null;
        // Act
        List<DzTagVO> result = merchantMemberPriceBottomTagOpt.compute(activityCxt, param, config);
        // Assert
        assertEquals(0, result.size());
    }
}
