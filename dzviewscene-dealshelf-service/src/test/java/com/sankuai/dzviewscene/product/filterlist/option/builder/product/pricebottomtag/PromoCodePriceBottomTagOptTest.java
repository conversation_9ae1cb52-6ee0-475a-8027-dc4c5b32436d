package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP.Param;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag.PromoCodePriceBottomTagOpt.Config;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoDetailVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodePriceBottomTagOptTest {

    @InjectMocks
    private PromoCodePriceBottomTagOpt promoCodePriceBottomTagOpt;

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private Config mockConfig;
    @Mock
    private ProductM mockProductM;
    @Mock
    private ProductPromoPriceM mockPromoPriceM;
    private MockedStatic<DzPromoUtils> dzPromoUtils;
    private MockedStatic<PriceUtils> priceUtils;

    private MockedStatic<MerchantMemberPromoUtils> merchantMemberPromoUtils;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        promoCodePriceBottomTagOpt = new PromoCodePriceBottomTagOpt();
        dzPromoUtils = Mockito.mockStatic(DzPromoUtils.class);
        merchantMemberPromoUtils = Mockito.mockStatic(MerchantMemberPromoUtils.class);
        priceUtils = Mockito.mockStatic(PriceUtils.class);
        DzTagVO t = new DzTagVO();
        dzPromoUtils.when(() -> DzPromoUtils.buildBasicDzTagVOWithColor(anyInt(), anyString(), anyString(), anyString(), any(), anyString(), anyString())).thenReturn(t);
        DzPromoDetailVO value = new DzPromoDetailVO();
        value.setPromoItems(Lists.newArrayList());
        dzPromoUtils.when(() -> DzPromoUtils.buildPromoDetail(any(), anyInt())).thenReturn(value);
        priceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(mockPromoPriceM());
    }

    @After
    public void after() {
        dzPromoUtils.close();
        priceUtils.close();
        merchantMemberPromoUtils.close();
    }


    /**
     * 测试 compute 方法，当 config 为 null 时
     */
    @Test
    public void testComputeConfigIsNull() {
        List<DzTagVO> result = promoCodePriceBottomTagOpt.compute(mockActivityCxt, mockParam, null);
        assertTrue("结果应为空列表", result.isEmpty());
    }


    /**
     * 测试 compute 方法，当 promoCombinationWithMagicalMemberCoupon 为 true 时，结果不能为空
     */
    @Test
    public void testComputePromoCombinationWithMagicalMemberCouponIsTrue() {

        ActivityCxt mockActivityCxt = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setMarketPrice("100.00");
        productM.setSalePrice(BigDecimal.valueOf(80));
        Param mockParam = Param.builder().platform(2).productM(productM).build();
        dzPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(true);
        List<DzTagVO> result = promoCodePriceBottomTagOpt.compute(mockActivityCxt, mockParam, mockConfig);
        assertFalse("结果不应为空列表", result.isEmpty());
    }

    /**
     * 测试 compute 方法，当 promoCombinationWithMagicalMemberCoupon 为 true 时，结果不能为空
     */
    @Test
    public void testComputePromoCombinationWithMerchantMemberIsTrue() {

        ActivityCxt mockActivityCxt = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setMarketPrice("100.00");
        productM.setSalePrice(BigDecimal.valueOf(80));
        productM.setPromoPrices(Lists.newArrayList(mockPromoPriceM()));
        Param mockParam = Param.builder().platform(2).productM(productM).build();
        dzPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(false);
        merchantMemberPromoUtils.when(() -> MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(any())).thenReturn(true);
        merchantMemberPromoUtils.when(() -> MerchantMemberPromoUtils.getMerchantMemberPromo(any())).thenReturn(mockMerchantMemberProductPromoData());
        List<DzTagVO> result = promoCodePriceBottomTagOpt.compute(mockActivityCxt, mockParam, mockConfig);
        assertFalse("结果不应为空列表", result.isEmpty());
    }


    private MerchantMemberProductPromoData mockMerchantMemberProductPromoData() {
        MerchantMemberProductPromoData merchantMemberProductPromoData = new MerchantMemberProductPromoData();
        merchantMemberProductPromoData.setProductPromoPrice(mockPromoPriceM());
        merchantMemberProductPromoData.setMemberDiscountType(2);
        return merchantMemberProductPromoData;
    }

    private ProductPromoPriceM mockPromoPriceM() {
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoTag("优惠标签");
        promoPriceM.setPromoPrice(BigDecimal.valueOf(100));
        promoPriceM.setPromoType(1);
        promoPriceM.setDiscount(BigDecimal.valueOf(0.8));
        promoPriceM.setDiscountTag("8折");
        promoPriceM.setTotalPromoPrice(BigDecimal.valueOf(20));
        promoPriceM.setTotalPromoPriceTag("icon");
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setSourceType(1);
        promoItemM.setNewUser(false);
        promoItemM.setCanAssign(false);
        promoItemM.setPromoType("11");
        promoItemM.setPromoTag("-¥90");
        promoItemM.setPromoPrice(BigDecimal.valueOf(90));
        promoItemM.setPromoId(1030354719);
        promoItemM.setPromoTypeCode(11);
        promoItemM.setIcon("xxx");
        promoItemM.setRemainStock(0);
        promoItemM.setEndTime(0);
        promoItemM.setEffectiveEndTime(0);
        promoItemM.setAmount(BigDecimal.valueOf(90.00));
        promoPriceM.setPromoItemList(Lists.newArrayList(promoItemM));
        return promoPriceM;
    }

    @Test
    public void test_null(){
        try(MockedStatic<ProductMPromoInfoUtils> productMPromoInfoUtilsMockedStatic = Mockito.mockStatic(ProductMPromoInfoUtils.class)){
            when(mockParam.getPlatform()).thenReturn(2);
            when(mockParam.getProductM()).thenReturn(mockProductM);
            when(mockProductM.getMarketPrice()).thenReturn(null);
            when(mockParam.getSalePrice()).thenReturn(null);
            productMPromoInfoUtilsMockedStatic.when(() -> ProductMPromoInfoUtils.getSecKillPromoPriceM(anyList(),anyString(),anyString(),anyInt(),anyInt())).thenReturn(null);
            DzTagVO dzTagVO = promoCodePriceBottomTagOpt.buildSecKillTag(mockParam, mockPromoPriceM, mockConfig);
            Assert.isNull(dzTagVO);
        }
    }
}