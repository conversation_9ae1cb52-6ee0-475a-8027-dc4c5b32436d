package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagsVP.Param;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeUnitePriceBottomTagsOptTest {

    @InjectMocks
    private PromoCodeUnitePriceBottomTagsOpt promoCodeUnitePriceBottomTagsOpt;

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private PromoCodeUnitePriceBottomTagsOpt.Config mockConfig;
    @Mock
    private PromoCodeUnitePriceBottomTagsOpt.TagStyleConfig mockTagStyleConfig;
    @Mock
    private ProductM mockProductM;
    @Mock
    private CardM mockCardM;
    @Mock
    private ProductPromoPriceM mockPromoPriceM;

    private MockedStatic<DzPromoUtils> dzPromoUtils;
    private MockedStatic<PriceUtils> priceUtils;
    private MockedStatic<MerchantMemberPromoUtils> merchantMemberPromoUtils;

    @Before
    public void setUp() {
        promoCodeUnitePriceBottomTagsOpt = new PromoCodeUnitePriceBottomTagsOpt();
        dzPromoUtils = Mockito.mockStatic(DzPromoUtils.class);
        priceUtils = Mockito.mockStatic(PriceUtils.class);
        merchantMemberPromoUtils = Mockito.mockStatic(MerchantMemberPromoUtils.class);

        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getCardM()).thenReturn(mockCardM);
        when(mockConfig.getTagStyleConfig()).thenReturn(mockTagStyleConfig);
        when(mockTagStyleConfig.getTextColor()).thenReturn("#FF0000");
        when(mockTagStyleConfig.getBackground()).thenReturn("#FFFFFF");
        when(mockTagStyleConfig.isHasBorder()).thenReturn(true);

        priceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(mockPromoPriceM);
    }

    @After
    public void tearDown() {
        dzPromoUtils.close();
        priceUtils.close();
        merchantMemberPromoUtils.close();
    }

    /**
     * 测试 compute 方法，当 productM 为 null 时
     */
    @Test
    public void testComputeProductMIsNull() {
        when(mockParam.getProductM()).thenReturn(null);
        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);
        assertNull("结果应为null", result);
    }

    /**
     * 测试 compute 方法，当 config 为 null 时
     */
    @Test
    public void testComputeConfigIsNull() {
        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, null);
        assertNull("结果应为null", result);
    }

    /**
     * 测试 compute 方法，当 tagStyleConfig 为 null 时
     */
    @Test
    public void testComputeTagStyleConfigIsNull() {
        when(mockConfig.getTagStyleConfig()).thenReturn(null);
        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);
        assertNull("结果应为null", result);
    }

    /**
     * 测试 compute 方法，当是团单多次卡优惠时
     */
    @Test
    public void testComputeTimesDeal() {
        when(mockProductM.isTimesDeal()).thenReturn(true);

        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName("团单多次卡优惠");
        List<DzPromoVO> dzPromoVOS = Lists.newArrayList(dzPromoVO);
        when(mockParam.getDzPromoVOS()).thenReturn(dzPromoVOS);

        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull("结果不应为null", result);
        assertEquals("结果应有1个标签", 1, result.size());
        assertEquals("标签名称应为'团单多次卡优惠'", "团单多次卡优惠", result.get(0).getName());
    }

    /**
     * 测试 compute 方法，当有会员价优惠时
     */
    @Test
    public void testComputeMerchantMemberPromo() {
        when(mockProductM.isTimesDeal()).thenReturn(false);

        merchantMemberPromoUtils.when(() -> MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(anyList())).thenReturn(true);

        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull("结果不应为null", result);
        assertEquals("结果应有1个标签", 1, result.size());
        assertEquals("标签名称应为'会员价'", "会员价", result.get(0).getName());
    }

    /**
     * 测试 compute 方法，当有神券价优惠时
     */
    @Test
    public void testComputeMagicalMemberCoupon() {
        when(mockProductM.isTimesDeal()).thenReturn(false);

        merchantMemberPromoUtils.when(() -> MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(anyList())).thenReturn(false);
        dzPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(true);

        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull("结果不应为null", result);
        assertEquals("结果应有1个标签", 1, result.size());
        assertEquals("标签名称应为'神券价'", "神券价", result.get(0).getName());
    }

    /**
     * 测试 compute 方法，当有普通折扣时
     */
    @Test
    public void testComputeNormalPromo() {
        when(mockProductM.isTimesDeal()).thenReturn(false);

        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName("满减优惠");
        List<DzPromoVO> dzPromoVOS = Lists.newArrayList(dzPromoVO);
        when(mockParam.getDzPromoVOS()).thenReturn(dzPromoVOS);

        merchantMemberPromoUtils.when(() -> MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(anyList())).thenReturn(false);
        dzPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(false);

        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull("结果不应为null", result);
        assertEquals("结果应有1个标签", 1, result.size());
        assertEquals("标签名称应为'满减优惠'", "满减优惠", result.get(0).getName());
    }

    /**
     * 测试 compute 方法，当没有任何优惠时
     */
    @Test
    public void testComputeNoPromo() {
        when(mockProductM.isTimesDeal()).thenReturn(false);
        when(mockParam.getDzPromoVOS()).thenReturn(null);

        merchantMemberPromoUtils.when(() -> MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(anyList())).thenReturn(false);
        dzPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(false);

        List<DzTagVO> result = promoCodeUnitePriceBottomTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNull("结果应为null", result);
    }

}