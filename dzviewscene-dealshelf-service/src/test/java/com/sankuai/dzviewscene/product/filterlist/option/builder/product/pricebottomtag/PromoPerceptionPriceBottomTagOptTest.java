package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo.MagicalMemberPromoTagBuildStrategy;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import java.lang.reflect.Constructor;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

@RunWith(MockitoJUnitRunner.class)
public class PromoPerceptionPriceBottomTagOptTest {

    @InjectMocks
    @Spy
    private PromoPerceptionPriceBottomTagOpt promoPerceptionPriceBottomTagOpt;

    @Mock
    private MagicalMemberPromoTagBuildStrategy magicalMemberPromoTagBuildStrategy;

    private ActivityCxt context;

    private ProductPriceBottomTagVP.Param param;

    private ProductM productM;

    private PromoPerceptionPriceBottomTagOpt.Config config;

    @Before
    public void setUp() throws Exception {
        initializeContext();
        initializeProductM();
        initializeParam();
    }

    private void initializeContext() {
        context = new ActivityCxt();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("userAgent", 200);
        context.setParameters(parameters);
    }

    private void initializeProductM() {
        productM = mock(ProductM.class);
    }

    private void initializeParam() throws Exception {
        // Use reflection to create an instance of Param
        Constructor<ProductPriceBottomTagVP.Param> constructor = ProductPriceBottomTagVP.Param.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        param = constructor.newInstance();
        param.setProductM(productM);
        param.setPlatform(1);
        config = new PromoPerceptionPriceBottomTagOpt.Config();
    }

    /**
     * Test case: when product is times deal, should return null
     * Expected: return null when isTimesDeal is true
     */
    @Test
    public void testComputeWhenProductIsTimesDeal() throws Throwable {
        // arrange
        when(productM.isTimesDeal()).thenReturn(true);
        // act
        List<DzTagVO> result = promoPerceptionPriceBottomTagOpt.compute(context, param, null);
        // assert
        assertNull("Should return null when product is times deal", result);
    }

    /**
     * Test case: when product is not times deal but no promo tag available
     * Expected: return null when final return is reached
     */
    @Test
    public void testComputeWhenNoPromoTagAvailable() throws Throwable {
        // arrange
        when(productM.isTimesDeal()).thenReturn(false);
        // Since we cannot mock the private method, we will test the behavior of compute
        // act
        List<DzTagVO> result = promoPerceptionPriceBottomTagOpt.compute(context, param, config);
        // assert
        assertNull("Should return null when no promo tag is available", result);
    }

    /**
     * Test case: when context is null
     * Expected: return null
     */
    @Test
    public void testComputeWhenContextIsNull() throws Throwable {
        // arrange
        context = null;
        // act
        List<DzTagVO> result = promoPerceptionPriceBottomTagOpt.compute(context, param, config);
        // assert
        assertNull("Should return null when context is null", result);
    }

    /**
     * Test case: when config is null
     * Expected: return null
     */
    @Test
    public void testComputeWhenConfigIsNull() throws Throwable {
        // act
        List<DzTagVO> result = promoPerceptionPriceBottomTagOpt.compute(context, param, config);
        // assert
        assertNull("Should return null when config is null", result);
    }
}
