package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AbstractTimesDealListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductListVP.Param param;

    @Mock
    private AbstractTimesDealListOpt abstractTimesDealListOpt;

    private void setupContextWithCurrentDealId(int currentDealId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("entityId", currentDealId);
    }

    private ProductM createProduct(int productId, boolean timesDealQueryFlag) {
        ProductM productM = new ProductM();
        productM.setProductId(productId);
        productM.setTimesDealQueryFlag(timesDealQueryFlag);
        return productM;
    }

    @Test
    public void testComputeWhenParamProductMSIsNull() throws Throwable {
        List<ProductM> result = abstractTimesDealListOpt.compute(context, param, null);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenFilterProductListIsEmpty() throws Throwable {
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(new ProductM());
        List<ProductM> result = abstractTimesDealListOpt.compute(context, param, null);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenCurrentProductNotInFilterProductList() throws Throwable {
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(createProduct(1, true));
        // Current deal ID does not match any product
        setupContextWithCurrentDealId(2);
        List<ProductM> result = abstractTimesDealListOpt.compute(context, param, null);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenCurrentProductInFilterProductListAndTimesDeAndSingleDealExist() throws Throwable {
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(createProduct(1, true));
        // Current deal ID matches the product
        setupContextWithCurrentDealId(1);
        // Mock the expected behavior
        when(abstractTimesDealListOpt.compute(context, param, null)).thenReturn(productMS);
        List<ProductM> result = abstractTimesDealListOpt.compute(context, param, null);
        assertEquals(1, result.size());
    }

    @Test
    public void testComputeWhenResultSizeLessThanOne() throws Throwable {
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(createProduct(1, true));
        // Ensure the current product is in the list
        setupContextWithCurrentDealId(1);
        // Mock the expected behavior
        when(abstractTimesDealListOpt.compute(context, param, null)).thenReturn(productMS);
        List<ProductM> result = abstractTimesDealListOpt.compute(context, param, null);
        assertEquals(1, result.size());
    }
}
