package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.product.filterlist.utils.SkuItemUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractTimesDealListOpt_GetProductCategoryListTest {

    @Mock
    private ProductM productM;

    @Mock
    private SkuItemDto skuItemDto;

    @InjectMocks
    private AbstractTimesDealListOpt abstractTimesDealListOpt = new AbstractTimesDealListOpt() {

        @Override
        protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
            return null;
        }
    };

    @Test
    public void testGetProductCategoryListSkuItemListIsEmpty() throws Throwable {
        when(productM.getAttr(anyString())).thenReturn(null);
        List<Long> result = abstractTimesDealListOpt.getProductCategoryList(productM);
        assertEquals(0, result.size());
    }
}
