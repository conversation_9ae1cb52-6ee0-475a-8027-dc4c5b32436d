package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import static org.mockito.Mockito.when;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class AbstractTimesDealListOpt_GetTimesDealListTest {

    private AbstractTimesDealListOpt abstractTimesDealListOpt;

    @Test
    public void testGetTimesDealListWhenListIsEmpty() throws Throwable {
        AbstractTimesDealListOpt abstractTimesDealListOpt = Mockito.mock(AbstractTimesDealListOpt.class, Mockito.CALLS_REAL_METHODS);
        List<ProductM> result = abstractTimesDealListOpt.getTimesDealList(new ArrayList<>(), 1);
        assertTrue("The result should be empty when the input list is empty", result.isEmpty());
    }

    @Test
    public void testGetTimesDealListWhenNoProductIsTimesDealQueryFlag() throws Throwable {
        AbstractTimesDealListOpt abstractTimesDealListOpt = Mockito.mock(AbstractTimesDealListOpt.class, Mockito.CALLS_REAL_METHODS);
        List<ProductM> list = new ArrayList<>();
        ProductM product = Mockito.mock(ProductM.class);
        when(product.isTimesDealQueryFlag()).thenReturn(false);
        list.add(product);
        List<ProductM> result = abstractTimesDealListOpt.getTimesDealList(list, 1);
        assertTrue("The result should be empty when no product has the timesDealQueryFlag set", result.isEmpty());
    }

    @Test
    public void testGetTimesDealListWhenNoProductIsTimesDeal() throws Throwable {
        AbstractTimesDealListOpt abstractTimesDealListOpt = Mockito.mock(AbstractTimesDealListOpt.class, Mockito.CALLS_REAL_METHODS);
        List<ProductM> list = new ArrayList<>();
        ProductM product = Mockito.mock(ProductM.class);
        when(product.isTimesDealQueryFlag()).thenReturn(true);
        when(product.isTimesDeal()).thenReturn(false);
        list.add(product);
        List<ProductM> result = abstractTimesDealListOpt.getTimesDealList(list, 1);
        assertTrue("The result should be empty when no product is a times deal", result.isEmpty());
    }

    @Test
    public void testGetTimesDealListWhenAllProductsAreTimesDeal() throws Throwable {
        AbstractTimesDealListOpt abstractTimesDealListOpt = Mockito.mock(AbstractTimesDealListOpt.class, Mockito.CALLS_REAL_METHODS);
        List<ProductM> list = new ArrayList<>();
        ProductM product = Mockito.mock(ProductM.class);
        when(product.isTimesDealQueryFlag()).thenReturn(true);
        when(product.isTimesDeal()).thenReturn(true);
        list.add(product);
        List<ProductM> result = abstractTimesDealListOpt.getTimesDealList(list, 1);
        assertTrue("The result should be empty when all products are times deals", result.isEmpty());
    }

    @Test
    public void testGetTimesDealListWhenProductsAreTimesDealAndNotTimesDealAndProductIdEqualsCurrentDealId() throws Throwable {
        AbstractTimesDealListOpt abstractTimesDealListOpt = Mockito.mock(AbstractTimesDealListOpt.class, Mockito.CALLS_REAL_METHODS);
        List<ProductM> list = new ArrayList<>();
        ProductM product1 = Mockito.mock(ProductM.class);
        ProductM product2 = Mockito.mock(ProductM.class);
        when(product1.isTimesDealQueryFlag()).thenReturn(true);
        when(product1.isTimesDeal()).thenReturn(true);
        when(product1.getProductId()).thenReturn(1);
        when(product2.isTimesDealQueryFlag()).thenReturn(true);
        when(product2.isTimesDeal()).thenReturn(false);
        when(product2.getProductId()).thenReturn(1);
        list.add(product1);
        list.add(product2);
        List<ProductM> result = abstractTimesDealListOpt.getTimesDealList(list, 1);
        assertTrue("The result should be empty when products match the currentDealId", result.isEmpty());
    }
}
