package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.runner.RunWith;
import java.math.BigDecimal;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractTimesDealListOpt_IsValidTest {

    private AbstractTimesDealListOpt abstractTimesDealListOpt = new AbstractTimesDealListOpt() {

        @Override
        protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
            return null;
        }
    };

    @Test
    public void testIsValidProductIsNull() throws Throwable {
        assertFalse(abstractTimesDealListOpt.isValid(null));
    }

    @Test
    public void testIsValidSalePriceIsNull() throws Throwable {
        ProductM productM = new ProductM();
        assertFalse(abstractTimesDealListOpt.isValid(productM));
    }

    @Test
    public void testIsValidProductTypeIsNotDeal() throws Throwable {
        ProductM productM = new ProductM();
        // Corrected
        productM.setSalePrice(new BigDecimal(100));
        assertFalse(abstractTimesDealListOpt.isValid(productM));
    }

    @Test
    public void testIsValidDealStatusAttrIsNotTrue() throws Throwable {
        ProductM productM = new ProductM();
        // Corrected
        productM.setSalePrice(new BigDecimal(100));
        productM.setAttr("productType", "deal");
        assertFalse(abstractTimesDealListOpt.isValid(productM));
    }

    @Test
    public void testIsValidAttrSearchHiddenStatusIsNotFalse() throws Throwable {
        ProductM productM = new ProductM();
        // Corrected
        productM.setSalePrice(new BigDecimal(100));
        productM.setAttr("productType", "deal");
        productM.setAttr("dealStatusAttr", "true");
        assertFalse(abstractTimesDealListOpt.isValid(productM));
    }

    @Test
    public void testIsValidSaleIsNull() throws Throwable {
        ProductM productM = new ProductM();
        // Corrected
        productM.setSalePrice(new BigDecimal(100));
        productM.setAttr("productType", "deal");
        productM.setAttr("dealStatusAttr", "true");
        productM.setAttr("attr_search_hidden_status", "false");
        assertFalse(abstractTimesDealListOpt.isValid(productM));
    }

    @Test
    public void testIsValidAllRulesAreMet() throws Throwable {
        ProductM productM = new ProductM();
        // Corrected
        productM.setSalePrice(new BigDecimal(100));
        productM.setAttr("productType", "deal");
        productM.setAttr("dealStatusAttr", "true");
        productM.setAttr("attr_search_hidden_status", "false");
        ProductSaleM sale = new ProductSaleM();
        productM.setSale(sale);
        assertTrue(abstractTimesDealListOpt.isValid(productM));
    }
}
