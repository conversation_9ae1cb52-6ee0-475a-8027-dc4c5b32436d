package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Before;

public class BarSimilarGuessJoyFilterAcsOptTest {

    private BarSimilarGuessJoyFilterAcsOpt barSimilarGuessJoyFilterAcsOpt;

    private ActivityCxt context;

    private ProductListVP.Param param;

    private BarSimilarGuessJoyFilterAcsOpt.Config config;

    private BarSimilarGuessJoyFilterAcsOpt initializeClassUnderTest() {
        return new BarSimilarGuessJoyFilterAcsOpt();
    }

    private ActivityCxt mockActivityCxt() {
        return mock(ActivityCxt.class);
    }

    private ProductListVP.Param mockParam(List<ProductM> productMS) {
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        when(param.getProductMS()).thenReturn(productMS);
        return param;
    }

    private BarSimilarGuessJoyFilterAcsOpt.Config mockConfig(int limit) {
        BarSimilarGuessJoyFilterAcsOpt.Config config = mock(BarSimilarGuessJoyFilterAcsOpt.Config.class);
        when(config.getLimit()).thenReturn(limit);
        return config;
    }

    @Test
    public void testComputeWhenProductMSIsEmpty() throws Throwable {
        BarSimilarGuessJoyFilterAcsOpt classUnderTest = initializeClassUnderTest();
        ActivityCxt context = mockActivityCxt();
        ProductListVP.Param param = mockParam(new ArrayList<>());
        BarSimilarGuessJoyFilterAcsOpt.Config config = mockConfig(3);
        List<ProductM> result = classUnderTest.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWhenProductMSSizeIsOne() throws Throwable {
        BarSimilarGuessJoyFilterAcsOpt classUnderTest = initializeClassUnderTest();
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(mock(ProductM.class));
        ActivityCxt context = mockActivityCxt();
        ProductListVP.Param param = mockParam(productMS);
        BarSimilarGuessJoyFilterAcsOpt.Config config = mockConfig(3);
        List<ProductM> result = classUnderTest.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWhenProductMSSizeIsGreaterThanOneButFilteredResultIsEmpty() throws Throwable {
        BarSimilarGuessJoyFilterAcsOpt classUnderTest = initializeClassUnderTest();
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(mock(ProductM.class));
        productMS.add(mock(ProductM.class));
        ActivityCxt context = mockActivityCxt();
        ProductListVP.Param param = mockParam(productMS);
        BarSimilarGuessJoyFilterAcsOpt.Config config = mockConfig(3);
        List<ProductM> result = classUnderTest.compute(context, param, config);
        assertNull(result);
    }
}
