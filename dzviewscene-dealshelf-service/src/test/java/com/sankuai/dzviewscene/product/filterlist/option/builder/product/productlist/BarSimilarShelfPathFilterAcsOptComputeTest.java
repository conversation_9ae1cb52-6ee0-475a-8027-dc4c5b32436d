package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.api.annotation.Param;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.BarSimilarShelfPathFilterAcsOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class BarSimilarShelfPathFilterAcsOptComputeTest {

    private BarSimilarShelfPathFilterAcsOpt opt;

    private ActivityCxt context;

    private ProductListVP.Param param;

    private Config config;

    /**
     * Test case: When entityId is not found in product list
     * Expected: Should return null
     */
    @Test
    public void testComputeWhenEntityIdNotFound() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        Config config = mock(Config.class);
        // arrange
        List<ProductM> products = new ArrayList<>();
        ProductM product = new ProductM();
        // Different from entityId
        product.setProductId(456);
        products.add(product);
        when(param.getProductMS()).thenReturn(products);
        when(config.getLimit()).thenReturn(5);
        // act
        List<ProductM> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test case: When product list is empty
     * Expected: Should return null
     */
    @Test
    public void testComputeWithEmptyProductList() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        Config config = mock(Config.class);
        // arrange
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        when(config.getLimit()).thenReturn(5);
        // act
        List<ProductM> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    /**
     * Test case: When product list has only one item matching entityId
     * Expected: Should return null
     */
    @Test
    public void testComputeWithSingleProduct() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        Config config = mock(Config.class);
        // arrange
        List<ProductM> products = new ArrayList<>();
        ProductM product = new ProductM();
        product.setProductId(123);
        product.setTitle("Test Product");
        List<AttrM> attrs = new ArrayList<>();
        AttrM serviceAttr = new AttrM();
        serviceAttr.setName("service_type");
        serviceAttr.setValue("门票入场券");
        attrs.add(serviceAttr);
        product.setExtAttrs(attrs);
        products.add(product);
        when(param.getProductMS()).thenReturn(products);
        when(config.getLimit()).thenReturn(5);
        // act
        List<ProductM> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }
}
