package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;


@RunWith(MockitoJUnitRunner.class)
public class BarSimilarShelfPathFilterAcsOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductListVP.Param param;

    @Mock
    private BarSimilarShelfPathFilterAcsOpt.Config config;

    /**
     * Tests the exception scenario.
     */
    @Test(expected = NullPointerException.class)
    public void testComputeException() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        // No need to setup mocks as we are passing null to compute method to trigger NullPointerException
        opt.compute(null, null, null);
    }

    private BarSimilarShelfPathFilterAcsOpt.Config createMockConfig(int limit) {
        BarSimilarShelfPathFilterAcsOpt.Config config = mock(BarSimilarShelfPathFilterAcsOpt.Config.class);
        when(config.getLimit()).thenReturn(limit);
        return config;
    }

    @Test
    public void testComputeWithEmptyProductList() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        List<ProductM> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWithNoMatchingTopProduct() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);
        List<ProductM> products = new ArrayList<>();
        ProductM product = new ProductM();
        product.setProductId(456);
        products.add(product);
        when(param.getProductMS()).thenReturn(products);
        List<ProductM> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWithMultipleProducts() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);
        List<ProductM> products = new ArrayList<>();
        // Top product
        ProductM topProduct = new ProductM();
        topProduct.setProductId(123);
        topProduct.setTitle("Top Product");
        List<AttrM> topAttrs = new ArrayList<>();
        AttrM topServiceAttr = new AttrM();
        topServiceAttr.setName("service_type");
        topServiceAttr.setValue("bar_service");
        topAttrs.add(topServiceAttr);
        topProduct.setExtAttrs(topAttrs);
        products.add(topProduct);
        // Other products
        for (int i = 1; i <= 3; i++) {
            ProductM product = new ProductM();
            product.setProductId(100 + i);
            product.setTitle("Product " + i);
            List<AttrM> attrs = new ArrayList<>();
            AttrM serviceAttr = new AttrM();
            serviceAttr.setName("service_type");
            serviceAttr.setValue("bar_service");
            attrs.add(serviceAttr);
            product.setExtAttrs(attrs);
            products.add(product);
        }
        when(param.getProductMS()).thenReturn(products);
        List<ProductM> result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals(123, result.get(0).getProductId());
    }

    @Test
    public void testComputeWithLimitConfiguration() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(3);
        List<ProductM> products = new ArrayList<>();
        // Top product
        ProductM topProduct = new ProductM();
        topProduct.setProductId(123);
        topProduct.setTitle("Top Product");
        List<AttrM> topAttrs = new ArrayList<>();
        AttrM topServiceAttr = new AttrM();
        topServiceAttr.setName("service_type");
        topServiceAttr.setValue("bar_service");
        topAttrs.add(topServiceAttr);
        topProduct.setExtAttrs(topAttrs);
        products.add(topProduct);
        // Other products
        for (int i = 1; i <= 10; i++) {
            ProductM product = new ProductM();
            product.setProductId(100 + i);
            product.setTitle("Product " + i);
            List<AttrM> attrs = new ArrayList<>();
            AttrM serviceAttr = new AttrM();
            serviceAttr.setName("service_type");
            serviceAttr.setValue("bar_service");
            attrs.add(serviceAttr);
            product.setExtAttrs(attrs);
            products.add(product);
        }
        when(param.getProductMS()).thenReturn(products);
        List<ProductM> result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(123, result.get(0).getProductId());
    }

    @Test
    public void testComputeFiltersProductsWithoutTitle() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);
        List<ProductM> products = new ArrayList<>();
        // Valid product with title (should be kept)
        ProductM validProduct = new ProductM();
        // Must match entityId
        validProduct.setProductId(123);
        validProduct.setTitle("Valid Product");
        List<AttrM> validAttrs = new ArrayList<>();
        AttrM validServiceAttr = new AttrM();
        validServiceAttr.setName("service_type");
        // Not "门票入场券"
        validServiceAttr.setValue("bar_service");
        validAttrs.add(validServiceAttr);
        validProduct.setExtAttrs(validAttrs);
        products.add(validProduct);
        // Product with null title (should be filtered out)
        ProductM nullTitleProduct = new ProductM();
        nullTitleProduct.setProductId(124);
        nullTitleProduct.setTitle(null);
        List<AttrM> nullTitleAttrs = new ArrayList<>();
        AttrM nullTitleServiceAttr = new AttrM();
        nullTitleServiceAttr.setName("service_type");
        nullTitleServiceAttr.setValue("bar_service");
        nullTitleAttrs.add(nullTitleServiceAttr);
        nullTitleProduct.setExtAttrs(nullTitleAttrs);
        products.add(nullTitleProduct);
        // Product with empty title (should be filtered out)
        ProductM emptyTitleProduct = new ProductM();
        emptyTitleProduct.setProductId(125);
        emptyTitleProduct.setTitle("");
        List<AttrM> emptyTitleAttrs = new ArrayList<>();
        AttrM emptyTitleServiceAttr = new AttrM();
        emptyTitleServiceAttr.setName("service_type");
        emptyTitleServiceAttr.setValue("bar_service");
        emptyTitleAttrs.add(emptyTitleServiceAttr);
        emptyTitleProduct.setExtAttrs(emptyTitleAttrs);
        products.add(emptyTitleProduct);
        when(param.getProductMS()).thenReturn(products);
        List<ProductM> result = opt.compute(context, param, config);
        // 由于过滤后只剩下一个产品，topAndFilterResult会返回null
        assertNull("Result should be null when only one valid product remains after filtering", result);
    }

    @Test
    public void testComputeSortsProductsCorrectly() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);
        List<ProductM> products = new ArrayList<>();
        // Top product
        ProductM topProduct = new ProductM();
        topProduct.setProductId(123);
        topProduct.setTitle("Top Product");
        List<AttrM> topAttrs = new ArrayList<>();
        AttrM topServiceAttr = new AttrM();
        topServiceAttr.setName("service_type");
        topServiceAttr.setValue("bar_service");
        topAttrs.add(topServiceAttr);
        AttrM topPkgAttr = new AttrM();
        topPkgAttr.setName("number_of_pkg");
        topPkgAttr.setValue("2");
        topAttrs.add(topPkgAttr);
        topProduct.setExtAttrs(topAttrs);
        products.add(topProduct);
        // Product with same package number (should sort by price)
        ProductM samePkgProduct = new ProductM();
        samePkgProduct.setProductId(124);
        samePkgProduct.setTitle("Same Pkg Product");
        List<AttrM> samePkgAttrs = new ArrayList<>();
        AttrM samePkgServiceAttr = new AttrM();
        samePkgServiceAttr.setName("service_type");
        samePkgServiceAttr.setValue("bar_service");
        samePkgAttrs.add(samePkgServiceAttr);
        AttrM samePkgPkgAttr = new AttrM();
        samePkgPkgAttr.setName("number_of_pkg");
        samePkgPkgAttr.setValue("2");
        samePkgAttrs.add(samePkgPkgAttr);
        samePkgProduct.setExtAttrs(samePkgAttrs);
        products.add(samePkgProduct);
        // Product with different package number
        ProductM diffPkgProduct = new ProductM();
        diffPkgProduct.setProductId(125);
        diffPkgProduct.setTitle("Diff Pkg Product");
        List<AttrM> diffPkgAttrs = new ArrayList<>();
        AttrM diffPkgServiceAttr = new AttrM();
        diffPkgServiceAttr.setName("service_type");
        diffPkgServiceAttr.setValue("bar_service");
        diffPkgAttrs.add(diffPkgServiceAttr);
        AttrM diffPkgPkgAttr = new AttrM();
        diffPkgPkgAttr.setName("number_of_pkg");
        diffPkgPkgAttr.setValue("1");
        diffPkgAttrs.add(diffPkgPkgAttr);
        diffPkgProduct.setExtAttrs(diffPkgAttrs);
        products.add(diffPkgProduct);
        when(param.getProductMS()).thenReturn(products);
        List<ProductM> result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(123, result.get(0).getProductId());
        assertEquals(124, result.get(1).getProductId());
        assertEquals(125, result.get(2).getProductId());
    }

    @Test
    public void testComputeWithNullContextParams() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);
        List<ProductM> products = new ArrayList<>();
        ProductM product = new ProductM();
        product.setProductId(123);
        product.setTitle("Test Product");
        products.add(product);
        when(param.getProductMS()).thenReturn(products);
        List<ProductM> result = opt.compute(context, param, config);
        assertNull(result);
    }

    /**
     * 测试getTitle方法 - 当dealStructContent属性为空时
     */
    @Test
    public void testGetTitleWithNoDealStructContent() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);

        // 创建两个产品，确保有多个产品以便compute方法返回非null结果
        List<ProductM> products = new ArrayList<>();

        // 产品1 - 主产品，没有dealStructContent属性
        ProductM product1 = new ProductM();
        product1.setProductId(123);
        product1.setTitle("Original Title 1");
        List<AttrM> attrs1 = new ArrayList<>();
        AttrM serviceAttr1 = new AttrM();
        serviceAttr1.setName("service_type");
        serviceAttr1.setValue("bar_service");
        attrs1.add(serviceAttr1);
        // 不添加dealStructContent属性
        product1.setExtAttrs(attrs1);
        products.add(product1);

        // 产品2 - 次要产品
        ProductM product2 = new ProductM();
        product2.setProductId(456);
        product2.setTitle("Original Title 2");
        List<AttrM> attrs2 = new ArrayList<>();
        AttrM serviceAttr2 = new AttrM();
        serviceAttr2.setName("service_type");
        serviceAttr2.setValue("bar_service");
        attrs2.add(serviceAttr2);
        product2.setExtAttrs(attrs2);
        products.add(product2);

        when(param.getProductMS()).thenReturn(products);

        List<ProductM> result = opt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Original Title 1", result.get(0).getTitle()); // 标题应保持不变
    }

    /**
     * 测试getTitle方法 - 使用洋酒类别
     */
    @Test
    public void testGetTitleWithYangjiuCategory() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);

        // 创建两个产品，确保有多个产品以便compute方法返回非null结果
        List<ProductM> products = new ArrayList<>();

        // 产品1 - 主产品，包含洋酒类别
        ProductM product1 = new ProductM();
        product1.setProductId(123);
        product1.setTitle("Original Title 1");
        List<AttrM> attrs1 = new ArrayList<>();

        // 添加service_type属性
        AttrM serviceAttr1 = new AttrM();
        serviceAttr1.setName("service_type");
        serviceAttr1.setValue("bar_service");
        attrs1.add(serviceAttr1);

        // 添加dealStructContent属性，包含洋酒类别(2104534)
        AttrM dealStructAttr1 = new AttrM();
        dealStructAttr1.setName("dealStructContent");
        String dealStructContent = "{\"stract\":\"{\\\"skuUniStructuredDto\\\":{\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"attrItems\\\":[{\\\"attrName\\\":\\\"skuCateId\\\",\\\"attrValue\\\":\\\"2104534\\\"}],\\\"marketPrice\\\":100.00}]}]}}\"}";
        dealStructAttr1.setValue(dealStructContent);
        attrs1.add(dealStructAttr1);

        product1.setExtAttrs(attrs1);
        products.add(product1);

        // 产品2 - 次要产品
        ProductM product2 = new ProductM();
        product2.setProductId(456);
        product2.setTitle("Original Title 2");
        List<AttrM> attrs2 = new ArrayList<>();
        AttrM serviceAttr2 = new AttrM();
        serviceAttr2.setName("service_type");
        serviceAttr2.setValue("bar_service");
        attrs2.add(serviceAttr2);
        product2.setExtAttrs(attrs2);
        products.add(product2);

        when(param.getProductMS()).thenReturn(products);

        // 使用spy来监控getTitle方法的调用
        BarSimilarShelfPathFilterAcsOpt spyOpt = spy(opt);


        List<ProductM> result = spyOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * 测试getTitle方法 - 使用啤酒类别
     */
    @Test
    public void testGetTitleWithPijiuCategory() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);

        // 创建两个产品，确保有多个产品以便compute方法返回非null结果
        List<ProductM> products = new ArrayList<>();

        // 产品1 - 主产品，包含啤酒类别
        ProductM product1 = new ProductM();
        product1.setProductId(123);
        product1.setTitle("Original Title 1");
        List<AttrM> attrs1 = new ArrayList<>();

        // 添加service_type属性
        AttrM serviceAttr1 = new AttrM();
        serviceAttr1.setName("service_type");
        serviceAttr1.setValue("bar_service");
        attrs1.add(serviceAttr1);

        // 添加dealStructContent属性，包含啤酒类别(2104532)
        AttrM dealStructAttr1 = new AttrM();
        dealStructAttr1.setName("dealStructContent");
        String dealStructContent = "{\"stract\":\"{\\\"skuUniStructuredDto\\\":{\\\"optionalGroups\\\":[{\\\"skuItems\\\":[{\\\"attrItems\\\":[{\\\"attrName\\\":\\\"skuCateId\\\",\\\"attrValue\\\":\\\"2104532\\\"}],\\\"marketPrice\\\":50.00}]}]}}\"}";
        dealStructAttr1.setValue(dealStructContent);
        attrs1.add(dealStructAttr1);

        product1.setExtAttrs(attrs1);
        products.add(product1);

        // 产品2 - 次要产品
        ProductM product2 = new ProductM();
        product2.setProductId(456);
        product2.setTitle("Original Title 2");
        List<AttrM> attrs2 = new ArrayList<>();
        AttrM serviceAttr2 = new AttrM();
        serviceAttr2.setName("service_type");
        serviceAttr2.setValue("bar_service");
        attrs2.add(serviceAttr2);
        product2.setExtAttrs(attrs2);
        products.add(product2);

        when(param.getProductMS()).thenReturn(products);

        // 使用spy来监控getTitle方法的调用
        BarSimilarShelfPathFilterAcsOpt spyOpt = spy(opt);

        List<ProductM> result = spyOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * 测试getTitle方法 - 使用其他类型酒品类别
     */
    @Test
    public void testGetTitleWithOtherAlcoholCategory() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);

        // 创建两个产品，确保有多个产品以便compute方法返回非null结果
        List<ProductM> products = new ArrayList<>();

        // 产品1 - 主产品，包含其他类型酒品类别
        ProductM product1 = new ProductM();
        product1.setProductId(123);
        product1.setTitle("Original Title 1");
        List<AttrM> attrs1 = new ArrayList<>();

        // 添加service_type属性
        AttrM serviceAttr1 = new AttrM();
        serviceAttr1.setName("service_type");
        serviceAttr1.setValue("bar_service");
        attrs1.add(serviceAttr1);

        // 添加dealStructContent属性，包含其他类型酒品类别(2104538)
        AttrM dealStructAttr1 = new AttrM();
        dealStructAttr1.setName("dealStructContent");
        String dealStructContent = "{\"stract\":\"{\\\"skuUniStructuredDto\\\":{\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"attrItems\\\":[{\\\"attrName\\\":\\\"skuCateId\\\",\\\"attrValue\\\":\\\"2104538\\\"}],\\\"marketPrice\\\":75.00}]}]}}\"}";
        dealStructAttr1.setValue(dealStructContent);
        attrs1.add(dealStructAttr1);

        product1.setExtAttrs(attrs1);
        products.add(product1);

        // 产品2 - 次要产品
        ProductM product2 = new ProductM();
        product2.setProductId(456);
        product2.setTitle("Original Title 2");
        List<AttrM> attrs2 = new ArrayList<>();
        AttrM serviceAttr2 = new AttrM();
        serviceAttr2.setName("service_type");
        serviceAttr2.setValue("bar_service");
        attrs2.add(serviceAttr2);
        product2.setExtAttrs(attrs2);
        products.add(product2);

        when(param.getProductMS()).thenReturn(products);

        // 使用spy来监控getTitle方法的调用
        BarSimilarShelfPathFilterAcsOpt spyOpt = spy(opt);

        List<ProductM> result = spyOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * 测试getTitle方法 - 多个酒水类别，选择价格最高的
     */
    @Test
    public void testGetTitleWithMultipleAlcoholCategories() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);

        // 创建两个产品，确保有多个产品以便compute方法返回非null结果
        List<ProductM> products = new ArrayList<>();

        // 产品1 - 主产品，包含多个酒水类别
        ProductM product1 = new ProductM();
        product1.setProductId(123);
        product1.setTitle("Original Title 1");
        List<AttrM> attrs1 = new ArrayList<>();

        // 添加service_type属性
        AttrM serviceAttr1 = new AttrM();
        serviceAttr1.setName("service_type");
        serviceAttr1.setValue("bar_service");
        attrs1.add(serviceAttr1);

        // 添加dealStructContent属性，包含多个酒水类别，洋酒(2104534)价格最高
        AttrM dealStructAttr1 = new AttrM();
        dealStructAttr1.setName("dealStructContent");
        String dealStructContent = "{\"stract\":\"{\\\"skuUniStructuredDto\\\":{\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"attrItems\\\":[{\\\"attrName\\\":\\\"skuCateId\\\",\\\"attrValue\\\":\\\"2104534\\\"}],\\\"marketPrice\\\":200.00},{\\\"attrItems\\\":[{\\\"attrName\\\":\\\"skuCateId\\\",\\\"attrValue\\\":\\\"2104532\\\"}],\\\"marketPrice\\\":50.00}]}]}}\"}";
        dealStructAttr1.setValue(dealStructContent);
        attrs1.add(dealStructAttr1);

        product1.setExtAttrs(attrs1);
        products.add(product1);

        // 产品2 - 次要产品
        ProductM product2 = new ProductM();
        product2.setProductId(456);
        product2.setTitle("Original Title 2");
        List<AttrM> attrs2 = new ArrayList<>();
        AttrM serviceAttr2 = new AttrM();
        serviceAttr2.setName("service_type");
        serviceAttr2.setValue("bar_service");
        attrs2.add(serviceAttr2);
        product2.setExtAttrs(attrs2);
        products.add(product2);

        when(param.getProductMS()).thenReturn(products);

        // 使用spy来监控getTitle方法的调用
        BarSimilarShelfPathFilterAcsOpt spyOpt = spy(opt);

        List<ProductM> result = spyOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * 测试getTitle方法 - 空属性项
     */
    @Test
    public void testGetTitleWithEmptyAttrItems() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);

        // 创建两个产品，确保有多个产品以便compute方法返回非null结果
        List<ProductM> products = new ArrayList<>();

        // 产品1 - 主产品，包含空属性项
        ProductM product1 = new ProductM();
        product1.setProductId(123);
        product1.setTitle("Original Title 1");
        List<AttrM> attrs1 = new ArrayList<>();

        // 添加service_type属性
        AttrM serviceAttr1 = new AttrM();
        serviceAttr1.setName("service_type");
        serviceAttr1.setValue("bar_service");
        attrs1.add(serviceAttr1);

        // 添加dealStructContent属性，包含空属性项
        AttrM dealStructAttr1 = new AttrM();
        dealStructAttr1.setName("dealStructContent");
        String dealStructContent = "{\"stract\":\"{\\\"skuUniStructuredDto\\\":{\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"attrItems\\\":[],\\\"marketPrice\\\":100.00}]}]}}\"}";
        dealStructAttr1.setValue(dealStructContent);
        attrs1.add(dealStructAttr1);

        product1.setExtAttrs(attrs1);
        products.add(product1);

        // 产品2 - 次要产品
        ProductM product2 = new ProductM();
        product2.setProductId(456);
        product2.setTitle("Original Title 2");
        List<AttrM> attrs2 = new ArrayList<>();
        AttrM serviceAttr2 = new AttrM();
        serviceAttr2.setName("service_type");
        serviceAttr2.setValue("bar_service");
        attrs2.add(serviceAttr2);
        product2.setExtAttrs(attrs2);
        products.add(product2);

        when(param.getProductMS()).thenReturn(products);

        // 使用spy来监控getTitle方法的调用
        BarSimilarShelfPathFilterAcsOpt spyOpt = spy(opt);

        List<ProductM> result = spyOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        // 由于我们模拟了getDealDetailModel返回null，所以标题应保持不变
        assertEquals("Original Title 1", result.get(0).getTitle());
    }

    /**
     * 测试getTitle方法 - 没有找到酒水类别
     */
    @Test
    public void testGetTitleWithNoAlcoholCategory() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> params = new HashMap<>();
        params.put(PmfConstants.Params.entityId, 123);
        context.setParameters(params);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        BarSimilarShelfPathFilterAcsOpt.Config config = createMockConfig(5);

        // 创建两个产品，确保有多个产品以便compute方法返回非null结果
        List<ProductM> products = new ArrayList<>();

        // 产品1 - 主产品，包含不存在的类别ID
        ProductM product1 = new ProductM();
        product1.setProductId(123);
        product1.setTitle("Original Title 1");
        List<AttrM> attrs1 = new ArrayList<>();

        // 添加service_type属性
        AttrM serviceAttr1 = new AttrM();
        serviceAttr1.setName("service_type");
        serviceAttr1.setValue("bar_service");
        attrs1.add(serviceAttr1);

        // 添加dealStructContent属性，包含不存在的类别ID
        AttrM dealStructAttr1 = new AttrM();
        dealStructAttr1.setName("dealStructContent");
        String dealStructContent = "{\"stract\":\"{\\\"skuUniStructuredDto\\\":{\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"attrItems\\\":[{\\\"attrName\\\":\\\"skuCateId\\\",\\\"attrValue\\\":\\\"9999999\\\"}],\\\"marketPrice\\\":100.00}]}]}}\"}";
        dealStructAttr1.setValue(dealStructContent);
        attrs1.add(dealStructAttr1);

        product1.setExtAttrs(attrs1);
        products.add(product1);

        // 产品2 - 次要产品
        ProductM product2 = new ProductM();
        product2.setProductId(456);
        product2.setTitle("Original Title 2");
        List<AttrM> attrs2 = new ArrayList<>();
        AttrM serviceAttr2 = new AttrM();
        serviceAttr2.setName("service_type");
        serviceAttr2.setValue("bar_service");
        attrs2.add(serviceAttr2);
        product2.setExtAttrs(attrs2);
        products.add(product2);

        when(param.getProductMS()).thenReturn(products);

        // 使用spy来监控getTitle方法的调用
        BarSimilarShelfPathFilterAcsOpt spyOpt = spy(opt);

        // 模拟getDealDetailModel方法的行为

        List<ProductM> result = spyOpt.compute(context, param, config);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        // 由于我们模拟了getDealDetailModel返回null，所以标题应保持不变
        assertEquals("Original Title 1", result.get(0).getTitle());
    }
}
