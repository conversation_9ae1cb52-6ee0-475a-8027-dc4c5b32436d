package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.model.AvailableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.CycleAvailableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.UseRuleM;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Comparator;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;

/**
 * @author: created by hang.yu on 2024/4/12 11:32
 */
@RunWith(MockitoJUnitRunner.class)
public class BathSimilarListOptTest extends SimilarListOptTest {

    @InjectMocks
    private BathSimilarListOpt bathSimilarListOpt;


    @Test
    public void getSimilarDealList() {
        List<ProductM> similarDealList = bathSimilarListOpt.getSimilarDealList(getShelf(), getProduct("浴资票"), getKtvProductList());
        Assert.assertNotNull(similarDealList);

        similarDealList = bathSimilarListOpt.getSimilarDealList(getGuess(), getProduct("浴资票"), getKtvProductList());
        Assert.assertNotNull(similarDealList);
    }

    @Test
    public void getGuessList() {
        List<ProductM> list = bathSimilarListOpt.getGuessList(getBathProductList(), getProduct("浴资票"));
        Assert.assertNotNull(list);
    }

    @Test
    public void filterFeeServiceType() {
        boolean result = bathSimilarListOpt.filterFeeServiceType(getProduct("浴资票"));
        Assert.assertTrue(result);
    }

    @Test
    public void filterInStoreServiceType() {
        boolean result = bathSimilarListOpt.filterInStoreServiceType(getProduct("店内服务"));
        Assert.assertTrue(result);
    }

    @Test
    public void weekAllDaysAvailable() {
        boolean b = bathSimilarListOpt.weekAllDaysAvailable(buildAvailableDateM());
        Assert.assertFalse(b);
    }

    @Test
    public void repeatDayAvailable() {
        boolean result = bathSimilarListOpt.repeatDayAvailable(buildAvailableDateM(), buildAvailableDateM());
        Assert.assertTrue(result);
    }

    @Test
    public void getAvailableDays() {

        Set<Integer> availableDays = bathSimilarListOpt.getAvailableDays(null);
        Assert.assertNotNull(availableDays);

        List<CycleAvailableDateM> list = getCycleAvailableDateMS();
        availableDays = bathSimilarListOpt.getAvailableDays(list);
        Assert.assertNotNull(availableDays);
    }

    public AvailableDateM buildAvailableDateM() {
        AvailableDateM availableDateM = new AvailableDateM();
        availableDateM.setAvailableType(0);
        availableDateM.setCycleAvailableDateList(getCycleAvailableDateMS());
        return availableDateM;
    }

    public List<CycleAvailableDateM> getCycleAvailableDateMS() {
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(Lists.newArrayList(1));
        return Lists.newArrayList(cycleAvailableDateM);
    }

    @Test
    public void getAvailableDateM() {
        ProductM productM = new ProductM();
        AvailableDateM availableDateM = bathSimilarListOpt.getAvailableDateM(productM);
        Assert.assertNull(availableDateM);

        UseRuleM useRuleM = new UseRuleM();
        productM.setUseRuleM(useRuleM);
        availableDateM = bathSimilarListOpt.getAvailableDateM(productM);
        Assert.assertNull(availableDateM);

        useRuleM.setAvailableDate(new AvailableDateM());
        availableDateM = bathSimilarListOpt.getAvailableDateM(productM);
        Assert.assertNotNull(availableDateM);

    }

    @Test
    public void sortFeeList() {
        List<ProductM> productMS = bathSimilarListOpt.sortFeeList(getBathProductList());
        Assert.assertNotNull(productMS);
    }

    @Test
    public void sortFeeAndInStoreList() {
        List<ProductM> productMS = bathSimilarListOpt.sortFeeAndInStoreList(getBathProductList(), Lists.newArrayList());
        Assert.assertNotNull(productMS);
    }

    @Test
    public void sortInStoreList() {
        List<ProductM> productMS = bathSimilarListOpt.sortInStoreList(getBathProductList(), Lists.newArrayList());
        Assert.assertNotNull(productMS);
    }

    @Test
    public void sortByProjectCategoryAndSale() {
        Comparator<ProductM> productMComparator = bathSimilarListOpt.sortByProjectCategoryAndSale(Lists.newArrayList());
        Assert.assertNotNull(productMComparator);
    }

}