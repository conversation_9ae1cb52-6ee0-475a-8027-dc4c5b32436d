package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import java.util.Arrays;
import org.junit.Before;

public class BathSimilarListOpt_FilterFeeServiceTypeTest {

    // Removed @Before setup to comply with the rules
    private BathSimilarListOpt bathSimilarListOpt;

    private BathSimilarListOpt getBathSimilarListOpt() {
        return new BathSimilarListOpt();
    }

    @Test
    public void testFilterFeeServiceTypeNullProduct() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = getBathSimilarListOpt();
        try {
            bathSimilarListOpt.filterFeeServiceType(null);
            Assert.fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // Expected exception
        }
    }

    @Test
    public void testFilterFeeServiceTypeNullExtAttrs() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = getBathSimilarListOpt();
        ProductM productM = new ProductM();
        Assert.assertFalse(bathSimilarListOpt.filterFeeServiceType(productM));
    }

    @Test
    public void testFilterFeeServiceTypeEmptyExtAttrs() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = getBathSimilarListOpt();
        ProductM productM = new ProductM();
        productM.setExtAttrs(Arrays.asList());
        Assert.assertFalse(bathSimilarListOpt.filterFeeServiceType(productM));
    }

    @Test
    public void testFilterFeeServiceTypeNullServiceType() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = getBathSimilarListOpt();
        ProductM productM = new ProductM();
        productM.setExtAttrs(Arrays.asList(new AttrM("service_type", null)));
        Assert.assertFalse(bathSimilarListOpt.filterFeeServiceType(productM));
    }

    @Test
    public void testFilterFeeServiceTypeOtherServiceType() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = getBathSimilarListOpt();
        ProductM productM = new ProductM();
        productM.setExtAttrs(Arrays.asList(new AttrM("service_type", "OTHER_SERVICE_TYPE")));
        Assert.assertFalse(bathSimilarListOpt.filterFeeServiceType(productM));
    }
}
