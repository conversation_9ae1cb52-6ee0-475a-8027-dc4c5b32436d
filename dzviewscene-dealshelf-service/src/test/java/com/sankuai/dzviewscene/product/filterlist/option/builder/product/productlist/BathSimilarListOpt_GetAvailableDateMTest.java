package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AvailableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.UseRuleM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.Before;

public class BathSimilarListOpt_GetAvailableDateMTest {

    private BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();

    /**
     * Test case for when ProductM object is null.
     * Based on the current implementation, this scenario leads to a NullPointerException.
     * This test is documented for completeness but is not executed due to the expected exception.
     * Future implementations should consider handling null inputs gracefully.
     */
    /**
     * Test case for when UseRuleM property of ProductM is null.
     */
    @Test
    public void testGetAvailableDateMUseRuleMIsNull() throws Throwable {
        ProductM productM = new ProductM();
        AvailableDateM availableDateM = bathSimilarListOpt.getAvailableDateM(productM);
        Assert.assertNull(availableDateM);
    }

    /**
     * Test case for when availableDate property of UseRuleM is null.
     */
    @Test
    public void testGetAvailableDateMAvailableDateIsNull() throws Throwable {
        ProductM productM = new ProductM();
        productM.setUseRuleM(new UseRuleM());
        AvailableDateM availableDateM = bathSimilarListOpt.getAvailableDateM(productM);
        Assert.assertNull(availableDateM);
    }

    /**
     * Test case for when availableDate property of UseRuleM is not null.
     */
    @Test
    public void testGetAvailableDateMAvailableDateIsNotNull() throws Throwable {
        ProductM productM = new ProductM();
        UseRuleM useRuleM = new UseRuleM();
        useRuleM.setAvailableDate(new AvailableDateM());
        productM.setUseRuleM(useRuleM);
        AvailableDateM availableDateM = bathSimilarListOpt.getAvailableDateM(productM);
        Assert.assertNotNull(availableDateM);
    }
}
