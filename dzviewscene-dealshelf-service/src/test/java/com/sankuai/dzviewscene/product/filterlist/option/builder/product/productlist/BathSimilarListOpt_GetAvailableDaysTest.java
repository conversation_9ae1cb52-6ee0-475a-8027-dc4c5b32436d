package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.CycleAvailableDateM;
import org.junit.Assert;
import org.junit.Test;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.junit.Before;

public class BathSimilarListOpt_GetAvailableDaysTest {

    private BathSimilarListOpt bathSimilarListOpt;

    /**
     * Test getAvailableDays method when the input list is null
     */
    @Test
    public void testGetAvailableDaysWhenListIsNull() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        Set<Integer> result = bathSimilarListOpt.getAvailableDays(null);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * Test getAvailableDays method when the input list is not empty, but all CycleAvailableDateM objects' availableDays field is null
     */
    @Test
    public void testGetAvailableDaysWhenAllAvailableDaysAreNull() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(null);
        List<CycleAvailableDateM> list = Collections.singletonList(cycleAvailableDateM);
        Set<Integer> result = bathSimilarListOpt.getAvailableDays(list);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * Test getAvailableDays method when the input list is not empty, and at least one CycleAvailableDateM object's availableDays field is not null, but no element is contained in WeekConstants.ALL_DAYS collection
     */
    @Test
    public void testGetAvailableDaysWhenNoAvailableDayIsInAllDays() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        // Assuming 8, 9, 10 are not in WeekConstants.ALL_DAYS for the sake of this example
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(Arrays.asList(8, 9, 10));
        List<CycleAvailableDateM> list = Collections.singletonList(cycleAvailableDateM);
        Set<Integer> result = bathSimilarListOpt.getAvailableDays(list);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * Test getAvailableDays method when the input list is not empty, and at least one CycleAvailableDateM object's availableDays field is not null, and at least one element is contained in WeekConstants.ALL_DAYS collection
     */
    @Test
    public void testGetAvailableDaysWhenAtLeastOneAvailableDayIsInAllDays() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        // Assuming 1, 2, 3 are in WeekConstants.ALL_DAYS for the sake of this example
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(Arrays.asList(1, 2, 3));
        List<CycleAvailableDateM> list = Collections.singletonList(cycleAvailableDateM);
        Set<Integer> result = bathSimilarListOpt.getAvailableDays(list);
        Assert.assertFalse(result.isEmpty());
    }
}
