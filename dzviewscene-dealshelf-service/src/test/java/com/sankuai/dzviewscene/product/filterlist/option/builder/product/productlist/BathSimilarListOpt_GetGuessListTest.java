package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class BathSimilarListOpt_GetGuessListTest {

    private BathSimilarListOpt bathSimilarListOpt;

    // Helper method to create a mocked ProductM with empty extAttrs
    private ProductM createMockProductWithEmptyExtAttrs() {
        ProductM currentProduct = Mockito.mock(ProductM.class);
        Mockito.when(currentProduct.getExtAttrs()).thenReturn(Collections.emptyList());
        return currentProduct;
    }

    @Test
    public void testGetGuessListWhenListIsEmptyAndCurrentProductIsNull() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        ProductM currentProduct = createMockProductWithEmptyExtAttrs();
        List<ProductM> result = bathSimilarListOpt.getGuessList(new ArrayList<>(), currentProduct);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGuessListWhenListIsEmptyAndCurrentProductIsNotNull() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        ProductM currentProduct = createMockProductWithEmptyExtAttrs();
        List<ProductM> result = bathSimilarListOpt.getGuessList(new ArrayList<>(), currentProduct);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGuessListWhenListIsNotEmptyAndCurrentProductIsNull() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        List<ProductM> list = new ArrayList<>();
        list.add(Mockito.mock(ProductM.class));
        ProductM currentProduct = createMockProductWithEmptyExtAttrs();
        List<ProductM> result = bathSimilarListOpt.getGuessList(list, currentProduct);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGuessListWhenListIsNotEmptyAndCurrentProductHasNoServiceType() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        List<ProductM> list = new ArrayList<>();
        list.add(Mockito.mock(ProductM.class));
        ProductM currentProduct = createMockProductWithEmptyExtAttrs();
        List<ProductM> result = bathSimilarListOpt.getGuessList(list, currentProduct);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGuessListWhenListIsNotEmptyAndCurrentProductHasInStoreServiceType() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        List<ProductM> list = new ArrayList<>();
        list.add(Mockito.mock(ProductM.class));
        ProductM currentProduct = createMockProductWithEmptyExtAttrs();
        List<ProductM> result = bathSimilarListOpt.getGuessList(list, currentProduct);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGuessListWhenListIsNotEmptyAndCurrentProductHasBathFeeServiceType() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        List<ProductM> list = new ArrayList<>();
        list.add(Mockito.mock(ProductM.class));
        ProductM currentProduct = createMockProductWithEmptyExtAttrs();
        List<ProductM> result = bathSimilarListOpt.getGuessList(list, currentProduct);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGuessListWhenListIsNotEmptyAndCurrentProductHasOtherServiceType() throws Throwable {
        BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();
        List<ProductM> list = new ArrayList<>();
        list.add(Mockito.mock(ProductM.class));
        ProductM currentProduct = createMockProductWithEmptyExtAttrs();
        List<ProductM> result = bathSimilarListOpt.getGuessList(list, currentProduct);
        Assert.assertTrue(result.isEmpty());
    }
}
