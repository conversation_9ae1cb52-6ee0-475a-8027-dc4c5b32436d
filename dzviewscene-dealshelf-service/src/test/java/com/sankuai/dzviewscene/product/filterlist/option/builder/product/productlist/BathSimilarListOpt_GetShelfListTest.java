package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.mockito.Mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BathSimilarListOpt_GetShelfListTest {

    private BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();

    // Helper method to setup current product with service type
    private ProductM setupCurrentProduct(String serviceType) {
        ProductM product = new ProductM();
        List<AttrM> currentProductAttrs = new ArrayList<>();
        currentProductAttrs.add(new AttrM("service_type", serviceType));
        product.setExtAttrs(currentProductAttrs);
        return product;
    }

    // Helper method to create a list of products with specified service type
    private List<ProductM> createProductList(String serviceType) {
        List<ProductM> list = new ArrayList<>();
        ProductM product = new ProductM();
        product.setExtAttrs(new ArrayList<>());
        product.getExtAttrs().add(new AttrM("service_type", serviceType));
        list.add(product);
        return list;
    }

    // Test when current product is of OTHER_SERVICE_TYPE
    @Test
    public void testGetShelfListWhenCurrentProductIsOtherServiceType() throws Throwable {
        ProductM currentProduct = setupCurrentProduct("OTHER_SERVICE_TYPE");
        List<ProductM> list = createProductList("OTHER_SERVICE_TYPE");
        List<ProductM> result = bathSimilarListOpt.getShelfList(list, currentProduct);
        assertEquals("Expected no products in the result list", 0, result.size());
    }

    // Test when input product list is empty
    @Test
    public void testGetShelfListWhenInputProductListIsEmpty() throws Throwable {
        ProductM currentProduct = setupCurrentProduct("BATH_FEE_SERVICE_TYPE");
        List<ProductM> list = new ArrayList<>();
        List<ProductM> result = bathSimilarListOpt.getShelfList(list, currentProduct);
        assertEquals("Expected no products in the result list when input list is empty", 0, result.size());
    }

    // Test when input product list has no matched product
    @Test
    public void testGetShelfListWhenInputProductListHasNoMatchedProduct() throws Throwable {
        ProductM currentProduct = setupCurrentProduct("BATH_FEE_SERVICE_TYPE");
        List<ProductM> list = createProductList("OTHER_SERVICE_TYPE");
        List<ProductM> result = bathSimilarListOpt.getShelfList(list, currentProduct);
        assertEquals("Expected no products in the result list when no matched product", 0, result.size());
    }
}
