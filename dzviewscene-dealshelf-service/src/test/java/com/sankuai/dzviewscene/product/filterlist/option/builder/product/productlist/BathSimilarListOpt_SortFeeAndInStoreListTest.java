package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.mockito.Mockito;
import java.util.List;
import java.util.Arrays;
import org.apache.commons.lang3.reflect.FieldUtils;
import java.math.BigDecimal;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class BathSimilarListOpt_SortFeeAndInStoreListTest {

    private BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();

    @Test
    public void testSortFeeAndInStoreListWithEmptyList() throws Throwable {
        List<ProductM> result = bathSimilarListOpt.sortFeeAndInStoreList(Lists.newArrayList(), Lists.newArrayList());
        Assert.assertTrue("The result should be an empty list", result.isEmpty());
    }

    @Test
    public void testSortFeeAndInStoreListWithNonEmptyListNoMatch() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        ProductSaleM productSaleM = Mockito.mock(ProductSaleM.class);
        Mockito.when(productSaleM.getSale()).thenReturn(10);
        Mockito.when(productM.getSale()).thenReturn(productSaleM);
        Mockito.when(productM.getExtAttrs()).thenReturn(Lists.newArrayList(new AttrM("SERVICE_TYPE", "NON_MATCHING_SERVICE_TYPE")));
        List<ProductM> inputList = Lists.newArrayList(productM);
        List<ProductM> result = bathSimilarListOpt.sortFeeAndInStoreList(inputList, Lists.newArrayList(1L, 2L));
        Assert.assertTrue("The result should be an empty list as no service types match", result.isEmpty());
    }
}
