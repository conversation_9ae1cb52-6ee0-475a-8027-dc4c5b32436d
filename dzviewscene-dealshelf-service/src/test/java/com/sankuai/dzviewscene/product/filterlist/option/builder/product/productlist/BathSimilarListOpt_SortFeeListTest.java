package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BathSimilarListOpt_SortFeeListTest {

    private BathSimilarListOpt bathSimilarListOpt;

    @Before
    public void setUp() {
        bathSimilarListOpt = new BathSimilarListOpt();
    }

    /**
     * Helper method to create a mocked ProductM with specified service type.
     */
    private ProductM createMockProductWithServiceType(String serviceType) {
        ProductM productM = Mockito.mock(ProductM.class);
        AttrM serviceTypeAttr = new AttrM("SERVICE_TYPE", serviceType);
        Mockito.when(productM.getExtAttrs()).thenReturn(Arrays.asList(serviceTypeAttr));
        return productM;
    }

    /**
     * 测试sortFeeList方法，输入的产品列表为空
     */
    @Test
    public void testSortFeeListEmptyList() throws Throwable {
        List<ProductM> result = bathSimilarListOpt.sortFeeList(new ArrayList<>());
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试sortFeeList方法，输入的产品列表不为空，但没有产品服务类型为BATH_FEE_SERVICE_TYPE和BATH_FEE_IN_STORE_SERVICE_TYPE
     */
    @Test
    public void testSortFeeListNoServiceType() throws Throwable {
        List<ProductM> list = new ArrayList<>();
        ProductM productM = createMockProductWithServiceType("OTHER_SERVICE_TYPE");
        list.add(productM);
        List<ProductM> result = bathSimilarListOpt.sortFeeList(list);
        Assert.assertEquals(0, result.size());
    }
}
