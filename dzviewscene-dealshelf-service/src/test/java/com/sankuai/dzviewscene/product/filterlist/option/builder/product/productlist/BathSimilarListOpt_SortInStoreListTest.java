package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class BathSimilarListOpt_SortInStoreListTest {

    private BathSimilarListOpt bathSimilarListOpt = new BathSimilarListOpt();

    @Test
    public void testSortInStoreListWithEmptyList() throws Throwable {
        // Test sorting with empty product and category lists
        List<ProductM> result = bathSimilarListOpt.sortInStoreList(new ArrayList<>(), new ArrayList<>());
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testSortInStoreListWithNoStoreService() throws Throwable {
        // Test sorting with products not matching the IN_STORE_SERVICE_TYPE
        List<ProductM> productList = new ArrayList<>();
        ProductM product = Mockito.mock(ProductM.class);
        Mockito.when(product.getExtAttrs()).thenReturn(Collections.singletonList(new AttrM("SERVICE_TYPE", "NON_IN_STORE_SERVICE_TYPE")));
        productList.add(product);
        List<ProductM> result = bathSimilarListOpt.sortInStoreList(productList, new ArrayList<>());
        // Expecting 0 since the service type does not match
        Assert.assertEquals(0, result.size());
    }
}
