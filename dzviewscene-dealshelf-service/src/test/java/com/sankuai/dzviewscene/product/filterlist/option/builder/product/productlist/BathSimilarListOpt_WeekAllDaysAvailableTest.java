package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AvailableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.CycleAvailableDateM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import java.util.Arrays;

public class BathSimilarListOpt_WeekAllDaysAvailableTest {

    private BathSimilarListOpt bathSimilarListOpt;

    @Before
    public void setUp() {
        bathSimilarListOpt = new BathSimilarListOpt();
    }

    @Test
    public void testWeekAllDaysAvailableWhenAvailableTypeIsNotZero() {
        AvailableDateM availableDateM = new AvailableDateM();
        availableDateM.setAvailableType(1);
        Assert.assertFalse(bathSimilarListOpt.weekAllDaysAvailable(availableDateM));
    }

    @Test
    public void testWeekAllDaysAvailableWhenCycleAvailableDateListIsEmpty() {
        AvailableDateM availableDateM = new AvailableDateM();
        availableDateM.setAvailableType(0);
        Assert.assertFalse(bathSimilarListOpt.weekAllDaysAvailable(availableDateM));
    }

    @Test
    public void testWeekAllDaysAvailableWhenAvailableDaysIsEmpty() {
        AvailableDateM availableDateM = new AvailableDateM();
        availableDateM.setAvailableType(0);
        availableDateM.setCycleAvailableDateList(Arrays.asList(new CycleAvailableDateM()));
        Assert.assertFalse(bathSimilarListOpt.weekAllDaysAvailable(availableDateM));
    }

    @Test
    public void testWeekAllDaysAvailableWhenAvailableDaysDoesNotContainAllDays() {
        AvailableDateM availableDateM = new AvailableDateM();
        availableDateM.setAvailableType(0);
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(Arrays.asList(1, 2, 3));
        availableDateM.setCycleAvailableDateList(Arrays.asList(cycleAvailableDateM));
        Assert.assertFalse(bathSimilarListOpt.weekAllDaysAvailable(availableDateM));
    }

    @Test
    public void testWeekAllDaysAvailableWhenAvailableDaysContainsAllDays() {
        AvailableDateM availableDateM = new AvailableDateM();
        availableDateM.setAvailableType(0);
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(Arrays.asList(1, 2, 3, 4, 5, 6, 7));
        availableDateM.setCycleAvailableDateList(Arrays.asList(cycleAvailableDateM));
        Assert.assertTrue(bathSimilarListOpt.weekAllDaysAvailable(availableDateM));
    }
}
