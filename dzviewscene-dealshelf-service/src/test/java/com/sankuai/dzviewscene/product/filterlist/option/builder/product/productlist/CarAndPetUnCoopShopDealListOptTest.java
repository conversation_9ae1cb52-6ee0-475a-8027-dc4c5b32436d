package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CarAndPetUnCoopShopDealListOptTest {

    @Mock
    private ActivityCxt activityCxt;

    private CarAndPetUnCoopShopDealListOpt opt;
    private CarAndPetUnCoopShopDealListOpt.Config config;
    @Mock
    private ProductListVP.Param param;

    @Before
    public void setUp() {
        opt = new CarAndPetUnCoopShopDealListOpt();
        config = new CarAndPetUnCoopShopDealListOpt.Config();
    }

    @Test
    public void compute_whenValidProduct_thenUpdateDistance() {
        // given
        ProductM product = createValidProduct("1.2km");
        when(param.getProductMS()).thenReturn(Collections.singletonList(product));

        // when
        List<ProductM> result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(1, result.size());
        assertEquals("距商户1.2km", result.get(0).getShopMs().get(0).getDistance());
    }

    @Test
    public void compute_whenCustomDistanceFormat_thenUseCustomFormat() {
        // given
        ProductM product = createValidProduct("1.2km");
        when(param.getProductMS()).thenReturn(Collections.singletonList(product));
        config.setDistanceFormat("离你%s远");

        // when
        List<ProductM> result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(1, result.size());
        assertEquals("离你1.2km远", result.get(0).getShopMs().get(0).getDistance());
    }

    @Test
    public void compute_whenEmptyDistanceFormat_thenUseDefaultFormat() {
        // given
        ProductM product = createValidProduct("1.2km");
        when(param.getProductMS()).thenReturn(Collections.singletonList(product));
        config.setDistanceFormat("");

        // when
        List<ProductM> result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(1, result.size());
        assertEquals("距商户1.2km", result.get(0).getShopMs().get(0).getDistance());
    }

    @Test
    public void compute_whenInvalidProducts_thenFilterOut() {
        // given
        ProductM validProduct = createValidProduct("1.2km");
        ProductM nullProduct = null;
        ProductM emptyShopsProduct = new ProductM();
        ProductM nullShopProduct = createProductWithNullShop();
        ProductM emptyDistanceProduct = createProductWithEmptyDistance();

        when(param.getProductMS()).thenReturn(Arrays.asList(validProduct, nullProduct, emptyShopsProduct, nullShopProduct,emptyDistanceProduct));

        // when
        List<ProductM> result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(1, result.size());
        assertEquals("距商户1.2km", result.get(0).getShopMs().get(0).getDistance());
    }

    private ProductM createValidProduct(String distance) {
        ProductM product = new ProductM();
        ShopM shop = new ShopM();
        shop.setDistance(distance);
        product.setShopMs(Collections.singletonList(shop));
        return product;
    }

    private ProductM createProductWithNullShop() {
        ProductM product = new ProductM();
        product.setShopMs(Collections.singletonList(null));
        return product;
    }

    private ProductM createProductWithEmptyDistance() {
        ProductM product = new ProductM();
        ShopM shop = new ShopM();
        shop.setDistance("");
        product.setShopMs(Collections.singletonList(shop));
        return product;
    }
}
