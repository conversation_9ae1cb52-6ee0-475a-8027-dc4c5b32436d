package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DentistrySimilarFilterAcsOptTest extends SimilarListOptTest {

    @InjectMocks
    private DentistrySimilarFilterAcsOpt dentistrySimilarFilterAcsOpt;

    @Test
    public void testWash() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("洗牙")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(false);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }

    @Test
    public void testWashWithGrey() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("洗牙")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(true);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }

    @Test
    public void testFluoride() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("涂氟")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(false);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }

    @Test
    public void testFluorideWithGrey() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("涂氟")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(true);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }


    @Test
    public void testVault() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("窝沟封闭")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(false);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }

    @Test
    public void testVaultWithGrey() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("窝沟封闭")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(true);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }

    @Test
    public void testImplant() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("种植牙")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(false);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }

    @Test
    public void testImplantWithGrey() {
        ActivityCxt ctx = createCtx();
        ProductListVP.Param param = ProductListVP.Param
                .builder().productMS(createProductMS("种植牙")).douHuMS(buildDouhuList()).build();
        DentistrySimilarFilterAcsOpt.Config config = buildConfig(true);
        List<ProductM> compute = dentistrySimilarFilterAcsOpt.compute(ctx, param, config);
        Assert.assertEquals(3, compute.size());
    }

    private DentistrySimilarFilterAcsOpt.Config buildConfig(boolean needGrey) {
        DentistrySimilarFilterAcsOpt.Config config = new DentistrySimilarFilterAcsOpt.Config();
        config.setLimit(3);
        config.setHitGreyList(Lists.newArrayList("1_c"));
        config.setNeedGrey(needGrey);
        return config;
    }

    private List<DouHuM> buildDouhuList() {
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("1_c");
        return Lists.newArrayList(douHuM);
    }

    private List<ProductM> createProductMS(String serviceType) {
        List<ProductM> productMS = Lists.newArrayList();
        for (int i = 1; i < 7; i++) {
            ProductM productM = new ProductM();
            productM.setCategoryId(506);
            productM.setProductId(i);
            productM.setExtAttrs(Lists.newArrayList(
                    new AttrM("service_type", serviceType),
                    new AttrM("combo_type", "种植服务套餐"),
                    new AttrM("dealStructContent", buildContent())));
            productM.setUseRuleM(buildUseRuleM());
            productMS.add(productM);
        }
        return productMS;
    }

    private UseRuleM buildUseRuleM() {
        UseRuleM useRuleM = new UseRuleM();
        DisableDateM disableDateM = new DisableDateM();
        useRuleM.setDisableDate(disableDateM);
        disableDateM.setDisableDays(Lists.newArrayList(6, 7));
        return useRuleM;
    }

    private String buildContent() {
        DealStructModel dealStructModel = new DealStructModel();
        DealDetailStructuredModel structuredModel = new DealDetailStructuredModel();
        dealStructModel.setDealDetailStructuredData(structuredModel);
        DealDetailSkuUniStructuredModel skuUniStructuredModel = new DealDetailSkuUniStructuredModel();
        structuredModel.setDealDetailSkuUniStructuredModel(skuUniStructuredModel);
        MustSkusGroupModel mustSkusGroupModel = new MustSkusGroupModel();
        skuUniStructuredModel.setMustGroups(Lists.newArrayList(mustSkusGroupModel));
        SkuModel skuModel = new SkuModel();
        mustSkusGroupModel.setSkus(Lists.newArrayList(skuModel));
        List<DealSkuAttrModel> skuAttrs = Lists.newArrayList();
        skuModel.setSkuAttrs(skuAttrs);
        skuAttrs.add(buildDealSkuAttrModel("suitCrowds", "成人"));
        skuAttrs.add(buildDealSkuAttrModel("category", "手工洗牙"));
        skuAttrs.add(buildDealSkuAttrModel("sandblasting", "含喷砂"));
        skuAttrs.add(buildDealSkuAttrModel("quantityUnit", "2颗"));
        skuAttrs.add(buildDealSkuAttrModel("Sealantmaterial", "银汞合金"));
        return JsonCodec.encode(dealStructModel);
    }

    private DealSkuAttrModel buildDealSkuAttrModel(String attrName, String attrValue) {
        DealSkuAttrModel model = new DealSkuAttrModel();
        model.setAttrName(attrName);
        model.setAttrName(attrValue);
        return model;
    }

    private ActivityCxt createCtx() {
        ActivityCxt cxt = getGuess();
        cxt.addParam(PmfConstants.Params.entityId, 1);
        return cxt;
    }
}
