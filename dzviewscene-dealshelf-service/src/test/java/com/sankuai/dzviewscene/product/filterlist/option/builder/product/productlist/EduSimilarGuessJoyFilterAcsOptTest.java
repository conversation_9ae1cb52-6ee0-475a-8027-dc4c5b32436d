package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Before;

public class EduSimilarGuessJoyFilterAcsOptTest {

    // Given the constraints, initialization is done within each test method to avoid @Before annotations.
    private EduSimilarGuessJoyFilterAcsOpt eduSimilarGuessJoyFilterAcsOpt;

    private ActivityCxt context;

    private ProductListVP.Param param;

    private EduSimilarGuessJoyFilterAcsOpt.Config config;

    @Test
    public void testComputeShowSwitchClose() throws Throwable {
        EduSimilarGuessJoyFilterAcsOpt eduSimilarGuessJoyFilterAcsOpt = new EduSimilarGuessJoyFilterAcsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        EduSimilarGuessJoyFilterAcsOpt.Config config = mock(EduSimilarGuessJoyFilterAcsOpt.Config.class);
        when(config.getShowSwitch()).thenReturn("close");
        List<ProductM> result = eduSimilarGuessJoyFilterAcsOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeProductSizeLessThanTwo() throws Throwable {
        EduSimilarGuessJoyFilterAcsOpt eduSimilarGuessJoyFilterAcsOpt = new EduSimilarGuessJoyFilterAcsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        EduSimilarGuessJoyFilterAcsOpt.Config config = mock(EduSimilarGuessJoyFilterAcsOpt.Config.class);
        when(param.getProductMS()).thenReturn(Collections.singletonList(mock(ProductM.class)));
        List<ProductM> result = eduSimilarGuessJoyFilterAcsOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeCheckShowTypeTrue() throws Throwable {
        EduSimilarGuessJoyFilterAcsOpt eduSimilarGuessJoyFilterAcsOpt = new EduSimilarGuessJoyFilterAcsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductListVP.Param param = mock(ProductListVP.Param.class);
        EduSimilarGuessJoyFilterAcsOpt.Config config = mock(EduSimilarGuessJoyFilterAcsOpt.Config.class);
        when(param.getProductMS()).thenReturn(Arrays.asList(mock(ProductM.class), mock(ProductM.class)));
        when(config.getShowSwitch()).thenReturn("guess");
        List<ProductM> result = eduSimilarGuessJoyFilterAcsOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void test_null() throws Throwable {
        EduSimilarGuessJoyFilterAcsOpt eduSimilarGuessJoyFilterAcsOpt = new EduSimilarGuessJoyFilterAcsOpt();
        ActivityCxt context = new ActivityCxt();
        ProductListVP.Param param = ProductListVP.Param.builder().build();
        EduSimilarGuessJoyFilterAcsOpt.Config config = new EduSimilarGuessJoyFilterAcsOpt.Config();
        config.setShowSwitch("guess");
        List<ProductM> productMS = new ArrayList<>();
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setAttr("attr_search_hidden_status", "false");
        productMS.add(productM);
        ProductM productM2 = new ProductM();
        productM2.setProductId(2);
        productM2.setAttr("attr_search_hidden_status", "false");
        productMS.add(productM2);
        param.setProductMS(productMS);
        context.addParam(PmfConstants.Params.entityId,1);
        context.addParam(PmfConstants.Params.pageSource,"guess");
        List<ProductM> compute = eduSimilarGuessJoyFilterAcsOpt.compute(context, param, config);
        Assert.notNull(compute);
    }
}
