package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import java.util.ArrayList;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EmptyProductListOptTest {

    /**
     * Tests whether the compute method returns null.
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        EmptyProductListOpt emptyProductListOpt = new EmptyProductListOpt();
        ActivityCxt context = new ActivityCxt();
        List<ProductM> productMS = new ArrayList<>();
        List<DouHuM> douHuMS = new ArrayList<>();
        // Utilizing the builder pattern to create Param instance
        ProductListVP.Param param = ProductListVP.Param.builder().productMS(productMS).douHuMS(douHuMS).build();
        EmptyProductListOpt.Config config = new EmptyProductListOpt.Config();
        // act
        List<ProductM> result = emptyProductListOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }
}
