package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Assert;
import org.junit.Test;
import org.testng.collections.Maps;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class FitnessProductListOptTest {

    private static final String ATTR_SEARCH_HIDDEN_STATUS = "attr_search_hidden_status";

    private static final String DEAL_STATUS_ATTR = "dealStatusAttr";

    private static final String PRODUCT_TYPE = "productType";

    @Test
    public void test() {
        List<ProductM> productMS = new FitnessProductListOpt().compute(buildActivityCxt(), buildParam(), null);
        Assert.assertNotNull(productMS);
    }

    private ActivityCxt buildActivityCxt() {
        ActivityCxt context = new ActivityCxt();

        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(ShelfActivityConstants.Params.entityId, 123);

        context.setParameters(parameters);
        return context;
    }

    private FitnessProductListOpt.Param buildParam() {
        FitnessProductListOpt.Param param =
                ProductListVP.Param.builder()
                        .productMS(buildProductMS())
                        .build();
        return param;
    }

    private List<ProductM> buildProductMS() {
        ProductM productM = new ProductM();
        productM.setAttr(PRODUCT_TYPE, "deal");
        productM.setAttr(DEAL_STATUS_ATTR, "true");
        productM.setAttr(ATTR_SEARCH_HIDDEN_STATUS, "false");
        productM.setAttr(ShelfActivityConstants.Params.entityId, "123");

        productM.setProductId(123);
        productM.setSale(new ProductSaleM());
        productM.setSalePrice(BigDecimal.ONE);
        return Collections.singletonList(productM);
    }

}