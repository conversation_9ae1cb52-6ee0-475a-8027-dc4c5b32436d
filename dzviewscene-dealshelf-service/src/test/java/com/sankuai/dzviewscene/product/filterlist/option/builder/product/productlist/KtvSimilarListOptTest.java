package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * @author: created by hang.yu on 2024/4/15 16:32
 */
@RunWith(MockitoJUnitRunner.class)
public class KtvSimilarListOptTest extends SimilarListOptTest {

    @InjectMocks
    private KtvSimilarListOpt ktvSimilarListOpt;

    @Test
    public void getSimilarDealList() {
        List<ProductM> similarDealList = ktvSimilarListOpt.getSimilarDealList(getShelf(), getProduct("纯欢唱套餐"), getKtvProductList());
        Assert.assertNotNull(similarDealList);

        similarDealList = ktvSimilarListOpt.getSimilarDealList(getGuess(), getProduct("纯欢唱套餐"), getKtvProductList());
        Assert.assertNotNull(similarDealList);
    }

    @Test
    public void filterSingServiceType() {
        boolean b = ktvSimilarListOpt.filterSingServiceType(getProduct("纯欢唱套餐"));
        Assert.assertTrue(b);
    }

    @Test
    public void filterWineServiceType() {
        boolean b = ktvSimilarListOpt.filterWineServiceType(getProduct("欢唱和含酒类套餐"));
        Assert.assertTrue(b);
    }

    @Test
    public void filterDisableDays() {
        boolean b = ktvSimilarListOpt.filterDisableDays(Lists.newArrayList(), new ProductM());
        Assert.assertTrue(b);


        ProductM productM = new ProductM();
        UseRuleM useRuleM = new UseRuleM();
        DisableDateM disableDate = new DisableDateM();
        disableDate.setDisableDays(Lists.newArrayList(1));
        useRuleM.setDisableDate(disableDate);
        productM.setUseRuleM(useRuleM);

        b = ktvSimilarListOpt.filterDisableDays(Lists.newArrayList(), productM);
        Assert.assertFalse(b);

        b = ktvSimilarListOpt.filterDisableDays(Lists.newArrayList(1), productM);
        Assert.assertTrue(b);
    }

    @Test
    public void filterPeriod() {
        boolean filterPeriod = ktvSimilarListOpt.filterPeriod(Lists.newArrayList("凌晨档"), getProduct("欢唱和含酒类套餐"));
        Assert.assertFalse(filterPeriod);
    }

    @Test
    public void getDisableDays() {
        ProductM productM = new ProductM();
        UseRuleM useRuleM = new UseRuleM();
        DisableDateM disableDate = new DisableDateM();
        disableDate.setDisableDays(Lists.newArrayList(1));
        useRuleM.setDisableDate(disableDate);
        productM.setUseRuleM(useRuleM);
        List<Integer> disableDays = ktvSimilarListOpt.getDisableDays(productM);
        Assert.assertNotNull(disableDays);
    }

    @Test
    public void getPackageType() {
        Map<String, String> packageType = ktvSimilarListOpt.getPackageType(new ProductM());
        Assert.assertNotNull(packageType);
    }

    @Test
    public void sortSingList() {
        List<ProductM> productMS = ktvSimilarListOpt.sortSingList(getKtvProductList());
        Assert.assertNotNull(productMS);
    }

    @Test
    public void sortFoodList() {
        List<ProductM> productMS = ktvSimilarListOpt.sortFoodList(getKtvProductList());
        Assert.assertNotNull(productMS);
    }

    @Test
    public void sortSingAndWineList() {
        List<ProductM> productMS = ktvSimilarListOpt.sortSingAndWineList(getKtvProductList());
        Assert.assertNotNull(productMS);
    }

    @Test
    public void sortSingAndBeverageList() {
        List<ProductM> productMS = ktvSimilarListOpt.sortSingAndBeverageList(getKtvProductList());
        Assert.assertNotNull(productMS);
    }

}