package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.Mockito.when;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class KtvSimilarListOpt_FilterPackageTypeTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    private ProductM productM;

    @Before
    public void setUp() {
        ktvSimilarListOpt = new KtvSimilarListOpt();
        productM = Mockito.mock(ProductM.class);
    }

    private List<AttrM> createMockAttrList(String attrName, String attrValue) {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM(attrName, attrValue));
        return attrs;
    }

    @Test
    public void testFilterPackageTypeWhenCurrentPackageTypeIsNull() throws Throwable {
        Map<String, String> currentPackageType = null;
        boolean result = ktvSimilarListOpt.filterPackageType(currentPackageType, productM);
        assertTrue(result);
    }

    @Test
    public void testFilterPackageTypeWhenCurrentPackageTypeIsEmpty() throws Throwable {
        Map<String, String> currentPackageType = new HashMap<>();
        boolean result = ktvSimilarListOpt.filterPackageType(currentPackageType, productM);
        assertTrue(result);
    }

    @Test
    public void testFilterPackageTypeWhenServiceTypeIsSingAndWineOrSingAndBeverageAndPackageTypeContainsSmallPackage() throws Throwable {
        Map<String, String> currentPackageType = new HashMap<>();
        currentPackageType.put("SMALL_PACKAGE", "");
        when(productM.getExtAttrs()).thenReturn(createMockAttrList("service_type", "SING_AND_WINE_SERVICE_TYPE"));
        boolean result = ktvSimilarListOpt.filterPackageType(currentPackageType, productM);
        assertTrue(result);
    }

    @Test
    public void testFilterPackageTypeWhenServiceTypeIsNotSingAndWineOrSingAndBeverage() throws Throwable {
        Map<String, String> currentPackageType = new HashMap<>();
        currentPackageType.put("SMALL_PACKAGE", "");
        when(productM.getExtAttrs()).thenReturn(createMockAttrList("service_type", "OTHER_SERVICE_TYPE"));
        boolean result = ktvSimilarListOpt.filterPackageType(currentPackageType, productM);
        assertTrue(result);
    }
}
