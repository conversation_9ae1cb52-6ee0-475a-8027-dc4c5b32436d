package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.Collections;

public class KtvSimilarListOpt_FilterSingServiceTypeTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    @Before
    public void setUp() {
        ktvSimilarListOpt = new KtvSimilarListOpt();
    }

    /**
     * Test case when the extAttrs of the product is null.
     * The method should return false.
     */
    @Test
    public void testFilterSingServiceTypeWhenExtAttrsIsNull() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.getExtAttrs()).thenReturn(null);
        Assert.assertFalse(ktvSimilarListOpt.filterSingServiceType(productM));
    }

    /**
     * Test case when the extAttrs of the product is empty.
     * The method should return false.
     */
    @Test
    public void testFilterSingServiceTypeWhenExtAttrsIsEmpty() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.getExtAttrs()).thenReturn(Collections.emptyList());
        Assert.assertFalse(ktvSimilarListOpt.filterSingServiceType(productM));
    }

    /**
     * Test case when the service type exists but its value is not in the predefined set.
     * The method should return false.
     */
    @Test
    public void testFilterSingServiceTypeWhenServiceTypeExistsAndValueNotInSet() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        AttrM attrM = new AttrM("SERVICE_TYPE", "NOT_SING");
        Mockito.when(productM.getExtAttrs()).thenReturn(Arrays.asList(attrM));
        Assert.assertFalse(ktvSimilarListOpt.filterSingServiceType(productM));
    }

    /**
     * Test case when the service type does not exist.
     * The method should return false.
     */
    @Test
    public void testFilterSingServiceTypeWhenServiceTypeNotExists() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        AttrM attrM = new AttrM("OTHER_TYPE", "SING");
        Mockito.when(productM.getExtAttrs()).thenReturn(Arrays.asList(attrM));
        Assert.assertFalse(ktvSimilarListOpt.filterSingServiceType(productM));
    }
}
