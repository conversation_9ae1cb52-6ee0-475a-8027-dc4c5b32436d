package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.Collections;

public class KtvSimilarListOpt_FilterWineServiceTypeTest {

    // Removed @Before setup method and initialize KtvSimilarListOpt in each test method
    private KtvSimilarListOpt ktvSimilarListOpt;

    @Test
    public void testFilterWineServiceTypeWhenExtAttrsIsNull() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.getExtAttrs()).thenReturn(null);
        boolean result = ktvSimilarListOpt.filterWineServiceType(productM);
        Assert.assertFalse("The method should return false when extAttrs is null", result);
    }

    @Test
    public void testFilterWineServiceTypeWhenExtAttrsIsEmpty() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.getExtAttrs()).thenReturn(Collections.emptyList());
        boolean result = ktvSimilarListOpt.filterWineServiceType(productM);
        Assert.assertFalse("The method should return false when extAttrs is empty", result);
    }

    @Test
    public void testFilterWineServiceTypeWhenAttrNotExists() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        AttrM attrM = new AttrM("other", "other");
        Mockito.when(productM.getExtAttrs()).thenReturn(Arrays.asList(attrM));
        boolean result = ktvSimilarListOpt.filterWineServiceType(productM);
        Assert.assertFalse("The method should return false when the attribute does not exist", result);
    }

    @Test
    public void testFilterWineServiceTypeWhenAttrNotExistsInWineSet() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        AttrM attrM = new AttrM("serviceType", "other");
        Mockito.when(productM.getExtAttrs()).thenReturn(Arrays.asList(attrM));
        boolean result = ktvSimilarListOpt.filterWineServiceType(productM);
        Assert.assertFalse("The method should return false when the attribute does not exist in the wine set", result);
    }
}
