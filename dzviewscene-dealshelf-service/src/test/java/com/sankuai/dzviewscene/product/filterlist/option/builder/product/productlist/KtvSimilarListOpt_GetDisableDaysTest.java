package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.DisableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.UseRuleM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import java.util.Arrays;

public class KtvSimilarListOpt_GetDisableDaysTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    @Before
    public void setUp() {
        ktvSimilarListOpt = new KtvSimilarListOpt();
    }

    /**
     * 测试 UseRuleM 为 null 的情况
     */
    @Test
    public void testGetDisableDaysUseRuleMIsNull() {
        ProductM productM = new ProductM();
        Assert.assertTrue(ktvSimilarListOpt.getDisableDays(productM).isEmpty());
    }

    /**
     * 测试 disableDate 为 null 的情况
     */
    @Test
    public void testGetDisableDaysDisableDateIsNull() {
        ProductM productM = new ProductM();
        productM.setUseRuleM(new UseRuleM());
        Assert.assertTrue(ktvSimilarListOpt.getDisableDays(productM).isEmpty());
    }

    /**
     * 测试 disableDays 为空的情况
     */
    @Test
    public void testGetDisableDaysDisableDaysIsEmpty() {
        ProductM productM = new ProductM();
        UseRuleM useRuleM = new UseRuleM();
        useRuleM.setDisableDate(new DisableDateM());
        productM.setUseRuleM(useRuleM);
        Assert.assertTrue(ktvSimilarListOpt.getDisableDays(productM).isEmpty());
    }

    /**
     * 测试 disableDays 中的元素在 WeekConstants.ALL_DAYS 中不存在的情况
     */
    @Test
    public void testGetDisableDaysElementNotInAllDays() {
        ProductM productM = new ProductM();
        UseRuleM useRuleM = new UseRuleM();
        DisableDateM disableDate = new DisableDateM();
        disableDate.setDisableDays(Arrays.asList(100));
        useRuleM.setDisableDate(disableDate);
        productM.setUseRuleM(useRuleM);
        Assert.assertTrue(ktvSimilarListOpt.getDisableDays(productM).isEmpty());
    }

    /**
     * 测试 disableDays 中的元素在 WeekConstants.ALL_DAYS 中存在的情况
     */
    @Test
    public void testGetDisableDaysElementInAllDays() {
        ProductM productM = new ProductM();
        UseRuleM useRuleM = new UseRuleM();
        DisableDateM disableDate = new DisableDateM();
        disableDate.setDisableDays(Arrays.asList(1));
        useRuleM.setDisableDate(disableDate);
        productM.setUseRuleM(useRuleM);
        Assert.assertEquals(Arrays.asList(1), ktvSimilarListOpt.getDisableDays(productM));
    }
}
