package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class KtvSimilarListOpt_GetGuessListTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    private ProductM currentProduct;

    private List<ProductM> list;

    @Before
    public void setUp() {
        ktvSimilarListOpt = Mockito.spy(new KtvSimilarListOpt());
        currentProduct = new ProductM();
        // Initialize ProductM objects with necessary fields
        ProductM product1 = createProductWithSale(10);
        ProductM product2 = createProductWithSale(5);
        list = Arrays.asList(product1, product2);
    }

    // Helper method to create a ProductM object with a specified sale value
    private ProductM createProductWithSale(int saleValue) {
        ProductM product = new ProductM();
        ProductSaleM sale = new ProductSaleM();
        sale.setSale(saleValue);
        product.setSale(sale);
        return product;
    }

    @Test
    public void testGetGuessList_FoodServiceTypeNoMatch() throws Throwable {
        // Arrange
        when(ktvSimilarListOpt.filterSingServiceType(currentProduct)).thenReturn(false);
        when(ktvSimilarListOpt.sortProductBySale(list)).thenReturn(Arrays.asList());
        // Act
        List<ProductM> result = ktvSimilarListOpt.getGuessList(currentProduct, list);
        // Assert
        assertEquals(Arrays.asList(), result);
    }

    @Test
    public void testGetGuessList_OtherServiceType() throws Throwable {
        // Arrange
        when(ktvSimilarListOpt.filterSingServiceType(currentProduct)).thenReturn(false);
        // Act
        List<ProductM> result = ktvSimilarListOpt.getGuessList(currentProduct, list);
        // Assert
        assertEquals(Arrays.asList(), result);
    }
}
