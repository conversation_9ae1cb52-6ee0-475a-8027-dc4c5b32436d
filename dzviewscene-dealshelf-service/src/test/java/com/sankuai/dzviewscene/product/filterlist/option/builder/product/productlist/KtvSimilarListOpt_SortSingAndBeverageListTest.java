package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class KtvSimilarListOpt_SortSingAndBeverageListTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    @Before
    public void setUp() {
        ktvSimilarListOpt = new KtvSimilarListOpt();
    }

    /**
     * Test sortSingAndBeverageList method when filterList is null
     */
    @Test(expected = NullPointerException.class)
    public void testSortSingAndBeverageListWhenFilterListIsNull() throws Throwable {
        ktvSimilarListOpt.sortSingAndBeverageList(null);
    }
}
