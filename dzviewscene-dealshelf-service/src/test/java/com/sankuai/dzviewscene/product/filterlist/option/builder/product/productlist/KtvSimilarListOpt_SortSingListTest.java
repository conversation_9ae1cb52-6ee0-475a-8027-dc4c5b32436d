package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class KtvSimilarListOpt_SortSingListTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    private KtvSimilarListOpt createKtvSimilarListOpt() {
        return new KtvSimilarListOpt();
    }

    @Test
    public void testSortSingListEmptyFilterList() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = createKtvSimilarListOpt();
        List<ProductM> result = ktvSimilarListOpt.sortSingList(new ArrayList<>());
        Assert.assertTrue(result.isEmpty());
    }
}
