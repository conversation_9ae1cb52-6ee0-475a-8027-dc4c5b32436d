package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.BaseDealIdQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class LifeCleanSimilarFilterAcsOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductListVP.Param param;

    @Mock
    private LifeCleanSimilarFilterAcsOpt.Config config;

    @Mock
    private BaseDealIdQueryHandler baseDealIdQueryHandler;

    @InjectMocks
    private LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt;

    private void mockContextParameter(String key, Object value) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(key, value);
        when(context.getParameters()).thenReturn(parameters);
    }

    @Test
    public void testComputeProductListIsEmpty() throws Throwable {
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        List<ProductM> result = lifeCleanSimilarFilterAcsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    /**
     * 测试compute方法，当baseDealIdQueryHandler.runSelfOperatedCleaning返回true时
     */
    @Test
    public void testComputeWithSelfOperatedCleaning() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        context.addParam(PmfConstants.Params.entityId, 1);

        // arrange
        List<ProductM> mockProductList = getMockProductList();
        when(param.getProductMS()).thenReturn(mockProductList);
        when(baseDealIdQueryHandler.runSelfOperatedCleaning(context)).thenReturn(true);

        // act
        List<ProductM> result = lifeCleanSimilarFilterAcsOpt.compute(context, param, config);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试compute方法，当baseDealIdQueryHandler.runSelfOperatedCleaning返回true时
     */
    @Test
    public void testComputeWithSelfOperatedCleaningReturn2() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        context.addParam(PmfConstants.Params.entityId, 1);

        // arrange
        List<ProductM> mockProductList = getMockProductList2();
        when(param.getProductMS()).thenReturn(mockProductList);
        when(baseDealIdQueryHandler.runSelfOperatedCleaning(context)).thenReturn(true);
        when(config.getLimit()).thenReturn(2);

        // act
        List<ProductM> result = lifeCleanSimilarFilterAcsOpt.compute(context, param, config);

        // assert
        assertEquals(2, result.size());
    }

    private List<ProductM> getMockProductList() {
        List<ProductM> productList = new ArrayList<>();
        ProductM productM0 = new ProductM();
        productM0.setProductId(1);
        productM0.setSale(new ProductSaleM());
        productM0.getSale().setSale(10);
        productM0.setAttr("dealStructContent", "{\"productCategories\":[{\"productCategoryId\":841,\"cnName\":\"家庭保洁\"}]}");
        productList.add(productM0);
        return productList;
    }

    private List<ProductM> getMockProductList2() {
        List<ProductM> productList = new ArrayList<>();
        ProductM productM0 = new ProductM();
        productM0.setProductId(1);
        productM0.setSale(new ProductSaleM());
        productM0.getSale().setSale(10);
        productM0.setAttr("dealStructContent", "{\"productCategories\":[{\"productCategoryId\":841,\"cnName\":\"家庭保洁\"}]}");
        productList.add(productM0);

        ProductM productM1 = new ProductM();
        productM1.setProductId(2);
        productM1.setSale(new ProductSaleM());
        productM1.getSale().setSale(10);
        productM1.setAttr("dealStructContent", "{\"productCategories\":[{\"productCategoryId\":841,\"cnName\":\"家庭保洁\"}]}");
        productList.add(productM1);
        return productList;
    }
}
