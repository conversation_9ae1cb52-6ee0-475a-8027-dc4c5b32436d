package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import org.mockito.Mockito;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class LifeCleanSimilarFilterAcsOpt_GetAvailableProductTest {

    private LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt;

    // Helper method to set the private field
    private void setValidProductCategories(LifeCleanSimilarFilterAcsOpt instance, List<Long> categories) throws Exception {
        Field validProductCategoriesField = LifeCleanSimilarFilterAcsOpt.class.getDeclaredField("validProductCategories");
        validProductCategoriesField.setAccessible(true);
        validProductCategoriesField.set(instance, categories);
    }

    @Test
    public void testGetAvailableProductCurrentProductIsNull() throws Throwable {
        LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt = new LifeCleanSimilarFilterAcsOpt();
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Arrays.asList(1L));
        ProductM productM = mock(ProductM.class);
        assertFalse(lifeCleanSimilarFilterAcsOpt.getAvailableProduct(productM, null));
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Collections.emptyList());
    }

    @Test
    public void testGetAvailableProductCurrentProductCategoriesIsEmpty() throws Throwable {
        LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt = new LifeCleanSimilarFilterAcsOpt();
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Arrays.asList(1L));
        ProductM productM = mock(ProductM.class);
        ProductM currentProduct = mock(ProductM.class);
        when(currentProduct.getAttr("dealStructContent")).thenReturn("");
        assertFalse(lifeCleanSimilarFilterAcsOpt.getAvailableProduct(productM, currentProduct));
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Collections.emptyList());
    }

    @Test
    public void testGetAvailableProductCurrentProductCategoryNotInValidProductCategories() throws Throwable {
        LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt = new LifeCleanSimilarFilterAcsOpt();
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Arrays.asList(1L));
        ProductM productM = mock(ProductM.class);
        ProductM currentProduct = mock(ProductM.class);
        when(currentProduct.getAttr("dealStructContent")).thenReturn("dealStructContent");
        when(productM.getAttr("dealStructContent")).thenReturn("dealStructContent");
        assertFalse(lifeCleanSimilarFilterAcsOpt.getAvailableProduct(productM, currentProduct));
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Collections.emptyList());
    }

    @Test
    public void testGetAvailableProductProductCategoriesIsEmpty() throws Throwable {
        LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt = new LifeCleanSimilarFilterAcsOpt();
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Arrays.asList(1L));
        ProductM productM = mock(ProductM.class);
        ProductM currentProduct = mock(ProductM.class);
        when(currentProduct.getAttr("dealStructContent")).thenReturn("dealStructContent");
        when(productM.getAttr("dealStructContent")).thenReturn("dealStructContent");
        assertFalse(lifeCleanSimilarFilterAcsOpt.getAvailableProduct(productM, currentProduct));
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Collections.emptyList());
    }

    @Test
    public void testGetAvailableProductProductCategoryNotEqualsCurrentProductCategory() throws Throwable {
        LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt = new LifeCleanSimilarFilterAcsOpt();
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Arrays.asList(1L));
        ProductM productM = mock(ProductM.class);
        ProductM currentProduct = mock(ProductM.class);
        when(currentProduct.getAttr("dealStructContent")).thenReturn("dealStructContent");
        when(productM.getAttr("dealStructContent")).thenReturn("dealStructContent");
        assertFalse(lifeCleanSimilarFilterAcsOpt.getAvailableProduct(productM, currentProduct));
        setValidProductCategories(lifeCleanSimilarFilterAcsOpt, Collections.emptyList());
    }
}
