package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/1/8 11:32
 */
@RunWith(MockitoJUnitRunner.class)
public class LifeCleanSimilarFilterByIsSupportReserveTest {
    @InjectMocks
    private LifeCleanSimilarFilterAcsOpt lifeCleanSimilarFilterAcsOpt;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Test
    public void test() {
        List<ProductM> productMS = JacksonUtils.deserialize(productsData, List.class);
        Map<String, Object> parameters = JacksonUtils.deserialize(parametersData, Map.class);
        ActivityCxt context = new ActivityCxt();
        context.setParameters(parameters);

        List<Integer> supportCategories = Lists.newArrayList(409, 461, 459);
        ReflectionTestUtils.setField(lifeCleanSimilarFilterAcsOpt, "supportCategories", supportCategories);

        when(compositeAtomService.queryIsSupportStrictReserve(anyLong()))
                .thenReturn(CompletableFuture.completedFuture(true));
        List<ProductM> result = ReflectionTestUtils.invokeMethod(lifeCleanSimilarFilterAcsOpt,
                "filterByIsSupportReserve", productMS, context);
        assert CollectionUtils.isNotEmpty(result);
    }

    private static final String parametersData = "{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ctxShop\\\"}\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ShopM\",\"groupName\":null,\"shopId\":69411727,\"longShopId\":69411727,\"shopName\":\"春晖到家 保洁家政 上海\",\"branchName\":null,\"distance\":null,\"distanceNum\":0.0,\"pic\":null,\"detailUrl\":null,\"shopTags\":null,\"shopType\":80,\"useType\":0,\"category\":33998,\"categoryName\":null,\"secondCategoryName\":null,\"backCategory\":null,\"lat\":0.0,\"lng\":0.0,\"cityId\":1,\"sale\":null,\"phoneNo\":null,\"phoneNoList\":null,\"shopUuid\":\"l2txa3TkNhA64Pif\",\"address\":null,\"shopPower\":null,\"starStr\":null,\"score\":0.0,\"scoreTag\":null,\"labels\":null,\"mainRegionName\":null,\"businessHours\":null,\"status\":5,\"extInfos\":null,\"reviewCount\":null,\"scoreDetail\":null,\"userEqShop\":false,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"selfOperatedCleaningShop\",\"value\":\"false\"}]]},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"appVersion\\\"}\":\"12.27.200\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"unsupportedSortNavTags\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"reCountProducts\\\"}\":false,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"productIdList\\\"}\":\"\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"channel\\\"}\":\"dealFilterList\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"pageSize\\\"}\":20,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mtPoiId\\\"}\":119542111,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"locationCityId\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"deviceId\\\"}\":\"0000000000000F44351DD90D94BB49DDFF6EB3854AC8EA171946598023583060\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mtUserId\\\"}\":[\"java.lang.Long\",183041850],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"extPointClassMap\\\"}\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"FilterFetcherExt\\\"}\":[\"java.lang.Class\",\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultipleConfigFilterFetcherExt\"]},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"filterComponent2Group\\\"}\":{\"@class\":\"java.util.LinkedHashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"团购\\\"}\":\"团购\"},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dpCityId\\\"}\":1,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"clientType\\\"}\":\"android\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dpPoiIdL\\\"}\":[\"java.lang.Long\",69411727],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"sceneCode\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"pageNo\\\"}\":1,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"keyword\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"lat\\\"}\":31.2717001,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"productComponent2Group\\\"}\":{\"@class\":\"java.util.LinkedHashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"团购\\\"}\":\"团购\"},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"searchterm\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mtgsig\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"unionId\\\"}\":\"23a1cdf8d2794ca79a6e52948ea22228a171946598025470584\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"lng\\\"}\":121.5294045,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"selectedFilterId\\\"}\":[\"java.lang.Long\",200121902],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"topN\\\"}\":0,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"recalledFilterId\\\"}\":[\"java.lang.Long\",200121902],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"sortid\\\"}\":0,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"reportMagicMember\\\"}\":true,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"position\\\"}\":\"2104\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"shopType\\\"}\":80,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"shelfDefaultConvert\\\"}\":{\"@class\":\"java.util.concurrent.CompletableFuture\",\"numberOfDependents\":0,\"done\":true,\"cancelled\":false,\"completedExceptionally\":false},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mtLocationCityId\\\"}\":0,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"abilityClassMap\\\"}\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ProductPaddingFetcher\\\"}\":[\"java.lang.Class\",\"com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher\"],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"FilterFetcher\\\"}\":[\"java.lang.Class\",\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcher\"],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ProductFetcher\\\"}\":[\"java.lang.Class\",\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher\"]},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"wttRegionId\\\"}\":\"3000110050\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"shopuuid\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"platform\\\"}\":2,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"extra\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"shopMtCityId\\\"}\":10,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"multiGroupParams\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"pageSourceNew\\\"}\":\"pre_order_deal\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mtCityId\\\"}\":10,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"userAgent\\\"}\":200,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"entityId\\\"}\":\"712270843\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mmcUse\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mmcInflate\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mtPoiIdL\\\"}\":[\"java.lang.Long\",119542111],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mmcBuy\\\"}\":null,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"spaceKey\\\"}\":\"deal_detail_list\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dpPoiId\\\"}\":69411727,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dealSaleChannel\\\"}\":{\"@class\":\"com.sankuai.dztheme.deal.res.DealProductSaleChannelAggregationDTO\",\"allSupport\":true,\"supportChannels\":[\"java.util.ArrayList\",[]],\"notSupportChannels\":[\"java.util.ArrayList\",[]]},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"groupNames\\\"}\":[\"java.util.ArrayList\",[\"团购\"]],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"groupParams\\\"}\":{\"@class\":\"java.util.LinkedHashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"团购\\\"}\":{\"@class\":\"java.util.LinkedHashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"paddingType\\\"}\":\"dealThemePadding\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"paddingProductType\\\"}\":1,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"promoTemplateId\\\"}\":244,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"attributeKeys\\\"}\":[\"java.util.ArrayList\",[\"reservation_is_needed_or_not\",\"reservation_is_needed_or_not_3\",\"service_type\",\"support_home_service\",\"preSaleTag\",\"DealPricePowerTag\",\"facadeCleanPart\",\"serviceDuration\"]],\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"directPromoScene\\\"}\":400200,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"enablePreSalePromoTag\\\"}\":true,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"priceDescType\\\"}\":4,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"querySecKillSceneStrategy\\\"}\":3,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"planId\\\"}\":\"10002476\"}},\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"shopDpCityId\\\"}\":1,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"timesDealQueryFlag\\\"}\":false,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"selectPerFirstLeafNode\\\"}\":false,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"pageSource\\\"}\":\"shelf\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"category\\\"}\":33998,\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dealCategoryId\\\"}\":409}";

    private static final String productsData = "[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":1,\"productId\":712253595,\"id\":null,\"categoryId\":409,\"categoryName\":null,\"spuType\":0,\"title\":\"【日常保洁3小时】家政保洁上门/家政上门搞卫生（随买随约）\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=712253595&poiid=119542111&pagefrom=poiShelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoXSqZzmJ1aSfLkPrLO5-vRZDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPB10gRLe9-re5FBhFy6GoIaICJpT1ZfggVQbrLaeum9HjcKugLxGWV-yAJwO0-2xeZQwVRjFpkFbx7PzfwgDY6X28i2Xkf-EieWXYFGTUBmYGbxU1nwjmXNM4SbtVG_mVaOmoXPyPVSyCs-a5b4yccwuTsbO_IQcnVSS-uSihjVEWGpFoaF3Kh7gYvbTeCNNObeOFo-24tqBhNj7ZNAo5jipRpb_QBz3kTyvgadZM9Xmov8hjsv4lqpJtbpUmtmNQfiNvz6xjD1iHFpks2DQ1rgLPHUEGMeRHnCgeftmB9_OpgSTCSWTXnFfehM6MFJe7w6m2ItqAtU7wiCnlW3dKIfk7ncKaTj0aj5_AxcYQeNHeFclwbzeH0EB5gOQNiOYMpbWxj5Ig0dVrw8VdzufcYIsP0yHmE15XhliwnGu_hSTQ\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":7223,\"saleTag\":\"年售7000+\"},\"stock\":null,\"basePriceTag\":\"¥195\",\"basePriceDesc\":null,\"basePrice\":[\"java.math.BigDecimal\",195],\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"reservation_is_needed_or_not_3\",\"value\":\"是\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"保洁\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"preSaleTag\",\"value\":\"false\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":null,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",134],\"timesDealQueryFlag\":false,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":0,\"top\":false,\"dealSpuId\":0,\"unifyProduct\":false,\"additionalDeal\":false,\"actProductId\":712253595,\"actProductType\":1,\"timesDeal\":false},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":1,\"productId\":712275704,\"id\":null,\"categoryId\":409,\"categoryName\":null,\"spuType\":0,\"title\":\"【日常保洁4小时】家政保洁上门/家政上门搞卫生（随买随约）\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=712275704&poiid=119542111&pagefrom=poiShelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoXSqZzmJ1aSfLkPrLO5-vRZDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPB10gRLe9-re5FBhFy6GoIaICJpT1ZfggVQbrLaeum9HjcKugLxGWV-yAJwO0-2xeZQwVRjFpkFbx7PzfwgDY6X28i2Xkf-EieWXYFGTUBmYGbxU1nwjmXNM4SbtVG_mVaOmoXPyPVSyCs-a5b4yccwuTsbO_IQcnVSS-uSihjVEWGpFoaF3Kh7gYvbTeCNNObeOFo-24tqBhNj7ZNAo5jipRpb_QBz3kTyvgadZM9Xmov8hjsv4lqpJtbpUmtmNQfiNvz6xjD1iHFpks2DQ1rgLPHUEGMeRHnCgeftmB9_OpgSTCSWTXnFfehM6MFJe7w6m2ItqAtU7wiCnlW3dKIfk7ncKaTj0aj5_AxcYQeNHeFclwbzeH0EB5gOQNiOYMpbWxj5Ig0dVrw8VdzufcYIsP0yHmE15XhliwnGu_hSTQ\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":1578,\"saleTag\":\"年售1000+\"},\"stock\":null,\"basePriceTag\":\"¥248\",\"basePriceDesc\":null,\"basePrice\":[\"java.math.BigDecimal\",248],\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"reservation_is_needed_or_not_3\",\"value\":\"是\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"保洁\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"preSaleTag\",\"value\":\"false\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":null,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",184],\"timesDealQueryFlag\":false,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":0,\"top\":false,\"dealSpuId\":0,\"unifyProduct\":false,\"additionalDeal\":false,\"actProductId\":712275704,\"actProductType\":1,\"timesDeal\":false},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":1,\"productId\":741177599,\"id\":null,\"categoryId\":409,\"categoryName\":null,\"spuType\":0,\"title\":\"【专业整理收纳1小时】包括全屋衣橱/厨卫等\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=741177599&poiid=119542111&pagefrom=poiShelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoXSqZzmJ1aSfLkPrLO5-vRZDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPB10gRLe9-re5FBhFy6GoIaICJpT1ZfggVQbrLaeum9HjcKugLxGWV-yAJwO0-2xeZQwVRjFpkFbx7PzfwgDY6X28i2Xkf-EieWXYFGTUBmYGbxU1nwjmXNM4SbtVG_mVaOmoXPyPVSyCs-a5b4yccwuTsbO_IQcnVSS-uSihjVEWGpFoaF3Kh7gYvbTeCNNObeOFo-24tqBhNj7ZNAo5jipRpb_QBz3kTyvgadZM9Xmov8hjsv4lqpJtbpUmtmNQfiNvz6xjD1iHFpks2DQ1rgLPHUEGMeRHnCgeftmB9_OpgSTCSWTXnFfehM6MFJe7w6m2ItqAtU7wiCnlW3dKIfk7ncKaTj0aj5_AxcYQeNHeFclwbzeH0EB5gOQNiOYMpbWxj5Ig0dVrw8VdzufcYIsP0yHmE15XhliwnGu_hSTQ\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":362,\"saleTag\":\"年售300+\"},\"stock\":null,\"basePriceTag\":\"¥98\",\"basePriceDesc\":null,\"basePrice\":[\"java.math.BigDecimal\",98],\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"reservation_is_needed_or_not_3\",\"value\":\"是\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"保洁\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"preSaleTag\",\"value\":\"false\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":null,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",93],\"timesDealQueryFlag\":false,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":0,\"top\":false,\"dealSpuId\":0,\"unifyProduct\":false,\"additionalDeal\":false,\"actProductId\":741177599,\"actProductType\":1,\"timesDeal\":false}]]";
}
