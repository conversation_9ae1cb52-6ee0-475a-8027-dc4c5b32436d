package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 11:03
 */
@RunWith(MockitoJUnitRunner.class)
public class MassageSimilarListOptTest extends SimilarListOptTest {

    @InjectMocks
    private MassageSimilarListOpt massageSimilarListOpt;

    @Test
    public void testGetSimilarDealList() {
        List<ProductM> similarDealList = massageSimilarListOpt.getSimilarDealList(getGuess(), getProduct("足疗"), getMassgeProductList());
        Assert.assertNotNull(similarDealList);

        similarDealList = massageSimilarListOpt.getSimilarDealList(getShelf(), getProduct("足疗"), getMassgeProductList());
        Assert.assertNotNull(similarDealList);
    }

    @Test
    public void testCompute() {
        ProductListVP.Param param = ProductListVP.Param.builder().productMS(getMassgeProductList()).build();
        List<ProductM> compute = massageSimilarListOpt.compute(getShelf(), param, null);
        Assert.assertNotNull(compute);
    }

}