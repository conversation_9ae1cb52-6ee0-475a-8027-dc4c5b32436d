package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.List;

public class MassageSimilarListOpt_GetSimilarDealListTest {

    private MassageSimilarListOpt massageSimilarListOpt;

    private ActivityCxt context;

    private ProductM currentProduct;

    private List<ProductM> list;

    @Before
    public void setUp() {
        massageSimilarListOpt = new MassageSimilarListOpt();
        context = Mockito.mock(ActivityCxt.class);
        currentProduct = Mockito.mock(ProductM.class);
        list = Arrays.asList(new ProductM(), new ProductM());
    }

    /**
     * 测试pageSource等于GUESS的情况
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsGuess() {
        // arrange
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("GUESS");
        // act
        List<ProductM> result = massageSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试pageSource等于SHELF的情况
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsShelf() {
        // arrange
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("SHELF");
        // act
        List<ProductM> result = massageSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试pageSource既不等于GUESS也不等于SHELF的情况
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsNeitherGuessNorShelf() {
        // arrange
        // act
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("OTHER");
        List<ProductM> result = massageSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        // assert
        Assert.assertTrue(result.isEmpty());
    }
}
