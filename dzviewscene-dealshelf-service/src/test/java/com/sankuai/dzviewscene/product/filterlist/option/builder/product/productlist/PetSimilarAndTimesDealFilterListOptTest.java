package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.combine.CombineDTO;
import com.sankuai.general.product.query.center.client.dto.combine.CombineItemDTO;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @ClassName PetSimilarAndTimesDealFilterListOptTest.java
 * @createTime 2024/04/22 17:38
 */

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class PetSimilarAndTimesDealFilterListOptTest {
    @Spy
    private ActivityCxt context;
    @Mock
    private Param mockParam;
    private PetSimilarAndTimesDealFilterListOpt.Config mockConfig = new PetSimilarAndTimesDealFilterListOpt.Config();
    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private PetSimilarFilterAcsOpt petSimilarFilterAcsOpt;

    @InjectMocks
    private PetSimilarAndTimesDealFilterListOpt petSimilarAndTimesDealFilterListOpt;

    @Test
    public void testCompute() {
        when(mockParam.getProductMS()).thenReturn(Collections.emptyList());

        List<ProductM> result = petSimilarAndTimesDealFilterListOpt.compute(context, mockParam, mockConfig);

        assertTrue("结果应为空列表", result.isEmpty());

        context.setParameters(getParams());

        QueryDealGroupListResult queryDealGroupListResult = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();

        CombineDTO combineDTO = new CombineDTO();
        CombineItemDTO combineItemDTO = new CombineItemDTO();
        combineItemDTO.setCombineItemId(*********L);
        combineDTO.setCombineItems(Lists.newArrayList(combineItemDTO));
        dealGroupDTO.setCombines(Lists.newArrayList(combineDTO));
        queryDealGroupListResult.setList(Lists.newArrayList(dealGroupDTO));

        when(compositeAtomService.queryByDealGroupIds(any())).thenReturn(CompletableFuture.completedFuture(queryDealGroupListResult));
        //when(petSimilarFilterAcsOpt.compute(any(), any(), any())).thenReturn(Lists.newArrayList());

        when(mockParam.getProductMS()).thenReturn(getProductMList());
        try {
            result = petSimilarAndTimesDealFilterListOpt.compute(context, mockParam, mockConfig);
        } catch (Exception e) {
            log.error("PetSimilarAndTimesDealFilterListOptTestError", e);
            throw e;
        }

        assertNotNull(result);
    }

    private static List<ProductM> getProductMList() {
        String productJSON = "[{\"actProductId\":*********,\"actProductType\":1,\"activities\":[],\"basePrice\":90.00,\"basePriceTag\":\"¥90\",\"beginDate\":0,\"categoryId\":1701,\"compositeScore\":0.0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"apply_pets_in_wash_beauty\",\"value\":\"猫咪\"},{\"name\":\"service_type\",\"value\":\"洗澡\"},{\"name\":\"hair_length_pet_cat\",\"value\":\"短毛\"},{\"name\":\"wegith_pet_cat\",\"value\":\"自定义体重区段3-7KG\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=733598891&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUv-wA1YtPPwwAYWml6QXXwdJoysUgj2g8F6BNjqSnplWLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZXCGWPkywVecwDC0_HQ1L6_BIg8tP78DPfyKLOGUNOoWsYhLOuV_dnAYaZ62MyDnUixpLlA98bPuDyLkhRSJUPht6DKKvzXfPbKpKTy_HdU_WjDSxxpX33KgqQVwFam6LcBPNR1BwUm8Fs39SIjMTua9D74URZOnvrR85NsmeX6EDKtg-T9DBkr2KZ2Ha340F0TqtSekUCZSrcoQ-xPcZfTv_qQwKucawLkZJf5i9vsNUn0RA0qzyasZQKhnYQpJiL1tc4djgfS--96FhTELgB6W79dkk0j424Ir5x-cX46tlbLUXmUeWuIYk30H_O1BF9EfG5rRlgQh1x-USaKk6Q0e6q4aqxPa-XnwPgScsw6rMJIyb8Z_Wo04rZrVBngQYM9FjIX7d91cOgIlYJHsuXX1MkYwYORoNcCpTAATlzfeOAWGtsHj52B-pb0omOJ30v-5szmASBi3KTXp4mVoO7iZ1MJkCbYAQylcCLt92smA\",\"orderUsers\":[],\"productId\":*********,\"productTagList\":[],\"productType\":1,\"promoPrices\":[{\"discount\":0.61,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"\",\"pricePromoInfoMap\":{},\"promoItemList\":[{\"amount\":58.00,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":58.00,\"promoTag\":\"-¥58\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1}],\"promoPrice\":90.00,\"promoPriceTag\":\"¥90\",\"promoQuantityLimit\":0,\"promoTag\":\"特惠促销共省¥58\",\"promoTagType\":10,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":58.00,\"totalPromoPriceTag\":\"-¥¥58\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"salePrice\":90.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"猫咪洗澡·3-7KG·短毛\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":*********,\"actProductType\":1,\"activities\":[],\"basePrice\":180.00,\"basePriceTag\":\"¥180\",\"beginDate\":0,\"categoryId\":1701,\"compositeScore\":0.0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"apply_pets_in_wash_beauty\",\"value\":\"猫咪\"},{\"name\":\"service_type\",\"value\":\"洗澡\"},{\"name\":\"hair_length_pet_cat\",\"value\":\"无毛长限制\"},{\"name\":\"wegith_pet_cat\",\"value\":\"自定义体重区段3-7KG\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=733598891&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUv-wA1YtPPwwAYWml6QXXwdJoysUgj2g8F6BNjqSnplWLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZXCGWPkywVecwDC0_HQ1L6_BIg8tP78DPfyKLOGUNOoWsYhLOuV_dnAYaZ62MyDnUixpLlA98bPuDyLkhRSJUPht6DKKvzXfPbKpKTy_HdU_WjDSxxpX33KgqQVwFam6LcBPNR1BwUm8Fs39SIjMTua9D74URZOnvrR85NsmeX6EDKtg-T9DBkr2KZ2Ha340F0TqtSekUCZSrcoQ-xPcZfTv_qQwKucawLkZJf5i9vsNUn0RA0qzyasZQKhnYQpJiL1tc4djgfS--96FhTELgB6W79dkk0j424Ir5x-cX46tlbLUXmUeWuIYk30H_O1BF9EfG5rRlgQh1x-USaKk6Q0e6q4aqxPa-XnwPgScsw6rMJIyb8Z_Wo04rZrVBngQYM9FjIX7d91cOgIlYJHsuXX1MkYwYORoNcCpTAATlzfeOAWGtsHj52B-pb0omOJ30v-5szmASBi3KTXp4mVoO7iZ1MJkCbYAQylcCLt92smA\",\"orderUsers\":[],\"productId\":*********,\"productTagList\":[],\"productType\":1,\"promoPrices\":[{\"discount\":0.66,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"https://p1.meituan.net/travelcube/83518c6e14ec54a491bac580cb43d2fe4222.png\",\"pricePromoInfoMap\":{},\"promoItemList\":[{\"amount\":78.00,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":78.00,\"promoTag\":\"-¥78\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1},{\"amount\":1E+1,\"canAssign\":false,\"desc\":\"满99元减10元\",\"effectiveEndTime\":0,\"endTime\":1714406399000,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"minConsumptionAmount\":99,\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"MERCHANT_COUPON\",\"subTitle\":\"满99元减10元\",\"title\":\"商家优惠券\"},\"promoPrice\":1E+1,\"promoTag\":\"-¥10\",\"promoType\":\"商家优惠券\",\"promoTypeCode\":5,\"remainStock\":0,\"sourceType\":2}],\"promoPrice\":170.00,\"promoPriceTag\":\"¥170\",\"promoQuantityLimit\":0,\"promoTag\":\"共省¥88\",\"promoTagType\":60,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":88.00,\"totalPromoPriceTag\":\"-¥¥88\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"salePrice\":170.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"猫咪洗澡·3-7KG\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":*********,\"actProductType\":1,\"activities\":[],\"basePrice\":100.00,\"basePriceTag\":\"¥100\",\"beginDate\":0,\"categoryId\":1701,\"compositeScore\":0.0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"apply_pets_in_wash_beauty\",\"value\":\"猫咪\"},{\"name\":\"service_type\",\"value\":\"洗澡\"},{\"name\":\"hair_length_pet_cat\",\"value\":\"长毛\"},{\"name\":\"wegith_pet_cat\",\"value\":\"自定义体重区段3-7KG\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=733598891&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUv-wA1YtPPwwAYWml6QXXwdJoysUgj2g8F6BNjqSnplWLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZXCGWPkywVecwDC0_HQ1L6_BIg8tP78DPfyKLOGUNOoWsYhLOuV_dnAYaZ62MyDnUixpLlA98bPuDyLkhRSJUPht6DKKvzXfPbKpKTy_HdU_WjDSxxpX33KgqQVwFam6LcBPNR1BwUm8Fs39SIjMTua9D74URZOnvrR85NsmeX6EDKtg-T9DBkr2KZ2Ha340F0TqtSekUCZSrcoQ-xPcZfTv_qQwKucawLkZJf5i9vsNUn0RA0qzyasZQKhnYQpJiL1tc4djgfS--96FhTELgB6W79dkk0j424Ir5x-cX46tlbLUXmUeWuIYk30H_O1BF9EfG5rRlgQh1x-USaKk6Q0e6q4aqxPa-XnwPgScsw6rMJIyb8Z_Wo04rZrVBngQYM9FjIX7d91cOgIlYJHsuXX1MkYwYORoNcCpTAATlzfeOAWGtsHj52B-pb0omOJ30v-5szmASBi3KTXp4mVoO7iZ1MJkCbYAQylcCLt92smA\",\"orderUsers\":[],\"productId\":*********,\"productTagList\":[],\"productType\":1,\"promoPrices\":[{\"discount\":0.54,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"https://p1.meituan.net/travelcube/83518c6e14ec54a491bac580cb43d2fe4222.png\",\"pricePromoInfoMap\":{},\"promoItemList\":[{\"amount\":68.00,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":68.00,\"promoTag\":\"-¥68\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1},{\"amount\":1E+1,\"canAssign\":false,\"desc\":\"满99元减10元\",\"effectiveEndTime\":0,\"endTime\":1714406399000,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"minConsumptionAmount\":99,\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"MERCHANT_COUPON\",\"subTitle\":\"满99元减10元\",\"title\":\"商家优惠券\"},\"promoPrice\":1E+1,\"promoTag\":\"-¥10\",\"promoType\":\"商家优惠券\",\"promoTypeCode\":5,\"remainStock\":0,\"sourceType\":2}],\"promoPrice\":90.00,\"promoPriceTag\":\"¥90\",\"promoQuantityLimit\":0,\"promoTag\":\"共省¥78\",\"promoTagType\":60,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":78.00,\"totalPromoPriceTag\":\"-¥¥78\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"salePrice\":90.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"猫咪洗澡·3-7KG·长毛\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":*********,\"actProductType\":1,\"activities\":[],\"basePrice\":241.00,\"basePriceTag\":\"¥241\",\"beginDate\":0,\"categoryId\":1701,\"compositeScore\":0.0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"apply_pets_in_wash_beauty\",\"value\":\"猫咪\"},{\"name\":\"service_type\",\"value\":\"洗澡\"},{\"name\":\"hair_length_pet_cat\",\"value\":\"长毛\"},{\"name\":\"wegith_pet_cat\",\"value\":\"自定义体重区段3-7KG\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=733598891&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUv-wA1YtPPwwAYWml6QXXwdJoysUgj2g8F6BNjqSnplWLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZXCGWPkywVecwDC0_HQ1L6_BIg8tP78DPfyKLOGUNOoWsYhLOuV_dnAYaZ62MyDnUixpLlA98bPuDyLkhRSJUPht6DKKvzXfPbKpKTy_HdU_WjDSxxpX33KgqQVwFam6LcBPNR1BwUm8Fs39SIjMTua9D74URZOnvrR85NsmeX6EDKtg-T9DBkr2KZ2Ha340F0TqtSekUCZSrcoQ-xPcZfTv_qQwKucawLkZJf5i9vsNUn0RA0qzyasZQKhnYQpJiL1tc4djgfS--96FhTELgB6W79dkk0j424Ir5x-cX46tlbLUXmUeWuIYk30H_O1BF9EfG5rRlgQh1x-USaKk6Q0e6q4aqxPa-XnwPgScsw6rMJIyb8Z_Wo04rZrVBngQYM9FjIX7d91cOgIlYJHsuXX1MkYwYORoNcCpTAATlzfeOAWGtsHj52B-pb0omOJ30v-5szmASBi3KTXp4mVoO7iZ1MJkCbYAQylcCLt92smA\",\"orderUsers\":[],\"productId\":*********,\"productTagList\":[],\"productType\":1,\"promoPrices\":[{\"discount\":0.79,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"https://p0.meituan.net/travelcube/fa34868fbc0d453719f5ef682f26fa424322.png\",\"pricePromoInfoMap\":{},\"promoItemList\":[{\"amount\":27.00,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":27.00,\"promoTag\":\"-¥27\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1},{\"amount\":3E+1,\"canAssign\":false,\"desc\":\"品牌新用户专享，首单立省30元\",\"effectiveEndTime\":0,\"endTime\":1717862399000,\"icon\":\"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\"newUser\":true,\"promoId\":1770603525,\"promoIdentity\":\"priceDisplayCustomizeShopNewUser\",\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\"promoDivideType\":\"NEW_CUSTOMER_DISCOUNT\",\"subTitle\":\"品牌新用户专享，首单立省30元\",\"title\":\"新客特惠\"},\"promoPrice\":3E+1,\"promoTag\":\"-¥30\",\"promoType\":\"新客特惠\",\"promoTypeCode\":1,\"remainStock\":2,\"sourceType\":2}],\"promoPrice\":211.00,\"promoPriceTag\":\"¥211\",\"promoQuantityLimit\":0,\"promoTag\":\"共省¥57\",\"promoTagType\":50,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":57.00,\"totalPromoPriceTag\":\"-¥¥57\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"salePrice\":211.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"猫咪洗澡·3-7KG·长毛\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false},{\"actProductId\":*********,\"actProductType\":1,\"activities\":[],\"basePrice\":205.00,\"basePriceTag\":\"¥205\",\"beginDate\":0,\"categoryId\":1701,\"compositeScore\":0.0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"apply_pets_in_wash_beauty\",\"value\":\"猫咪\"},{\"name\":\"service_type\",\"value\":\"洗澡\"},{\"name\":\"hair_length_pet_cat\",\"value\":\"短毛\"},{\"name\":\"wegith_pet_cat\",\"value\":\"自定义体重区段1-3KG\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=733598891&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUv-wA1YtPPwwAYWml6QXXwdJoysUgj2g8F6BNjqSnplWLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZXCGWPkywVecwDC0_HQ1L6_BIg8tP78DPfyKLOGUNOoWsYhLOuV_dnAYaZ62MyDnUixpLlA98bPuDyLkhRSJUPht6DKKvzXfPbKpKTy_HdU_WjDSxxpX33KgqQVwFam6LcBPNR1BwUm8Fs39SIjMTua9D74URZOnvrR85NsmeX6EDKtg-T9DBkr2KZ2Ha340F0TqtSekUCZSrcoQ-xPcZfTv_qQwKucawLkZJf5i9vsNUn0RA0qzyasZQKhnYQpJiL1tc4djgfS--96FhTELgB6W79dkk0j424Ir5x-cX46tlbLUXmUeWuIYk30H_O1BF9EfG5rRlgQh1x-USaKk6Q0e6q4aqxPa-XnwPgScsw6rMJIyb8Z_Wo04rZrVBngQYM9FjIX7d91cOgIlYJHsuXX1MkYwYORoNcCpTAATlzfeOAWGtsHj52B-pb0omOJ30v-5szmASBi3KTXp4mVoO7iZ1MJkCbYAQylcCLt92smA\",\"orderUsers\":[],\"productId\":*********,\"productTagList\":[],\"productType\":1,\"promoPrices\":[{\"discount\":0.82,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"https://p1.meituan.net/travelcube/83518c6e14ec54a491bac580cb43d2fe4222.png\",\"pricePromoInfoMap\":{},\"promoItemList\":[{\"amount\":23.00,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":23.00,\"promoTag\":\"-¥23\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1},{\"amount\":2E+1,\"canAssign\":false,\"desc\":\"满199元减20元\",\"effectiveEndTime\":0,\"endTime\":1714406399000,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"minConsumptionAmount\":199,\"newUser\":false,\"promoId\":*********,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"MERCHANT_COUPON\",\"subTitle\":\"满199元减20元\",\"title\":\"商家优惠券\"},\"promoPrice\":2E+1,\"promoTag\":\"-¥20\",\"promoType\":\"商家优惠券\",\"promoTypeCode\":5,\"remainStock\":0,\"sourceType\":2}],\"promoPrice\":185.00,\"promoPriceTag\":\"¥185\",\"promoQuantityLimit\":0,\"promoTag\":\"共省¥43\",\"promoTagType\":60,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":43.00,\"totalPromoPriceTag\":\"-¥¥43\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"salePrice\":185.00,\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"timesDeal\":false,\"timesDealQueryFlag\":false,\"title\":\"猫咪洗澡·1-3KG·短毛\",\"top\":false,\"unifyProduct\":false,\"userSubscribe\":false}]";
        List<ProductM> productMS = JSON.parseArray(productJSON, ProductM.class);
        for (ProductM productM : productMS) {
            productM.setAttr("productType", "deal");
            productM.setAttr("dealStatusAttr", "true");
            productM.setAttr("attr_search_hidden_status", "false");
        }
        return productMS;
    }

    private static Map<String, Object> getParams() {
        String paramsJSON = "{\"ctxShop\":{\"category\":34043,\"cityId\":1,\"distanceNum\":0.0,\"lat\":31.270971,\"lng\":121.530611,\"longShopId\":733598891,\"score\":0.0,\"shopId\":733598891,\"shopName\":\"Cheers Cat萌宠猫舍\",\"shopType\":95,\"shopUuid\":\"l5kCwykryTJrzf3B\",\"status\":5,\"useType\":0,\"userEqShop\":false},\"appVersion\":\"12.20.400\",\"unsupportedSortNavTags\":\"\",\"reCountProducts\":false,\"channel\":\"dealFilterList\",\"abilityClassMap\":{\"FilterFetcher\":\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcher\",\"ProductFetcher\":\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher\"},\"pageSize\":300,\"mtPoiId\":733598891,\"deviceId\":\"0000000000000E5683A9334F64405BF2E4FE7E4A6E339A170361054628084189\",\"mtUserId\":1613531182,\"platform\":2,\"extPointClassMap\":{\"FilterFetcherExt\":\"com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultipleConfigFilterFetcherExt\"},\"filterComponent2Group\":{\"团购\":\"团购\"},\"dpCityId\":1,\"clientType\":\"ios\",\"dpPoiIdL\":733598891,\"pageNo\":1,\"lat\":31.27306001431404,\"productComponent2Group\":{\"团购\":\"团购\"},\"unionId\":\"e5683a9334f64405bf2e4fe7e4a6e339a170361054628084189\",\"lng\":121.5262829347652,\"mtCityId\":10,\"userAgent\":200,\"entityId\":\"*********\",\"mtPoiIdL\":733598891,\"selectedFilterId\":200122937,\"topN\":0,\"spaceKey\":\"deal_detail_list\",\"dpPoiId\":733598891,\"groupNames\":[\"团购\"],\"groupParams\":{\"团购\":{\"paddingType\":\"dealThemePadding\",\"attributeKeys\":[\"apply_pets\",\"apply_pets_in_wash_beauty\",\"service_type\",\"body_type_pet_dog\",\"wegith_pet_cat\",\"hair_length_pet_dog\",\"hair_length_pet_cat\",\"apply_pet\",\"pet_type\",\"available_count_pet\",\"available_time_pet\",\"pet_target\",\"pet_weight\",\"standardDealGroupKey\",\"preSaleTag\"],\"planId\":\"10002463\",\"directPromoScene\":400200,\"priceDescType\":4,\"enablePreSalePromoTag\":true,\"dealShelfStyleType\":1}},\"sortid\":0,\"timesDealQueryFlag\":false,\"selectPerFirstLeafNode\":false,\"pageSource\":\"shelf\",\"shopType\":95,\"category\":34043,\"dealCategoryId\":1701}";
        return JSON.parseObject(paramsJSON, new TypeReference<Map<String, Object>>(){});
    }
}
