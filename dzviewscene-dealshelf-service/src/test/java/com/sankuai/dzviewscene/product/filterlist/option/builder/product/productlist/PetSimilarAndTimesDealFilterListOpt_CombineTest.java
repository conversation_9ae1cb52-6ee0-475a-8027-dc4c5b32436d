package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class PetSimilarAndTimesDealFilterListOpt_CombineTest {

    private PetSimilarAndTimesDealFilterListOpt petSimilarAndTimesDealFilterListOpt;

    @Before
    public void setUp() {
        petSimilarAndTimesDealFilterListOpt = new PetSimilarAndTimesDealFilterListOpt();
    }

    @Test
    public void testCombineBothEmpty() {
        List<ProductM> result = petSimilarAndTimesDealFilterListOpt.combine(Arrays.asList(), Arrays.asList());
        assertEquals(0, result.size());
    }

    @Test
    public void testCombineTimesDealResEmpty() {
        ProductM product = new ProductM();
        product.setProductId(1);
        List<ProductM> result = petSimilarAndTimesDealFilterListOpt.combine(Arrays.asList(), Arrays.asList(product));
        assertEquals(1, result.size());
        assertEquals(product, result.get(0));
    }

    @Test
    public void testCombinePetSimilarResEmpty() {
        ProductM product = new ProductM();
        product.setProductId(1);
        List<ProductM> result = petSimilarAndTimesDealFilterListOpt.combine(Arrays.asList(product), Arrays.asList());
        assertEquals(1, result.size());
        assertEquals(product, result.get(0));
    }

    @Test
    public void testCombineNoDuplicate() {
        ProductM product1 = new ProductM();
        product1.setProductId(1);
        ProductM product2 = new ProductM();
        product2.setProductId(2);
        List<ProductM> result = petSimilarAndTimesDealFilterListOpt.combine(Arrays.asList(product1), Arrays.asList(product2));
        assertEquals(2, result.size());
        assertEquals(product1, result.get(0));
        assertEquals(product2, result.get(1));
    }

    @Test
    public void testCombineWithDuplicate() {
        ProductM product1 = new ProductM();
        product1.setProductId(1);
        ProductM product2 = new ProductM();
        product2.setProductId(1);
        List<ProductM> result = petSimilarAndTimesDealFilterListOpt.combine(Arrays.asList(product1), Arrays.asList(product2));
        assertEquals(1, result.size());
        assertEquals(product1, result.get(0));
    }
}
