package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.title.PetSimilarAndTimesDealTitleOpt;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @ClassName PetSimilarAndTimesDealTitleOptTest.java
 * @Description TODO
 * @createTime 2024/04/22 19:33
 */

@RunWith(MockitoJUnitRunner.class)
public class PetSimilarAndTimesDealTitleOptTest {
    @Mock
    private ActivityCxt context;
    @Mock
    private ProductTitleVP.Param mockParam;
    private PetSimilarAndTimesDealTitleOpt.Config mockConfig = new PetSimilarAndTimesDealTitleOpt.Config();

    private PetSimilarAndTimesDealTitleOpt petSimilarAndTimesDealTitleOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        petSimilarAndTimesDealTitleOpt = new PetSimilarAndTimesDealTitleOpt();
    }


    @Test
    public void testCompute() {
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setTradeType(20);
        productM.setTitle("title");
        when(mockParam.getProductM()).thenReturn(productM);
        String title = petSimilarAndTimesDealTitleOpt.compute(context, mockParam, mockConfig);
        assertNotNull(title);

        productM.setTradeType(19);
        productM.setAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER, "3");
        productM.setSalePrice(new BigDecimal("110"));
        when(mockParam.getProductM()).thenReturn(productM);
        title = petSimilarAndTimesDealTitleOpt.compute(context, mockParam, mockConfig);
        assertNotNull(title);
    }
}
