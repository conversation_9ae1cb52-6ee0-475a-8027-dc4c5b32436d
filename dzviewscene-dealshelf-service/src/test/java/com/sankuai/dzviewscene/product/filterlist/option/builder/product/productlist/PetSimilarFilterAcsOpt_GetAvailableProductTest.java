package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarFilterAcsOpt;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarFilterAcsOpt.Config;
import com.sankuai.dzviewscene.product.enums.DealCategoryEnum;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class PetSimilarFilterAcsOpt_GetAvailableProductTest {

    private PetSimilarFilterAcsOpt petSimilarFilterAcsOpt;

    private ProductM productM;

    private ProductM topProduct;

    private Config config;

    @Before
    public void setUp() {
        petSimilarFilterAcsOpt = new PetSimilarFilterAcsOpt();
        productM = new ProductM();
        topProduct = new ProductM();
        config = new Config();
    }

    @Test
    public void testGetAvailableProductWhenProductListSizeIsOne() {
        assertFalse(petSimilarFilterAcsOpt.getAvailableProduct(productM, topProduct, Collections.singletonList(productM), config));
    }

    @Test
    public void testGetAvailableProductWhenProductIdIsSameAsTopProductId() {
        productM.setProductId(1);
        topProduct.setProductId(1);
        assertTrue(petSimilarFilterAcsOpt.getAvailableProduct(productM, topProduct, Arrays.asList(productM, topProduct), config));
    }
    // 其他测试用例...
}
