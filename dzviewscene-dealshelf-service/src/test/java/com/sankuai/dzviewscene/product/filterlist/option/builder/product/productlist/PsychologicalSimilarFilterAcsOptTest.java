package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.BaseDealIdQueryHandler;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PsychologicalSimilarFilterAcsOptTest {

    private PsychologicalSimilarFilterAcsOpt psychologicalSimilarFilterAcsOpt = new PsychologicalSimilarFilterAcsOpt();

    @Mock
    private ActivityCxt context;

    @Mock
    private PsychologicalSimilarFilterAcsOpt.Param param;

    @Mock
    private PsychologicalSimilarFilterAcsOpt.Config config;

    @InjectMocks
    private PsychologicalSimilarFilterAcsOpt filter;

    /**
     * 测试 serviceType 为 null 或者空字符串的情况
     */
    @Test
    public void testIsInValidServiceTypeNullOrEmpty() throws Throwable {
        // arrange
        String serviceType = null;
        // act
        boolean result = psychologicalSimilarFilterAcsOpt.isInValidServiceType(serviceType);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 serviceType 不为 null 或者空字符串，但是不包含在 PSYCHOLOGICAL_CONSULT_TYPE 列表中的情况
     */
    @Test
    public void testIsInValidServiceTypeNotInList() throws Throwable {
        // arrange
        String serviceType = "心理课程";
        // act
        boolean result = psychologicalSimilarFilterAcsOpt.isInValidServiceType(serviceType);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 serviceType 不为 null 或者空字符串，且包含在 PSYCHOLOGICAL_CONSULT_TYPE 列表中的情况
     */
    @Test
    public void testIsInValidServiceTypeInList() throws Throwable {
        // arrange
        String serviceType = "心理咨询";
        // act
        boolean result = psychologicalSimilarFilterAcsOpt.isInValidServiceType(serviceType);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when ProductM object is null and categoryId is any value.
     * Adjusted to expect a NullPointerException as per the method's behavior.
     */
    @Test(expected = NullPointerException.class)
    public void testIsSameCategoryIdProductMIsNull() throws Throwable {
        psychologicalSimilarFilterAcsOpt.isSameCategoryId(null, 1);
    }

    /**
     * Test case when ProductM object is not null, but categoryId is null.
     * Adjusted to expect a NullPointerException as per the method's behavior.
     */
    @Test(expected = NullPointerException.class)
    public void testIsSameCategoryIdCategoryIdIsNull() throws Throwable {
        ProductM productM = new ProductM();
        productM.setCategoryId(1);
        psychologicalSimilarFilterAcsOpt.isSameCategoryId(productM, null);
    }

    /**
     * Test case when both ProductM object and categoryId are not null, but their categoryIds do not match.
     */
    @Test
    public void testIsSameCategoryIdNotEqual() throws Throwable {
        ProductM productM = new ProductM();
        productM.setCategoryId(1);
        assertFalse(psychologicalSimilarFilterAcsOpt.isSameCategoryId(productM, 2));
    }

    /**
     * Test case when both ProductM object and categoryId are not null, and their categoryIds match.
     */
    @Test
    public void testIsSameCategoryIdEqual() throws Throwable {
        ProductM productM = new ProductM();
        productM.setCategoryId(1);
        assertTrue(psychologicalSimilarFilterAcsOpt.isSameCategoryId(productM, 1));
    }

    @Test(expected = NullPointerException.class)
    public void testIsSameServiceTypeProductMIsNull() throws Throwable {
        PsychologicalSimilarFilterAcsOpt opt = new PsychologicalSimilarFilterAcsOpt();
        opt.isSameServiceType(null, "service_type");
    }

    @Test
    public void testIsSameServiceTypeServiceTypeIsNull() throws Throwable {
        PsychologicalSimilarFilterAcsOpt opt = new PsychologicalSimilarFilterAcsOpt();
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr("service_type")).thenReturn(null);
        assertFalse(opt.isSameServiceType(productM, "service_type"));
    }

    @Test
    public void testIsSameServiceTypeServiceTypeIsInvalid() throws Throwable {
        PsychologicalSimilarFilterAcsOpt opt = new PsychologicalSimilarFilterAcsOpt();
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr("service_type")).thenReturn("invalid_service_type");
        assertFalse(opt.isSameServiceType(productM, "service_type"));
    }

    @Test
    public void testIsSameServiceTypeServiceTypeIsValidButNotEqual() throws Throwable {
        PsychologicalSimilarFilterAcsOpt opt = new PsychologicalSimilarFilterAcsOpt();
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr("service_type")).thenReturn("valid_service_type");
        assertFalse(opt.isSameServiceType(productM, "service_type"));
    }

    @Test
    public void testIsSameServiceTypeServiceTypeIsValidAndEqual() throws Throwable {
        PsychologicalSimilarFilterAcsOpt opt = new PsychologicalSimilarFilterAcsOpt();
        ProductM productM = mock(ProductM.class);
        // Ensure the mock returns a valid service type to match the expected behavior
        // Use a valid service type
        when(productM.getAttr("service_type")).thenReturn("心理咨询");
        assertTrue(opt.isSameServiceType(productM, "心理咨询"));
    }

    @Test
    public void testComputeProductMSIsEmpty() throws Throwable {
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        List<ProductM> result = filter.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeProductMSIsNotEmptyButSimilarProductFilterReturnsEmptyList() throws Throwable {
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(new ProductM());
        when(param.getProductMS()).thenReturn(productMS);
        List<ProductM> result = filter.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeProductMSIsNotEmptyAndSimilarProductFilterReturnsNonEmptyList() throws Throwable {
        // Create the current product
        ProductM currentProduct = new ProductM();
        currentProduct.setProductId(1);
        currentProduct.setCategoryId(101);
        // Set up service_type attribute for current product
        List<AttrM> currentAttrs = new ArrayList<>();
        AttrM currentAttr = new AttrM();
        currentAttr.setName("service_type");
        currentAttr.setValue("心理咨询");
        currentAttrs.add(currentAttr);
        currentProduct.setExtAttrs(currentAttrs);
        // Create a similar product
        ProductM similarProduct = new ProductM();
        similarProduct.setProductId(2);
        similarProduct.setCategoryId(101);
        // Set up service_type attribute for similar product
        List<AttrM> similarAttrs = new ArrayList<>();
        AttrM similarAttr = new AttrM();
        similarAttr.setName("service_type");
        similarAttr.setValue("心理咨询");
        similarAttrs.add(similarAttr);
        similarProduct.setExtAttrs(similarAttrs);
        // Create the product list
        List<ProductM> productMS = new ArrayList<>();
        productMS.add(currentProduct);
        productMS.add(similarProduct);
        // Set up the parameters map
        Map<String, Object> parameters = new HashMap<>();
        // Match currentProduct's ID
        parameters.put("entityId", 1);
        // Mock the context
        when(context.getParameters()).thenReturn(parameters);
        // Mock param to return the product list
        when(param.getProductMS()).thenReturn(productMS);
        // Configure the limit in config
        when(config.getLimit()).thenReturn(10);
        // Execute the method
        List<ProductM> result = filter.compute(context, param, config);
        // Verify the result
        assertEquals("Should return one similar product", 1, result.size());
        // Additional verifications if the test passes
        if (!result.isEmpty()) {
            ProductM resultProduct = result.get(0);
            assertEquals("Should return the similar product", similarProduct.getProductId(), resultProduct.getProductId());
            assertEquals("Should have same category ID", currentProduct.getCategoryId(), resultProduct.getCategoryId());
            assertEquals("Should have same service type", "心理咨询", resultProduct.getAttr("service_type"));
        }
    }
}