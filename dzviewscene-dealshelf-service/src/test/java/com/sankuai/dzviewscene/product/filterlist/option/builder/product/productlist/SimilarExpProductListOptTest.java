package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class SimilarExpProductListOptTest {

    private SimilarExpProductListOpt similarListOpt;

    private ActivityCxt context;

    private ProductListVP.Param param;

    private SimilarExpProductListOpt.Config config;

    @Before
    public void setUp() {
        similarListOpt = new SimilarExpProductListOpt();
        context = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(ProductListVP.Param.class);
        config = Mockito.mock(SimilarExpProductListOpt.Config.class);
    }

    @Test
    public void testComputeContextIsNull() throws Throwable {
        List<ProductM> result = similarListOpt.compute(null, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeEntityIdIsNull() throws Throwable {
        when(context.getParameters()).thenReturn(new HashMap<>());
        List<ProductM> result = similarListOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeProductMSIsNull() throws Throwable {
        when(context.getParameters()).thenReturn(new HashMap<>());
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        List<ProductM> result = similarListOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeDouHuMSAndConfigAreNull() throws Throwable {
        when(context.getParameters()).thenReturn(new HashMap<>());
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        when(param.getDouHuMS()).thenReturn(null);
        when(config.getExpSk2Limit()).thenReturn(null);
        List<ProductM> result = similarListOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeConfigIsEmpty() throws Throwable {
        when(context.getParameters()).thenReturn(new HashMap<>());
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        when(param.getDouHuMS()).thenReturn(new ArrayList<>());
        when(config.getExpSk2Limit()).thenReturn(new HashMap<>());
        List<ProductM> result = similarListOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeGetLimitByDouHuMLessThanOrEqualToZero() throws Throwable {
        when(context.getParameters()).thenReturn(new HashMap<>());
        when(param.getProductMS()).thenReturn(new ArrayList<>());
        when(param.getDouHuMS()).thenReturn(new ArrayList<>());
        Map<String, Integer> expSk2Limit = new HashMap<>();
        expSk2Limit.put("key", 0);
        when(config.getExpSk2Limit()).thenReturn(expSk2Limit);
        List<ProductM> result = similarListOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeNormalCase() throws Throwable {
        when(context.getParameters()).thenReturn(new HashMap<>());
        List<ProductM> productMS = new ArrayList<>();
        ProductM product = new ProductM();
        product.setTitle("title");
        productMS.add(product);
        when(param.getProductMS()).thenReturn(productMS);
        when(param.getDouHuMS()).thenReturn(new ArrayList<>());
        Map<String, Integer> expSk2Limit = new HashMap<>();
        expSk2Limit.put("key", 1);
        when(config.getExpSk2Limit()).thenReturn(expSk2Limit);
        List<ProductM> result = similarListOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertEquals("title", result.get(0).getTitle());
    }
}
