package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 11:06
 */
public class SimilarListOptTest {

    public ActivityCxt getShelf() {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.pageSource, "shelf");
        context.addParam(ShelfActivityConstants.Params.entityId, 1);
        return context;
    }

    public ActivityCxt getGuess() {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.pageSource, "guess");
        return context;
    }

    public ProductM getProduct(String serviceType) {
        ProductM currentProduct = new ProductM();
        currentProduct.setTimesDealQueryFlag(true);
        currentProduct.setProductId(1);
        currentProduct.setSalePrice(BigDecimal.ONE);

        ProductSaleM sale = new ProductSaleM();
        sale.setSale(100);
        currentProduct.setSale(sale);

        AttrM attrM = new AttrM();
        attrM.setName("service_type");
        attrM.setValue(serviceType);

        AttrM attrM1 = new AttrM();
        attrM1.setName("productType");
        attrM1.setValue("deal");

        currentProduct.setExtAttrs(Lists.newArrayList(attrM, attrM1));
        return currentProduct;
    }


    public List<ProductM> getMassgeProductList() {
        return Lists.newArrayList(getProduct("足疗"), getProduct("足疗"));
    }

    public List<ProductM> getBathProductList() {
        return Lists.newArrayList(getProduct("浴资票"), getProduct("店内服务"));
    }


    public List<ProductM> getKtvProductList() {
        return Lists.newArrayList(getProduct("纯欢唱套餐"), getProduct("欢唱和含酒类套餐")
                , getProduct("欢唱和软饮类套餐"), getProduct("纯美食酒水套餐"));
    }


}
