package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Maps;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.mock;

/**
 * @author: created by hang.yu on 2024/3/4 19:03
 */
@RunWith(MockitoJUnitRunner.class)
public class TimesDealListOptTest extends SimilarListOptTest {

    @InjectMocks
    private TimesDealListOpt timesDealListOpt;

    @Test
    public void testCompute() {
        ActivityCxt context = mock(ActivityCxt.class);

        TimesDealListOpt timesDealListOpt = new TimesDealListOpt();

        ProductListVP.Param param = ProductListVP.Param.builder().build();
        List<ProductM> result = timesDealListOpt.compute(context, param, null);
        Assert.assertNotNull(result);

        ProductM productM0 = new ProductM();
        param.setProductMS(Lists.newArrayList(productM0));
        result = timesDealListOpt.compute(context, param, null);
        Assert.assertNotNull(result);

        ProductM productM1 = new ProductM();
        productM1.setSalePrice(BigDecimal.TEN);
        productM1.setTimesDealQueryFlag(true);
        productM1.setAttr("attr_search_hidden_status", "false");
        param.getProductMS().add(productM1);
        result = timesDealListOpt.compute(context, param, null);
        Assert.assertNotNull(result);

        ProductM productM2 = new ProductM();
        productM2.setSalePrice(BigDecimal.ONE);
        productM2.setTimesDealQueryFlag(true);
        productM2.setAttr("attr_search_hidden_status", "false");
        param.getProductMS().add(productM2);
        result = timesDealListOpt.compute(context, param, null);
        Assert.assertNotNull(result);

        productM1.setProductId(1);
        productM2.setProductId(2);

        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(ShelfActivityConstants.Params.entityId, 1);
        context.setParameters(parameters);
        result = timesDealListOpt.compute(context, param, null);
        Assert.assertNotNull(result);
    }


    @Test
    public void testIsValid() {
        TimesDealListOpt timesDealListOpt = new TimesDealListOpt();
        boolean valid = timesDealListOpt.isValid(null);
        Assert.assertFalse(valid);

        valid = timesDealListOpt.isValid(new ProductM());
        Assert.assertFalse(valid);

        ProductM productM = new ProductM();
        productM.setSalePrice(BigDecimal.ONE);
        valid = timesDealListOpt.isValid(productM);
        Assert.assertFalse(valid);


        productM.setAttr("productType", "deal");
        valid = timesDealListOpt.isValid(productM);
        Assert.assertFalse(valid);

        productM.setAttr("dealStatusAttr", "true");
        valid = timesDealListOpt.isValid(productM);
        Assert.assertFalse(valid);

        productM.setAttr("attr_search_hidden_status", "false");
        valid = timesDealListOpt.isValid(productM);
        Assert.assertFalse(valid);

        productM.setSale(new ProductSaleM());
        valid = timesDealListOpt.isValid(productM);
        Assert.assertTrue(valid);
    }

    @Test
    public void testGetSimilarDealList() {
        List<ProductM> similarDealList = timesDealListOpt.getSimilarDealList(null, null, null);
        Assert.assertNotNull(similarDealList);
    }


    @Test
    public void testGetCurrentProductM() {
        ProductM currentProductM = timesDealListOpt.getCurrentProductM(getMassgeProductList(), 1);
        Assert.assertNotNull(currentProductM);
    }

    @Test
    public void testGetTimesDealList() {
        ProductM productM = new ProductM();
        productM.setSalePrice(BigDecimal.ONE);
        List<ProductM> list = Lists.newArrayList(productM);
        List<ProductM> result = timesDealListOpt.getTimesDealList(list, 1);
        Assert.assertNotNull(result);

        productM.setTimesDealQueryFlag(true);
        result = timesDealListOpt.getTimesDealList(list, 1);
        Assert.assertNotNull(result);

        productM.setTradeType(19);
        result = timesDealListOpt.getTimesDealList(list, 1);
        Assert.assertNotNull(result);

        List<ProductM> timesDealList = timesDealListOpt.getTimesDealList(list, 1);
        Assert.assertNotNull(timesDealList);
    }

    @Test
    public void testGetProductCategoryList() {
        List<Long> productCategoryList = timesDealListOpt.getProductCategoryList(getProduct("足疗"));
        Assert.assertNotNull(productCategoryList);
    }


}