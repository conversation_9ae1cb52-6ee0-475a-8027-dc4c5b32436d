package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductRichTagsVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.specialtags.AggregateItemRecommendTagOpt;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EduRecommendReasonRichTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    @Mock
    private Config config;

    @InjectMocks
    private EduRecommendReasonRichTagsOpt eduRecommendReasonRichTagsOpt;

    private com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Param createParamWithProductM(ProductTypeEnum productTypeEnum) {
        ProductM productM = new ProductM();
        productM.setProductType(productTypeEnum.getType());
        return com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Param.builder().productM(productM).build();
    }

    @Test
    public void test_getPhotoAttrValues_null() {
        ProductM productM = new ProductM();
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        EduRecommendReasonRichTagsOpt.TagCfg tagCfg = new EduRecommendReasonRichTagsOpt.TagCfg();
        List<String> attrValues = Lists.newArrayList();
        attrValues.add("不满意随时退");
        EduRecommendReasonRichTagsOpt opt = new EduRecommendReasonRichTagsOpt();
        List<String> photoAttrValues = opt.getPhotoAttrValues(productM, tagCfg, attrValues);
        Assert.assertTrue(CollectionUtils.isNotEmpty(photoAttrValues));
    }

    @Test
    public void test_getPhotoAttrValues() {
        ProductM productM = new ProductM();
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setAttr("goodReviewRateCompetitiveTag", "销量高于xxxx");
        EduRecommendReasonRichTagsOpt.TagCfg tagCfg = new EduRecommendReasonRichTagsOpt.TagCfg();
        tagCfg.setAttrKeys(Lists.newArrayList("goodReviewRateCompetitiveTag"));
        List<String> attrValues = Lists.newArrayList();
        EduRecommendReasonRichTagsOpt opt = new EduRecommendReasonRichTagsOpt();
        List<String> photoAttrValues = opt.getPhotoAttrValues(productM, tagCfg, attrValues);
        System.out.println(JSON.toJSONString(photoAttrValues));
        Assert.assertTrue(CollectionUtils.isNotEmpty(photoAttrValues));
    }

    @Test
    public void testComputeProductMIsNull() throws Throwable {
        // Setup
        com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Param param = createParamWithProductM(ProductTypeEnum.UNKNOWN);
        // Execution
        List<IconRichLabelVO> result = eduRecommendReasonRichTagsOpt.compute(activityCxt, param, config);
        // Verification
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeProductMIsGeneralSpu() throws Throwable {
        // Setup
        com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Param param = createParamWithProductM(ProductTypeEnum.GENERAL_SPU);
        // Execution
        List<IconRichLabelVO> result = eduRecommendReasonRichTagsOpt.compute(activityCxt, param, config);
        // Verification
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeProductMIsDeal() throws Throwable {
        // Setup
        com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Param param = createParamWithProductM(ProductTypeEnum.DEAL);
        // Execution
        List<IconRichLabelVO> result = eduRecommendReasonRichTagsOpt.compute(activityCxt, param, config);
        // Verification
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeProductMIsOther() throws Throwable {
        // Setup
        com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Param param = createParamWithProductM(ProductTypeEnum.SPU);
        // Execution
        List<IconRichLabelVO> result = eduRecommendReasonRichTagsOpt.compute(activityCxt, param, config);
        // Verification
        assertTrue(result.isEmpty());
    }


    @Test
    public void testDealShopNum() throws Throwable {
        // Setup
        com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags.EduRecommendReasonRichTagsOpt.Param param = createParamWithProductM(ProductTypeEnum.DEAL);
        param.getProductM().setAttr("from_group", "置顶");
        param.getProductM().setAttr("dealApplyShopNumberAttr", "100");
        EduRecommendReasonRichTagsOpt.TagCfg tagCfg = new EduRecommendReasonRichTagsOpt.TagCfg();
        tagCfg.setShowNonHeadProductTag(false);
        tagCfg.setShowSingleTag(false);
        tagCfg.setAttrVals(Lists.newArrayList("%s家门店通用"));
        when(config.getDealAttrCfg()).thenReturn(Lists.newArrayList(tagCfg));
        when(config.isShowSpecificTag()).thenReturn(true);
        // Execution
        List<IconRichLabelVO> result = eduRecommendReasonRichTagsOpt.compute(activityCxt, param, config);
        // Verification
        assertEquals(result.size(),1);
    }

}
