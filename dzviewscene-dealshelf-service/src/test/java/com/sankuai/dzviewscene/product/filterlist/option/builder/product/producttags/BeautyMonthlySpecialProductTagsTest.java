package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import org.junit.runner.RunWith;
import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;

@RunWith(MockitoJUnitRunner.class)
public class BeautyMonthlySpecialProductTagsTest {

    private BeautyMonthlySpecialProductTags beautyMonthlySpecialProductTags;

    @Before
    public void setUp() {
        beautyMonthlySpecialProductTags = new BeautyMonthlySpecialProductTags();
    }

    private ProductTagsVP.Param createParam(ProductM productM) throws Exception {
        Constructor<ProductTagsVP.Param> constructor = ProductTagsVP.Param.class.getDeclaredConstructor(ProductM.class);
        constructor.setAccessible(true);
        return constructor.newInstance(productM);
    }

    @Test
    public void testComputeWhenExtAttrsIsEmpty() throws Throwable {
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ProductM productM = mock(ProductM.class);
        when(productM.getExtAttrs()).thenReturn(Collections.emptyList());
        ProductTagsVP.Param param = createParam(productM);
        BeautyMonthlySpecialProductTags.Config config = new BeautyMonthlySpecialProductTags.Config();
        List<String> result = beautyMonthlySpecialProductTags.compute(activityCxt, param, config);
        assertEquals(Arrays.asList("限时抢购", "随时退"), result);
    }

    @Test
    public void testComputeWhenExtAttrsIsNotEmptyButNoMatchedName() throws Throwable {
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ProductM productM = mock(ProductM.class);
        AttrM attrM = mock(AttrM.class);
        when(attrM.getName()).thenReturn("otherName");
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(attrM));
        ProductTagsVP.Param param = createParam(productM);
        BeautyMonthlySpecialProductTags.Config config = new BeautyMonthlySpecialProductTags.Config();
        List<String> result = beautyMonthlySpecialProductTags.compute(activityCxt, param, config);
        assertEquals(Arrays.asList("限时抢购", "随时退"), result);
    }

    @Test
    public void testComputeWhenExtAttrsIsNotEmptyAndMatchedNameWithValue1() throws Throwable {
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ProductM productM = mock(ProductM.class);
        AttrM attrM = mock(AttrM.class);
        when(attrM.getName()).thenReturn("dealApplyShopNumberAttr");
        when(attrM.getValue()).thenReturn("1");
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(attrM));
        ProductTagsVP.Param param = createParam(productM);
        BeautyMonthlySpecialProductTags.Config config = new BeautyMonthlySpecialProductTags.Config();
        List<String> result = beautyMonthlySpecialProductTags.compute(activityCxt, param, config);
        assertEquals(Arrays.asList("限时抢购", "随时退", "本店通用"), result);
    }

    @Test
    public void testComputeWhenExtAttrsIsNotEmptyAndMatchedNameWithValueNot1() throws Throwable {
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ProductM productM = mock(ProductM.class);
        AttrM attrM = mock(AttrM.class);
        when(attrM.getName()).thenReturn("dealApplyShopNumberAttr");
        when(attrM.getValue()).thenReturn("2");
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(attrM));
        ProductTagsVP.Param param = createParam(productM);
        BeautyMonthlySpecialProductTags.Config config = new BeautyMonthlySpecialProductTags.Config();
        List<String> result = beautyMonthlySpecialProductTags.compute(activityCxt, param, config);
        assertEquals(Arrays.asList("限时抢购", "随时退", "2店通用"), result);
    }
}
