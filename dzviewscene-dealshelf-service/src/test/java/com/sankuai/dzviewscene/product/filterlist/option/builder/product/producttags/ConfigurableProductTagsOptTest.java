package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * @author: wuwenqiang
 * @create: 2024-07-24
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class ConfigurableProductTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private ProductM productM;
    @Mock
    private ProductTagsVP.Param param;

    private ConfigurableProductTagsOpt.Config config;
    private ConfigurableProductTagsOpt configurableProductTagsOpt;

    @Before
    public void setUp() {
        configurableProductTagsOpt = new ConfigurableProductTagsOpt();
        config = new ConfigurableProductTagsOpt.Config();
    }

    /**
     * 测试配置为空的场景
     */
    @Test
    public void testComputeConfigNull() {
        List<String> result = configurableProductTagsOpt.compute(activityCxt, param, null);

        assertEquals(0, result.size());
    }

    /**
     * 测试产品模型为空的场景
     */
    @Test
    public void testComputeProductMNull() {
        config.setAttrKeys(Arrays.asList("key1"));
        when(param.getProductM()).thenReturn(null);

        List<String> result = configurableProductTagsOpt.compute(activityCxt, param, config);

        assertEquals(0, result.size());
    }

    /**
     * 测试属性值为空的场景
     */
    @Test
    public void testComputeAttrValNull() {
        config.setAttrKeys(Arrays.asList("key1"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("key1")).thenReturn(null);

        List<String> result = configurableProductTagsOpt.compute(activityCxt, param, config);

        assertEquals(0, result.size());
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testComputeNormalScenario() {
        config.setAttrKeys(Arrays.asList("key1", "key2"));
        Map<String, String> attrsTemplate = Maps.newHashMap();
        attrsTemplate.put("key1", "Template %s");
        config.setAttrsTemplate(attrsTemplate);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("key1")).thenReturn("value1");
        when(productM.getAttr("key2")).thenReturn("value2");

        List<String> result = configurableProductTagsOpt.compute(activityCxt, param, config);

        assertEquals(Arrays.asList("Template value1", "value2"), result);
    }

    /**
     * 测试属性从objAttr获取的情况
     */
    @Test
    public void testComputeAttrValFromObjAttr() {
        config.setAttrKeys(Arrays.asList("key1", "key2"));
        Map<String, String> attrsTemplate = Maps.newHashMap();
        attrsTemplate.put("key1", "Template %s");
        config.setAttrsTemplate(attrsTemplate);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("key1")).thenReturn("value1");
        when(productM.getObjAttr("key2")).thenReturn("value2");

        List<String> result = configurableProductTagsOpt.compute(activityCxt, param, config);

        assertEquals(Arrays.asList("Template value1", "value2"), result);
    }

}
