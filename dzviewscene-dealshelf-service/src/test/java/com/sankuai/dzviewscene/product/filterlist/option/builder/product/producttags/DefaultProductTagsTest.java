package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductTagsTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DefaultProductTags.Param param;

    @Mock
    private DefaultProductTags.Config config;

    @Mock
    private ProductM // Corrected type
    productM;

    /**
     * Tests compute method when ProductM object is not null and productTags list is not empty.
     */
    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        DefaultProductTags defaultProductTags = new DefaultProductTags();
        when(param.getProductM()).thenReturn(productM);
        // Assuming getProductTags() is the correct method to use
        when(productM.getProductTags()).thenReturn(Arrays.asList("tag1", "tag2"));
        // act
        List<String> result = defaultProductTags.compute(activityCxt, param, config);
        // assert
        assertEquals(Arrays.asList("tag1", "tag2"), result);
    }

    /**
     * Tests compute method when ProductM object is null.
     */
    @Test(expected = NullPointerException.class)
    public void testComputeProductMIsNull() throws Throwable {
        // arrange
        DefaultProductTags defaultProductTags = new DefaultProductTags();
        when(param.getProductM()).thenReturn(null);
        // act
        defaultProductTags.compute(activityCxt, param, config);
    }

    /**
     * Tests compute method when ProductM object is not null, but productTags list is empty.
     */
    @Test
    public void testComputeProductTagsIsEmpty() throws Throwable {
        // arrange
        DefaultProductTags defaultProductTags = new DefaultProductTags();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(Arrays.asList());
        // act
        List<String> result = defaultProductTags.compute(activityCxt, param, config);
        // assert
        assertEquals(Arrays.asList(), result);
    }
}
