package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * PressOnNailListProductTagsOpt测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class PressOnNailListProductTagsOptTest {

    private PressOnNailListProductTagsOpt pressOnNailListProductTagsOpt;
    private ActivityCxt context;
    private PressOnNailListProductTagsOpt.Param param;
    private PressOnNailListProductTagsOpt.Config config;

    private MockedStatic<PressOnNailUtils> pressOnNailUtilsMockedStatic;

    private MockedStatic<DealDetailUtils> dealDetailUtilsMockedStatic;

    @Before
    public void setUp() {
        pressOnNailListProductTagsOpt = new PressOnNailListProductTagsOpt();
        context = mock(ActivityCxt.class);
        param = mock(PressOnNailListProductTagsOpt.Param.class);
        config = new PressOnNailListProductTagsOpt.Config();
        pressOnNailUtilsMockedStatic = mockStatic(PressOnNailUtils.class);
        dealDetailUtilsMockedStatic = mockStatic(DealDetailUtils.class);
    }

    @After
    public void tearDown() {
        pressOnNailUtilsMockedStatic.close();
        dealDetailUtilsMockedStatic.close();
    }

    /**
     * 测试产品为空时返回空列表
     */
    @Test
    public void testComputeWithNullProductReturnsEmptyList() {
        when(param.getProductM()).thenReturn(null);

        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试产品扩展属性为空时返回空列表
     */
    @Test
    public void testComputeWithEmptyExtAttrsReturnsEmptyList() {
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(Collections.emptyList());

        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试穿戴甲独占时返回空列表
     */
    @Test
    public void testComputeWithExclusivePressOnNailReturnsEmptyList() {
        ProductM productM = mock(ProductM.class);
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("name", "value"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        pressOnNailUtilsMockedStatic.when(() -> PressOnNailUtils.checkExclusive(any())).thenReturn(true);
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试到店免费佩戴标签添加成功
     */
    @Test
    public void testComputeWithCanWearAtStoreAddsTag() {
        ProductM productM = mock(ProductM.class);
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("name", "value"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        pressOnNailUtilsMockedStatic.when(() -> PressOnNailUtils.checkExclusive(any())).thenReturn(false);
        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.isCanWearAtStore(attrs)).thenReturn(true);

        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);

        assertFalse(result.isEmpty());
        assertTrue(StringUtils.isNotBlank(result.get(0)));
    }

    /**
     * 测试穿戴甲类型标签添加成功
     */
    @Test
    public void testComputeWithWearingNailsTypeAddsTag() {
        ProductM productM = mock(ProductM.class);
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("wearingNailsType", "普通"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        pressOnNailUtilsMockedStatic.when(() -> PressOnNailUtils.checkExclusive(any())).thenReturn(false);

        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试附加项目标签添加成功
     */
    @Test
    public void testComputeWithNailAdditionalItemAddsTag() {
        ProductM productM = mock(ProductM.class);
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("nail_additional_item", "[\"附加项目1\"]"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        pressOnNailUtilsMockedStatic.when(() -> PressOnNailUtils.checkExclusive(any())).thenReturn(false);

        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);

        assertTrue(result.isEmpty());
    }
}
