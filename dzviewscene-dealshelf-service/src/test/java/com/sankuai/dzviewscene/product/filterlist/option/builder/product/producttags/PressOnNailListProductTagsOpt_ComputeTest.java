package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class PressOnNailListProductTagsOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private PressOnNailListProductTagsOpt.Config config;

    @Mock
    private PressOnNailListProductTagsOpt.Param param;

    @Mock
    private ProductM productM;

    private PressOnNailListProductTagsOpt pressOnNailListProductTagsOpt;

    public PressOnNailListProductTagsOpt_ComputeTest() {
        MockitoAnnotations.initMocks(this);
        pressOnNailListProductTagsOpt = new PressOnNailListProductTagsOpt();
    }

    private void mockContextGetParamForList() {
        when(context.getParam(anyString())).thenReturn(new ArrayList<Long>());
    }

    private void assertJsonEquals(String expectedJson, String actualJson) {
        Map<String, Object> expectedMap = JSON.parseObject(expectedJson, Map.class);
        Map<String, Object> actualMap = JSON.parseObject(actualJson, Map.class);
        assertEquals(expectedMap, actualMap);
    }

    @Test
    public void testComputeWithNullProductM() throws Throwable {
        mockContextGetParamForList();
        when(param.getProductM()).thenReturn(null);
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWithEmptyExtAttrs() throws Throwable {
        mockContextGetParamForList();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(new ArrayList<>());
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWithCheckExclusiveTrue() throws Throwable {
        when(context.getParam(anyString())).thenReturn(Arrays.asList(12345L));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(new ArrayList<>());
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWithCanWearAtStoreTrue() throws Throwable {
        mockContextGetParamForList();
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("isFreeWearingAtStore", "true"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertJsonEquals("{\"text\":\"到店免费佩戴\",\"textcolor\":\"#FF6633\",\"textsize\":11}", result.get(0));
    }

    @Test
    public void testComputeWithWearingNailsType() throws Throwable {
        mockContextGetParamForList();
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("wearingNailsType", "普通"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertJsonEquals("{\"text\":\"普通\",\"textcolor\":\"#777777\",\"textsize\":11}", result.get(0));
    }

    @Test
    public void testComputeWithAdditionalItemJson() throws Throwable {
        mockContextGetParamForList();
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("nail_additional_item", "[\"颜色\",\"款式\"]"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertJsonEquals("{\"text\":\"颜色\",\"textcolor\":\"#777777\",\"textsize\":11}", result.get(0));
    }

    @Test
    public void testComputeWithAdditionalItemNonJson() throws Throwable {
        mockContextGetParamForList();
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("nail_additional_item", "颜色,款式"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertJsonEquals("{\"text\":\"颜色\",\"textcolor\":\"#777777\",\"textsize\":11}", result.get(0));
    }

    @Test
    public void testComputeWithAdditionalItemEmpty() throws Throwable {
        mockContextGetParamForList();
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("nail_additional_item", ""));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(attrs);
        List<String> result = pressOnNailListProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }
}
