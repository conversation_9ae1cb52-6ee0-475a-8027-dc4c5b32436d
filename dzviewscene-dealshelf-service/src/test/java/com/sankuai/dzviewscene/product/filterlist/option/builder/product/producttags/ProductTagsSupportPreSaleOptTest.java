package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.ItemProductTagsSupportPreSaleOpt;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * <AUTHOR>
 * @since 2024/4/28 14:01
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductTagsSupportPreSaleOptTest {
    private ProductTagsSupportPreSaleOpt productTagsSupportPreSaleOpt;
    private ProductTagsSupportPreSaleOpt.Config config;
    private ProductM productM;
    private List<String> result;
    private ActivityCxt activityCxt;
    private ProductTagsSupportPreSaleOpt.Param param;

    @Before
    public void setUp() {
        productTagsSupportPreSaleOpt = new ProductTagsSupportPreSaleOpt();
        config = new ProductTagsSupportPreSaleOpt.Config();
        productM = new ProductM();
        result = new ArrayList<>();
        param = Mockito.mock(ProductTagsSupportPreSaleOpt.Param.class);
        activityCxt = Mockito.mock(ActivityCxt.class);
    }

    @Test
    public void tesCompute() {
        ProductM productM1 = new ProductM();
        productM1.setAttr("preSaleTag", "false");
        productM1.setProductTags(Lists.newArrayList("1"));
        List<String> result = productTagsSupportPreSaleOpt.compute(activityCxt, ProductTagsSupportPreSaleOpt.Param.builder().productM(productM).build(), config);
        assertNotNull(result);
    }

    // 测试预售
    @Test
    public void tesCompute1() {
        ProductM productM1 = new ProductM();
        productM1.setAttr("preSaleTag", "true");
        productM1.setAttr("preSaleStartDate", "今天");
        productM1.setProductTags(Lists.newArrayList("1"));
        List<String> result = productTagsSupportPreSaleOpt.compute(activityCxt, ProductTagsSupportPreSaleOpt.Param.builder().productM(productM1).build(), config);
        assertNotNull(result);
    }


    @After
    public void tearDown() {
        productTagsSupportPreSaleOpt = null;
        config = null;
        productM = null;
        result = null;
    }
}