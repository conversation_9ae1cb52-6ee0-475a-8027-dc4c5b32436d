package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;



import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP.Param;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ConfigDiscountProductPromoOptUnitTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private ConfigDiscountProductPromoOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private ConfigDiscountProductPromoOpt configDiscountProductPromoOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        configDiscountProductPromoOpt = new ConfigDiscountProductPromoOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    /**
     * 测试场景：当销售价格和市场价格都为0时，且配置不显示追加值时，应返回空列表
     */
    @Test
    public void testCompute_WhenSaleAndMarketPriceZeroAndNoAppendValue() {
        when(mockParam.getSalePrice()).thenReturn("0");
        when(mockProductM.getMarketPrice()).thenReturn("0");
        when(mockConfig.isShowAppendValueWhenNoDiscount()).thenReturn(false);

        List<DzPromoVO> result = configDiscountProductPromoOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：当销售价格和市场价格有效，但折扣值超过上限时，且配置不显示追加值时，应返回空列表
     */
    @Test
    public void testCompute_WhenDiscountAboveLimitAndNoAppendValue() {
        when(mockParam.getSalePrice()).thenReturn("9");
        when(mockProductM.getMarketPrice()).thenReturn("10");
        when(mockConfig.getMaxDiscount()).thenReturn(9.0);

        List<DzPromoVO> result = configDiscountProductPromoOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertTrue(CollectionUtils.isNotEmpty(result));
    }

    /**
     * 测试场景：当销售价格和市场价格有效，且折扣值在有效范围内时，应返回包含折扣信息的列表
     */
    @Test
    public void testCompute_WhenValidSaleAndMarketPrice() {
        when(mockParam.getSalePrice()).thenReturn("8");
        when(mockProductM.getMarketPrice()).thenReturn("10");
        when(mockConfig.getMaxDiscount()).thenReturn(9.0);
        when(mockConfig.getAppendTemplate()).thenReturn("%s|%s");
        when(mockConfig.getAppendAttrKeys()).thenReturn(null);

        List<DzPromoVO> result = configDiscountProductPromoOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertFalse(result.isEmpty());
        assertEquals("8.0折", result.get(0).getName());
    }

    /**
     * 测试场景：当销售价格有效，市场价格为空，且配置显示追加值时，应返回包含追加值的列表
     */
    @Test
    public void testCompute_WhenMarketPriceEmptyAndShowAppendValue() {
        when(mockParam.getSalePrice()).thenReturn("8");
        when(mockProductM.getMarketPrice()).thenReturn("");
        when(mockConfig.isShowAppendValueWhenNoDiscount()).thenReturn(true);
        when(mockConfig.getAppendAttrKeys()).thenReturn(Lists.newArrayList("attrKey"));
        when(mockProductM.getAttr("attrKey")).thenReturn("attrValue");

        List<DzPromoVO> result = configDiscountProductPromoOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertFalse(result.isEmpty());
    }

}
