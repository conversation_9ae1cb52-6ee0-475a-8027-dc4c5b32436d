package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class DiscountProductPromoOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductPromosVP.Param param;

    @Mock
    private DiscountProductPromoOpt.Config config;

    @Mock
    private ProductM productM;

    @InjectMocks
    private DiscountProductPromoOpt discountProductPromoOpt;

    private void mockCommonBehavior() {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn("100");
    }

    @Test
    public void testComputeWhenSalePriceOrMarketPriceIsZero() throws Throwable {
        mockCommonBehavior();
        when(param.getSalePrice()).thenReturn("0");
        List<DzPromoVO> result = discountProductPromoOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeWhenRoundingTypeIsOne() throws Throwable {
        mockCommonBehavior();
        when(param.getSalePrice()).thenReturn("10");
        when(config.getRoundingType()).thenReturn(1);
        List<DzPromoVO> result = discountProductPromoOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertEquals("1.0折", result.get(0).getName());
    }

    @Test
    public void testComputeWhenDiscountValIsGreaterThanMaxDiscount() throws Throwable {
        mockCommonBehavior();
        when(param.getSalePrice()).thenReturn("10");
        // Adjust the maxDiscount to ensure the condition for an empty list is met
        // Adjusted to a value that ensures the discount value is greater
        when(config.getMaxDiscount()).thenReturn(0.9);
        List<DzPromoVO> result = discountProductPromoOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeNormalCase() throws Throwable {
        mockCommonBehavior();
        when(param.getSalePrice()).thenReturn("10");
        when(config.getMaxDiscount()).thenReturn(10.0);
        List<DzPromoVO> result = discountProductPromoOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertEquals("1.0折", result.get(0).getName());
    }

    /**
     * 测试使用多次卡标签且有效时
     */
    @Test
    public void testComputeUsingTimeDealTagWithValidValue() throws Throwable {
        mockCommonBehavior();
        when(param.getProductM()).thenReturn(productM);
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("10");
        when(config.isUseTimeDealTag()).thenReturn(true);
        when(config.getTimeDealTag()).thenReturn("%s次¥%s");
        when(param.getSalePrice()).thenReturn("50");

        List<DzPromoVO> result = discountProductPromoOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertEquals("10次¥50", result.get(0).getName());
    }

    /**
     * 测试使用多次卡标签有效时安心学的商品
     */
    @Test
    public void testComputeUsingTimeDealTagWithEduLearnDeal() throws Throwable {
        mockCommonBehavior();
        when(config.isUseTimeDealTag()).thenReturn(true);
        HashMap<String, String> value = Maps.newHashMap();
        value.put("安心学", "%s节¥%s");
        when(config.getProductTag2timeDealTag()).thenReturn(value);
        when(param.getProductM()).thenReturn(productM);
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("10");
        when(productM.getProductTagList()).thenReturn(Lists.newArrayList(new TagM(1, "安心学","安心学")));
        when(param.getSalePrice()).thenReturn("50");
        List<DzPromoVO> result = discountProductPromoOpt.compute(context, param, config);
        assertEquals(1, result.size());
        assertEquals("10节¥50", result.get(0).getName());
    }


}
