package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.lang.reflect.Constructor;
import java.util.List;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NullProductPromosOptTest {

    @Mock
    private ActivityCxt context;

    /**
     * Tests the compute method, expecting a null return.
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // Arrange
        NullProductPromosOpt nullProductPromosOpt = new NullProductPromosOpt();
        ProductM productM = mock(ProductM.class);
        String salePrice = "100";
        // Using reflection to create an instance of ProductPromosVP.Param
        Class<?> paramClass = Class.forName("com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP$Param");
        Constructor<?> constructor = paramClass.getDeclaredConstructor(ProductM.class, String.class);
        // Bypassing the access check
        constructor.setAccessible(true);
        Object paramInstance = constructor.newInstance(productM, salePrice);
        // Act
        List<DzPromoVO> result = nullProductPromosOpt.compute(context, (ProductPromosVP.Param) paramInstance, null);
        // Assert
        assertNull(result);
    }
}
