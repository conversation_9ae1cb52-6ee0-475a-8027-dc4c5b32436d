package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.List;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoSupportPreSaleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductPromosVP.Param param;

    @Mock
    private ProductM productM;

    private PromoSupportPreSaleOpt.Config createConfig(boolean enablePreSale) {
        PromoSupportPreSaleOpt.Config config = new PromoSupportPreSaleOpt.Config();
        config.setEnablePreSale(enablePreSale);
        return config;
    }

    @Test
    public void testComputePreSaleEnabledAndIsPreSaleDeal() throws Throwable {
        try (MockedStatic<PreSaleUtils> mocked = mockStatic(PreSaleUtils.class)) {
            mocked.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(true);
            PromoSupportPreSaleOpt promoSupportPreSaleOpt = new PromoSupportPreSaleOpt();
            when(param.getProductM()).thenReturn(productM);
            List<DzPromoVO> result = promoSupportPreSaleOpt.compute(context, param, createConfig(true));
            assertNotNull(result);
        }
    }

    @Test
    public void testComputePreSaleEnabledButNotPreSaleDeal() throws Throwable {
        try (MockedStatic<PreSaleUtils> mocked = mockStatic(PreSaleUtils.class)) {
            mocked.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(false);
            PromoSupportPreSaleOpt promoSupportPreSaleOpt = new PromoSupportPreSaleOpt();
            when(param.getProductM()).thenReturn(productM);
            List<DzPromoVO> result = promoSupportPreSaleOpt.compute(context, param, createConfig(true));
            assertNotNull(result);
        }
    }

    @Test
    public void testComputePreSaleDisabledButIsPreSaleDeal() throws Throwable {
        try (MockedStatic<PreSaleUtils> mocked = mockStatic(PreSaleUtils.class)) {
            mocked.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(true);
            PromoSupportPreSaleOpt promoSupportPreSaleOpt = new PromoSupportPreSaleOpt();
            when(param.getProductM()).thenReturn(productM);
            List<DzPromoVO> result = promoSupportPreSaleOpt.compute(context, param, createConfig(false));
            assertNotNull(result);
        }
    }

    @Test
    public void testComputePreSaleDisabledAndNotPreSaleDeal() throws Throwable {
        try (MockedStatic<PreSaleUtils> mocked = mockStatic(PreSaleUtils.class)) {
            mocked.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(false);
            PromoSupportPreSaleOpt promoSupportPreSaleOpt = new PromoSupportPreSaleOpt();
            when(param.getProductM()).thenReturn(productM);
            List<DzPromoVO> result = promoSupportPreSaleOpt.compute(context, param, createConfig(false));
            assertNotNull(result);
        }
    }
}
