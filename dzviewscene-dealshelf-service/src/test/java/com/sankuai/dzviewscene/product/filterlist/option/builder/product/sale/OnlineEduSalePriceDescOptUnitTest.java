package com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceDescVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OnlineEduSalePriceDescOptUnitTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductSalePriceDescVP.Param param;

    @Mock
    private OnlineEduSalePriceDescOpt.Config config;

    @Mock
    private ProductM productM;

    /**
     * 测试正常情况
     */
    @Test
    public void testComputeNormal() {
        OnlineEduSalePriceDescOpt onlineEduSalePriceDescOpt = new OnlineEduSalePriceDescOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(OnlineEduSalePriceDescOpt.EDU_SALE_PRICE_REMARK)).thenReturn("test");

        String result = onlineEduSalePriceDescOpt.compute(activityCxt, param, config);

        assertEquals("test", result);
    }

    /**
     * 测试异常情况：getAttr(EDU_SALE_PRICE_REMARK)返回null
     */
    @Test
    public void testComputeException2() {
        OnlineEduSalePriceDescOpt onlineEduSalePriceDescOpt = new OnlineEduSalePriceDescOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(OnlineEduSalePriceDescOpt.EDU_SALE_PRICE_REMARK)).thenReturn(null);

        String result = onlineEduSalePriceDescOpt.compute(activityCxt, param, config);

        assertEquals(null, result);
    }

}
