package com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSaleVP.Param;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class ShopMerchantMemberProductSaleOptTest {

    private ShopMerchantMemberProductSaleOpt shopMerchantMemberProductSaleOpt;

    private ActivityCxt activityCxt;

    private ProductM productM;

    @Before
    public void setUp() {
        shopMerchantMemberProductSaleOpt = new ShopMerchantMemberProductSaleOpt();
        activityCxt = Mockito.mock(ActivityCxt.class);
        productM = Mockito.mock(ProductM.class);
    }

    private Param createParam(ProductM productM) {
        return Param.builder().productM(productM).build();
    }

    @Test
    public void testComputeWhenProductSaleIsNull() throws Throwable {
        when(productM.getSale()).thenReturn(null);
        Param param = createParam(productM);
        String result = shopMerchantMemberProductSaleOpt.compute(activityCxt, param, "test");
        assertEquals("", result);
    }

    @Test
    public void testComputeWhenSaleTagIsEmpty() throws Throwable {
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSaleTag("");
        when(productM.getSale()).thenReturn(productSaleM);
        Param param = createParam(productM);
        String result = shopMerchantMemberProductSaleOpt.compute(activityCxt, param, "test");
        assertEquals("", result);
    }

    @Test
    public void testComputeWhenIsValidWarmUpStageIsTrue() throws Throwable {
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSale(100);
        productSaleM.setSaleTag("已售100");
        when(productM.getSale()).thenReturn(productSaleM);
        when(productM.getAttr(Mockito.anyString())).thenReturn("{\"stage\":\"ONLY_WARM_UP_SALE\",\"warmUpStartTime\":1609459200000}");
        Param param = createParam(productM);
        String result = shopMerchantMemberProductSaleOpt.compute(activityCxt, param, "test");
        assertEquals("已售100+", result);
    }

    @Test
    public void testComputeWhenBuildSectionSaleTagReturnsString() throws Throwable {
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSale(1000);
        productSaleM.setSaleTag("已售1000");
        when(productM.getSale()).thenReturn(productSaleM);
        when(productM.getAttr(Mockito.anyString())).thenReturn("{\"stage\":\"ONLY_WARM_UP_SALE\",\"warmUpStartTime\":1609459200000}");
        Param param = createParam(productM);
        String result = shopMerchantMemberProductSaleOpt.compute(activityCxt, param, "test");
        assertEquals("已售1000+", result);
    }
}
