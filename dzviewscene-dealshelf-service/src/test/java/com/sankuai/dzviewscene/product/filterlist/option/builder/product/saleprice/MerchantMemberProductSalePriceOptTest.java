package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class // 其他测试用例类似，这里省略...
MerchantMemberProductSalePriceOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    @Mock
    private CardM cardM;

    @InjectMocks
    private MerchantMemberProductSalePriceOpt merchantMemberProductSalePriceOpt;

    private static final String BASE_PRICE_TAG = "团购价格";

    @Test
    public void testComputeCardMIsNull() throws Throwable {
        when(activityCxt.getSource(any())).thenReturn(null);
        when(productM.getBasePriceTag()).thenReturn(BASE_PRICE_TAG);
        String result = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals(BASE_PRICE_TAG, result);
    }

    @Test
    public void testComputeCardMIsNotNullButNoUserHoldCardPromo() throws Throwable {
        when(activityCxt.getSource(any())).thenReturn(cardM);
        when(productM.getBasePriceTag()).thenReturn(BASE_PRICE_TAG);
        String result = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals(BASE_PRICE_TAG, result);
    }

    @Test
    public void testComputeCardMIsNotNullAndUserHoldCardPromoButCardPromoDaoGua() throws Throwable {
        List<ProductPromoPriceM> promos = new ArrayList<>();
        ProductPromoPriceM directPromo = new ProductPromoPriceM();
        directPromo.setPromoType(PromoTypeEnum.DIRECT_PROMO.getType());
        directPromo.setPromoPrice(BigDecimal.valueOf(100));
        promos.add(directPromo);
        ProductPromoPriceM cardPromo = new ProductPromoPriceM();
        cardPromo.setPromoType(PromoTypeEnum.DISCOUNT_CARD.getType());
        cardPromo.setPromoPrice(BigDecimal.valueOf(200));
        promos.add(cardPromo);
        when(activityCxt.getSource(any())).thenReturn(cardM);
        when(productM.getPromoPrices()).thenReturn(promos);
        when(productM.getBasePriceTag()).thenReturn(BASE_PRICE_TAG);
        String result = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals(BASE_PRICE_TAG, result);
    }

    @Test
    public void testComputeCardMIsNotNullAndUserHoldCardPromoAndNotCardPromoDaoGuaButNotDuringPerfectActivity() throws Throwable {
        List<ProductPromoPriceM> promos = new ArrayList<>();
        ProductPromoPriceM directPromo = new ProductPromoPriceM();
        directPromo.setPromoType(PromoTypeEnum.DIRECT_PROMO.getType());
        directPromo.setPromoPrice(BigDecimal.valueOf(100));
        promos.add(directPromo);
        ProductPromoPriceM cardPromo = new ProductPromoPriceM();
        cardPromo.setPromoType(PromoTypeEnum.DISCOUNT_CARD.getType());
        cardPromo.setPromoPrice(BigDecimal.valueOf(50));
        promos.add(cardPromo);
        when(activityCxt.getSource(any())).thenReturn(cardM);
        when(productM.getPromoPrices()).thenReturn(promos);
        when(productM.getBasePriceTag()).thenReturn(BASE_PRICE_TAG);
        String result = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals(BASE_PRICE_TAG, result);
    }

    /**
     * Test when cardPromo exists with promoPriceTag and not dao gua
     */
    @Test
    public void testComputeWhenCardPromoExistsAndNotDaoGua() throws Throwable {
        // arrange
        List<ProductPromoPriceM> promos = new ArrayList<>();
        ProductPromoPriceM cardPromo = new ProductPromoPriceM();
        cardPromo.setPromoType(PromoTypeEnum.DISCOUNT_CARD.getType());
        cardPromo.setPromoPriceTag("88");
        cardPromo.setPromoPrice(BigDecimal.valueOf(88));
        promos.add(cardPromo);
        when(activityCxt.getSource(any())).thenReturn(cardM);
        when(cardM.getUserCardList()).thenReturn(Arrays.asList(1));
        when(productM.getPromoPrices()).thenReturn(promos);
        // act
        String result = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        // assert
        assertEquals("88", result);
    }

    /**
     * Test when productPromoPriceM exists with promoPriceTag
     */
    @Test
    public void testComputeWhenProductPromoPriceExistsWithTag() throws Throwable {
        // arrange
        ProductPromoPriceM directPromo = new ProductPromoPriceM();
        directPromo.setPromoType(PromoTypeEnum.DIRECT_PROMO.getType());
        directPromo.setPromoPriceTag("66");
        when(activityCxt.getSource(any())).thenReturn(null);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(directPromo);
        // act
        String result = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        // assert
        assertEquals("66", result);
    }

    /**
     * Test when exception occurs during execution
     */
    @Test
    public void testComputeWhenExceptionOccurs() throws Throwable {
        // arrange
        when(activityCxt.getSource(any())).thenThrow(new RuntimeException("Test exception"));
        when(productM.getBasePriceTag()).thenReturn("100");
        // act
        String result = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        // assert
        assertEquals("100", result);
        verify(activityCxt, times(1)).getSource(any());
    }
}
