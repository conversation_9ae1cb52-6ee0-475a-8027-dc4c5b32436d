package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP.Param;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice.PromoCodeMerchantMemberProductSalePriceOpt.MerchantMemberConfig;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class PromoCodeMerchantMemberProductSalePriceOptTest {

    private static final String MERCHANT_MEMBER_ATTR_KEY = "MERCHANT_MEMBER_DEAL";
    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private MerchantMemberConfig config;
    @Mock
    private ProductM productM;

    private PromoCodeMerchantMemberProductSalePriceOpt opt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        opt = new PromoCodeMerchantMemberProductSalePriceOpt();
        when(param.getProductM()).thenReturn(productM);
    }

    /**
     * 测试场景：当没有卡优惠且会员优惠存在时，且会员为免费会员时，应返回会员优惠标签
     */
    @Test
    public void testCompute_WithNoCardPromoAndFreeMerchantMemberPromo_ReturnsMerchantMemberPromoTag() {
        // 省略具体实现，因为涉及到多个静态方法和私有方法的调用，需要根据实际情况使用Mockito进行模拟
        CardM cardM = new CardM();
        cardM.setUserCardList(Lists.newArrayList(1));
        when(activityCxt.getSource(CardFetcher.CODE)).thenReturn(cardM);
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoType(16);
        productPromoPriceM.setPromoPriceTag("会员优惠卡价");
        productPromoPriceM.setPromoPrice(BigDecimal.valueOf(100));
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoTypeCode(16);
        productPromoPriceM.setPromoItemList(Lists.newArrayList(promoItemM));
        when(productM.getPromoPrices()).thenReturn(java.util.Collections.singletonList(productPromoPriceM));
        when(productM.getAttr(MERCHANT_MEMBER_ATTR_KEY)).thenReturn("{\n" +
                "  \"memberDiscountType\": 2,\n" +
                "  \"merchantMember\": {\n" +
                "    \"isMember\": false,\n" +
                "    \"isNewMember\": false\n" +
                "  },\n" +
                "  \"chargeType\": 1\n" +
                "}");
        String result = opt.compute(activityCxt, param, config);

        assertEquals("会员优惠卡价", result);
    }

    /**
     * 测试场景：当没有卡优惠且会员优惠存在时，且会员为付费会员时，如果未入会则不应返回会员优惠标签
     */
    @Test
    public void testCompute_WithNoCardPromoAndPaidMerchantMemberPromo_ReturnsMerchantMemberPromoTag() {
        // 省略具体实现，因为涉及到多个静态方法和私有方法的调用，需要根据实际情况使用Mockito进行模拟
        CardM cardM = new CardM();
        cardM.setUserCardList(Lists.newArrayList(1));
        when(activityCxt.getSource(CardFetcher.CODE)).thenReturn(cardM);
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoType(16);
        productPromoPriceM.setPromoPriceTag("会员优惠卡价");
        productPromoPriceM.setPromoPrice(BigDecimal.valueOf(100));
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoTypeCode(16);
        productPromoPriceM.setPromoItemList(Lists.newArrayList(promoItemM));
        when(productM.getPromoPrices()).thenReturn(Lists.newArrayList(productPromoPriceM));
        when(productM.getBasePriceTag()).thenReturn("基础价格卡价");
        when(productM.getAttr(MERCHANT_MEMBER_ATTR_KEY)).thenReturn("{\n" +
                "  \"memberDiscountType\": 2,\n" +
                "  \"merchantMember\": {\n" +
                "    \"isMember\": false,\n" +
                "    \"isNewMember\": false\n" +
                "  },\n" +
                "  \"chargeType\": 2\n" +
                "}");
        String result = opt.compute(activityCxt, param, config);

        assertEquals("基础价格卡价", result);
    }

    /**
     * 测试场景：当没有卡优惠和会员优惠，但存在立减优惠时，应返回立减优惠标签
     */
    @Test
    public void testCompute_WithNoCardPromoAndNoMerchantMemberPromoButDirectPromo_ReturnsDirectPromoTag() {
        CardM cardM = new CardM();
        cardM.setUserCardList(Lists.newArrayList(1));
        when(activityCxt.getSource(CardFetcher.CODE)).thenReturn(cardM);
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoType(0);
        productPromoPriceM.setPromoPriceTag("立减优惠卡价");
        when(productM.getPromoPrices()).thenReturn(Lists.newArrayList(productPromoPriceM));
        when(productM.getPromo(0)).thenReturn(productPromoPriceM);
        when(productM.getBasePriceTag()).thenReturn("基础价格卡价");
        String result = opt.compute(activityCxt, param, config);

        assertEquals("立减优惠卡价", result);
    }

    /**
     * 测试场景：当没有任何优惠时，应返回基础价格标签
     */
    @Test
    public void testCompute_WithNoPromos_ReturnsBasePriceTag() {
        when(activityCxt.getSource(CardFetcher.CODE)).thenReturn(null);
        when(productM.getBasePriceTag()).thenReturn("基础价格卡价");

        String result = opt.compute(activityCxt, param, config);

        assertEquals("基础价格卡价", result);
    }

    /**
     * 测试场景：当发生异常时，应正确处理异常并返回基础价格标签
     */
    @Test
    public void testCompute_WithException_ReturnsBasePriceTag() {
        when(activityCxt.getSource(CardFetcher.CODE)).thenThrow(new RuntimeException("模拟异常"));
        when(productM.getBasePriceTag()).thenReturn("基础价格卡价");

        String result = opt.compute(activityCxt, param, config);

        assertEquals("基础价格卡价", result);
    }
}