package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class SelfOperatedCleaningDealSalePriceOptTest {

    private SelfOperatedCleaningDealSalePriceOpt opt;
    private ActivityCxt context;
    private SelfOperatedCleaningDealSalePriceOpt.Config config;

    @Before
    public void setUp() {
        opt = new SelfOperatedCleaningDealSalePriceOpt();
        context = new ActivityCxt();
        config = new SelfOperatedCleaningDealSalePriceOpt.Config();
    }

    /**
     * 测试场景：参数无效时返回null
     */
    @Test
    public void testCompute_InvalidParam_ReturnsNull() {
        Param param = Param.builder().build();

        String result = opt.compute(context, param, config);

        assertNull(result);
    }

    /**
     * 测试场景：产品有销售价格时，返回格式化后的销售价格
     */
    @Test
    public void testCompute_WithSalePrice_ReturnsFormattedSalePrice() {
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.getSalePrice()).thenReturn(new BigDecimal("123.45"));
        Param param = Param.builder().productM(productM).build();

        String result = opt.compute(context, param, config);

        assertEquals("￥123", result);
    }

    /**
     * 测试场景：产品无销售价格但有基础价格标签时，返回格式化后的基础价格标签
     */
    @Test
    public void testCompute_WithBasePriceTag_ReturnsFormattedBasePriceTag() {
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.getSalePrice()).thenReturn(null);
        Mockito.when(productM.getBasePriceTag()).thenReturn("200");
        Param param = Param.builder().productM(productM).build();

        String result = opt.compute(context, param, config);

        assertEquals("￥200", result);
    }

    /**
     * 测试场景：产品无销售价格且基础价格标签为空时，返回null
     */
    @Test
    public void testCompute_NoSalePriceAndBasePriceTag_ReturnsNull() {
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.getSalePrice()).thenReturn(null);
        Mockito.when(productM.getBasePriceTag()).thenReturn("");
        Param param = Param.builder().productM(productM).build();

        String result = opt.compute(context, param, config);

        assertNull(result);
    }
}
