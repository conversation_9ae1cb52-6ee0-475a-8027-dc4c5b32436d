package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.math.BigDecimal;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class TimesDealSalePriceOpt_ComputeTest {

    /**
     * 测试 compute 方法，当 param.getProductM().getSalePrice() 为 null 时
     */
    @Test
    public void testComputeWhenSalePriceIsNull() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        TimesDealSalePriceOpt opt = new TimesDealSalePriceOpt();
        ProductM productM = new ProductM();
        productM.setSalePrice(null);
        // act
        String result = opt.compute(context, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 compute 方法，当 param.getProductM().getSalePrice() 不为 null 时
     */
    @Test
    public void testComputeWhenSalePriceIsNotNull() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        TimesDealSalePriceOpt opt = new TimesDealSalePriceOpt();
        ProductM productM = new ProductM();
        productM.setSalePrice(BigDecimal.TEN);
        // act
        String result = opt.compute(context, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals("¥10", result);
    }
}
