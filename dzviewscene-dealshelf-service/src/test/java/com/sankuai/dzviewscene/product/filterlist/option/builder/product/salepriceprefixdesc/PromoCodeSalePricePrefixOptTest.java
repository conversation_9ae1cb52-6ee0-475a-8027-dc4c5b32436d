package com.sankuai.dzviewscene.product.filterlist.option.builder.product.salepriceprefixdesc;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePricePrefixDescVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class PromoCodeSalePricePrefixOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private ProductSalePricePrefixDescVP.Param param;
    @Mock
    private ProductM productM;
    private PromoCodeSalePricePrefixOpt.Config config;

    private PromoCodeSalePricePrefixOpt promoCodeSalePricePrefixOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        promoCodeSalePricePrefixOpt = new PromoCodeSalePricePrefixOpt();
        config = new PromoCodeSalePricePrefixOpt.Config();
        config.setTimeDealPrefix("单次");
        when(param.getProductM()).thenReturn(productM);
    }

    /**
     * 测试场景：当产品是限时交易时，应返回配置中的时间交易前缀
     */
    @Test
    public void testCompute_WhenProductIsTimesDeal_ReturnTimeDealPrefix() {
        // arrange
        when(productM.isTimesDeal()).thenReturn(true);

        // act
        String result = promoCodeSalePricePrefixOpt.compute(context, param, config);

        // assert
        assertEquals("单次", result);
    }

    /**
     * 测试场景：当产品不是限时交易时，应返回空字符串
     */
    @Test
    public void testCompute_WhenProductIsNotTimesDeal_ReturnEmptyString() {
        // arrange
        when(productM.isTimesDeal()).thenReturn(false);

        // act
        String result = promoCodeSalePricePrefixOpt.compute(context, param, config);

        // assert
        assertEquals(StringUtils.EMPTY, result);
    }


    /**
     * 测试场景：当Param中的ProductM为null时，应返回空字符串
     */
    @Test
    public void testCompute_WhenProductMIsNull_ReturnEmptyString() {
        // arrange
        when(param.getProductM()).thenReturn(null);

        // act
        String result = promoCodeSalePricePrefixOpt.compute(context, param, config);

        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试场景：当Config中的timeDealPrefix为null时，产品是团单多次卡交易，应返回设定的null
     */
    @Test
    public void testCompute_WhenTimeDealPrefixIsNullAndProductIsTimesDeal_ReturnEmptyString() {
        // arrange
        config.setTimeDealPrefix(null);
        when(productM.isTimesDeal()).thenReturn(true);

        // act
        String result = promoCodeSalePricePrefixOpt.compute(context, param, config);

        // assert
        assertEquals(null, result);
    }

    /**
     * 测试场景：当Param为null时，应抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testCompute_WhenParamIsNull_ThrowNullPointerException() {
        // act
        promoCodeSalePricePrefixOpt.compute(context, null, config);
    }

    /**
     * 测试场景：当Context为null时，产品是限时交易，应正常返回配置中的时间交易前缀
     */
    @Test
    public void testCompute_WhenContextIsNullAndProductIsTimesDeal_ReturnTimeDealPrefix() {
        // arrange
        when(productM.isTimesDeal()).thenReturn(true);

        // act
        String result = promoCodeSalePricePrefixOpt.compute(null, param, config);

        // assert
        assertEquals("单次", result);
    }
}