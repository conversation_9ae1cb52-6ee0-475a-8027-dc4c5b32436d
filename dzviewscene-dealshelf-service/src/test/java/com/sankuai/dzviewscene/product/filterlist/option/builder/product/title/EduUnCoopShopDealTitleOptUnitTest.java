package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP.Param;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.title.EduUnCoopShopDealTitleOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;


public class EduUnCoopShopDealTitleOptUnitTest {

    private EduUnCoopShopDealTitleOpt eduUnCoopShopDealTitleOpt;
    private ActivityCxt context;
    private Param param;
    private Config config;
    private ProductM productM;

    @Before
    public void setUp() {
        eduUnCoopShopDealTitleOpt = new EduUnCoopShopDealTitleOpt();
        context = new ActivityCxt();
        config = new Config();
        productM = Mockito.mock(ProductM.class);
        param = ProductTitleVP.Param.builder().productM(productM).build();
    }

    /**
     * 测试商品为空时返回null
     */
    @Test
    public void testComputeProductIsNull() {
        param = ProductTitleVP.Param.builder().productM(null).build();

        String result = eduUnCoopShopDealTitleOpt.compute(context, param, config);

        assertNull(result);
    }

    /**
     * 测试商品标题为空时返回null
     */
    @Test
    public void testComputeProductTitleIsEmpty() {
        Mockito.when(productM.getTitle()).thenReturn("");

        String result = eduUnCoopShopDealTitleOpt.compute(context, param, config);

        assertNull(result);
    }

    /**
     * 测试标品且有主题时返回标题【可选主题】
     */
    @Test
    public void testComputeGeneralSpuWithTitle() {
        Mockito.when(productM.getProductType()).thenReturn(ProductTypeEnum.GENERAL_SPU.getType());
        Mockito.when(productM.getTitle()).thenReturn("标题");
        Mockito.when(productM.getAttr(Mockito.anyString())).thenReturn("主题");

        String result = eduUnCoopShopDealTitleOpt.compute(context, param, config);

        assertEquals("标题【可选主题】", result);
    }

    /**
     * 测试团购且有品牌名时返回品牌名+标题
     */
    @Test
    public void testComputeDealWithTitleAndBrandName() {
        Mockito.when(productM.getProductType()).thenReturn(ProductTypeEnum.DEAL.getType());
        Mockito.when(productM.getTitle()).thenReturn("标题");
        Mockito.when(productM.getBrandName()).thenReturn("品牌名");
        Mockito.when(productM.getShopMs()).thenReturn(new ArrayList<>());
        productM.getShopMs().add(new ShopM()); // 添加一个对象以模拟非空列表

        String result = eduUnCoopShopDealTitleOpt.compute(context, param, config);

        assertEquals("品牌名 | 标题", result);
    }

    /**
     * 测试团购但无品牌名时返回标题
     */
    @Test
    public void testComputeDealWithTitleNoBrandName() {
        Mockito.when(productM.getProductType()).thenReturn(ProductTypeEnum.DEAL.getType());
        Mockito.when(productM.getTitle()).thenReturn("标题");
        Mockito.when(productM.getBrandName()).thenReturn("");
        Mockito.when(productM.getShopMs()).thenReturn(new ArrayList<>());
        productM.getShopMs().add(new ShopM()); // 添加一个对象以模拟非空列表

        String result = eduUnCoopShopDealTitleOpt.compute(context, param, config);

        assertEquals("标题", result);
    }

    /**
     * 测试标品但无主题时返回标题
     */
    @Test
    public void testComputeGeneralSpuNoSubject() {
        Mockito.when(productM.getProductType()).thenReturn(ProductTypeEnum.GENERAL_SPU.getType());
        Mockito.when(productM.getTitle()).thenReturn("标题");
        Mockito.when(productM.getAttr(Mockito.anyString())).thenReturn("");

        String result = eduUnCoopShopDealTitleOpt.compute(context, param, config);

        assertEquals("标题", result);
    }

}
