package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.product.filterlist.option.factory.MassageFactory;
import com.sankuai.dzviewscene.product.filterlist.option.factory.MassageStrategy;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.math.BigDecimal;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;

public class MassageTitleOpt_ComputeTest {

    @InjectMocks
    private MassageTitleOpt massageTitleOpt;

    @Mock
    private MassageFactory massageFactory;

    @Mock
    private MassageStrategy massageStrategy;

    @Mock
    private ActivityCxt context;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testComputeTimesDealQueryFlagTrue() throws Throwable {
        ProductM productM = new ProductM();
        productM.setTimesDealQueryFlag(true);
        productM.setSalePrice(new BigDecimal("100"));
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        MassageTitleOpt.Config config = new MassageTitleOpt.Config();
        String result = massageTitleOpt.compute(context, param, config);
        Assert.assertNotNull(result);
    }

    @Test
    public void testComputeSkuItemNull() throws Throwable {
        ProductM productM = new ProductM();
        productM.setTimesDealQueryFlag(false);
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        when(massageFactory.getStrategy(anyLong())).thenReturn(massageStrategy);
        MassageTitleOpt.Config config = new MassageTitleOpt.Config();
        String result = massageTitleOpt.compute(context, param, config);
        Assert.assertEquals(productM.getTitle(), result);
    }

    @Test
    public void testComputeMassageStrategyNull() throws Throwable {
        ProductM productM = new ProductM();
        productM.setTimesDealQueryFlag(false);
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        when(massageFactory.getStrategy(anyLong())).thenReturn(null);
        MassageTitleOpt.Config config = new MassageTitleOpt.Config();
        String result = massageTitleOpt.compute(context, param, config);
        Assert.assertEquals(productM.getTitle(), result);
    }

    @Test
    public void testComputeFilterListTitleEmpty() throws Throwable {
        ProductM productM = new ProductM();
        productM.setTimesDealQueryFlag(false);
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        when(massageFactory.getStrategy(anyLong())).thenReturn(massageStrategy);
        when(massageStrategy.getFilterListTitle(any(), any())).thenReturn("");
        MassageTitleOpt.Config config = new MassageTitleOpt.Config();
        String result = massageTitleOpt.compute(context, param, config);
        Assert.assertEquals(productM.getTitle(), result);
    }
}
