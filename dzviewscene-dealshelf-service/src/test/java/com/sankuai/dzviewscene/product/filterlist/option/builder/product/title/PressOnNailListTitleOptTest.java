package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailListTitleOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private ProductTitleVP.Param param;
    @Mock
    private ProductM productM;

    private PressOnNailListTitleOpt pressOnNailListTitleOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailListTitleOpt = new PressOnNailListTitleOpt();
    }

    /**
     * 测试材料列表为空
     */
    @Test
    public void testCompute_MaterialListIsEmpty() {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getTitle()).thenReturn("Product Title");
        when(productM.getMaterialList()).thenReturn(new ArrayList<>());

        String result = pressOnNailListTitleOpt.compute(context, param, null);

        assertEquals("Product Title", result);
    }

    /**
     * 测试材料列表不为空，但材料名称为空
     */
    @Test
    public void testCompute_MaterialListIsNotEmptyButNameIsEmpty() {
        List<DealProductMaterialM> materialList = new ArrayList<>();
        materialList.add(new DealProductMaterialM());
        when(param.getProductM()).thenReturn(productM);
        when(productM.getTitle()).thenReturn("Product Title");
        when(productM.getMaterialList()).thenReturn(materialList);

        String result = pressOnNailListTitleOpt.compute(context, param, null);

        assertEquals("Product Title", result);
    }

    /**
     * 测试材料列表不为空，且材料名称不为空
     */
    @Test
    public void testCompute_MaterialListIsNotEmptyAndNameIsNotEmpty() {
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("Material Name");
        materialList.add(materialM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getTitle()).thenReturn("Product Title");
        when(productM.getMaterialList()).thenReturn(materialList);

        String result = pressOnNailListTitleOpt.compute(context, param, null);

        assertEquals("Material Name | Product Title", result);
    }
}
