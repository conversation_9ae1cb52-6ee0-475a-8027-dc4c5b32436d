package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class PressOnNailListTitleOpt_ComputeTest {

    private PressOnNailListTitleOpt pressOnNailListTitleOpt;

    private ActivityCxt context;

    private ProductTitleVP.Param param;

    private ProductM productM;

    @Before
    public void setUp() {
        pressOnNailListTitleOpt = new PressOnNailListTitleOpt();
        context = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(ProductTitleVP.Param.class);
        productM = Mockito.mock(ProductM.class);
    }

    /**
     * 测试材料列表为空
     */
    @Test
    public void testCompute_MaterialListIsEmpty() {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getTitle()).thenReturn("Product Title");
        when(productM.getMaterialList()).thenReturn(new ArrayList<>());
        String result = pressOnNailListTitleOpt.compute(context, param, null);
        assertEquals("Product Title", result);
    }

    /**
     * 测试材料列表不为空，但所有材料的名称都为空
     */
    @Test
    public void testCompute_MaterialListIsNotEmptyAndNameIsEmpty() {
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("");
        materialList.add(materialM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getTitle()).thenReturn("Product Title");
        when(productM.getMaterialList()).thenReturn(materialList);
        String result = pressOnNailListTitleOpt.compute(context, param, null);
        assertEquals("Product Title", result);
    }

    /**
     * 测试材料列表不为空，且存在材料名称不为空
     */
    @Test
    public void testCompute_MaterialListIsNotEmptyAndNameIsNotEmpty() {
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("Material Name");
        materialList.add(materialM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getTitle()).thenReturn("Product Title");
        when(productM.getMaterialList()).thenReturn(materialList);
        String result = pressOnNailListTitleOpt.compute(context, param, null);
        assertEquals("Material Name | Product Title", result);
    }
}
