package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import java.math.BigDecimal;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class TimesDealTitleOpt_ComputeTest {

    /**
     * 测试产品不是次卡召回的情况
     */
    @Test
    public void testComputeNotTimesDealQueryFlag() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        TimesDealTitleOpt timesDealTitleOpt = new TimesDealTitleOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.isTimesDealQueryFlag()).thenReturn(false);
        Mockito.when(productM.getTitle()).thenReturn("Original Title");
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        TimesDealTitleOpt.Config config = new TimesDealTitleOpt.Config();
        String title = timesDealTitleOpt.compute(context, param, config);
        Assert.assertEquals("Original Title", title);
    }

    /**
     * 测试产品是次卡召回，但不是团购次卡的情况
     */
    @Test
    public void testComputeNotTimesDeal() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        TimesDealTitleOpt timesDealTitleOpt = new TimesDealTitleOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.isTimesDealQueryFlag()).thenReturn(true);
        Mockito.when(productM.isTimesDeal()).thenReturn(false);
        // Mock salePrice to avoid NullPointerException
        Mockito.when(productM.getSalePrice()).thenReturn(new BigDecimal("100"));
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        TimesDealTitleOpt.Config config = new TimesDealTitleOpt.Config();
        config.setTitleTemplate("%s次 单次¥%s");
        String title = timesDealTitleOpt.compute(context, param, config);
        Assert.assertEquals("1次 单次¥100", title);
    }

    /**
     * 测试产品是次卡召回，是团购次卡，但次数或单次价格为空的情况
     */
    @Test
    public void testComputeTimesDealWithEmptySinglePrice() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        TimesDealTitleOpt timesDealTitleOpt = new TimesDealTitleOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.isTimesDealQueryFlag()).thenReturn(true);
        Mockito.when(productM.isTimesDeal()).thenReturn(true);
        Mockito.when(productM.getTitle()).thenReturn("Original Title");
        Mockito.when(productM.getAttr(Mockito.anyString())).thenReturn(null);
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        TimesDealTitleOpt.Config config = new TimesDealTitleOpt.Config();
        config.setTitleTemplate("%s次 单次¥%s");
        String title = timesDealTitleOpt.compute(context, param, config);
        Assert.assertEquals("Original Title", title);
    }

    /**
     * 测试产品是次卡召回，是团购次卡，次数和单次价格都不为空的情况
     */
    @Test
    public void testComputeTimesDealWithNonEmptySinglePrice() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        TimesDealTitleOpt timesDealTitleOpt = new TimesDealTitleOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        Mockito.when(productM.isTimesDealQueryFlag()).thenReturn(true);
        Mockito.when(productM.isTimesDeal()).thenReturn(true);
        Mockito.when(productM.getTitle()).thenReturn("Original Title");
        Mockito.when(productM.getAttr(Mockito.anyString())).thenReturn("2");
        Mockito.when(productM.getSalePrice()).thenReturn(new BigDecimal("100"));
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();
        TimesDealTitleOpt.Config config = new TimesDealTitleOpt.Config();
        config.setTitleTemplate("%s次 单次¥%s");
        String title = timesDealTitleOpt.compute(context, param, config);
        Assert.assertEquals("2次 单次¥50", title);
    }
}
