package com.sankuai.dzviewscene.product.filterlist.option.builder.product.unitprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductUnitPriceVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.unitprice.PromoCodeTimeDealCardUnitPriceOpt.Config;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

public class PromoCodeTimeDealCardUnitPriceOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private ProductUnitPriceVP.Param param;
    @Mock
    private Config config;
    @Mock
    private ProductM productM;

    private PromoCodeTimeDealCardUnitPriceOpt promoCodeTimeDealCardUnitPriceOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        promoCodeTimeDealCardUnitPriceOpt = new PromoCodeTimeDealCardUnitPriceOpt();
        when(param.getProductM()).thenReturn(productM);
    }

    /**
     * 测试场景：产品不是次卡交易，应返回null
     */
    @Test
    public void testCompute_NotTimesDeal_ReturnsNull() {
        when(productM.isTimesDeal()).thenReturn(false);

        String result = promoCodeTimeDealCardUnitPriceOpt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试场景：产品是次卡交易但销售价格为空，应返回null
     */
    @Test
    public void testCompute_TimesDealButSalePriceIsNull_ReturnsNull() {
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getSalePrice()).thenReturn(null);

        String result = promoCodeTimeDealCardUnitPriceOpt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试场景：产品是次卡交易，销售价格不为空，但次数为0，应返回null
     */
    @Test
    public void testCompute_TimesDealAndSalePriceNotNullButTimesIsZero_ReturnsNull() {
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getSalePrice()).thenReturn(new BigDecimal("100"));
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("0");

        String result = promoCodeTimeDealCardUnitPriceOpt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试场景：产品是次卡交易，销售价格不为空，次数大于0，应正确计算单位价格
     */
    @Test
    public void testCompute_TimesDealAndSalePriceNotNullAndTimesGreaterThanZero_ReturnsCorrectUnitPrice() {
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getSalePrice()).thenReturn(new BigDecimal("100"));
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("2");

        String result = promoCodeTimeDealCardUnitPriceOpt.compute(activityCxt, param, config);

        assertEquals("50", result);
    }
}