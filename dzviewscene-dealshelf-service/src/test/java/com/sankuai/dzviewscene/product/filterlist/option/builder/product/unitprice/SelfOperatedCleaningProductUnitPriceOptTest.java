package com.sankuai.dzviewscene.product.filterlist.option.builder.product.unitprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductUnitPriceVP.Param;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试自营保洁团单单位价格计算
 */
@RunWith(MockitoJUnitRunner.class)
public class SelfOperatedCleaningProductUnitPriceOptTest {

    private SelfOperatedCleaningProductUnitPriceOpt opt;
    private ActivityCxt context;
    private Param param;
    private SelfOperatedCleaningProductUnitPriceOpt.Config config;

    @Before
    public void setUp() {
        opt = new SelfOperatedCleaningProductUnitPriceOpt();
        context = new ActivityCxt();
        param = mock(Param.class);
        config = new SelfOperatedCleaningProductUnitPriceOpt.Config();
    }

    /**
     * 测试参数无效的情况
     */
    @Test
    public void testComputeInvalidParam() {
        // arrange
        when(param.getProductM()).thenReturn(null);

        // act
        String result = opt.compute(context, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试dealStructContent解析异常的情况
     */
    @Test
    public void testComputeDealStructContentParseError() {
        // arrange
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(null);

        // act
        String result = opt.compute(context, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试正常计算单位价格的情况
     */
    @Test
    public void testComputeNormal() {
        // arrange
        ProductM productM = mock(ProductM.class);
        DzProductVO dzProductVO = new DzProductVO();
        dzProductVO.setSalePrice("￥100");
        when(param.getProductM()).thenReturn(productM);
        when(param.getDzProductVO()).thenReturn(dzProductVO);

        List<AttrM> attrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dealStructContent");
        attrM.setValue("{\"dealDetailStructuredData\":{\"dealDetailSkuUniStructuredModel\":{\"mustGroups\":[{\"skus\":[{\"skuAttrs\":[{\"attrName\":\"serviceDuration\",\"attrValue\":\"2小时\"}]}]}]}}}");
        attrs.add(attrM);

        when(productM.getExtAttrs()).thenReturn(attrs); // 模拟解析dealStructContent的情况

        // act
         String result = opt.compute(context, param, config);

        // assert
         assertNotNull(result);
         assertEquals("￥50.0/小时", result);
    }

    /**
     * 测试服务时长为0时的情况
     */
    @Test
    public void testComputeServiceDurationZero() {
        // arrange
        ProductM productM = mock(ProductM.class);
        DzProductVO dzProductVO = new DzProductVO();
        dzProductVO.setSalePrice("￥100");
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(null); // 模拟无法解析dealStructContent的情况

        // 此处省略对dealStructContent解析成功但服务时长为0的模拟

        // act
         String result = opt.compute(context, param, config);

        // assert
         assertNull(result);
    }
}
