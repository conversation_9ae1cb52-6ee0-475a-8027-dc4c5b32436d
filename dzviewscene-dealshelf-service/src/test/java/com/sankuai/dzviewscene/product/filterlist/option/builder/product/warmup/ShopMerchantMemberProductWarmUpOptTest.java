package com.sankuai.dzviewscene.product.filterlist.option.builder.product.warmup;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductWarmUpVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.WarmUpVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShopMerchantMemberProductWarmUpOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductWarmUpVP.Param param;

    @Mock
    private ProductM productM;

    @InjectMocks
    private ShopMerchantMemberProductWarmUpOpt opt;

    // Test case for when WarmUpStageResult is invalid (null stage)
    @Test
    public void testComputeWarmUpStageResultIsInvalid() throws Throwable {
        try (MockedStatic<WarmUpStageEnum> mockedStatic = Mockito.mockStatic(WarmUpStageEnum.class)) {
            when(param.getProductM()).thenReturn(productM);
            WarmUpStageEnum.WarmUpStageResult mockResult = mock(WarmUpStageEnum.WarmUpStageResult.class);
            mockedStatic.when(() -> WarmUpStageEnum.getWarmUpStageResult(any(ProductM.class))).thenReturn(mockResult);
            WarmUpVO result = opt.compute(activityCxt, param, null);
            Assert.assertNull("Expected null result when WarmUpStageResult is invalid", result);
        }
    }
}
