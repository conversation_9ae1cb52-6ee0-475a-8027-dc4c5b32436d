package com.sankuai.dzviewscene.product.filterlist.option.factory;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import java.util.Collections;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import java.util.Arrays;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import scala.collection.immutable.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class AbstractMassageStrategyTest {

    private static final String SERVICE_DURATION_INT = "serviceDurationInt";

    /**
     * Test getServiceDuration with valid duration
     */
    @Test
    public void testGetServiceDurationWithValidDuration() throws Throwable {
        try (MockedStatic<DealStructUtils> mockedStatic = Mockito.mockStatic(DealStructUtils.class)) {
            // arrange
            SkuItemDto skuItemDtoMock = mock(SkuItemDto.class);
            mockedStatic.when(() -> DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDtoMock, SERVICE_DURATION_INT)).thenReturn("90");
            AbstractMassageStrategy strategy = new AbstractMassageStrategy() {

                @Override
                public java.util.List<Long> getProductCategorys() {
                    return null;
                }

                @Override
                public String getFilterListTitle(SkuItemDto skuItemDto, String serviceType) {
                    return null;
                }
            };
            // act
            String result = strategy.getServiceDuration(skuItemDtoMock);
            // assert
            assertEquals("90分钟", result);
        }
    }

    /**
     * Test getServiceDuration with no duration
     */
    @Test
    public void testGetServiceDurationWithNoDuration() throws Throwable {
        try (MockedStatic<DealStructUtils> mockedStatic = Mockito.mockStatic(DealStructUtils.class)) {
            // arrange
            SkuItemDto skuItemDtoMock = mock(SkuItemDto.class);
            mockedStatic.when(() -> DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDtoMock, SERVICE_DURATION_INT)).thenReturn("");
            AbstractMassageStrategy strategy = new AbstractMassageStrategy() {

                @Override
                public java.util.List<Long> getProductCategorys() {
                    return null;
                }

                @Override
                public String getFilterListTitle(SkuItemDto skuItemDto, String serviceType) {
                    return null;
                }
            };
            // act
            String result = strategy.getServiceDuration(skuItemDtoMock);
            // assert
            assertEquals("", result);
        }
    }

    /**
     * Test getServiceDuration when SkuItemDto's attribute list is empty
     */
    @Test
    public void testGetServiceDurationWhenAttributeListIsEmpty() throws Throwable {
        try (MockedStatic<DealStructUtils> mockedStatic = Mockito.mockStatic(DealStructUtils.class)) {
            // arrange
            SkuItemDto skuItemDtoMock = mock(SkuItemDto.class);
            mockedStatic.when(() -> DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDtoMock, SERVICE_DURATION_INT)).thenReturn("");
            AbstractMassageStrategy strategy = new AbstractMassageStrategy() {

                @Override
                public java.util.List<Long> getProductCategorys() {
                    return null;
                }

                @Override
                public String getFilterListTitle(SkuItemDto skuItemDto, String serviceType) {
                    return null;
                }
            };
            // act
            String result = strategy.getServiceDuration(skuItemDtoMock);
            // assert
            assertEquals("", result);
        }
    }
}
