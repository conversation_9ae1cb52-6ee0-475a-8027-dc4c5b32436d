package com.sankuai.dzviewscene.product.filterlist.option.factory;

import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class MassageFactoryTest {

    @InjectMocks
    private MassageFactory massageFactory;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private MassageStrategy massageStrategy;

    @Before
    public void setUp() {
        when(applicationContext.getBeansOfType(MassageStrategy.class)).thenReturn(new HashMap<String, MassageStrategy>() {

            {
                put("massageStrategy", massageStrategy);
            }
        });
        when(massageStrategy.getProductCategorys()).thenReturn(Arrays.asList(1L, 2L, 3L));
    }

    /**
     * 测试initRegisterStrategy方法，正常场景
     */
    @Test
    public void testInitRegisterStrategyNormal() throws Throwable {
        // arrange
        // act
        massageFactory.initRegisterStrategy();
        // assert
        verify(massageStrategy, times(1)).getProductCategorys();
    }

    /**
     * 测试initRegisterStrategy方法，边界场景
     */
    @Test
    public void testInitRegisterStrategyBoundary() throws Throwable {
        // arrange
        when(massageStrategy.getProductCategorys()).thenReturn(null);
        // act
        massageFactory.initRegisterStrategy();
        // assert
        verify(massageStrategy, times(1)).getProductCategorys();
    }

    /**
     * 测试initRegisterStrategy方法，异常场景
     */
    @Test
    public void testInitRegisterStrategyException() throws Throwable {
        // arrange
        when(applicationContext.getBeansOfType(MassageStrategy.class)).thenReturn(new HashMap<String, MassageStrategy>());
        // act
        massageFactory.initRegisterStrategy();
        // assert
        verify(applicationContext, times(1)).getBeansOfType(MassageStrategy.class);
    }
}
