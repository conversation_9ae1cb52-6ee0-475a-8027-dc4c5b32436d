package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import org.junit.Assert;
import org.junit.Test;
import java.util.Arrays;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.junit.Before;
import org.mockito.Mockito;

public class CupStrategyImplTest {

    // Removed the setUp method and directly instantiate the CupStrategyImpl
    private CupStrategyImpl cupStrategy = new CupStrategyImpl();

    /**
     * 测试 skuItemDto 为 null 的情况
     */
    @Test
    public void testGetFilterListTitleSkuItemDtoIsNull() throws Throwable {
        String result = cupStrategy.getFilterListTitle(null, "");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 为空的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsIsEmpty() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        String result = cupStrategy.getFilterListTitle(skuItemDto, "");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 中不存在 SERVICE_DURATION_INT 或 SERVICE_TECHNIQUE 属性的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsNotContainsKey() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("otherKey");
        attrItemDto.setAttrValue("otherValue");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto));
        String result = cupStrategy.getFilterListTitle(skuItemDto, "");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 中存在 SERVICE_DURATION_INT 属性，但不存在 SERVICE_TECHNIQUE 属性的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsContainsServiceDurationInt() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto));
        String result = cupStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("10分钟serviceType", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 中既存在 SERVICE_DURATION_INT 属性，也存在 SERVICE_TECHNIQUE 属性的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsContainsServiceDurationIntAndServiceTechnique() throws Throwable {
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");
        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("serviceTechnique");
        attrItemDto1.setAttrValue("手法");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto, attrItemDto1));
        String result = cupStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("10分钟serviceType（手法）", result);
    }
}
