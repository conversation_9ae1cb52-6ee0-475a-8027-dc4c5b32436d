package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;

public class EarStrategyImplTest {

    private EarStrategyImpl earStrategy;

    @Before
    public void setUp() {
        earStrategy = new EarStrategyImpl();
    }

    /**
     * 测试 skuItemDto 为 null 的情况
     */
    @Test
    public void testGetFilterListTitleSkuItemDtoIsNull() {
        String result = earStrategy.getFilterListTitle(null, "serviceType");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 为空的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsIsEmpty() {
        SkuItemDto skuItemDto = new SkuItemDto();
        String result = earStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 中不存在 SERVICE_DURATION_INT 或 SERVICE_TECHNIQUE 属性的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsNotContainsKey() {
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("otherKey");
        attrItemDto.setAttrValue("otherValue");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto));
        String result = earStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 中存在 SERVICE_DURATION_INT 和 SERVICE_TECHNIQUE 属性的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsContainsKey() {
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");
        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("serviceTechnique");
        attrItemDto1.setAttrValue("手法");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto, attrItemDto1));
        String result = earStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("10分钟手法", result);
    }
}
