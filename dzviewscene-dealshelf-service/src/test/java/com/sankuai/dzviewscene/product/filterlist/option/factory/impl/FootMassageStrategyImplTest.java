package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;

public class FootMassageStrategyImplTest {

    private FootMassageStrategyImpl footMassageStrategy;

    @Before
    public void setUp() {
        footMassageStrategy = new FootMassageStrategyImpl();
    }

    /**
     * 测试getFilterListTitle方法，当getServiceDuration返回空字符串时
     */
    @Test
    public void testGetFilterListTitleWhenServiceDurationIsEmpty() {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        String serviceType = "足疗";
        // act
        String result = footMassageStrategy.getFilterListTitle(skuItemDto, serviceType);
        // assert
        Assert.assertEquals(serviceType, result);
    }

    /**
     * 测试getFilterListTitle方法，当getServiceDuration返回非空字符串时
     */
    @Test
    public void testGetFilterListTitleWhenServiceDurationIsNotEmpty() {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto));
        String serviceType = "足疗";
        // act
        String result = footMassageStrategy.getFilterListTitle(skuItemDto, serviceType);
        // assert
        Assert.assertEquals("10分钟足疗", result);
    }
}
