package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;

public class HeadStrategyImplTest {

    private HeadStrategyImpl headStrategy;

    private SkuItemDto skuItemDto;

    private SkuAttrItemDto skuAttrItemDto;

    @Before
    public void setUp() {
        headStrategy = new HeadStrategyImpl();
        skuItemDto = new SkuItemDto();
        skuAttrItemDto = new SkuAttrItemDto();
    }

    /**
     * 测试 skuItemDto 为 null 的情况
     */
    @Test
    public void testGetFilterListTitleSkuItemDtoIsNull() {
        String result = headStrategy.getFilterListTitle(null, "serviceType");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 为空的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsIsEmpty() {
        String result = headStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 中不存在对应 attrName 的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsNotExist() {
        skuAttrItemDto.setAttrName("notExist");
        skuAttrItemDto.setAttrValue("value");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        String result = headStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 skuItemDto 的 attrItems 中存在对应 attrName 的情况
     */
    @Test
    public void testGetFilterListTitleAttrItemsExist() {
        skuAttrItemDto.setAttrName("serviceDurationInt");
        skuAttrItemDto.setAttrValue("10");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        String result = headStrategy.getFilterListTitle(skuItemDto, "serviceType");
        Assert.assertEquals("10分钟", result);
    }
}
