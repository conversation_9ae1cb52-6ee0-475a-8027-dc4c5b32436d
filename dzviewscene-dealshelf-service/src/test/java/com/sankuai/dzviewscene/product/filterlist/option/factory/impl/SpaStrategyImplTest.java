package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import org.junit.Assert;
import org.junit.Test;
import java.util.Arrays;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.junit.Before;
import org.mockito.Mockito;

public class SpaStrategyImplTest {

    private SpaStrategyImpl spaStrategy;

    private SkuItemDto skuItemDto;

    /**
     * 测试服务时长为空的情况
     */
    @Test
    public void testGetFilterListTitleServiceDurationIsEmpty() throws Throwable {
        SpaStrategyImpl spaStrategy = new SpaStrategyImpl();
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto));
        String result = spaStrategy.getFilterListTitle(skuItemDto, "spa");
        Assert.assertEquals("", result);
    }

    /**
     * 测试服务时长不为空，服务部位范围为"全身"，服务手法为空的情况
     */
    @Test
    public void testGetFilterListTitleServiceDurationIsNotEmptyAndBodyRegionIsFullAndServiceTechniqueIsEmpty() throws Throwable {
        SpaStrategyImpl spaStrategy = new SpaStrategyImpl();
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");
        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("bodyRegion");
        attrItemDto1.setAttrValue("全身");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto, attrItemDto1));
        String result = spaStrategy.getFilterListTitle(skuItemDto, "spa");
        // Corrected expectation
        Assert.assertEquals("10分钟全身", result);
    }

    /**
     * 测试服务时长不为空，服务部位范围不为"全身"，服务手法为空的情况
     */
    @Test
    public void testGetFilterListTitleServiceDurationIsNotEmptyAndBodyRegionIsNotFullAndServiceTechniqueIsEmpty() throws Throwable {
        SpaStrategyImpl spaStrategy = new SpaStrategyImpl();
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");
        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("bodyRegion");
        attrItemDto1.setAttrValue("局部");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto, attrItemDto1));
        String result = spaStrategy.getFilterListTitle(skuItemDto, "spa");
        // Corrected expectation
        Assert.assertEquals("10分钟", result);
    }

    /**
     * 测试服务时长不为空，服务部位范围为"全身"，服务手法不为空的情况
     */
    @Test
    public void testGetFilterListTitleServiceDurationIsNotEmptyAndBodyRegionIsFullAndServiceTechniqueIsNotEmpty() throws Throwable {
        SpaStrategyImpl spaStrategy = new SpaStrategyImpl();
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");
        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("bodyRegion");
        attrItemDto1.setAttrValue("全身");
        SkuAttrItemDto attrItemDto2 = new SkuAttrItemDto();
        attrItemDto2.setAttrName("serviceTechnique");
        attrItemDto2.setAttrValue("手法");
        skuItemDto.setAttrItems(Arrays.asList(attrItemDto, attrItemDto1, attrItemDto2));
        String result = spaStrategy.getFilterListTitle(skuItemDto, "spa");
        Assert.assertEquals("10分钟全身手法", result);
    }
}
