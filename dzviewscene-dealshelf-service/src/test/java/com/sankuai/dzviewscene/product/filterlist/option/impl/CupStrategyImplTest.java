package com.sankuai.dzviewscene.product.filterlist.option.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.impl.CupStrategyImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 10:43
 */
@RunWith(MockitoJUnitRunner.class)
public class CupStrategyImplTest {

    @InjectMocks
    private CupStrategyImpl cupStrategy;

    @Test
    public void getFilterListTitle() {
        SkuItemDto skuItemDto = new SkuItemDto();

        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");

        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("serviceTechnique");
        attrItemDto1.setAttrValue("手法");

        skuItemDto.setAttrItems(Lists.newArrayList(attrItemDto, attrItemDto1));
        String filterListTitle = cupStrategy.getFilterListTitle(skuItemDto, "拔罐");
        Assert.assertNotNull(filterListTitle);
    }

    @Test
    public void getProductCategorys() {
        List<Long> productCategorys = cupStrategy.getProductCategorys();
        Assert.assertNotNull(productCategorys);
    }
}