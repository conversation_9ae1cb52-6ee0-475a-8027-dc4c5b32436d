package com.sankuai.dzviewscene.product.filterlist.option.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.impl.EarStrategyImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 10:47
 */
@RunWith(MockitoJUnitRunner.class)
public class EarStrategyImplTest {

    @InjectMocks
    private EarStrategyImpl earStrategy;

    @Test
    public void getFilterListTitle() {
        SkuItemDto skuItemDto = new SkuItemDto();

        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");

        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("serviceTechnique");
        attrItemDto1.setAttrValue("手法");

        skuItemDto.setAttrItems(Lists.newArrayList(attrItemDto, attrItemDto1));
        String filterListTitle = earStrategy.getFilterListTitle(skuItemDto, "采耳");
        Assert.assertNotNull(filterListTitle);
    }

    @Test
    public void getProductCategorys() {
        List<Long> productCategorys = earStrategy.getProductCategorys();
        Assert.assertNotNull(productCategorys);
    }

}