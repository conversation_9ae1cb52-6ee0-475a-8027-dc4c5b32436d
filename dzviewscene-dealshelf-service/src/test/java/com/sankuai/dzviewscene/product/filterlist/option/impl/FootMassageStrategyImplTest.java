package com.sankuai.dzviewscene.product.filterlist.option.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.impl.FootMassageStrategyImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 10:50
 */
@RunWith(MockitoJUnitRunner.class)
public class FootMassageStrategyImplTest {

    @InjectMocks
    private FootMassageStrategyImpl footMassageStrategy;

    @Test
    public void getFilterListTitle() {
        SkuItemDto skuItemDto = new SkuItemDto();

        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");

        skuItemDto.setAttrItems(Lists.newArrayList(attrItemDto));
        String filterListTitle = footMassageStrategy.getFilterListTitle(skuItemDto, "足疗");
        Assert.assertNotNull(filterListTitle);
    }

    @Test
    public void getProductCategorys() {
        List<Long> productCategorys = footMassageStrategy.getProductCategorys();
        Assert.assertNotNull(productCategorys);
    }
}