package com.sankuai.dzviewscene.product.filterlist.option.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.impl.HeadStrategyImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 10:52
 */
@RunWith(MockitoJUnitRunner.class)
public class HeadStrategyImplTest {

    @InjectMocks
    private HeadStrategyImpl headStrategy;


    @Test
    public void getFilterListTitle() {
        SkuItemDto skuItemDto = new SkuItemDto();

        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");

        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("serviceTechnique");
        attrItemDto1.setAttrValue("手法");

        skuItemDto.setAttrItems(Lists.newArrayList(attrItemDto, attrItemDto1));
        String filterListTitle = headStrategy.getFilterListTitle(skuItemDto, "头疗");
        Assert.assertNotNull(filterListTitle);
    }

    @Test
    public void getProductCategorys() {
        List<Long> productCategorys = headStrategy.getProductCategorys();
        Assert.assertNotNull(productCategorys);
    }
}