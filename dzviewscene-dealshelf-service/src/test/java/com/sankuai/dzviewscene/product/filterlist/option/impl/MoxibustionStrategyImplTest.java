package com.sankuai.dzviewscene.product.filterlist.option.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.impl.MoxibustionStrategyImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 10:54
 */
@RunWith(MockitoJUnitRunner.class)
public class MoxibustionStrategyImplTest {

    @InjectMocks
    private MoxibustionStrategyImpl moxibustionStrategy;

    @Test
    public void getFilterListTitle() {
        SkuItemDto skuItemDto = new SkuItemDto();

        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");

        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("serviceTechnique");
        attrItemDto1.setAttrValue("手法");

        SkuAttrItemDto attrItemDto2 = new SkuAttrItemDto();
        attrItemDto2.setAttrName("moxibustionMethod");
        attrItemDto2.setAttrValue("手法");

        skuItemDto.setAttrItems(Lists.newArrayList(attrItemDto, attrItemDto1, attrItemDto2));
        String filterListTitle = moxibustionStrategy.getFilterListTitle(skuItemDto, "艾灸");
        Assert.assertNotNull(filterListTitle);
    }

    @Test
    public void getProductCategorys() {
        List<Long> productCategorys = moxibustionStrategy.getProductCategorys();
        Assert.assertNotNull(productCategorys);
    }

}