package com.sankuai.dzviewscene.product.filterlist.utils;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ContextParamBuildUtilsUnitTest {


    @Mock
    private ActivityCxt ctx;

    @Test
    public void testGetTotalProductNumNoGroupName() throws Throwable {
        // arrange
        when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(null);

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, true);

        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetTotalProductNumNoProductGroups() throws Throwable {
        // arrange
        when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(Collections.singletonList("团购"));
        when(ctx.getAttachment(PaddingFetcher.Attachments.productGroups)).thenReturn(null);

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, true);

        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetTotalProductNumNoProductGroupForGroupName() throws Throwable {
        // arrange
        when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(Collections.singletonList("团购"));
        when(ctx.getAttachment(PaddingFetcher.Attachments.productGroups)).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, true);

        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetTotalProductNumReturnTotalProductNum() throws Throwable {
        // arrange
        String groupName = "团购";
        when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(Collections.singletonList(groupName));
        Map<String, ProductGroupM> productGroupMap = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM(10, null);
        productGroupMap.put(groupName, productGroupM);
        when(ctx.getAttachment(PaddingFetcher.Attachments.productGroups)).thenReturn(CompletableFuture.completedFuture(productGroupMap));

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, true);

        // assert
        assertEquals(10, result);
    }

    @Test
    public void testGetTotalProductNumReturnCurrentProductNum() throws Throwable {
        // arrange
        String groupName = "团购";
        when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(Collections.singletonList(groupName));
        Map<String, ProductGroupM> productGroupMap = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM(10, Collections.emptyList());
        productGroupMap.put(groupName, productGroupM);
        when(ctx.getAttachment(PaddingFetcher.Attachments.productGroups)).thenReturn(CompletableFuture.completedFuture(productGroupMap));

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, false);

        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetTotalProductNumProductGroupMIsNull() throws Throwable {
        // arrange
        String groupName = "团购";
        when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(Collections.singletonList(groupName));
        Map<String, ProductGroupM> productGroupMap = new HashMap<>();
        productGroupMap.put(groupName, null);
        when(ctx.getAttachment(PaddingFetcher.Attachments.productGroups)).thenReturn(CompletableFuture.completedFuture(productGroupMap));

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, true);

        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetTotalProductNumWithGroupName() throws Throwable {
        // arrange
        String groupName = "团购";
        //when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(Collections.singletonList(groupName));
        Map<String, ProductGroupM> productGroupMap = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM(10, Collections.emptyList());
        productGroupMap.put(groupName, productGroupM);
        when(ctx.getAttachment(PaddingFetcher.Attachments.productGroups)).thenReturn(CompletableFuture.completedFuture(productGroupMap));

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, groupName,true);

        // assert
        assertEquals(10, result);
    }

    @Test
    public void testGetTotalProductNumProductsIsEmpty() throws Throwable {
        // arrange
        String groupName = "团购";
        when(ctx.getParam(QueryFetcher.Params.groupNames)).thenReturn(Collections.singletonList(groupName));
        Map<String, ProductGroupM> productGroupMap = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM(10, Collections.emptyList());
        productGroupMap.put(groupName, productGroupM);
        when(ctx.getAttachment(PaddingFetcher.Attachments.productGroups)).thenReturn(CompletableFuture.completedFuture(productGroupMap));

        // act
        int result = ContextParamBuildUtils.getTotalProductNum(ctx, false);

        // assert
        assertEquals(0, result);
    }

    /**
     * Test getParamFromExtraMap when key is null
     */
    @Test
    public void testGetParamFromExtraMap_WhenKeyIsNull() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        String defaultValue = "default";
        String key = null;
        // act
        String result = ContextParamBuildUtils.getParamFromExtraMap(context, key, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test getParamFromExtraMap when key is empty string
     */
    @Test
    public void testGetParamFromExtraMap_WhenKeyIsEmpty() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        String defaultValue = "default";
        String key = "";
        // act
        String result = ContextParamBuildUtils.getParamFromExtraMap(context, key, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test getParamFromExtraMap when key is blank string
     */
    @Test
    public void testGetParamFromExtraMap_WhenKeyIsBlank() {
        // arrange
        ActivityCxt context = new ActivityCxt();
        String defaultValue = "default";
        String key = "   ";
        // act
        String result = ContextParamBuildUtils.getParamFromExtraMap(context, key, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test case for getExtraMapFromContext when extra parameter contains valid URL encoded JSON
     */
    @Test
    public void testGetExtraMapFromContext_WithValidUrlEncodedJson() throws Throwable {
        // arrange
        String encodedJson = "%7B%22key%22%3A%22value%22%7D";
        Map<String, Object> expectedMap = new HashMap<>();
        expectedMap.put("key", "value");
        when(ctx.getParam(ShelfActivityConstants.Params.extra)).thenReturn(encodedJson);
        // act
        Map<String, Object> result = ContextParamBuildUtils.getExtraMapFromContext(ctx);
        // assert
        assertEquals(expectedMap, result);
    }

    /**
     * Test case for getExtraMapFromContext when JSON decoding fails
     */
    @Test
    public void testGetExtraMapFromContext_WhenJsonDecodingFails() throws Throwable {
        // arrange
        String encodedJson = "%7Binvalid-json%7D";
        when(ctx.getParam(ShelfActivityConstants.Params.extra)).thenReturn(encodedJson);
        // act
        Map<String, Object> result = ContextParamBuildUtils.getExtraMapFromContext(ctx);
        // assert
        // Adjusted the assertion to check for null instead of instance of HashMap and empty
        assertNull(result);
    }

    /**
     * Test case for getExtraMapFromContext when extra parameter is blank
     */
    @Test
    public void testGetExtraMapFromContext_WithBlankExtra() throws Throwable {
        // arrange
        when(ctx.getParam(ShelfActivityConstants.Params.extra)).thenReturn("");
        // act
        Map<String, Object> result = ContextParamBuildUtils.getExtraMapFromContext(ctx);
        // assert
        assertTrue(result instanceof HashMap);
        assertTrue(result.isEmpty());
    }

}
