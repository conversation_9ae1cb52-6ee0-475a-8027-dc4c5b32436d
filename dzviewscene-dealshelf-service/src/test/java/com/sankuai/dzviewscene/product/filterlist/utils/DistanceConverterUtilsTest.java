package com.sankuai.dzviewscene.product.filterlist.utils;

import org.junit.Test;
import static org.junit.Assert.*;

public class DistanceConverterUtilsTest {

    @Test
    public void testConvertToMeters_WithValidInput() {
        // 测试正常输入
        assertEquals(100.0, DistanceConverterUtils.convertToMeters("100m"), 0.001);
        assertEquals(1500.0, DistanceConverterUtils.convertToMeters("1.5km"), 0.001);
        assertEquals(2000.0, DistanceConverterUtils.convertToMeters("2km"), 0.001);
        assertEquals(0.5, DistanceConverterUtils.convertToMeters("0.5m"), 0.001);
        assertEquals(500.0, DistanceConverterUtils.convertToMeters(".5km"), 0.001);
    }

    @Test
    public void testConvertToMeters_WithComparisonSymbols() {
        // 测试带比较符号的输入
        assertEquals(100.0, DistanceConverterUtils.convertToMeters("<100m"), 0.001);
        assertEquals(30000.0, DistanceConverterUtils.convertToMeters(">30km"), 0.001);
    }

    @Test
    public void testConvertToMeters_WithInvalidInput() {
        // 测试无效输入
        assertEquals(-1, DistanceConverterUtils.convertToMeters(""), 0.001);
        assertEquals(-1, DistanceConverterUtils.convertToMeters(null), 0.001);
        assertEquals(-1, DistanceConverterUtils.convertToMeters("abc"), 0.001);
        assertEquals(-1, DistanceConverterUtils.convertToMeters("100"), 0.001);
        assertEquals(-1, DistanceConverterUtils.convertToMeters("km"), 0.001);
    }

    @Test
    public void testConvertToMeters_WithUnsupportedUnits() {
        // 测试不支持的单位
        assertEquals(-1, DistanceConverterUtils.convertToMeters("100cm"), 0.001);
        assertEquals(-1, DistanceConverterUtils.convertToMeters("5mi"), 0.001);
    }

    @Test
    public void testConvertToMeters_WithCaseInsensitiveUnits() {
        // 测试大小写不敏感的单位
        assertEquals(1000.0, DistanceConverterUtils.convertToMeters("1KM"), 0.001);
        assertEquals(200.0, DistanceConverterUtils.convertToMeters("200M"), 0.001);
        assertEquals(1500.0, DistanceConverterUtils.convertToMeters("1.5Km"), 0.001);
    }

    @Test
    public void testConvertToMeters_WithDecimalValues() {
        // 测试小数值
        assertEquals(1234.56, DistanceConverterUtils.convertToMeters("1234.56m"), 0.001);
        assertEquals(1234.56, DistanceConverterUtils.convertToMeters("1.23456km"), 0.001);
    }
}