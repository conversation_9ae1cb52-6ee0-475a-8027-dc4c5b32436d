package com.sankuai.dzviewscene.product.filterlist.utils;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;

public class FitnessCrossUtilsTest {

    /**
     * 健身通团购属性key
     */
    private static final String FITNESS_CROSS_KEY = "dealGroupFitnessPassConfig";

    /**
     * 健身通团购属性value
     */
    private static final String FITNESS_CROSS_VALUE = "fitnessPass";

    @Test
    public void isFitnessCrossDeal() {
        ProductM productM = new ProductM();
        productM.setAttr(FITNESS_CROSS_KEY, FITNESS_CROSS_VALUE);

        boolean fitnessCrossDeal = FitnessCrossUtils.isFitnessCrossDeal(productM);
        Assert.assertTrue(fitnessCrossDeal);
    }

    @Test
    public void isFitnessCrossDeal2() {
        AttrM attrM = new AttrM();
        attrM.setName(FITNESS_CROSS_KEY);
        attrM.setValue(FITNESS_CROSS_VALUE);

        DealDetailInfoModel model = new DealDetailInfoModel();
        model.setDealAttrs(Lists.newArrayList(attrM));

        ActivityCxt cxt = new ActivityCxt();
        cxt.attachSource(DealDetailFetcher.CODE, Lists.newArrayList(model));

        Assert.assertTrue(FitnessCrossUtils.isFitnessCrossDeal(cxt));

        attrM.setName(FITNESS_CROSS_KEY + "Fake");
        Assert.assertFalse(FitnessCrossUtils.isFitnessCrossDeal(cxt));

        model.setDealAttrs(null);
        Assert.assertFalse(FitnessCrossUtils.isFitnessCrossDeal(cxt));

        Assert.assertFalse(FitnessCrossUtils.isFitnessCrossDeal(new ActivityCxt()));

    }

}