package com.sankuai.dzviewscene.product.filterlist.utils;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HeadProductUtilsTest {

    @Mock
    private ProductM productM;

    @Test
    public void isHeadProduct_nullProduct_returnFalse() {
        // 测试产品为null的情况
        assertFalse(HeadProductUtils.isHeadProduct(null));
    }

    @Test
    public void isHeadProduct_withHeadAttrValue_returnTrue() {
        // 测试产品有置顶属性的情况
        when(productM.getAttr(HeadProductUtils.PRODUCT_ATTR_FROM_GROUP)).thenReturn(HeadProductUtils.HEAD_ATTR_VALUE);
        assertTrue(HeadProductUtils.isHeadProduct(productM));
    }

    @Test
    public void isHeadProduct_withoutHeadAttrValue_returnFalse() {
        // 测试产品没有置顶属性的情况
        when(productM.getAttr(HeadProductUtils.PRODUCT_ATTR_FROM_GROUP)).thenReturn("其他值");
        assertFalse(HeadProductUtils.isHeadProduct(productM));
    }

    @Test
    public void isHeadProduct_withNullAttrValue_returnFalse() {
        // 测试产品属性值为null的情况
        when(productM.getAttr(HeadProductUtils.PRODUCT_ATTR_FROM_GROUP)).thenReturn(null);
        assertFalse(HeadProductUtils.isHeadProduct(productM));
    }
}