package com.sankuai.dzviewscene.product.filterlist.utils;


import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.junit.Assert.assertEquals;

public class ImageUtilsTest {

    @Before
    public void setUp() {
        ImageUtils.venusDomainList.add("test.com");
    }

    /**
     * 测试图片URL为空
     */
    @Test
    public void testTransferImg2WebpWithEmptyUrl() {
        String result = ImageUtils.transferImg2Webp("");
        assertEquals("", result);
    }

    /**
     * 测试图片URL不包含venus域名
     */
    @Test
    public void testTransferImg2WebpWithNonVenusDomain() {
        String result = ImageUtils.transferImg2Webp("http://example.com/image.jpg");
        assertEquals("http://example.com/image.jpg", result);
    }

    /**
     * 测试开关关闭时
     */
    @Test
    public void testTransferImg2WebpWithSwitchOff() {
        try (MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class);
             MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class)) {
            mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
            mockedLion.when(() -> Lion.getBoolean("testApp", ImageUtils.PIC_APPEND_WEBP_SUFFIX, true)).thenReturn(false);
            String result = ImageUtils.transferImg2Webp("http://no.test.com/image.jpg");
            assertEquals("http://no.test.com/image.jpg", result);
        }
    }

    /**
     * 测试正常转换为WebP格式
     */
    @Test
    public void testTransferImg2WebpWithNormalCase() {
        try (MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class);
             MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class)) {
            mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
            mockedLion.when(() -> Lion.getBoolean("testApp", ImageUtils.PIC_APPEND_WEBP_SUFFIX, true)).thenReturn(true);
            String result = ImageUtils.transferImg2Webp("http://test.com/image.jpg");
            assertEquals("http://test.com/image.jpg@format=webp", result);
        }
    }
}