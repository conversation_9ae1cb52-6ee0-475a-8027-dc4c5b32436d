package com.sankuai.dzviewscene.product.filterlist.utils;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mockStatic;

/**
 * Test class for {@link PromoCodeUtils#judgePromoShopSceneCode(ShelfLoadConfig)}
 */
@RunWith(MockitoJUnitRunner.class)
public class PromoCodeUtilsTest {

    /**
     * Test case when config is null
     */
    @Test
    public void testJudgePromoShopSceneCode_NullConfig() throws Throwable {
        // arrange
        ShelfLoadConfig config = null;
        // act
        String result = PromoCodeUtils.judgePromoShopSceneCode(config);
        // assert
        assertEquals(PromoCodeUtils.PROMO_CODE_SHOP_SCENE, result);
    }

    /**
     * Test case when loadCustomizeGroupPurchase is true
     */
    @Test
    public void testJudgePromoShopSceneCode_LoadCustomizeGroupPurchaseTrue() throws Throwable {
        // arrange
        ShelfLoadConfig config = new ShelfLoadConfig();
        config.setLoadCustomizeGroupPurchase(true);
        // act
        String result = PromoCodeUtils.judgePromoShopSceneCode(config);
        // assert
        assertEquals(PromoCodeUtils.PROMO_CODE_CUSTOMIZE_SHOP_SCENE, result);
    }

    /**
     * Test case when loadCustomizeGroupPurchase is false
     */
    @Test
    public void testJudgePromoShopSceneCode_LoadCustomizeGroupPurchaseFalse() throws Throwable {
        // arrange
        ShelfLoadConfig config = new ShelfLoadConfig();
        config.setLoadCustomizeGroupPurchase(false);
        // act
        String result = PromoCodeUtils.judgePromoShopSceneCode(config);
        // assert
        assertEquals(PromoCodeUtils.PROMO_CODE_SHOP_SCENE, result);
    }


    /**
     * 测试场景：scene2CategoryMap 为空
     */
    @Test
    public void testJudgePromoShopScene_WhenSceneMapIsEmpty() throws Throwable {
        // arrange
        Integer shopCategory = 1;
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getMap(Environment.getAppName(), PromoCodeUtils.SCENE_CATEGORY_MAP_KEY, List.class, new HashMap<>()))
                    .thenReturn(new HashMap<>());

            // act
            String result = PromoCodeUtils.judgePromoShopScene(shopCategory);

            // assert
            assertEquals(PromoCodeUtils.DEFAULT_SHOP_KEY, result);
        }
    }

    /**
     * 测试场景：scene2CategoryMap 不为空，但 shopCategory 不存在
     */
    @Test
    public void testJudgePromoShopScene_WhenCategoryNotFound() throws Throwable {
        // arrange
        Integer shopCategory = 1;
        Map<String, List<Integer>> scene2CategoryMap = new HashMap<>();
        scene2CategoryMap.put("scene1", Lists.newArrayList(2, 3));
        scene2CategoryMap.put("scene2", Lists.newArrayList(4, 5));

        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getMap(Environment.getAppName(), PromoCodeUtils.SCENE_CATEGORY_MAP_KEY, List.class, new HashMap<>()))
                    .thenReturn(scene2CategoryMap);

            // act
            String result = PromoCodeUtils.judgePromoShopScene(shopCategory);

            // assert
            assertEquals(PromoCodeUtils.DEFAULT_SHOP_KEY, result);
        }
    }

    /**
     * 测试场景：scene2CategoryMap 不为空，且 shopCategory 存在
     */
    @Test
    public void testJudgePromoShopScene_WhenCategoryFound() throws Throwable {
        // arrange
        Integer shopCategory = 1;
        Map<String, List<Integer>> scene2CategoryMap = new HashMap<>();
        scene2CategoryMap.put("scene1", Lists.newArrayList(1, 2));
        scene2CategoryMap.put("scene2", Lists.newArrayList(3, 4));

        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getMap(Environment.getAppName(), PromoCodeUtils.SCENE_CATEGORY_MAP_KEY, List.class, new HashMap<>()))
                    .thenReturn(scene2CategoryMap);

            // act
            String result = PromoCodeUtils.judgePromoShopScene(shopCategory);

            // assert
            assertEquals("scene1", result);
        }
    }

    /**
     * 测试场景：Lion.getMap 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testJudgePromoShopScene_WhenLionThrowsException() throws Throwable {
        // arrange
        Integer shopCategory = 1;
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getMap(Environment.getAppName(), PromoCodeUtils.SCENE_CATEGORY_MAP_KEY, List.class, new HashMap<>()))
                    .thenThrow(new RuntimeException("Mocked exception"));

            // act
            PromoCodeUtils.judgePromoShopScene(shopCategory);
        }
    }
}
