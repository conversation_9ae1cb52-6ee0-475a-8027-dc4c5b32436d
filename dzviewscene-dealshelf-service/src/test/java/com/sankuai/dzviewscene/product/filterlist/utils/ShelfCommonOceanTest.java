package com.sankuai.dzviewscene.product.filterlist.utils;

import com.alibaba.fastjson.JSON;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.ShelfCommonOcean;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class ShelfCommonOceanTest {

    @Test
    public void test_getProductMMap(){
        ActivityContext ctx = new ActivityContext();
        ctx.addParam(ShelfActivityConstants.Params.shelfType, 2);
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        List<ProductM> products = new ArrayList<>();
        ProductM productM = new ProductM();
        productM.setProductId(1234);
        productM.setAttr("recRealTimeInfos", "123213_23mt#12312312ww,2345_45mt#3434354343");
        products.add(productM);
        products.add(productM);
        productGroupM.setProducts(products);
        productGroupMs.put("团购", productGroupM);
        shelfGroupM.setProductGroupMs(productGroupMs);
        ctx.setMainData(CompletableFuture.completedFuture(shelfGroupM));
        int productType = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.shelfType);
        Map<Integer, ProductM> productMMap = ShelfCommonOcean.getProductMMap(ctx);
        List<Map<String, String>> recRealTimeInfos = ShelfCommonOcean.getRecRealTimeInfos(productMMap, 1234);
        Assert.assertTrue(recRealTimeInfos.size() >0);
    }
}
