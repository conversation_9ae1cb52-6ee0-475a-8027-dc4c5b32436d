package com.sankuai.dzviewscene.product.filterlist.utils;

import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class ShopNumberUtilsTest {

    @Test
    public void testProcessApplyShopNum_negativeValue() {
        // 测试负数情况
        assertNull(ShopNumberUtils.processApplyShopNum(-1));
    }

    @Test
    public void testProcessApplyShopNum_zero() {
        // 测试零值情况
        assertNull(ShopNumberUtils.processApplyShopNum(0));
    }

    @Test
    public void testProcessApplyShopNum_smallNumber() {
        // 测试小于等于49的数字
        assertEquals("1", ShopNumberUtils.processApplyShopNum(1));
        assertEquals("10", ShopNumberUtils.processApplyShopNum(10));
        assertEquals("49", ShopNumberUtils.processApplyShopNum(49));
    }

    @Test
    public void testProcessApplyShopNum_mediumNumber() {
        // 测试50-99范围的数字
        assertEquals("50+", ShopNumberUtils.processApplyShopNum(50));
        assertEquals("50+", ShopNumberUtils.processApplyShopNum(59));
        assertEquals("90+", ShopNumberUtils.processApplyShopNum(99));
        assertEquals("90+", ShopNumberUtils.processApplyShopNum(91));
    }

    @Test
    public void testProcessApplyShopNum_largeNumber() {
        // 测试100-999范围的数字
        assertEquals("100+", ShopNumberUtils.processApplyShopNum(100));
        assertEquals("100+", ShopNumberUtils.processApplyShopNum(199));
        assertEquals("900+", ShopNumberUtils.processApplyShopNum(999));
        assertEquals("900+", ShopNumberUtils.processApplyShopNum(901));
    }

    @Test
    public void testProcessApplyShopNum_veryLargeNumber() {
        // 测试1000及以上的数字
        assertEquals("1000+", ShopNumberUtils.processApplyShopNum(1000));
        assertEquals("1000+", ShopNumberUtils.processApplyShopNum(1999));
        assertEquals("2000+", ShopNumberUtils.processApplyShopNum(2000));
        assertEquals("10000+", ShopNumberUtils.processApplyShopNum(10001));
    }
}