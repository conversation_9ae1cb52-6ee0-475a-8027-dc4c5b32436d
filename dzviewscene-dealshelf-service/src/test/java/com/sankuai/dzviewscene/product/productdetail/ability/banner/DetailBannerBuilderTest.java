package com.sankuai.dzviewscene.product.productdetail.ability.banner;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.banner.DetailBannerCfg;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.DetailModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.BannerModuleVO;
import com.sankuai.dzviewscene.product.utils.CtxUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ShareVO;
import com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.product.ability.mergequery.ProductMergeQueryAbility;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DetailBannerBuilderTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DetailBannerParam param;

    @Mock
    private DetailBannerCfg cfg;

    @InjectMocks
    private DetailBannerBuilder detailBannerBuilder;

    @Before
    public void setUp() {
        when(cfg.getModuleKey()).thenReturn("banner");
        // Assuming BannerTitleVP and BannerShareVP are interfaces that need to be mocked
        // Mocking them directly is not possible due to constraints, so we ensure ctx returns non-null values for required sources
        Map<String, Object> sourceMap = new HashMap<>();
        // Mocking an empty product group map
        sourceMap.put(ProductMergeQueryAbility.CODE, new HashMap<>());
        // Assuming collect info is true for simplicity
        sourceMap.put(CollectInfoAbility.CODE, true);
        when(ctx.getSource(anyString())).thenAnswer(invocation -> sourceMap.get(invocation.getArgument(0)));
    }

    @Test
    public void testBuildProductListIsEmpty() throws Throwable {
        CompletableFuture<DetailModuleVO<BannerModuleVO>> result = detailBannerBuilder.build(ctx, param, cfg);
        assertNotNull(result);
        assertTrue(result.isDone());
        assertEquals("banner", result.get().getModuleKey());
    }
}
