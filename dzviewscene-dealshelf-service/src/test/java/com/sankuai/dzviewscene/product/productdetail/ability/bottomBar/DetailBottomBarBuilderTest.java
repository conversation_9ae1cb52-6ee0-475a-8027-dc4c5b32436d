package com.sankuai.dzviewscene.product.productdetail.ability.bottomBar;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.BottomBarModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.DetailModuleVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.concurrent.CompletableFuture;
import com.sankuai.dzviewscene.product.productdetail.ability.bottomBar.vpoints.BottomBarSingleButtonVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ButtonVO;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DetailBottomBarBuilderTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DetailBottomBarCfg cfg;

    // Assuming BottomBarSingleButtonVP is an interface or abstract class that can be mocked
    @Mock
    private BottomBarSingleButtonVP<ButtonVO> bottomBarSingleButtonVP;

    @InjectMocks
    private DetailBottomBarBuilder builder;

    @Test(expected = Exception.class)
    public void testBuildException() throws Throwable {
        // arrange
        when(cfg.getModuleKey()).thenThrow(new Exception());
        // act
        builder.build(ctx, null, cfg);
    }
}
