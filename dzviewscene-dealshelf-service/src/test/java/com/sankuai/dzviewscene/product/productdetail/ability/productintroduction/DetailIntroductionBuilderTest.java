package com.sankuai.dzviewscene.product.productdetail.ability.productintroduction;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.DetailModuleVO;
import com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.vpoints.DetailIntroductionVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.CompletableFuture;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import org.junit.*;
import static org.mockito.ArgumentMatchers.eq;
import java.util.concurrent.ExecutionException;

@RunWith(MockitoJUnitRunner.class)
public class DetailIntroductionBuilderTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DetailIntroductionCfg cfg;

    @Mock
    private DetailIntroductionVP<?> detailIntroductionVP;

    @Mock
    private DetailModuleVO<?> detailModuleVO;

    @InjectMocks
    private DetailIntroductionBuilder builder;

    /**
     * Tests the build method under exceptional conditions.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildException() throws Throwable {
        // Act
        builder.build(null, null, null);
    }
}
