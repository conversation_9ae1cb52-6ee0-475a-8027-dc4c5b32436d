package com.sankuai.dzviewscene.product.productdetail.ability.purchaserule;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.purchaserule.vpoints.PurchaseRuleModuleVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.DetailModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.PurchaseRuleModuleVO;
import com.sankuai.dzviewscene.product.utils.CtxUtils;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DetailPurchaseRuleBuilderTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DetailPurchaseRuleCfg cfg;

    @InjectMocks
    private DetailPurchaseRuleBuilder builder;

    @Mock
    private com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper pmfExecutionHelper;

    @Test(expected = NullPointerException.class)
    public void testBuildWithNullContext() throws Throwable {
        // Act & Assert
        builder.build(null, null, cfg);
    }

    @Test(expected = NullPointerException.class)
    public void testBuildWithNullConfig() throws Throwable {
        // Act & Assert
        builder.build(ctx, null, null);
    }

    /**
     * Test build method with normal content
     * Cover the code block that sets content and returns CompletableFuture
     */
    @Test
    public void testBuildWithNormalContent() throws Throwable {
        // arrange
        ActivityCxt ctx = new ActivityCxt();
        DetailPurchaseRuleCfg cfg = new DetailPurchaseRuleCfg();
        cfg.setModuleKey("testKey");
        // Mock the PmfExecutionHelper to return a valid PurchaseRuleModuleVP instance
        PurchaseRuleModuleVP mockVPoint = mock(PurchaseRuleModuleVP.class);
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), eq(DetailPurchaseRuleBuilder.CODE), eq(PurchaseRuleModuleVP.CODE))).thenReturn(mockVPoint);
        // act
        CompletableFuture<DetailModuleVO<PurchaseRuleModuleVO>> result = builder.build(ctx, null, cfg);
        // assert
        assertNotNull(result);
        DetailModuleVO<PurchaseRuleModuleVO> moduleVO = result.get();
        assertEquals("testKey", moduleVO.getModuleKey());
        // Since we cannot mock the internal behavior of findVPoint, we cannot assert the content directly.
        // This test focuses on ensuring the build method returns a non-null CompletableFuture and sets the moduleKey correctly.
    }
}
