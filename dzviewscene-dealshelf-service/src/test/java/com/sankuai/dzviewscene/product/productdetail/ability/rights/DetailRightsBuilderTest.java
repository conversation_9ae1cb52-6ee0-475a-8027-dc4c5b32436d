package com.sankuai.dzviewscene.product.productdetail.ability.rights;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.DetailModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.RightsModuleVO;
import com.sankuai.dzviewscene.product.utils.CtxUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dzviewscene.product.productdetail.ability.rights.vpoints.RightsModuleVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DetailRightsBuilderTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DetailRightsCfg cfg;

    @InjectMocks
    private DetailRightsBuilder builder;

    @Test(expected = NullPointerException.class)
    public void testBuildWithNullContext() throws Throwable {
        // Act & Assert
        builder.build(null, null, cfg);
    }

    @Test(expected = NullPointerException.class)
    public void testBuildWithNullConfig() throws Throwable {
        // Act & Assert
        builder.build(ctx, null, null);
    }
}
