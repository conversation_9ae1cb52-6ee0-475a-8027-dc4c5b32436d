package com.sankuai.dzviewscene.product.productdetail.options.banner.more;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.banner.vpoints.BannerShareVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ShareVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import java.util.Collections;
import java.util.Arrays;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultShareContentOptTest {

    /**
     * 测试 compute 方法，当 param.getProductMs() 为空的情况
     */
    @Test
    public void testComputeWhenProductMsIsEmpty() throws Throwable {
        // arrange
        DefaultShareContentOpt defaultShareContentOpt = new DefaultShareContentOpt();
        ActivityCxt ctx = new ActivityCxt();
        // Using builder to instantiate Param
        BannerShareVP.Param param = BannerShareVP.Param.builder().productMs(Collections.emptyList()).build();
        DefaultShareContentOpt.Config cfg = new DefaultShareContentOpt.Config();
        // act
        ShareVO result = defaultShareContentOpt.compute(ctx, param, cfg);
        // assert
        assertNull(result);
    }

    /**
     * 测试 compute 方法，当 param.getProductMs() 不为空的情况
     */
    @Test
    public void testComputeWhenProductMsIsNotEmpty() throws Throwable {
        // arrange
        DefaultShareContentOpt defaultShareContentOpt = new DefaultShareContentOpt();
        ActivityCxt ctx = new ActivityCxt();
        ProductM product = new ProductM();
        product.setTitle("title");
        product.setPicUrl("picUrl");
        product.setJumpUrl("jumpUrl");
        // Using builder to instantiate Param
        BannerShareVP.Param param = BannerShareVP.Param.builder().productMs(Collections.singletonList(product)).build();
        DefaultShareContentOpt.Config cfg = new DefaultShareContentOpt.Config();
        // act
        ShareVO result = defaultShareContentOpt.compute(ctx, param, cfg);
        // assert
        assertNotNull(result);
        assertEquals("title", result.getTitle());
        assertEquals("picUrl", result.getPic());
        assertEquals("jumpUrl", result.getJumpUrl());
    }
}
