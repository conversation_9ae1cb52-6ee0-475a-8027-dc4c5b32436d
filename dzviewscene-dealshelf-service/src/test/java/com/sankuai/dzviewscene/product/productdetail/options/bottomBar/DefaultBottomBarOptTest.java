package com.sankuai.dzviewscene.product.productdetail.options.bottomBar;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.bottomBar.vpoints.BottomBarSingleButtonVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultBottomBarOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private BottomBarSingleButtonVP.Param param;

    @Mock
    private DefaultBottomBarOpt.Config cfg;

    @Mock
    private ProductM productM;

    private DefaultBottomBarOpt defaultBottomBarOpt = new DefaultBottomBarOpt();

    /**
     * 测试ProductM的available属性为true的情况
     */
    @Test
    public void testComputeProductMAvailableTrue() {
        // arrange
        when(param.getProductMs()).thenReturn(java.util.Arrays.asList(productM));
        when(productM.getAvailable()).thenReturn(true);
        when(productM.getBasePriceTag()).thenReturn("100");
        when(productM.getOrderUrl()).thenReturn("orderUrl");
        when(cfg.getButtonName()).thenReturn("buttonName");
        // act
        ButtonVO buttonVO = defaultBottomBarOpt.compute(activityCxt, param, cfg);
        // assert
        assertEquals("100", buttonVO.getSalePrice());
        assertEquals("buttonName", buttonVO.getButtonName());
        assertEquals(1, buttonVO.getButtonStatus());
        assertEquals("", buttonVO.getDisableTips());
        assertEquals("orderUrl", buttonVO.getButtonUrl());
    }

    /**
     * 测试ProductM的available属性为false的情况
     */
    @Test
    public void testComputeProductMAvailableFalse() {
        // arrange
        when(param.getProductMs()).thenReturn(java.util.Arrays.asList(productM));
        when(productM.getAvailable()).thenReturn(false);
        when(productM.getBasePriceTag()).thenReturn("100");
        when(productM.getOrderUrl()).thenReturn("orderUrl");
        when(cfg.getDisableButtonName()).thenReturn("disableButtonName");
        when(cfg.getDisableTips()).thenReturn("disableTips");
        // act
        ButtonVO buttonVO = defaultBottomBarOpt.compute(activityCxt, param, cfg);
        // assert
        assertEquals("100", buttonVO.getSalePrice());
        assertEquals("disableButtonName", buttonVO.getButtonName());
        assertEquals(0, buttonVO.getButtonStatus());
        assertEquals("disableTips", buttonVO.getDisableTips());
        assertEquals("orderUrl", buttonVO.getButtonUrl());
    }
}
