package com.sankuai.dzviewscene.product.productdetail.options.productbasics.imglist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.productbasics.vpoints.DetailBasicImgListVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ImgVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.runner.RunWith.*;

public class FixedPriceDetailImgListOptTest {

    private ActivityCxt context;

    private DetailBasicImgListVP.Param param;

    private FixedPriceDetailImgListOpt.Cfg cfg;

    private ProductM productM;

    @Before
    public void setUp() throws Exception {
        context = new ActivityCxt();
        productM = Mockito.mock(ProductM.class);
        param = createParam(Collections.singletonList(productM));
        FixedPriceDetailImgListOpt opt = new FixedPriceDetailImgListOpt();
        cfg = opt.new Cfg();
    }

    @After
    public void tearDown() {
        context = null;
        param = null;
        cfg = null;
        productM = null;
    }

    private DetailBasicImgListVP.Param createParam(List<ProductM> productMs) {
        return DetailBasicImgListVP.Param.builder().productMs(productMs).build();
    }

    /**
     * Test compute when productM is null
     */
    @Test
    public void testComputeProductMIsNull() throws Throwable {
        param.setProductMs(Collections.singletonList(null));
        FixedPriceDetailImgListOpt opt = new FixedPriceDetailImgListOpt();
        List<ImgVO> result = opt.compute(context, param, cfg);
        assertTrue("Result should be empty when productM is null", result.isEmpty());
    }

    /**
     * Test compute when imgList is empty
     */
    @Test
    public void testComputeImgListIsEmpty() throws Throwable {
        Mockito.when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_DETAIL_PIC_LIST.getKey())).thenReturn(Collections.emptyList());
        param.setProductMs(Collections.singletonList(productM));
        FixedPriceDetailImgListOpt opt = new FixedPriceDetailImgListOpt();
        List<ImgVO> result = opt.compute(context, param, cfg);
        assertTrue("Result should be empty when imgList is empty", result.isEmpty());
    }

    /**
     * Test compute with valid imgList
     */
    @Test
    public void testComputeWithValidImgList() throws Throwable {
        List<String> imgList = Arrays.asList("img1", "img2");
        Mockito.when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_DETAIL_PIC_LIST.getKey())).thenReturn(imgList);
        param.setProductMs(Collections.singletonList(productM));
        FixedPriceDetailImgListOpt opt = new FixedPriceDetailImgListOpt();
        List<ImgVO> result = opt.compute(context, param, cfg);
        assertEquals("Result size should match imgList size", imgList.size(), result.size());
        for (int i = 0; i < imgList.size(); i++) {
            assertEquals("ImgVO picUrl should match imgList entry", imgList.get(i), result.get(i).getPicUrl());
        }
    }
}
