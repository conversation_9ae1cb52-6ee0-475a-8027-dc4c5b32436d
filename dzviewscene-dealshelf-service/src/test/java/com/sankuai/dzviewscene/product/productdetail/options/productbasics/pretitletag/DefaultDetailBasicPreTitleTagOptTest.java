package com.sankuai.dzviewscene.product.productdetail.options.productbasics.pretitletag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.productbasics.vpoints.DetailBasicPreTitleTagVP;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultDetailBasicPreTitleTagOptTest {

    /**
     * 测试 compute 方法，当 cfg 为 null 时，应返回空字符串
     */
    @Test
    public void testComputeCfgIsNull() {
        // arrange
        DefaultDetailBasicPreTitleTagOpt opt = new DefaultDetailBasicPreTitleTagOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DetailBasicPreTitleTagVP.Param param = mock(DetailBasicPreTitleTagVP.Param.class);
        // act
        String result = opt.compute(context, param, null);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 compute 方法，当 cfg 不为 null 时，应返回 cfg 的 tag 属性
     */
    @Test
    public void testComputeCfgIsNotNull() {
        // arrange
        DefaultDetailBasicPreTitleTagOpt opt = new DefaultDetailBasicPreTitleTagOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DetailBasicPreTitleTagVP.Param param = mock(DetailBasicPreTitleTagVP.Param.class);
        DefaultDetailBasicPreTitleTagOpt.Cfg cfg = mock(DefaultDetailBasicPreTitleTagOpt.Cfg.class);
        when(cfg.getTag()).thenReturn("testTag");
        // act
        String result = opt.compute(context, param, cfg);
        // assert
        assertEquals("testTag", result);
    }
}
