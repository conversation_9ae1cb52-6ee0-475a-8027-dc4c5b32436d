package com.sankuai.dzviewscene.product.productdetail.options.productbasics.price;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.PriceVO;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzTagVO;
import com.sankuai.dzviewscene.product.productdetail.ability.productbasics.vpoints.DetailBasicPriceVP;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import org.junit.*;
import static org.junit.Assert.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultDetailBasicPriceOptTest {

    @Test
    public void testComputeNormal() throws Throwable {
        DefaultDetailBasicPriceOpt defaultDetailBasicPriceOpt = new DefaultDetailBasicPriceOpt();
        ProductM productM = new ProductM();
        productM.setBasePriceTag("100");
        DetailBasicPriceVP.Param param = DetailBasicPriceVP.Param.builder().productMs(Arrays.asList(productM)).build();
        DefaultDetailBasicPriceOpt.Cfg cfg = new DefaultDetailBasicPriceOpt.Cfg();
        DzTagVO afterPriceTag = new DzTagVO("200", 0);
        cfg.setAfterPriceTag(afterPriceTag);
        PriceVO priceVO = defaultDetailBasicPriceOpt.compute(null, param, cfg);
        assertNotNull(priceVO);
        assertEquals("100", priceVO.getSalePrice());
        assertEquals("200", priceVO.getAfterPriceTag().getName());
    }

    @Test(expected = NullPointerException.class)
    public void testComputeParamNull() throws Throwable {
        DefaultDetailBasicPriceOpt defaultDetailBasicPriceOpt = new DefaultDetailBasicPriceOpt();
        defaultDetailBasicPriceOpt.compute(null, null, null);
    }

    @Test(expected = NullPointerException.class)
    public void testComputeParamNoProductM() throws Throwable {
        DefaultDetailBasicPriceOpt defaultDetailBasicPriceOpt = new DefaultDetailBasicPriceOpt();
        DetailBasicPriceVP.Param param = DetailBasicPriceVP.Param.builder().build();
        defaultDetailBasicPriceOpt.compute(null, param, null);
    }

    @Test(expected = NullPointerException.class)
    public void testComputeCfgNull() throws Throwable {
        DefaultDetailBasicPriceOpt defaultDetailBasicPriceOpt = new DefaultDetailBasicPriceOpt();
        ProductM productM = new ProductM();
        productM.setBasePriceTag("100");
        DetailBasicPriceVP.Param param = DetailBasicPriceVP.Param.builder().productMs(Arrays.asList(productM)).build();
        defaultDetailBasicPriceOpt.compute(null, param, null);
    }

    @Test
    public void testComputeCfgNoAfterPriceTag() throws Throwable {
        DefaultDetailBasicPriceOpt defaultDetailBasicPriceOpt = new DefaultDetailBasicPriceOpt();
        ProductM productM = new ProductM();
        productM.setBasePriceTag("100");
        DetailBasicPriceVP.Param param = DetailBasicPriceVP.Param.builder().productMs(Arrays.asList(productM)).build();
        DefaultDetailBasicPriceOpt.Cfg cfg = new DefaultDetailBasicPriceOpt.Cfg();
        PriceVO priceVO = defaultDetailBasicPriceOpt.compute(null, param, cfg);
        assertNotNull(priceVO);
        assertEquals("100", priceVO.getSalePrice());
        // Adjusted expectation
        assertNull(priceVO.getAfterPriceTag());
    }
}
