package com.sankuai.dzviewscene.product.productdetail.options.productintroduction;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.vpoints.DetailIntroductionVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.biz.massagespu.FixedPriceSpuIntroductionVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FixedPriceSpuIntroductionOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DetailIntroductionVP.Param param;

    @Mock
    private ProductM productM;

    private final FixedPriceSpuIntroductionOpt opt = new FixedPriceSpuIntroductionOpt();

    /**
     * Test compute method with valid ProductM containing all attributes
     */
    @Test
    public void testComputeWithValidProductAttributes() throws Throwable {
        // arrange
        List<String> introducePic = Arrays.asList("pic1", "pic2");
        List<String> serviceProcess = Arrays.asList("process1", "process2");
        when(param.getProductMs()).thenReturn(Collections.singletonList(productM));
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey())).thenReturn(introducePic);
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey())).thenReturn(serviceProcess);
        // act
        Content result = opt.compute(context, param, opt.new Cfg());
        // assert
        assertNotNull(result);
        assertTrue(result instanceof FixedPriceSpuIntroductionVO);
        FixedPriceSpuIntroductionVO vo = (FixedPriceSpuIntroductionVO) result;
        assertEquals(introducePic, vo.getIntroducePic());
        assertEquals(serviceProcess, vo.getServiceProcess());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey());
    }

    /**
     * Test compute method with ProductM having null attributes
     */
    @Test
    public void testComputeWithNullProductAttributes() throws Throwable {
        // arrange
        when(param.getProductMs()).thenReturn(Collections.singletonList(productM));
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey())).thenReturn(null);
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey())).thenReturn(null);
        // act
        Content result = opt.compute(context, param, opt.new Cfg());
        // assert
        assertNotNull(result);
        assertTrue(result instanceof FixedPriceSpuIntroductionVO);
        FixedPriceSpuIntroductionVO vo = (FixedPriceSpuIntroductionVO) result;
        assertNull(vo.getIntroducePic());
        assertNull(vo.getServiceProcess());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey());
    }

    /**
     * Test compute method with ProductM having partial attributes
     */
    @Test
    public void testComputeWithPartialProductAttributes() throws Throwable {
        // arrange
        List<String> introducePic = Arrays.asList("pic1", "pic2");
        when(param.getProductMs()).thenReturn(Collections.singletonList(productM));
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey())).thenReturn(introducePic);
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey())).thenReturn(null);
        // act
        Content result = opt.compute(context, param, opt.new Cfg());
        // assert
        assertNotNull(result);
        assertTrue(result instanceof FixedPriceSpuIntroductionVO);
        FixedPriceSpuIntroductionVO vo = (FixedPriceSpuIntroductionVO) result;
        assertEquals(introducePic, vo.getIntroducePic());
        assertNull(vo.getServiceProcess());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey());
    }

    /**
     * Test compute method with multiple ProductMs in list
     */
    @Test
    public void testComputeWithMultipleProductMs() throws Throwable {
        // arrange
        List<String> introducePic = Arrays.asList("pic1", "pic2");
        List<String> serviceProcess = Arrays.asList("process1", "process2");
        ProductM secondProductM = mock(ProductM.class);
        when(param.getProductMs()).thenReturn(Arrays.asList(productM, secondProductM));
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey())).thenReturn(introducePic);
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey())).thenReturn(serviceProcess);
        // act
        Content result = opt.compute(context, param, opt.new Cfg());
        // assert
        assertNotNull(result);
        assertTrue(result instanceof FixedPriceSpuIntroductionVO);
        FixedPriceSpuIntroductionVO vo = (FixedPriceSpuIntroductionVO) result;
        assertEquals(introducePic, vo.getIntroducePic());
        assertEquals(serviceProcess, vo.getServiceProcess());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_INTRODUCE_PIC.getKey());
        verify(productM, times(1)).getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_SERVICE_PROCESS.getKey());
        verify(secondProductM, never()).getObjAttr(any());
    }
}
