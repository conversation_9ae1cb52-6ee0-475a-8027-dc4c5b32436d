package com.sankuai.dzviewscene.product.productdetail.options.productintroduction;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.vpoints.DetailIntroductionVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FixedPriceSpuIntroductionOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DetailIntroductionVP.Param param;

    @Mock
    private ProductM productM;

    @InjectMocks
    private FixedPriceSpuIntroductionOpt opt;

    @Test
    public void testComputeWhenProductMsIsEmpty() throws Throwable {
        // arrange
        when(param.getProductMs()).thenReturn(Collections.emptyList());
        // act
        Content result = opt.compute(context, param, opt.new Cfg());
        // assert
        assertNull(result);
    }
}
