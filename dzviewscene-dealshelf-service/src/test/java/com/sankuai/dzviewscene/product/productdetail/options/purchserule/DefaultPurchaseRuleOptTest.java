package com.sankuai.dzviewscene.product.productdetail.options.purchserule;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.purchaserule.vpoints.PurchaseRuleModuleVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.PurchaseRuleModuleVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefaultPurchaseRuleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private PurchaseRuleModuleVP.Param param;

    @Mock
    private DefaultPurchaseRuleOpt.Cfg cfg;

    /**
     * 测试 compute 方法
     */
    @Test
    public void testCompute() throws Throwable {
        // arrange
        DefaultPurchaseRuleOpt defaultPurchaseRuleOpt = new DefaultPurchaseRuleOpt();
        // act
        PurchaseRuleModuleVO result = defaultPurchaseRuleOpt.compute(context, param, cfg);
        // assert
        // 在这里添加你的断言
        assertNull(result);
    }
}
