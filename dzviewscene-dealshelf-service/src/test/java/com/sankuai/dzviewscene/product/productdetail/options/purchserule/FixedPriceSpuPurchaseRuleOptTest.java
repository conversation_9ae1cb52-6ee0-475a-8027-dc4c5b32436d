package com.sankuai.dzviewscene.product.productdetail.options.purchserule;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.purchaserule.vpoints.PurchaseRuleModuleVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.PurchaseRuleModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import com.sankuai.dztheme.spuproduct.res.attrmodel.common.RuleDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.junit.Assert.*;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.RuleVO;
import java.util.List;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FixedPriceSpuPurchaseRuleOptTest {

    @Mock
    private ProductM productM;

    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        FixedPriceSpuPurchaseRuleOpt fixedPriceSpuPurchaseRuleOpt = new FixedPriceSpuPurchaseRuleOpt();
        ActivityCxt context = new ActivityCxt();
        PurchaseRuleModuleVP.Param param = PurchaseRuleModuleVP.Param.builder().productMs(Collections.singletonList(productM)).build();
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_PURCHASE_RULE.getKey())).thenReturn(Collections.singletonList(new RuleDTO("Test Title", Collections.singletonList("Test Rule"))));
        // act
        PurchaseRuleModuleVO result = fixedPriceSpuPurchaseRuleOpt.compute(context, param, new FixedPriceSpuPurchaseRuleOpt.Cfg());
        // assert
        assertNotNull(result);
        assertEquals("购买须知", result.getTitle());
        assertNotNull(result.getRuleList());
        assertEquals(1, result.getRuleList().size());
    }

    @Test(expected = NullPointerException.class)
    public void testComputeProductMIsNull() throws Throwable {
        // arrange
        FixedPriceSpuPurchaseRuleOpt fixedPriceSpuPurchaseRuleOpt = new FixedPriceSpuPurchaseRuleOpt();
        ActivityCxt context = new ActivityCxt();
        PurchaseRuleModuleVP.Param param = PurchaseRuleModuleVP.Param.builder().productMs(Collections.singletonList(null)).build();
        // act
        fixedPriceSpuPurchaseRuleOpt.compute(context, param, new FixedPriceSpuPurchaseRuleOpt.Cfg());
    }

    @Test
    public void testComputeRuleListIsEmpty() throws Throwable {
        // arrange
        FixedPriceSpuPurchaseRuleOpt fixedPriceSpuPurchaseRuleOpt = new FixedPriceSpuPurchaseRuleOpt();
        ActivityCxt context = new ActivityCxt();
        PurchaseRuleModuleVP.Param param = PurchaseRuleModuleVP.Param.builder().productMs(Collections.singletonList(productM)).build();
        when(productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_PURCHASE_RULE.getKey())).thenReturn(Collections.emptyList());
        // act
        PurchaseRuleModuleVO result = fixedPriceSpuPurchaseRuleOpt.compute(context, param, new FixedPriceSpuPurchaseRuleOpt.Cfg());
        // assert
        assertNotNull(result);
        assertEquals("购买须知", result.getTitle());
        assertNotNull(result.getRuleList());
        assertTrue(result.getRuleList().isEmpty());
    }
}
