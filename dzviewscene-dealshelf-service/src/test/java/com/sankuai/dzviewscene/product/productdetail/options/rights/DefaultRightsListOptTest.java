package com.sankuai.dzviewscene.product.productdetail.options.rights;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.rights.vpoints.RightsModuleVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.RightsModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import java.util.Collections;
import java.util.ArrayList;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultRightsListOptTest {

    @Test
    public void testComputeProductMsIsNull() throws Throwable {
        DefaultRightsListOpt defaultRightsListOpt = new DefaultRightsListOpt();
        ActivityCxt ctx = mock(ActivityCxt.class);
        RightsModuleVP.Param param = mock(RightsModuleVP.Param.class);
        DefaultRightsListOpt.Config cfg = mock(DefaultRightsListOpt.Config.class);
        when(param.getProductMs()).thenReturn(null);
        RightsModuleVO result = defaultRightsListOpt.compute(ctx, param, cfg);
        assertNull(result);
    }

    @Test
    public void testComputeCfgIsNull() throws Throwable {
        DefaultRightsListOpt defaultRightsListOpt = new DefaultRightsListOpt();
        ActivityCxt ctx = mock(ActivityCxt.class);
        RightsModuleVP.Param param = mock(RightsModuleVP.Param.class);
        RightsModuleVO result = defaultRightsListOpt.compute(ctx, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeNormal() throws Throwable {
        DefaultRightsListOpt defaultRightsListOpt = new DefaultRightsListOpt();
        ActivityCxt ctx = mock(ActivityCxt.class);
        RightsModuleVP.Param param = mock(RightsModuleVP.Param.class);
        DefaultRightsListOpt.Config cfg = mock(DefaultRightsListOpt.Config.class);
        // Corrected: Mocking to return a list of ProductM instead of Object
        // Assuming ProductM can be mocked
        ProductM mockProductM = mock(ProductM.class);
        when(param.getProductMs()).thenReturn(Collections.singletonList(mockProductM));
        // Assuming these are correctly mocked as their specific types were not provided
        when(cfg.getProductRights()).thenReturn(Collections.emptyList());
        when(cfg.getTradeRights()).thenReturn(Collections.emptyList());
        RightsModuleVO result = defaultRightsListOpt.compute(ctx, param, cfg);
        assertNotNull(result);
        assertTrue(result.getProductRights().isEmpty());
        assertTrue(result.getTradeRights().isEmpty());
    }
}
