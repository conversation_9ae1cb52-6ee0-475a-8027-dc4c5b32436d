package com.sankuai.dzviewscene.product.productdetail.options.rights;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.rights.vpoints.RightsModuleVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.RightsModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.RightsVO;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
// Corrected import for ProductM
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class FixedPriceSpuRightsOptTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private RightsModuleVP.Param param;

    @Mock
    private FixedPriceSpuRightsOpt.Config cfg;

    @Test
    public void testComputeWhenParamProductMsIsEmptyOrCfgIsNull() throws Throwable {
        // arrange
        FixedPriceSpuRightsOpt opt = new FixedPriceSpuRightsOpt();
        when(param.getProductMs()).thenReturn(null);
        // act
        RightsModuleVO result = opt.compute(ctx, param, cfg);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenIsPrivateSource() throws Throwable {
        // arrange
        FixedPriceSpuRightsOpt opt = new FixedPriceSpuRightsOpt();
        // Corrected instantiation
        when(param.getProductMs()).thenReturn(Collections.singletonList(new ProductM()));
        when(ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL)).thenReturn(1L);
        when(cfg.getPrivateProductRights()).thenReturn(Collections.singletonList(new RightsVO()));
        when(cfg.getTradeRights()).thenReturn(Collections.singletonList(new RightsVO()));
        // act
        RightsModuleVO result = opt.compute(ctx, param, cfg);
        // assert
        assertNotNull(result);
        assertEquals(result.getProductRights(), cfg.getPrivateProductRights());
    }

    @Test
    public void testComputeWhenIsNotPrivateSource() throws Throwable {
        // arrange
        FixedPriceSpuRightsOpt opt = new FixedPriceSpuRightsOpt();
        // Corrected instantiation
        when(param.getProductMs()).thenReturn(Collections.singletonList(new ProductM()));
        when(ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL)).thenReturn(0L);
        when(ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpPoiIdL)).thenReturn(0L);
        when(cfg.getProductRights()).thenReturn(Collections.singletonList(new RightsVO()));
        when(cfg.getTradeRights()).thenReturn(Collections.singletonList(new RightsVO()));
        // act
        RightsModuleVO result = opt.compute(ctx, param, cfg);
        // assert
        assertNotNull(result);
        assertEquals(result.getProductRights(), cfg.getProductRights());
    }
}
