package com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.impl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher.Config;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher.Request;
import com.sankuai.dzviewscene.product.shelf.common.OldEngineAdaptCfg;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.OldEngineAdaptUtil;
import com.sankuai.dzviewscene.product.shelf.utils.ShelfInterceptUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultDealIdQueryHandlerImplTest {

    @Mock
    private ComponentFinder componentFinder;

    @InjectMocks
    private DefaultDealIdQueryHandlerImpl defaultDealIdQueryHandlerImpl;

    private MockedStatic<ShelfInterceptUtils> shelfInterceptUtilsMockedStatic;

    private MockedStatic<OldEngineAdaptUtil> oldEngineAdaptUtilMockedStatic;

    private MockedStatic<ActivityCtxtUtils> activityCtxtUtilsMockedStatic;

    @Mock
    private ShelfQueryFetcher shelfQueryFetcher;

    @Before
    public void setUp() {
        shelfInterceptUtilsMockedStatic = mockStatic(ShelfInterceptUtils.class);
        oldEngineAdaptUtilMockedStatic = mockStatic(OldEngineAdaptUtil.class);
        activityCtxtUtilsMockedStatic = mockStatic(ActivityCtxtUtils.class);
    }

    @After
    public void tearDown() {
        shelfInterceptUtilsMockedStatic.close();
        oldEngineAdaptUtilMockedStatic.close();
        activityCtxtUtilsMockedStatic.close();
    }

    /**
     * 测试场景：当ShelfInterceptUtils.checkIntercept(ctx)返回true时，应直接返回空的Map
     */
    @Test
    public void testQuery_WhenCheckInterceptReturnsTrue() throws Throwable {
        // arrange
        ActivityCxt ctx = mock(ActivityCxt.class);
        Request request = new Request();
        Config config = new Config();
        shelfInterceptUtilsMockedStatic.when(() -> ShelfInterceptUtils.checkIntercept(ctx))
                .thenReturn(true);

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = defaultDealIdQueryHandlerImpl.query(ctx, request, config);

        // assert
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }

    /**
     * 测试场景：当config.getOldEngineCfg()为null时，应返回null
     */
    @Test
    public void testQuery_WhenOldEngineCfgIsNull() throws Throwable {
        // arrange
        ActivityCxt ctx = mock(ActivityCxt.class);
        Request request = new Request();
        Config config = new Config();
        shelfInterceptUtilsMockedStatic.when(() -> ShelfInterceptUtils.checkIntercept(ctx))
                .thenReturn(false);

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = defaultDealIdQueryHandlerImpl.query(ctx, request, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试场景：当config.getOldEngineCfg()不为null时，应调用componentFinder.findAbility并返回结果
     */
    @Test
    public void testQuery_WhenOldEngineCfgIsNotNull() throws Throwable {
        // arrange
        ActivityCxt ctx = mock(ActivityCxt.class);
        Request request = new Request();
        Config config = new Config();
        config.setOldEngineCfg(new OldEngineAdaptCfg());
        shelfInterceptUtilsMockedStatic.when(() -> ShelfInterceptUtils.checkIntercept(ctx))
                .thenReturn(false);

        oldEngineAdaptUtilMockedStatic.when(() -> OldEngineAdaptUtil.paddingAbilityToCtx(any(), anyString(), any()))
                        .thenAnswer(invocation -> null);

        activityCtxtUtilsMockedStatic.when(() -> ActivityCtxtUtils.toActivityContext(any()))
                        .thenReturn(mock(ActivityContext.class));

        when(componentFinder.findAbility(any(), anyString())).thenReturn(shelfQueryFetcher);

        Map<String, ProductGroupM> productGroupMMap = Maps.newHashMap();
        productGroupMMap.put("团购", new ProductGroupM());
        CompletableFuture<Map<String, ProductGroupM>> expectedResult = CompletableFuture.completedFuture(productGroupMMap);

        when(shelfQueryFetcher.build(any())).thenReturn(expectedResult);

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = defaultDealIdQueryHandlerImpl.query(ctx, request, config);

        // assert
        assertNotNull(result);
    }
}
