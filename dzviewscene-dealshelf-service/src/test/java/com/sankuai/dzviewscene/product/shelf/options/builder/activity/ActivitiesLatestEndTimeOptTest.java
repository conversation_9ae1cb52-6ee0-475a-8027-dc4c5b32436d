package com.sankuai.dzviewscene.product.shelf.options.builder.activity;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.activity.vp.ActivityEndTimeVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ActivitiesLatestEndTimeOptTest {

    @Test
    public void testComputeShelfGroupMIsNull() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = mock(ActivitiesLatestEndTimeOpt.Config.class);
        when(param.getShelfGroupM()).thenReturn(null);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testComputeProductGroupMsIsEmpty() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = mock(ActivitiesLatestEndTimeOpt.Config.class);
        ShelfGroupM shelfGroupM = mock(ShelfGroupM.class);
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        when(shelfGroupM.getProductGroupMs()).thenReturn(Collections.emptyMap());
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: When ShelfGroupM is null
     * Expected: Should return 0L
     */
    @Test
    public void testComputeWhenShelfGroupMIsNull() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        when(param.getShelfGroupM()).thenReturn(null);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: When ProductGroupMs map is empty
     * Expected: Should return 0L
     */
    @Test
    public void testComputeWhenProductGroupMsIsEmpty() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(new HashMap<>());
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: When ProductGroupM exists but products list is null
     * Expected: Should return 0L
     */
    @Test
    public void testComputeWhenProductsListIsNull() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(null);
        productGroupMs.put("test", productGroupM);
        shelfGroupM.setProductGroupMs(productGroupMs);
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: When ProductGroupM exists with empty products list
     * Expected: Should return 0L
     */
    @Test
    public void testComputeWhenProductsListIsEmpty() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(new ArrayList<>());
        productGroupMs.put("test", productGroupM);
        shelfGroupM.setProductGroupMs(productGroupMs);
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: When ProductGroupM exists with products and enablePeriodReduction is true
     * Expected: Should return result from buildLatestEndTime with enablePeriodReduction=true
     */
    @Test
    public void testComputeWithValidProductsAndPeriodReductionEnabled() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        config.setEnablePeriodReduction(true);
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        List<ProductM> products = new ArrayList<>();
        ProductM product = new ProductM();
        List<ProductPromoPriceM> promoPrices = new ArrayList<>();
        product.setPromoPrices(promoPrices);
        products.add(product);
        productGroupM.setProducts(products);
        productGroupMs.put("test", productGroupM);
        shelfGroupM.setProductGroupMs(productGroupMs);
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: When ProductGroupM exists with products and enablePeriodReduction is false
     * Expected: Should return result from buildLatestEndTime with enablePeriodReduction=false
     */
    @Test
    public void testComputeWithValidProductsAndPeriodReductionDisabled() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        config.setEnablePeriodReduction(false);
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        List<ProductM> products = new ArrayList<>();
        ProductM product = new ProductM();
        List<ProductPromoPriceM> promoPrices = new ArrayList<>();
        product.setPromoPrices(promoPrices);
        products.add(product);
        productGroupM.setProducts(products);
        productGroupMs.put("test", productGroupM);
        shelfGroupM.setProductGroupMs(productGroupMs);
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: productGroupM has products and buildLatestEndTime returns valid result
     * Expected: Should return the result from buildLatestEndTime
     */
    @Test
    public void testComputeWhenProductGroupHasProducts() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        config.setEnablePeriodReduction(false);
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        List<ProductM> products = new ArrayList<>();
        products.add(new ProductM());
        productGroupM.setProducts(products);
        productGroupMs.put("test", productGroupM);
        shelfGroupM.setProductGroupMs(productGroupMs);
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        // 0 is expected as per buildLatestEndTime implementation
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * Test case: productGroupM exists but has no products
     * Expected: Should return 0L
     */
    @Test
    public void testComputeWhenProductGroupHasNoProducts() {
        // arrange
        ActivitiesLatestEndTimeOpt opt = new ActivitiesLatestEndTimeOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ActivityEndTimeVP.Param param = mock(ActivityEndTimeVP.Param.class);
        ActivitiesLatestEndTimeOpt.Config config = new ActivitiesLatestEndTimeOpt.Config();
        config.setEnablePeriodReduction(false);
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(new ArrayList<>());
        productGroupMs.put("test", productGroupM);
        shelfGroupM.setProductGroupMs(productGroupMs);
        when(param.getShelfGroupM()).thenReturn(shelfGroupM);
        // act
        Long result = opt.compute(context, param, config);
        // assert
        assertEquals(Long.valueOf(0), result);
    }
}
