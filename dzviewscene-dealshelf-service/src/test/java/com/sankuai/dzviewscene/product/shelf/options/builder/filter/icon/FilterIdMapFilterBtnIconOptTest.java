package com.sankuai.dzviewscene.product.shelf.options.builder.filter.icon;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterBtnIconVP.Param;
import com.sankuai.dzviewscene.product.shelf.options.builder.filter.icon.FilterIdMapFilterIconOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FilterIdMapFilterBtnIconOptTest {

    private FilterIdMapFilterIconOpt filterIdMapFilterIconOpt;
    private ActivityCxt activityCxt;
    private Param param;
    private Config config;
    private DzPictureComponentVO componentVO;
    private FilterBtnM filterBtnM;

    @Before
    public void setUp() {
        filterIdMapFilterIconOpt = new FilterIdMapFilterIconOpt();
        activityCxt = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(Param.class);
        config = Mockito.mock(Config.class);
        componentVO = Mockito.mock(DzPictureComponentVO.class);
        filterBtnM = Mockito.mock(FilterBtnM.class);
    }



    @Test
    public void testComputeConfigIsNull() {
        // arrange

        // act
        DzPictureComponentVO result = filterIdMapFilterIconOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
    }


    /**
     * 测试compute方法，当filterId2IconCfg不为空且包含对应filterId时
     */
    @Test
    public void testComputeWithValidFilterId2IconCfg() {
        // arrange
        Map<Long, DzPictureComponentVO> filterId2IconCfg = new HashMap<>();
        filterId2IconCfg.put(1L, componentVO);
        when(config.getFilterId2IconCfg()).thenReturn(filterId2IconCfg);
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(1L);

        // act
        DzPictureComponentVO result = filterIdMapFilterIconOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(componentVO, result);
    }

    /**
     * 测试compute方法，当douhu2FilterId2IconCfg不为空且命中斗斛时
     */
    @Test
    public void testComputeWithValidDouhu2FilterId2IconCfg() {
        try (MockedStatic<DouHuUtils> mockedStatic = mockStatic(DouHuUtils.class)) {
            // Mock静态方法
            mockedStatic.when(() -> DouHuUtils.hitAnySk(any(), anyList())).thenReturn(true);
            // arrange
            Map<String, Map<Long, DzPictureComponentVO>> douhu2FilterId2IconCfg = new HashMap<>();
            Map<Long, DzPictureComponentVO> filterId2IconCfg = new HashMap<>();
            filterId2IconCfg.put(1L, componentVO);
            douhu2FilterId2IconCfg.put("exp_a", filterId2IconCfg);
            when(componentVO.getPicUrl()).thenReturn("iconUrl");
            when(config.getDouhu2FilterId2IconCfg()).thenReturn(douhu2FilterId2IconCfg);
            when(param.getFilterBtnM()).thenReturn(filterBtnM);
            when(filterBtnM.getFilterId()).thenReturn(1L);
            when(activityCxt.getSource(any())).thenReturn(Collections.emptyList());

            // act
            DzPictureComponentVO result = filterIdMapFilterIconOpt.compute(activityCxt, param, config);

            // assert
            assertEquals(componentVO, result);
        }
    }

    @Test
    public void testComputeWithValidDouhu2FilterId2IconCfg2() {
        try (MockedStatic<DouHuUtils> mockedStatic = mockStatic(DouHuUtils.class)) {
            // Mock静态方法
            mockedStatic.when(() -> DouHuUtils.hitAnySk(any(), anyList())).thenReturn(true);
            // arrange
            Map<String, Map<Long, DzPictureComponentVO>> douhu2FilterId2IconCfg = new HashMap<>();
            Map<Long, DzPictureComponentVO> filterId2IconCfg = new HashMap<>();
            filterId2IconCfg.put(1L, componentVO);
            douhu2FilterId2IconCfg.put("exp_a", filterId2IconCfg);
            when(config.getDouhu2FilterId2IconCfg()).thenReturn(douhu2FilterId2IconCfg);
            when(param.getFilterBtnM()).thenReturn(filterBtnM);
            when(filterBtnM.getFilterId()).thenReturn(2L);
            // act
            DzPictureComponentVO result = filterIdMapFilterIconOpt.compute(activityCxt, param, config);

            // assert
            assertNull(result);
        }
    }

    /**
     * 测试compute方法，当所有配置为空时
     */
    @Test
    public void testComputeWithAllConfigNull() {
        // arrange
        when(config.getFilterId2IconCfg()).thenReturn(null);
        when(config.getDouhu2FilterId2IconCfg()).thenReturn(null);

        // act
        DzPictureComponentVO result = filterIdMapFilterIconOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
    }
}
