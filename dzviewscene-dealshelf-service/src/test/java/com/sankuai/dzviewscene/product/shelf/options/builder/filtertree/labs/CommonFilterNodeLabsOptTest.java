package com.sankuai.dzviewscene.product.shelf.options.builder.filtertree.labs;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.builder.filtertree.labs.CommonFilterNodeLabsOpt.Config;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CommonFilterNodeLabsOptTest {

    @Mock
    private ActivityCxt context;

    // Assuming Param is a part of CommonFilterNodeLabsOpt class, but not directly accessible.
    // We'll use the full class name for now.
    @Mock
    private com.sankuai.dzviewscene.product.shelf.options.builder.filtertree.labs.CommonFilterNodeLabsOpt.Param param;

    @Mock
    private Config config;

    /**
     * Tests the compute method under normal conditions.
     */
    @Test
    public void testComputeNormal() throws Throwable {
        // Arrange
        CommonFilterNodeLabsOpt commonFilterNodeLabsOpt = new CommonFilterNodeLabsOpt();
        when(param.getTitle()).thenReturn("test");
        // Act
        String result = commonFilterNodeLabsOpt.compute(context, param, config);
        // Assert
        assertNotNull(result);
    }

    /**
     * Tests the compute method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testComputeException() throws Throwable {
        // Arrange
        CommonFilterNodeLabsOpt commonFilterNodeLabsOpt = new CommonFilterNodeLabsOpt();
        when(param.getTitle()).thenThrow(new Exception());
        // Act
        commonFilterNodeLabsOpt.compute(context, param, config);
    }
}
