package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.Arrays;
import org.junit.*;
import org.mockito.Mockito;

public class ReservationStockStrategy_BuildTest {

    private CarouselBuilderContext context;

    private ReservationStockStrategy strategy;

    @Before
    public void setUp() {
        strategy = new ReservationStockStrategy();
        context = Mockito.mock(CarouselBuilderContext.class);
    }

    @Test
    public void testBuildProductIsNull() throws Throwable {
        when(context.getProductM()).thenReturn(null);
        assertNull(strategy.build(context));
    }

    @Test
    public void testBuildExtAttrsIsEmpty() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(context.getProductM()).thenReturn(productM);
        when(productM.getAttr("reservation_stock")).thenReturn(null);
        assertNull(strategy.build(context));
    }

    @Test
    public void testBuildReservationStockIsNull() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(context.getProductM()).thenReturn(productM);
        when(productM.getAttr("reservation_stock")).thenReturn(null);
        assertNull(strategy.build(context));
    }

    @Test
    public void testBuildReservationStockIsNotNull() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(context.getProductM()).thenReturn(productM);
        // Ensure extAttrs is not empty
        when(productM.getExtAttrs()).thenReturn(Arrays.asList(new AttrM("reservation_stock", "100")));
        when(productM.getAttr("reservation_stock")).thenReturn("100");
        RichLabelVO result = strategy.build(context);
        assertNotNull(result);
        assertEquals(10, result.getTextSize());
        assertEquals("#FF4B10", result.getTextColor());
        assertEquals("100", result.getText());
    }
}
