package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemareatitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemAreaTitleVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemareatitle.HotSaleConfigItemAreaTitleOpt.Config;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemareatitle.HotSaleConfigItemAreaTitleOpt.TitleCfg;
import com.sankuai.dzviewscene.productshelf.vu.vo.TitleComponentVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class HotSaleConfigItemAreaTitleOptTest {

    private HotSaleConfigItemAreaTitleOpt hotSaleConfigItemAreaTitleOpt;

    private ActivityCxt activityCxt;

    private ItemAreaTitleVP.Param param;

    private HotSaleConfigItemAreaTitleOpt.Config config;

    @Test
    public void testComputeTitleCfgNotNull() throws Throwable {
        HotSaleConfigItemAreaTitleOpt hotSaleConfigItemAreaTitleOpt = new HotSaleConfigItemAreaTitleOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ItemAreaTitleVP.Param param = mock(ItemAreaTitleVP.Param.class);
        Config config = mock(Config.class);
        Map<String, TitleCfg> groupName2TitleCfgMap = new HashMap<>();
        TitleCfg titleCfg = mock(TitleCfg.class);
        groupName2TitleCfgMap.put("testGroupName", titleCfg);
        when(config.getGroupName2TitleCfgMap()).thenReturn(groupName2TitleCfgMap);
        when(param.getGroupName()).thenReturn("testGroupName");
        TitleComponentVO result = hotSaleConfigItemAreaTitleOpt.compute(activityCxt, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeTitleCfgNull() throws Throwable {
        HotSaleConfigItemAreaTitleOpt hotSaleConfigItemAreaTitleOpt = new HotSaleConfigItemAreaTitleOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ItemAreaTitleVP.Param param = mock(ItemAreaTitleVP.Param.class);
        Config config = mock(Config.class);
        Map<String, TitleCfg> groupName2TitleCfgMap = new HashMap<>();
        when(config.getGroupName2TitleCfgMap()).thenReturn(groupName2TitleCfgMap);
        when(param.getGroupName()).thenReturn("testGroupName");
        TitleComponentVO result = hotSaleConfigItemAreaTitleOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeFilterNameNotNull() throws Throwable {
        HotSaleConfigItemAreaTitleOpt hotSaleConfigItemAreaTitleOpt = new HotSaleConfigItemAreaTitleOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ItemAreaTitleVP.Param param = mock(ItemAreaTitleVP.Param.class);
        Config config = mock(Config.class);
        FilterM filterM = mock(FilterM.class);
        FilterBtnM filterBtnM = mock(FilterBtnM.class);
        Map<String, Map<String, TitleCfg>> filterName2GroupTitleCfgMap = new HashMap<>();
        Map<String, TitleCfg> groupName2TitleCfgMap = new HashMap<>();
        TitleCfg titleCfg = mock(TitleCfg.class);
        groupName2TitleCfgMap.put("testGroupName", titleCfg);
        filterName2GroupTitleCfgMap.put("testFilterName", groupName2TitleCfgMap);
        when(config.getFilterName2GroupTitleCfgMap()).thenReturn(filterName2GroupTitleCfgMap);
        when(param.getFilterId()).thenReturn(1L);
        when(param.getGroupName()).thenReturn("testGroupName");
        when(param.getFilterM()).thenReturn(filterM);
        List<FilterBtnM> filters = new ArrayList<>();
        filters.add(filterBtnM);
        when(filterM.getFilters()).thenReturn(filters);
        when(filterBtnM.getFilterId()).thenReturn(1L);
        when(filterBtnM.getTitle()).thenReturn("testFilterName");
        TitleComponentVO result = hotSaleConfigItemAreaTitleOpt.compute(activityCxt, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeFilterNameNull() throws Throwable {
        HotSaleConfigItemAreaTitleOpt hotSaleConfigItemAreaTitleOpt = new HotSaleConfigItemAreaTitleOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ItemAreaTitleVP.Param param = mock(ItemAreaTitleVP.Param.class);
        Config config = mock(Config.class);
        Map<String, Map<String, TitleCfg>> filterName2GroupTitleCfgMap = new HashMap<>();
        when(config.getFilterName2GroupTitleCfgMap()).thenReturn(filterName2GroupTitleCfgMap);
        when(param.getFilterId()).thenReturn(1L);
        when(param.getGroupName()).thenReturn("testGroupName");
        TitleComponentVO result = hotSaleConfigItemAreaTitleOpt.compute(activityCxt, param, config);
        assertNull(result);
    }
}
