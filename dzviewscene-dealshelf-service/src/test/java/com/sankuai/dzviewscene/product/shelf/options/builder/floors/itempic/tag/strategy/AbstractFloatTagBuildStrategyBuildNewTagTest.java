package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.dealshelf.shelfvo.FloatTagModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategy;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class AbstractFloatTagBuildStrategyBuildNewTagTest {

    @InjectMocks
    private AbstractFloatTagBuildStrategy mockStrategy;

    @Mock
    private FloatTagBuildReq param;

    @Mock
    private FloatTagBuildCfg config;

    @Before
    public void setUp() {
        mockStrategy = Mockito.mock(AbstractFloatTagBuildStrategy.class, Mockito.CALLS_REAL_METHODS);
    }

    @Test
    public void testBuildNewTagWhenBuildTagReturnsNull() throws Throwable {
        when(mockStrategy.buildTag(param, config)).thenReturn(null);
        FloatTagModel result = mockStrategy.buildNewTag(param, config);
        assertNull(result);
    }

    @Test
    public void testBuildNewTagWhenIconAndLabelAreNull() throws Throwable {
        FloatTagVO floatTagVO = new FloatTagVO();
        when(mockStrategy.buildTag(param, config)).thenReturn(floatTagVO);
        FloatTagModel result = mockStrategy.buildNewTag(param, config);
        assertNotNull(result);
        assertNotNull(result.getTag());
        assertNull(result.getTag().getIcon());
        assertNull(result.getTag().getText());
    }

    @Test
    public void testBuildNewTagWhenIconIsNotNullAndLabelIsNull() throws Throwable {
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(new DzPictureComponentVO());
        when(mockStrategy.buildTag(param, config)).thenReturn(floatTagVO);
        FloatTagModel result = mockStrategy.buildNewTag(param, config);
        assertNotNull(result);
        assertNotNull(result.getTag());
        assertNotNull(result.getTag().getIcon());
        assertNull(result.getTag().getText());
    }

    @Test
    public void testBuildNewTagWhenIconIsNullAndLabelIsNotNull() throws Throwable {
        FloatTagVO floatTagVO = new FloatTagVO();
        RichLabelVO labelVO = new RichLabelVO();
        // Ensure the label has a non-null text
        labelVO.setText("Test Label");
        floatTagVO.setLabel(labelVO);
        when(mockStrategy.buildTag(param, config)).thenReturn(floatTagVO);
        FloatTagModel result = mockStrategy.buildNewTag(param, config);
        assertNotNull(result);
        assertNotNull(result.getTag());
        assertNull(result.getTag().getIcon());
        assertNotNull(result.getTag().getText());
    }
}
