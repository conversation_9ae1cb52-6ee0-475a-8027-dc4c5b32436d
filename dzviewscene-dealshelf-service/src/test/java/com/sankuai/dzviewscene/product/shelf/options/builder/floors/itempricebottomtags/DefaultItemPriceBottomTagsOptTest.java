package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP.Param;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultItemPriceBottomTagsOptTest {

    @Mock
    private ActivityCxt context;

    @Test
    public void testComputeWhenCouponsIsNull() throws Throwable {
        DefaultItemPriceBottomTagsOpt defaultItemPriceBottomTagsOpt = new DefaultItemPriceBottomTagsOpt();
        // Use the builder pattern to create an instance of Param
        Param param = Param.builder().productM(new ProductM()).build();
        List<DzTagVO> result = defaultItemPriceBottomTagsOpt.compute(context, param, null);
        assertNull(result);
    }
}
