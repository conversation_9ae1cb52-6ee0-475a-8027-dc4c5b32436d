package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;
import java.math.BigDecimal;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;



import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2024/3/7 14:44
 */
@RunWith(MockitoJUnitRunner.class)
public class MassageItemPriceBottomTagsV2OptTest {
    @Mock
    private ActivityCxt context;

    @Test
    public void testIsNeedPriceGuaranteeTagWhenNeedPriceGuaranteeTagBySkListIsNull() {
        // arrange
        MassageItemPriceBottomTagsV2Opt opt = new MassageItemPriceBottomTagsV2Opt();
        MassageItemPriceBottomTagsV2Opt.Config config = new MassageItemPriceBottomTagsV2Opt.Config();

        // act
        boolean result = opt.isNeedPriceGuaranteeTag(context, config);

        // assert
        assertTrue(result);
    }

    @Test
    public void testIsNeedPriceGuaranteeTagWhenDouHuMListIsNull() {
        // arrange
        MassageItemPriceBottomTagsV2Opt opt = new MassageItemPriceBottomTagsV2Opt();
        MassageItemPriceBottomTagsV2Opt.Config config = new MassageItemPriceBottomTagsV2Opt.Config();
        config.setNeedPriceGuaranteeTagBySkList(Arrays.asList("sk1", "sk2"));

        // act
        boolean result = opt.isNeedPriceGuaranteeTag(context, config);

        // assert
        assertTrue(result);
    }

    @InjectMocks
    private MassageItemPriceBottomTagsV2Opt massageItemPriceBottomTagsV2Opt;

    @Mock
    private ProductMPromoInfoUtils productMPromoInfoUtils;

    @Mock
    private MerchantMemberPromoUtils merchantMemberPromoUtils;

    @Mock
    private RainbowSecKillUtils rainbowSecKillUtils;

    private ItemPriceBottomTagsVP.Param param;
    private MassageItemPriceBottomTagsV2Opt.Config config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 初始化Param和Config对象
        context = Mockito.mock(ActivityCxt.class);
        // 初始化ProductM对象
        ProductM productM = new ProductM();
        // 设置基础信息
        productM.setProductId(123456);
        productM.setTitle("测试商品");
        productM.setCategoryName("美食");
        productM.setProductDesc("这是一个测试商品的描述");
        productM.setPicUrl("http://example.com/pic.jpg");
        productM.setJumpUrl("http://example.com");
        productM.setOrderUrl("http://example.com/order");
        productM.setJumpText("点击跳转");
        productM.setAvailable(true);

        // 设置价格信息
        productM.setBasePrice(new BigDecimal("99.99"));
        productM.setMarketPrice("199.99");

        // 设置销量信息
        ProductSaleM sale = new ProductSaleM();
        sale.setSale(100);
        productM.setSale(sale);

        // 设置库存信息
        ProductStockM stock = new ProductStockM();
        stock.setRemainStock(50);
        productM.setStock(stock);

        // 设置商品Item列表
        List<ProductItemM> productItemMList = new ArrayList<>();
        ProductItemM item1 = new ProductItemM();
        item1.setPrice("1");
        productItemMList.add(item1);
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setIcon("");
        promoPriceM.setPromoQuantityLimit(0);
        promoPriceM.setStartTime(0L);
        promoPriceM.setEndTime(0L);
        promoPriceM.setPromoTagPrefix("");
        promoPriceM.setCoupons(Lists.newArrayList());
        promoPriceM.setUserHasCard(false);
        promoPriceM.setDiscountTag("");
        promoPriceM.setDiscount(new BigDecimal("0"));
        promoPriceM.setAvailableTime("");
        promoPriceM.setPromoType(0);
        promoPriceM.setPromoPrice(new BigDecimal("0"));
        promoPriceM.setPromoTag("easy");
        promoPriceM.setPromoPriceTag("easy");
        promoPriceM.setTotalPromoPrice(new BigDecimal("0"));
        promoPriceM.setTotalPromoPriceTag("easy");
        promoPriceM.setPromoItemList(Lists.newArrayList());
        promoPriceM.setMarketPrice("100");
        promoPriceM.setPromoTagType(0);
        promoPriceM.setSinglePrice(new BigDecimal("0"));
        promoPriceM.setPricePromoInfoMap(Maps.newHashMap());
        promoPriceM.setExtendDisplayInfo(Maps.newHashMap());
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoTypeCode(16);
        PromoItemM promoItemM1 = new PromoItemM();
        promoItemM1.setPromoTypeCode(0);
        promoPriceM.setPromoItemList(Arrays.asList(promoItemM,promoItemM1));

        productM.setPromoPrices(Arrays.asList(promoPriceM));
        productM.setProductItemMList(productItemMList);
        param = ItemPriceBottomTagsVP.Param.builder()
                .productM(productM) // 假设ProductM是另一个数据类，需要根据实际情况进行实例化和设置
                .cardM(new CardM()) // 同上
                .platform(1) // 根据实际测试需要设置
                .salePrice("100") // 假设的到手价
                .douHuList(new ArrayList<>()) // 假设DouHuM是另一个数据类，根据需要添加到列表中
                .build();
        config = new MassageItemPriceBottomTagsV2Opt.Config();
        // 假设这里对param和config进行了必要的设置
    }

    @Test
    public void testBuildPriceBottomTag() {
        // 调用测试方法
        List<DzTagVO> result = massageItemPriceBottomTagsV2Opt.compute(context,param, config);

        // 验证结果
        assertEquals(result.get(0).getName(), "easy");
    }
}