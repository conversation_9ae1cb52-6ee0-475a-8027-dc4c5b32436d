package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OtherPromoTagBuildStrategyTest {

    @InjectMocks
    private OtherPromoTagBuildStrategy strategy;

    @Mock
    private ProductM productM;

    @Mock
    private ActivityCxt activityCxt;

    /**
     * Test buildTag when promo is null
     * Covers: if (!isValidOtherPromo(productPromoPriceM)) { return null; }
     */
    @Test
    public void testBuildTag_WhenPromoIsNull() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(productM);
        req.setCardM(new CardM());
        try (MockedStatic<PriceUtils> mockedPriceUtils = mockStatic(PriceUtils.class)) {
            mockedPriceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(null);
            // act
            DzTagVO result = strategy.buildTag(req);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test buildTag when promoTagType is 0
     * Covers: if (!isValidOtherPromo(productPromoPriceM)) { return null; }
     */
    @Test
    public void testBuildTag_WhenPromoTagTypeIsZero() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(productM);
        req.setCardM(new CardM());
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoTagType(0);
        try (MockedStatic<PriceUtils> mockedPriceUtils = mockStatic(PriceUtils.class)) {
            mockedPriceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(promoPriceM);
            // act
            DzTagVO result = strategy.buildTag(req);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test buildTag when promoTagType is Member type
     * Covers: if (!isValidOtherPromo(productPromoPriceM)) { return null; }
     */
    @Test
    public void testBuildTag_WhenPromoTagTypeIsMember() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(productM);
        req.setCardM(new CardM());
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoTagType(PromoTagTypeEnum.Member.getCode());
        try (MockedStatic<PriceUtils> mockedPriceUtils = mockStatic(PriceUtils.class)) {
            mockedPriceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(promoPriceM);
            // act
            DzTagVO result = strategy.buildTag(req);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test buildTag when shop is in blacklist
     * Covers: if (isHitTagBlackShop(req, productPromoPriceM)) { return null; }
     */
    @Test
    public void testBuildTag_WhenShopInBlacklist() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(productM);
        req.setCardM(new CardM());
        // Set up blacklist config
        PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
        Map<Integer, java.util.List<Long>> blackShopIds = new HashMap<>();
        blackShopIds.put(50, Collections.singletonList(123L));
        cfg.setTagBlackDpShopIds(blackShopIds);
        req.setCfg(cfg);
        // Set up context with shop id
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.dpPoiIdL, 123L);
        when(activityCxt.getParameters()).thenReturn(params);
        req.setContext(activityCxt);
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoTagType(50);
        promoPriceM.setPromoTag("Test Promo");
        try (MockedStatic<PriceUtils> mockedPriceUtils = mockStatic(PriceUtils.class)) {
            mockedPriceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(promoPriceM);
            // act
            DzTagVO result = strategy.buildTag(req);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test buildTag when shop is not in blacklist
     * Negative test for: if (isHitTagBlackShop(req, productPromoPriceM)) { return null; }
     */
    @Test
    public void testBuildTag_WhenShopNotInBlacklist() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(productM);
        req.setCardM(new CardM());
        req.setPlatform(1);
        // Set up blacklist config with different shop id
        PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
        Map<Integer, java.util.List<Long>> blackShopIds = new HashMap<>();
        blackShopIds.put(50, Collections.singletonList(456L));
        cfg.setTagBlackDpShopIds(blackShopIds);
        req.setCfg(cfg);
        // Set up context with shop id
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.dpPoiIdL, 123L);
        when(activityCxt.getParameters()).thenReturn(params);
        req.setContext(activityCxt);
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoTagType(50);
        promoPriceM.setPromoTag("Test Promo");
        promoPriceM.setIcon("test-icon");
        try (MockedStatic<PriceUtils> mockedPriceUtils = mockStatic(PriceUtils.class)) {
            mockedPriceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(promoPriceM);
            // act
            DzTagVO result = strategy.buildTag(req);
            // assert
            assertNotNull(result);
            assertEquals("Test Promo", result.getName());
            assertNotNull(result.getPrePic());
            assertEquals("test-icon", result.getPrePic().getPicUrl());
        }
    }

    /**
     * Test buildTag when cfg is null
     * Coverage for isHitTagBlackShop branch
     */
    @Test
    public void testBuildTag_WhenCfgIsNull() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(productM);
        req.setCardM(new CardM());
        req.setPlatform(1);
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.dpPoiIdL, 123L);
        when(activityCxt.getParameters()).thenReturn(params);
        req.setContext(activityCxt);
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoTagType(50);
        promoPriceM.setPromoTag("Test Promo");
        promoPriceM.setIcon("test-icon");
        try (MockedStatic<PriceUtils> mockedPriceUtils = mockStatic(PriceUtils.class)) {
            mockedPriceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(promoPriceM);
            // act
            DzTagVO result = strategy.buildTag(req);
            // assert
            assertNotNull(result);
            assertEquals("Test Promo", result.getName());
        }
    }

    /**
     * Test buildTag when promo is invalid
     * Covers: if (!isValidOtherPromo(productPromoPriceM)) { return null; }
     */
    @Test
    public void testBuildTag_WhenPromoIsInvalid() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(productM);
        req.setCardM(new CardM());
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoTagType(PromoTagTypeEnum.Member.getCode());
        try (MockedStatic<PriceUtils> mockedPriceUtils = mockStatic(PriceUtils.class)) {
            mockedPriceUtils.when(() -> PriceUtils.getUserHasPromoPrice(any(), any())).thenReturn(promoPriceM);
            // act
            DzTagVO result = strategy.buildTag(req);
            // assert
            assertNull(result);
        }
    }
}
