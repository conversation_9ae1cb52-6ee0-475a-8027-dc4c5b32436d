package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductRichTagsVP.Param;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试 ShopShelfItemProductRichTagsOpt.compute 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class ShopShelfItemProductRichTagsOptTest {

    @InjectMocks
    private ShopShelfItemProductRichTagsOpt shopShelfItemProductRichTagsOpt;
    @Mock
    private ActivityCxt mockContext;
    @Mock
    private Param mockParam;
    @Mock
    private ProductM mockProductM;

    /**
     * 测试国家补贴标签存在
     */
    @Test
    public void testComputeWithNationalSubsidy() {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("platform", 2);
        when(mockContext.getParameters()).thenReturn(parameters);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setNationalSubsidyPrice(BigDecimal.valueOf(100));
        when(mockProductM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));

        // act
        List<RichLabelVO> result = shopShelfItemProductRichTagsOpt.compute(mockContext, mockParam, null);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("国家补贴", result.get(0).getText());
    }

    /**
     * 测试国家补贴标签不存在
     */
    @Test
    public void testComputeWithoutNationalSubsidy() {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("platform", 1);
        when(mockContext.getParameters()).thenReturn(parameters);
        when(mockParam.getProductM()).thenReturn(mockProductM);

        // act
        List<RichLabelVO> result = shopShelfItemProductRichTagsOpt.compute(mockContext, mockParam, null);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试普通标签存在
     */
    @Test
    public void testComputeWithNormalTags() {
        // arrange
        when(mockProductM.getProductTags()).thenReturn(Collections.singletonList("标签1"));
        when(mockParam.getProductM()).thenReturn(mockProductM);

        // act
        List<RichLabelVO> result = shopShelfItemProductRichTagsOpt.compute(mockContext, mockParam, null);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("标签1", result.get(0).getText());
    }

    /**
     * 测试普通标签为空
     */
    @Test
    public void testComputeWithEmptyNormalTags() {
        // arrange
        when(mockProductM.getProductTags()).thenReturn(Collections.emptyList());
        when(mockParam.getProductM()).thenReturn(mockProductM);

        // act
        List<RichLabelVO> result = shopShelfItemProductRichTagsOpt.compute(mockContext, mockParam, null);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试国家补贴和普通标签同时存在
     */
    @Test
    public void testComputeWithNationalSubsidyAndNormalTags() {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("platform", 2);
        when(mockContext.getParameters()).thenReturn(parameters);
        when(mockProductM.getProductTags()).thenReturn(Collections.singletonList("标签1"));
        when(mockParam.getProductM()).thenReturn(mockProductM);
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setNationalSubsidyPrice(BigDecimal.valueOf(100));
        when(mockProductM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));

        // act
        List<RichLabelVO> result = shopShelfItemProductRichTagsOpt.compute(mockContext, mockParam, null);

        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("国家补贴", result.get(0).getText());
        assertEquals("标签1", result.get(1).getText());
    }

    /**
     * 测试参数为 null
     */
    @Test(expected = NullPointerException.class)
    public void testComputeWithNullParam() {
        // act
        shopShelfItemProductRichTagsOpt.compute(mockContext, null, null);
    }

    /**
     * 测试上下文为 null
     */
    @Test(expected = NullPointerException.class)
    public void testComputeWithNullContext() {
        // act
        shopShelfItemProductRichTagsOpt.compute(null, mockParam, null);
    }
}