package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.mpmctmember.process.common.enums.MemberDiscountTypeEnum;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GameCoinItemPromoTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ItemPromoTagsVP.Param param;

    private ProductM buildProductM() {
        String productMStr = "{\n" + "\t\"actProductId\": *********,\n" + "\t\"actProductType\": 1,\n" + "\t\"activities\": [{\n" + "\t\t\"activityBeginTime\": 0,\n" + "\t\t\"activityEndTime\": 0,\n" + "\t\t\"activityPicUrlMap\": {\n" + "\t\t\t\"subIcon\": {\n" + "\t\t\t\t\"picUrlKey\": \"subIcon\",\n" + "\t\t\t\t\"url\": \"\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t},\n" + "\t\t\t\"bigIcon\": {\n" + "\t\t\t\t\"picUrlKey\": \"bigIcon\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t},\n" + "\t\t\t\"doubleRowShelfIcon\": {\n" + "\t\t\t\t\"picUrlKey\": \"doubleRowShelfIcon\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t},\n" + "\t\t\t\"icon\": {\n" + "\t\t\t\t\"picUrlKey\": \"icon\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t},\n" + "\t\t\t\"bigSurroundIcon\": {\n" + "\t\t\t\t\"picUrlKey\": \"bigSurroundIcon\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t},\n" + "\t\t\t\"shelfCardIcon\": {\n" + "\t\t\t\t\"picUrlKey\": \"shelfCardIcon\",\n" + "\t\t\t\t\"url\": \"https://p0.inf.test.sankuai.com/dprainbow/18196dcf2d333615cc514f22b6d8ff004702.jpg\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t},\n" + "\t\t\t\"singleRowShelfIcon\": {\n" + "\t\t\t\t\"picUrlKey\": \"singleRowShelfIcon\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t},\n" + "\t\t\t\"poiDealNewIconUrl\": {\n" + "\t\t\t\t\"picUrlKey\": \"poiDealNewIconUrl\",\n" + "\t\t\t\t\"urlAspectRadio\": 0.0\n" + "\t\t\t}\n" + "\t\t},\n" + "\t\t\"activityScene\": 0,\n" + "\t\t\"activityType\": 0,\n" + "\t\t\"displayType\": 0,\n" + "\t\t\"pageId\": 10002816,\n" + "\t\t\"preheat\": false,\n" + "\t\t\"priceStrengthTime\": 0,\n" + "\t\t\"remainingTime\": 649096,\n" + "\t\t\"remainingTimeMillisecond\": 0,\n" + "\t\t\"shelfActivityType\": 2,\n" + "\t\t\"skuId\": 0,\n" + "\t\t\"timeDisplayFlag\": false,\n" + "\t\t\"urlAspectRadio\": 0.0\n" + "\t}],\n" + "\t\"basePrice\": 59.90,\n" + "\t\"basePriceTag\": \"59.9\",\n" + "\t\"beginDate\": 0,\n" + "\t\"categoryId\": 322,\n" + "\t\"compositeScore\": 0.0,\n" + "\t\"dealSpuId\": 0,\n" + "\t\"dealThemePadded\": true,\n" + "\t\"endDate\": 0,\n" + "\t\"extAttrs\": [{\n" + "\t\t\"name\": \"attr_recommend\",\n" + "\t\t\"value\": \"false\"\n" + "\t}, {\n" + "\t\t\"name\": \"attr_totalProductCategoryIds\",\n" + "\t\t\"value\": \"[2054]\"\n" + "\t}, {\n" + "\t\t\"name\": \"attr_shopRecommend\",\n" + "\t\t\"value\": \"false\"\n" + "\t}, {\n" + "\t\t\"name\": \"reservation_is_needed_or_not\",\n" + "\t\t\"value\": \"否\"\n" + "\t}, {\n" + "\t\t\"name\": \"preSaleTag\",\n" + "\t\t\"value\": \"false\"\n" + "\t}, {\n" + "\t\t\"name\": \"GameDealDiscountAndPerPrice\",\n" + "\t\t\"value\": \"5.4折 | ¥0.62/币\"\n" + "\t}, {\n" + "\t\t\"name\": \"reservation_is_needed_or_not\",\n" + "\t\t\"value\": \"否\"\n" + "\t}, {\n" + "\t\t\"name\": \"preSaleTag\",\n" + "\t\t\"value\": \"false\"\n" + "\t}],\n" + "\t\"extendImages\": [],\n" + "\t\"jumpUrl\": \"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=307150166&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUGE5ylRMsuAdmZ8PctW2IiQeKrfs896TywBi7Ph4b-Dsl9ciErfpUBxz3lgbJopxV8nUKctNJN0Bnl3cnlxpPatT4oaKmHupd8k88iqcKIbCMi7CSwWTyFrbbBXPDNLYu2jo3HTI6C7AuDV0wlZnmHU783SwFOxU2AI_Zs185OIo9R_RXN_Lws0ZyNNiVQ1akWOL_6zxQ9sWBr48Z6w3yFWB7qzpmoW-X2cKqSJkSyHNNrkL-T976amKypWRPXduB-RPKj9QUPZPYQLgeRrcCHaXtQEit1uN1L8WoHSJ-_pvVkkHwmF-UJyIa5XU4Gjz9MurtAbFkZBoZ4P5kAO0RxActAFSbb3rPvDkflfES4hTw6ota45-rjtN_Z2pgGtnESG_Cwkt44iVGolJjJTvsH_VSCkPva5VfwFidvpLCHMPJ4NCU9PeTM5dX3EYoOUrUSZu6gln-krcIeofmTIKTeUlPK3BpHHt1HZ5uaexMWkqLMMiPSJNZcESlBGd9maQM\",\n" + "\t\"marketPrice\": \"100\",\n" + "\t\"materialList\": [],\n" + "\t\"orderUrl\": \"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=*********&shopid=307150166&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=poiShelf&expid=directPage&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUGE5ylRMsuAdmZ8PctW2IiQeKrfs896TywBi7Ph4b-Dsl9ciErfpUBxz3lgbJopxV8nUKctNJN0Bnl3cnlxpPatT4oaKmHupd8k88iqcKIbCMi7CSwWTyFrbbBXPDNLYu2jo3HTI6C7AuDV0wlZnmHU783SwFOxU2AI_Zs185OIo9R_RXN_Lws0ZyNNiVQ1akWOL_6zxQ9sWBr48Z6w3yFWB7qzpmoW-X2cKqSJkSyHNNrkL-T976amKypWRPXduB-RPKj9QUPZPYQLgeRrcCHaXtQEit1uN1L8WoHSJ-_pvVkkHwmF-UJyIa5XU4Gjz9MurtAbFkZBoZ4P5kAO0RxActAFSbb3rPvDkflfES4hTw6ota45-rjtN_Z2pgGtnESG_Cwkt44iVGolJjJTvsH_VSCkPva5VfwFidvpLCHMPJ4NCU9PeTM5dX3EYoOUrUSZu6gln-krcIeofmTIKTeUlPK3BpHHt1HZ5uaexMWkqLMMiPSJNZcESlBGd9maQM\",\n" + "\t\"orderUsers\": [],\n" + "\t\"picUrl\": \"https://p1.meituan.net/dpmerchantpic/c23b31d81f90129a3014f49323c403ca84559.jpg\",\n" + "\t\"productId\": *********,\n" + "\t\"productTagList\": [],\n" + "\t\"productTags\": [\"免预约\"],\n" + "\t\"productType\": 1,\n" + "\t\"promoPrices\": [{\n" + "\t\t\"discount\": 0.54,\n" + "\t\t\"endTime\": 0,\n" + "\t\t\"extendDisplayInfo\": {},\n" + "\t\t\"icon\": \"https://p1.meituan.net/travelcube/83518c6e14ec54a491bac580cb43d2fe4222.png\",\n" + "\t\t\"marketPrice\": \"100\",\n" + "\t\t\"pricePromoInfoMap\": {},\n" + "\t\t\"promoItemList\": [{\n" + "\t\t\t\"amount\": 40.10,\n" + "\t\t\t\"canAssign\": false,\n" + "\t\t\t\"desc\": \"\",\n" + "\t\t\t\"effectiveEndTime\": 0,\n" + "\t\t\t\"endTime\": 0,\n" + "\t\t\t\"icon\": \"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\n" + "\t\t\t\"newUser\": false,\n" + "\t\t\t\"promoId\": *********,\n" + "\t\t\t\"promoItemText\": {\n" + "\t\t\t\t\"icon\": \"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\n" + "\t\t\t\t\"promoDivideType\": \"DEAL_PROMO\",\n" + "\t\t\t\t\"subTitle\": \"\",\n" + "\t\t\t\t\"title\": \"团购优惠\"\n" + "\t\t\t},\n" + "\t\t\t\"promoPrice\": 40.10,\n" + "\t\t\t\"promoTag\": \"-¥40.1\",\n" + "\t\t\t\"promoType\": \"团购优惠\",\n" + "\t\t\t\"promoTypeCode\": 11,\n" + "\t\t\t\"remainStock\": 0,\n" + "\t\t\t\"sourceType\": 1\n" + "\t\t}, {\n" + "\t\t\t\"amount\": 6,\n" + "\t\t\t\"canAssign\": false,\n" + "\t\t\t\"desc\": \"下单立省6元，每人可享受1次\",\n" + "\t\t\t\"effectiveEndTime\": 0,\n" + "\t\t\t\"endTime\": 1718380799000,\n" + "\t\t\t\"icon\": \"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\n" + "\t\t\t\"newUser\": false,\n" + "\t\t\t\"promoId\": 2017568094,\n" + "\t\t\t\"promoItemText\": {\n" + "\t\t\t\t\"icon\": \"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\n" + "\t\t\t\t\"promoDivideType\": \"DISCOUNT_SELL\",\n" + "\t\t\t\t\"subTitle\": \"下单立省6元，每人可享受1次\",\n" + "\t\t\t\t\"title\": \"特惠促销\"\n" + "\t\t\t},\n" + "\t\t\t\"promoPrice\": 6,\n" + "\t\t\t\"promoTag\": \"-¥6\",\n" + "\t\t\t\"promoType\": \"特惠促销\",\n" + "\t\t\t\"promoTypeCode\": 1,\n" + "\t\t\t\"remainStock\": 100,\n" + "\t\t\t\"sourceType\": 2\n" + "\t\t}],\n" + "\t\t\"promoPrice\": 53.90,\n" + "\t\t\"promoPriceTag\": \"53.9\",\n" + "\t\t\"promoQuantityLimit\": 0,\n" + "\t\t\"promoTag\": \"共省¥46.1\",\n" + "\t\t\"promoTagType\": 60,\n" + "\t\t\"promoType\": 0,\n" + "\t\t\"startTime\": 0,\n" + "\t\t\"totalPromoPrice\": 46.10,\n" + "\t\t\"totalPromoPriceTag\": \"-¥46.1\",\n" + "\t\t\"userHasCard\": false\n" + "\t}],\n" + "\t\"resourceRank\": {\n" + "\t\t\"rankLinkUrl\": \"\",\n" + "\t\t\"rankName\": \"\"\n" + "\t},\n" + "\t\"sale\": {\n" + "\t\t\"sale\": 5,\n" + "\t\t\"saleTag\": \"半年消费5\"\n" + "\t},\n" + "\t\"shopMs\": [],\n" + "\t\"shopNum\": 0,\n" + "\t\"spuType\": 0,\n" + "\t\"timesDeal\": false,\n" + "\t\"timesDealQueryFlag\": false,\n" + "\t\"title\": \"【搭载sjyh】88枚游戏币套餐\",\n" + "\t\"top\": false,\n" + "\t\"tradeType\": 3,\n" + "\t\"unifyProduct\": false,\n" + "\t\"userSubscribe\": false\n" + "}";
        return JSONObject.parseObject(productMStr, ProductM.class);
    }

    @Test
    public void testMember() {
        GameCoinItemPromoTagsOpt coinItemPromoTagsOpt = new GameCoinItemPromoTagsOpt();
        String memberStr = "{\"memberDiscountType\":2,\"merchantMember\":{\"isMember\":true,\"isNewMember\":false}}";
        ProductM productM = buildProductM();
        List<ProductPromoPriceM> promoPriceMS = productM.getPromoPrices();
        ProductPromoPriceM productPromoPriceM = promoPriceMS.get(0);
        List<PromoItemM> promoItemMS = productPromoPriceM.getPromoItemList();
        promoItemMS.forEach(promoItemM -> {
            if (promoItemM.getPromoId() == 2017568094) {
                promoItemM.setPromoTypeCode(16);
            }
        });
        productM.setAttr("MERCHANT_MEMBER_DEAL", memberStr);
        productM.setAttr("GameCoinDealPerPrice", "￥0.6");
        Mockito.when(param.getProductM()).thenReturn(productM);
        List<DzPromoVO> dzPromoVOS = coinItemPromoTagsOpt.compute(activityCxt, param, null);
        Assert.assertNotNull(dzPromoVOS);
        Assert.assertEquals("会员价 ￥0.6", dzPromoVOS.get(0).getName());
    }

    @Test
    public void testNewMember() {
        GameCoinItemPromoTagsOpt coinItemPromoTagsOpt = new GameCoinItemPromoTagsOpt();
        String memberStr = "{\"memberDiscountType\":3,\"merchantMember\":{\"isMember\":true,\"isNewMember\":true}}";
        ProductM productM = buildProductM();
        List<ProductPromoPriceM> promoPriceMS = productM.getPromoPrices();
        ProductPromoPriceM productPromoPriceM = promoPriceMS.get(0);
        List<PromoItemM> promoItemMS = productPromoPriceM.getPromoItemList();
        promoItemMS.forEach(promoItemM -> {
            if (promoItemM.getPromoId() == 2017568094) {
                promoItemM.setPromoTypeCode(16);
            }
        });
        productM.setAttr("MERCHANT_MEMBER_DEAL", memberStr);
        productM.setAttr("GameCoinDealPerPrice", "￥0.6");
        Mockito.when(param.getProductM()).thenReturn(productM);
        List<DzPromoVO> dzPromoVOS = coinItemPromoTagsOpt.compute(activityCxt, param, null);
        Assert.assertNotNull(dzPromoVOS);
        Assert.assertEquals("新会员价 ￥0.6", dzPromoVOS.get(0).getName());
    }

    @Test
    public void testMemberExclusive() {
        GameCoinItemPromoTagsOpt coinItemPromoTagsOpt = new GameCoinItemPromoTagsOpt();
        ProductM productM = buildProductM();
        List<ProductPromoPriceM> promoPriceMS = productM.getPromoPrices();
        ProductPromoPriceM productPromoPriceM = promoPriceMS.get(0);
        List<PromoItemM> promoItemMS = productPromoPriceM.getPromoItemList();
        promoItemMS.forEach(promoItemM -> {
            if (promoItemM.getPromoId() == 2017568094) {
                promoItemM.setPromoTypeCode(16);
            }
        });
        productM.setAttr("MEMBER_EXCLUSIVE", "true");
        productM.setAttr("GameCoinDealPerPrice", "￥0.6");
        Mockito.when(param.getProductM()).thenReturn(productM);
        List<DzPromoVO> dzPromoVOS = coinItemPromoTagsOpt.compute(activityCxt, param, null);
        Assert.assertNotNull(dzPromoVOS);
        Assert.assertEquals("会员专属 ￥0.6", dzPromoVOS.get(0).getName());
    }

    /**
     * Test case for empty attrValue
     */
    @Test
    public void testComputeWithEmptyAttrValue() throws Throwable {
        // arrange
        GameCoinItemPromoTagsOpt opt = new GameCoinItemPromoTagsOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        GameCoinItemPromoTagsOpt.Param param = mock(GameCoinItemPromoTagsOpt.Param.class);
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("GameCoinDealPerPrice")).thenReturn("");
        // act
        List<DzPromoVO> result = opt.compute(activityCxt, param, null);
        // assert
        assertNull(result);
    }
}
