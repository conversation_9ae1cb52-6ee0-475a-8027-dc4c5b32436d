package com.sankuai.dzviewscene.product.shelf.options.maintitle;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.maintitle.vp.DealMainTitleVP;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.strategy.MainTitleTipsStrategy;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.strategy.MainTitleTipsStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.strategy.TipsStrategyConfig;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ConfigDealMainTitleOptComputeTest {

    @Mock
    private MainTitleTipsStrategyFactory mainTitleTipsStrategyFactory;

    @Mock
    private ConfigDealMainTitleOpt configDealMainTitleOptMock;

    private ActivityCxt context;

    @Before
    public void setUp() {
        context = new ActivityCxt();
        // Assuming there's a way to set up the context or any necessary preconditions here
    }

    private DealMainTitleVP.Param createParamInstance() throws Exception {
        Constructor<DealMainTitleVP.Param> constructor = DealMainTitleVP.Param.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    @Test
    public void testComputeWhenShopCategoryMatchesConfigCategoryIds() throws Throwable {
        // arrange
        DealMainTitleVP.Param param = createParamInstance();
        param.setPlatform(1);
        ConfigDealMainTitleOpt.Config config = new ConfigDealMainTitleOpt.Config();
        config.setCategoryIds(Arrays.asList(100, 200, 300));
        ShopM shopM = new ShopM();
        shopM.setCategory(200);
        context.addParam(ShelfActivityConstants.Ctx.ctxShop, shopM);
        TipsStrategyConfig tipsConfig = new TipsStrategyConfig();
        tipsConfig.setStrategyName("testStrategy");
        config.setTipsStrategies(Collections.singletonList(tipsConfig));
        MainTitleComponentVO mockTitleVO = new MainTitleComponentVO();
        mockTitleVO.setIcon("originalIcon");
        MainTitleTipsStrategy mockStrategy = mock(MainTitleTipsStrategy.class);
        when(mainTitleTipsStrategyFactory.getStrategy("testStrategy")).thenReturn(mockStrategy);
        when(mockStrategy.build(any(), any(), any())).thenReturn(Collections.emptyList());
        // act
        MainTitleComponentVO result = configMainTitleOpt.compute(context, param, config);
        // assert
        assertNull(result.getIcon());
    }

    @Test
    public void testComputeWithSuccessfulStrategyExecution() throws Throwable {
        // arrange
        DealMainTitleVP.Param param = createParamInstance();
        param.setPlatform(1);
        ConfigDealMainTitleOpt.Config config = new ConfigDealMainTitleOpt.Config();
        TipsStrategyConfig tipsConfig = new TipsStrategyConfig();
        tipsConfig.setStrategyName("testStrategy");
        config.setTipsStrategies(Collections.singletonList(tipsConfig));
        MainTitleComponentVO mockTitleVO = new MainTitleComponentVO();
        MainTitleTipsStrategy mockStrategy = mock(MainTitleTipsStrategy.class);
        List<IconRichLabelVO> mockTips = Arrays.asList(new IconRichLabelVO("icon1", null), new IconRichLabelVO("icon2", null));
        when(mainTitleTipsStrategyFactory.getStrategy("testStrategy")).thenReturn(mockStrategy);
        when(mockStrategy.build(any(), any(), any())).thenReturn(mockTips);
        // act
        MainTitleComponentVO result = configMainTitleOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getTags().size());
        verify(mockStrategy).build(eq(context), eq(tipsConfig), any());
    }

    @Test
    public void testComputeWhenShopCategoryDoesNotMatch() throws Throwable {
        // arrange
        DealMainTitleVP.Param param = createParamInstance();
        param.setPlatform(1);
        ConfigDealMainTitleOpt.Config config = new ConfigDealMainTitleOpt.Config();
        config.setCategoryIds(Arrays.asList(100, 200, 300));
        ShopM shopM = new ShopM();
        shopM.setCategory(400);
        context.addParam(ShelfActivityConstants.Ctx.ctxShop, shopM);
        TipsStrategyConfig tipsConfig = new TipsStrategyConfig();
        tipsConfig.setStrategyName("testStrategy");
        config.setTipsStrategies(Collections.singletonList(tipsConfig));
        MainTitleComponentVO mockTitleVO = new MainTitleComponentVO();
        // Ensure the icon is set to the default value from the config
        mockTitleVO.setIcon(config.getDpTitleIcon());
        MainTitleTipsStrategy mockStrategy = mock(MainTitleTipsStrategy.class);
        when(mainTitleTipsStrategyFactory.getStrategy("testStrategy")).thenReturn(mockStrategy);
        when(mockStrategy.build(any(), any(), any())).thenReturn(Collections.emptyList());
        // act
        MainTitleComponentVO result = configMainTitleOpt.compute(context, param, config);
        // assert
        assertEquals(config.getDpTitleIcon(), result.getIcon());
    }

    @InjectMocks
    private ConfigDealMainTitleOpt configMainTitleOpt;
}
