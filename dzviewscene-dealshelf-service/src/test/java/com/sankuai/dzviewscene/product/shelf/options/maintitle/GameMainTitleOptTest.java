package com.sankuai.dzviewscene.product.shelf.options.maintitle;

import com.dianping.communitylife.enums.Platform;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.maintitle.vp.DealMainTitleVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GameMainTitleOptTest {

    @Test
    public  void compute(){
        GameMainTitleOpt gameMainTitleOpt = new GameMainTitleOpt();
        ActivityCxt activityCxt = Mockito.mock(ActivityCxt.class);
        DealMainTitleVP.Param param = Mockito.mock(DealMainTitleVP.Param.class);
        Mockito.when(param.getPlatform()).thenReturn(VCPlatformEnum.MT.getType());
        MainTitleComponentVO componentVO = gameMainTitleOpt.compute(activityCxt, param, buildConfig());
        Assert.assertNotNull(componentVO);
        Assert.assertEquals("游戏币",componentVO.getTitle());


    }

    private GameMainTitleOpt.Config buildConfig(){
        String mtTitleIcon = "https://p0.meituan.net/ingee/d9590126dd5d4f3ccb49899292ab17981145.png";

        GameMainTitleOpt.Config config = new GameMainTitleOpt.Config();
        config.setTitle("游戏币");
        config.setDpTitleIcon("https://p0.meituan.net/ingee/e2bef2da6a1af2ad8ff69476c42f0506993.png");
        config.setTitleTagIcon("https://p0.meituan.net/dprainbow/eb0dd38ce2ef531801c7282e7995bfe71262.png");
        config.setMtTitleIcon("mtTitleIcon");

        config.setReturnExpiredTitle("过期退");
        config.setReturnAnyTimeTitle("随时退");
        config.setReturnCanAppointTitle("可预约");
        return config;

    }


}
