package com.sankuai.dzviewscene.product.shelf.options.maintitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.ShopGuaranteeMainTitleOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.dianping.vc.enums.VCPlatformEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopGuaranteeMainTitleOptTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private MainTitleComponentVO titleVO;

    @Mock
    private ShopGuaranteeMainTitleOpt.Config config;

    @Mock
    private ContextHandlerResult contextHandlerResult;

    /**
     * 测试 platform 等于 VCPlatformEnum.MT.getType()，config 不为 null 的情况
     */
    @Test
    public void testBuildBasicMainTitleComponentVO_PlatformIsMT() {
        // arrange
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();
        Config config = new Config();
        config.setTitle("testTitle");
        config.setMtTitleIcon("testMtTitleIcon");
        config.setDpTitleIcon("testDpTitleIcon");

        // act
        MainTitleComponentVO mainTitleComponentVO = shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.MT.getType(), config);

        // assert
        assertEquals("testTitle", mainTitleComponentVO.getTitle());
        assertEquals("testMtTitleIcon", mainTitleComponentVO.getIcon());
        assertTrue(mainTitleComponentVO.getTags().isEmpty());
    }

    /**
     * 测试 platform 不等于 VCPlatformEnum.MT.getType()，config 不为 null 的情况
     */
    @Test
    public void testBuildBasicMainTitleComponentVO_PlatformIsNotMT() {
        // arrange
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();
        Config config = new Config();
        config.setTitle("testTitle");
        config.setMtTitleIcon("testMtTitleIcon");
        config.setDpTitleIcon("testDpTitleIcon");

        // act
        MainTitleComponentVO mainTitleComponentVO = shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.DP.getType(), config);

        // assert
        assertEquals("testTitle", mainTitleComponentVO.getTitle());
        assertEquals("testDpTitleIcon", mainTitleComponentVO.getIcon());
        assertTrue(mainTitleComponentVO.getTags().isEmpty());
    }

    /**
     * 测试 config 为 null 的情况
     */
    @Test(expected = NullPointerException.class)
    public void testBuildBasicMainTitleComponentVO_ConfigIsNull() {
        // arrange
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();

        // act
        shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.MT.getType(), null);
    }

    /**
     * 测试场景：ActivityCxt 不包含门店保证标签
     */
    @Test
    public void testFillShopGuaranteeTagWithoutTag() {
        // arrange
        when(ctx.getSource(anyString())).thenReturn(null);
        ShopGuaranteeMainTitleOpt opt = new ShopGuaranteeMainTitleOpt();

        // act
        opt.fillShopGuaranteeTag(ctx, titleVO, config);

        // assert
        verify(titleVO, never()).getTags();
    }

    /**
     * 测试场景：ActivityCxt 包含门店保证标签
     */
    @Test
    public void testFillShopGuaranteeTagWithTag() {
        // arrange
        when(ctx.getSource(anyString())).thenReturn(contextHandlerResult);
        when(contextHandlerResult.isShopHasShopGuaranteeTag()).thenReturn(true);
        ShopGuaranteeMainTitleOpt opt = new ShopGuaranteeMainTitleOpt();

        // act
        opt.fillShopGuaranteeTag(ctx, titleVO, config);

        // assert
        verify(titleVO, times(1)).getTags();
    }

    /**
     * 测试 fillReturnTags 方法在正常情况下的行为
     */
    @Test
    public void testFillReturnTagsNormal() throws Throwable {
        // arrange
        ShopGuaranteeMainTitleOpt opt = new ShopGuaranteeMainTitleOpt();
        when(config.getTitleTagIcon()).thenReturn("iconUrl");
        when(config.getReturnAnyTimeTitle()).thenReturn("随时退");
        when(config.getReturnExpiredTitle()).thenReturn("过期退");

        // act
        opt.fillReturnTags(ctx, titleVO, config);

        // assert
        verify(titleVO, times(1)).getTags();
    }

    /**
     * 测试 fillReturnTags 方法在异常情况下的行为
     */
    @Test(expected = NullPointerException.class)
    public void testFillReturnTagsException() throws Throwable {
        // arrange
        ShopGuaranteeMainTitleOpt opt = new ShopGuaranteeMainTitleOpt();

        // act
        opt.fillReturnTags(null, null, null);
    }

    /**
     * 测试 fillReturnTags 方法在边界情况下的行为
     */
    @Test
    public void testFillReturnTagsBoundary() throws Throwable {
        // arrange
        ShopGuaranteeMainTitleOpt opt = new ShopGuaranteeMainTitleOpt();
        when(config.getTitleTagIcon()).thenReturn(null);
        when(config.getReturnAnyTimeTitle()).thenReturn(null);
        when(config.getReturnExpiredTitle()).thenReturn(null);

        // act
        opt.fillReturnTags(ctx, titleVO, config);

        // assert
        verify(titleVO, times(1)).getTags();
    }
}
