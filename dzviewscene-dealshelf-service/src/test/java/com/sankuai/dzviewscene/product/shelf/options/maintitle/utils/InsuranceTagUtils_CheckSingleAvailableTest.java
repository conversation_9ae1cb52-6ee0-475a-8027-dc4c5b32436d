package com.sankuai.dzviewscene.product.shelf.options.maintitle.utils;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class InsuranceTagUtils_CheckSingleAvailableTest {

    @Mock
    private ProductM product;

    /**
     * 测试 DealInsuranceInfo 属性为空的情况
     */
    @Test
    public void testCheckSingleAvailableDealInsuranceInfoIsNull() {
        // arrange
        when(product.getAttr("DealInsuranceInfo")).thenReturn(null);
        // act
        boolean result = InsuranceTagUtils.checkSingleAvailable(product);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 DealInsuranceInfo 属性不为空，但解码后的 DealInsurance 对象的 available 属性为 false 的情况
     */
    @Test
    public void testCheckSingleAvailableDealInsuranceAvailableIsFalse() {
        // arrange
        when(product.getAttr("DealInsuranceInfo")).thenReturn("{\"available\":false}");
        // act
        boolean result = InsuranceTagUtils.checkSingleAvailable(product);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 DealInsuranceInfo 属性不为空，解码后的 DealInsurance 对象的 available 属性为 true 的情况
     */
    @Test
    public void testCheckSingleAvailableDealInsuranceAvailableIsTrue() {
        // arrange
        when(product.getAttr("DealInsuranceInfo")).thenReturn("{\"available\":true}");
        // act
        boolean result = InsuranceTagUtils.checkSingleAvailable(product);
        // assert
        assertTrue(result);
    }
}
