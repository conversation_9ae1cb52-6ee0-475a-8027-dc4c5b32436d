package com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler;

import com.alibaba.fastjson.JSON;
import com.sankuai.FileUtil;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class UnifiedShelfResponseAssemblerTest {

    @Mock
    private ActivityCxt mockCtx;
    @Mock
    private UnifiedShelfResponseAssembler.Request mockRequest;
    @Mock
    private UnifiedShelfResponseAssembler.Config mockConfig;

    private UnifiedShelfResponseAssembler assemblerUnderTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        assemblerUnderTest = new UnifiedShelfResponseAssembler();
    }

    /**
     * 测试build方法，当ctx.getSource返回空列表时
     */
    @Test
    public void testBuildWhenSourceReturnsEmptyList() throws Throwable {
        // arrange
        when(mockCtx.getSource(ShelfDouHuFetcher.CODE)).thenReturn(Collections.emptyList());
        when(mockCtx.getSource(UnifiedProductAreaBuilder.CODE)).thenReturn(Collections.emptyList());

        // act
        CompletableFuture<UnifiedShelfResponse> result = assemblerUnderTest.build(mockCtx, mockRequest, mockConfig);

        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertNotNull(result.get());
        assertNull(result.get().getFilter());
        assertTrue(result.get().getFilterIdAndProductAreas().isEmpty());
    }

    /**
     * 测试build方法，当ctx.getSource抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testBuildWhenSourceThrowsException() throws Throwable {
        // arrange
        when(mockCtx.getSource(anyString())).thenThrow(new RuntimeException());

        // act
        assemblerUnderTest.build(mockCtx, mockRequest, mockConfig);

        // assert 在方法注解中已指定期望的异常类型
    }

    /**
     * 测试build方法，正常情况
     */
    @Test
    public void testBuildNormalCase() throws Throwable {
        // arrange
        List<DouHuM> douHuMList = Collections.singletonList(new DouHuM());
        List<ShelfFilterProductAreaVO> productAreaVOList = Collections.singletonList(new ShelfFilterProductAreaVO());
        when(mockCtx.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);
        when(mockCtx.getSource(UnifiedProductAreaBuilder.CODE)).thenReturn(productAreaVOList);

        // act
        CompletableFuture<UnifiedShelfResponse> result = assemblerUnderTest.build(mockCtx, mockRequest, mockConfig);

        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        UnifiedShelfResponse response = result.get();
        assertNotNull(response);
        assertEquals(productAreaVOList, response.getFilterIdAndProductAreas());
    }

    @Test
    public void test_findFirstFilterComponent() throws Throwable {
        // arrange

        String string = FileUtil.file2str("unified_shelf.json");
        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);
        List<ShelfFilterProductAreaVO> filterIdAndProductAreas = response.getFilterIdAndProductAreas();

        Method method = assemblerUnderTest.getClass().getDeclaredMethod("getFirstValidProAreaProductTotals", List.class);
        method.setAccessible(true);

        int size = (int) method.invoke(assemblerUnderTest, filterIdAndProductAreas);

        Assert.assertTrue(size > 0);


    }
}
