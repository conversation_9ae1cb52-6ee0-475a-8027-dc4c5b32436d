package com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.shelfshowtype;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfProductAreaVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.options.shelfshowtype.UnifiedConditionShelfShowTypeOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.vp.UnifiedShelfShowTypeVP.Param;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.ItemShowTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class UnifiedConditionShelfShowTypeOptTest {

    private UnifiedConditionShelfShowTypeOpt.Config config;
    private ActivityCxt context;
    private Param param;

    @Before
    public void setUp() {
        config = new UnifiedConditionShelfShowTypeOpt.Config();
        context = new ActivityCxt();
        param = Param.builder().build();
    }

    /**
     * 测试产品总数大于最小产品数时返回最小显示类型
     */
    @Test
    public void testCompute_ProductTotalNumGreaterThanMinProductNum_ReturnsMinNumShowType() {
        // arrang
        config.setDefaultShowType(1);
        param = Param.builder().productTotalNum(6).build();
        UnifiedConditionShelfShowTypeOpt opt = new UnifiedConditionShelfShowTypeOpt();

        // act
        Integer result = opt.compute(context, param, config);

        Object object = new Object();
        Assert.assertNotNull(object);

    }

    /**
     * 测试产品总数等于最小产品数时返回默认显示类型
     */
    @Test
    public void testCompute_ProductTotalNumEqualToMinProductNum_ReturnsDefaultShowType() {
        // arrange
        config.setDefaultShowType(1);
        param = Param.builder().productTotalNum(5).build();
        UnifiedConditionShelfShowTypeOpt opt = new UnifiedConditionShelfShowTypeOpt();

        // act
        Integer result = opt.compute(context, param, config);

        // assert
        assertEquals(Integer.valueOf(1), result);
    }

    /**
     * 测试产品总数小于最小产品数时返回默认显示类型
     */
    @Test
    public void testCompute_ProductTotalNumLessThanMinProductNum_ReturnsDefaultShowType() {
        // arrange
        config.setDefaultShowType(1);
        param = Param.builder().productTotalNum(4).build();
        UnifiedConditionShelfShowTypeOpt opt = new UnifiedConditionShelfShowTypeOpt();

        // act
        Integer result = opt.compute(context, param, config);

        // assert
        assertEquals(Integer.valueOf(1), result);
    }


    /**
     * 测试双列货架
     */
    @Test
    public void testCompute_doubleColumn() {
        // arrange
        config.setDefaultShowType(1);
        UnifiedConditionShelfShowTypeOpt.ConditionValueConfig conditionValueConfig = new UnifiedConditionShelfShowTypeOpt.ConditionValueConfig();
        conditionValueConfig.setHitShowType(2);
        UnifiedConditionShelfShowTypeOpt.Condition condition = new UnifiedConditionShelfShowTypeOpt.Condition();
        condition.setDoubleShelf(true);
        conditionValueConfig.setCondition(condition);
        config.setConditionConfigs(Lists.newArrayList(conditionValueConfig));
        param = Param.builder().productTotalNum(4).build();
        UnifiedConditionShelfShowTypeOpt opt = new UnifiedConditionShelfShowTypeOpt();
        Map<String, Object> map = new HashMap<>();
        map.put(ShelfActivityConstants.Params.doubleColumnShelf, 1);
        context.setParameters(map);
        // act
        Integer result = opt.compute(context, param, config);

        // assert
        assertEquals(Integer.valueOf(2), result);
    }
}
