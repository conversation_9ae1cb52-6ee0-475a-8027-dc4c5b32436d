package com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.vp;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.Collections;

public class UnifiedShelfShowTypeVPTest {

    @Test
    public void testProcessProductTotalNumNegative() throws Throwable {
        // arrange
        UnifiedShelfShowTypeVP.Param param = UnifiedShelfShowTypeVP.Param.builder()
                .productTotalNum(-1)
                .douHuList(Collections.singletonList(new DouHuM()))
                .shelfVersion(1)
                .build();
        UnifiedShelfShowTypeVP<Integer> vp = Mockito.mock(UnifiedShelfShowTypeVP.class);
        Integer compute = vp.compute(new ActivityCxt(), param, null);
        Object o = new Object();
        Assert.assertNotNull(o);
    }
}
