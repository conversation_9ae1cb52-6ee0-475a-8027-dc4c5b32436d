package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.labs.UnifiedShelfCommonFilterBtnLabsOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterBtnLabsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Assert;
import org.junit.Test;

public class UnifiedShelfCommonFilterBtnLabsOptTest {

    @Test
    public void test_void() {

        UnifiedShelfCommonFilterBtnLabsOpt opt = new UnifiedShelfCommonFilterBtnLabsOpt();

        UnifiedShelfFilterBtnLabsVP.Param param = UnifiedShelfFilterBtnLabsVP.Param.builder().build();

        FilterBtnM filterBtnM = new FilterBtnM();
        param.setBtn(filterBtnM);
        String compute = opt.compute(new ActivityCxt(), param, null);

        Assert.assertNotNull(compute);
    }

    @Test
    public void test_void2() {

        UnifiedShelfCommonFilterBtnLabsOpt opt = new UnifiedShelfCommonFilterBtnLabsOpt();

        UnifiedShelfFilterBtnLabsVP.Param param = UnifiedShelfFilterBtnLabsVP.Param.builder().build();

        FilterBtnM filterBtnM = new FilterBtnM();
        param.setBtn(filterBtnM);

        ShopM shop = new ShopM();
        shop.setCategory(1);
        param.setShop(shop);

        param.setPlatform(2);
        String compute = opt.compute(new ActivityCxt(), param, null);

        Assert.assertNotNull(compute);
    }
}
