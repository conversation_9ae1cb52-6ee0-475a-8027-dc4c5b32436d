package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.FileUtil;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterNodeVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterVO;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder.Request;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * UnifiedShelfFilterBuilder的测试类
 */
public class UnifiedShelfFilterBuilderTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Request mockRequest;
    @Mock
    private Config mockConfig;
    @Mock
    private ShelfGroupM mockShelfGroupM;

    private UnifiedShelfFilterBuilder unifiedShelfFilterBuilder;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedShelfFilterBuilder = new UnifiedShelfFilterBuilder();
        when(mockActivityCxt.getSource(ShelfMainDataAssembler.CODE)).thenReturn(mockShelfGroupM);
        UnifiedShelfFilterBuilder.filterSecondTabConfig = new LionConfigHelper.FilterSecondTabConfig();
    }

    /**
     * 测试build方法，当ShelfGroupM为null时
     */
    @Test
    public void testBuildWhenShelfGroupMIsNull() throws Throwable {
        // arrange
        when(mockActivityCxt.getSource(ShelfMainDataAssembler.CODE)).thenReturn(null);

        // act
        CompletableFuture<Map<String, ShelfFilterVO>> result = unifiedShelfFilterBuilder.build(mockActivityCxt, mockRequest, mockConfig);

        // assert
        assertTrue("结果应为空Map", result.get().isEmpty());
    }

    /**
     * 测试build方法，当ShelfGroupM没有产品时
     */
    @Test
    public void testBuildWhenShelfGroupMHasNoProducts() throws Throwable {
        // arrange
        when(mockShelfGroupM.getFilterMs()).thenReturn(Maps.newHashMap());

        // act
        CompletableFuture<Map<String, ShelfFilterVO>> result = unifiedShelfFilterBuilder.build(mockActivityCxt, mockRequest, mockConfig);

        // assert
        assertTrue("结果应为空Map", result.get().isEmpty());
    }

    /**
     * 测试build方法，当ShelfGroupM有产品但没有过滤器时
     */
    @Test
    public void testBuildWhenShelfGroupMHasProductsButNoFilters() throws Throwable {
        // arrange
        when(mockShelfGroupM.getFilterMs()).thenReturn(null);

        // act
        CompletableFuture<Map<String, ShelfFilterVO>> result = unifiedShelfFilterBuilder.build(mockActivityCxt, mockRequest, mockConfig);

        // assert
        assertTrue("结果应为空Map", result.get().isEmpty());
    }

    /**
     * 测试build方法，当ShelfGroupM有产品且有过滤器时
     */
    @Test
    public void testBuildWhenShelfGroupMHasProductsAndFilters() throws Throwable {
        // arrange
        Map<String, ShelfFilterVO> expectedMap = Maps.newHashMap();
        expectedMap.put("111", new ShelfFilterVO());
        when(mockShelfGroupM.getFilterMs()).thenReturn(Maps.newHashMap());
        // 此处简化，实际应当模拟更多的行为以确保buildFilterVO被正确调用

        // act
        CompletableFuture<Map<String, ShelfFilterVO>> result = unifiedShelfFilterBuilder.build(mockActivityCxt, mockRequest, mockConfig);

        // assert


    }

    /**
     * 测试build方法，当ShelfGroupM有产品且有过滤器时
     */
    @Test
    public void testBuildWhenShelfGroupMHasProductsAndFilters2() throws Throwable {
        // arrange

        FilterM filterM = JSON.parseObject(FileUtil.file2str("filter.json"), FilterM.class);
        ProductGroupM productGroupM = JSON.parseObject(FileUtil.file2str("product_group.json"), ProductGroupM.class);
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(new HashMap<>());
        shelfGroupM.setFilterMs(new HashMap<>());
        shelfGroupM.getFilterMs().put("团购", filterM);
        shelfGroupM.getProductGroupMs().put("团购", productGroupM);


        ActivityCxt cxt = new ActivityCxt();
        cxt.getSourceMap().put("ShelfMainDataAssembler", shelfGroupM);


        // act
        UnifiedShelfFilterBuilder filterBuilder = new UnifiedShelfFilterBuilder();
        CompletableFuture<Map<String, ShelfFilterVO>> build = filterBuilder.build(cxt, new Request(), new Config());
        Map<String, ShelfFilterVO> join = build.join();

        System.out.println(JSON.toJSONString(join));
        Assert.assertNotNull(join);
        // assert


    }

    /**
     * 测试场景：filterSecondTabConfig为null
     */
    @Test
    public void testAllChildrenMinShowNum_FilterSecondTabConfigIsNull() {
        UnifiedShelfFilterBuilder.filterSecondTabConfig = null;
        ShelfFilterNodeVO filterButtonVO = new ShelfFilterNodeVO();

        UnifiedShelfFilterBuilder.allChildrenMinShowNum(mockActivityCxt, mockConfig, filterButtonVO);

        assertEquals(0, filterButtonVO.getMinShowNum());
    }

    /**
     * 测试场景：filterSwitch关闭
     */
    @Test
    public void testAllChildrenMinShowNum_FilterSwitchIsOff() {
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setFilterSwitch(false);
        ShelfFilterNodeVO filterButtonVO = new ShelfFilterNodeVO();

        UnifiedShelfFilterBuilder.allChildrenMinShowNum(mockActivityCxt, mockConfig, filterButtonVO);

        assertEquals(0, filterButtonVO.getMinShowNum());
    }

    /**
     * 测试场景：childrenMinShowNum小于等于0
     */
    @Test
    public void testAllChildrenMinShowNum_ChildrenMinShowNumIsLessThanOrEqualToZero() {
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setFilterSwitch(true);
        when(mockConfig.getChildrenMinShowNum()).thenReturn(0);
        ShelfFilterNodeVO filterButtonVO = new ShelfFilterNodeVO();

        UnifiedShelfFilterBuilder.allChildrenMinShowNum(mockActivityCxt, mockConfig, filterButtonVO);

        assertEquals(0, filterButtonVO.getMinShowNum());
    }

    /**
     * 测试场景：不在黑名单中
     */
    @Test
    public void testAllChildrenMinShowNum_NotInBlackList() {
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setFilterSwitch(true);
        when(mockConfig.getChildrenMinShowNum()).thenReturn(0);
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setChildrenMinShowNum(5);
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setBlackSceneCodeList(Lists.newArrayList("scene1", "scene2"));
        when(mockActivityCxt.getSceneCode()).thenReturn("scene3");
        ShelfFilterNodeVO filterButtonVO = new ShelfFilterNodeVO();

        UnifiedShelfFilterBuilder.allChildrenMinShowNum(mockActivityCxt, mockConfig, filterButtonVO);

        assertEquals(5, filterButtonVO.getMinShowNum());
    }

    /**
     * 测试场景：在黑名单场景代码中
     */
    @Test
    public void testAllChildrenMinShowNum_InBlackSceneCodeList() {
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setFilterSwitch(true);
        when(mockConfig.getChildrenMinShowNum()).thenReturn(5);
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setBlackSceneCodeList(Lists.newArrayList("scene1", "scene2"));
        when(mockActivityCxt.getSceneCode()).thenReturn("scene1");
        ShelfFilterNodeVO filterButtonVO = new ShelfFilterNodeVO();

        UnifiedShelfFilterBuilder.allChildrenMinShowNum(mockActivityCxt, mockConfig, filterButtonVO);

        assertEquals(0, filterButtonVO.getMinShowNum());
    }

    /**
     * 测试场景：在黑名单店铺ID中
     */
    @Test
    public void testAllChildrenMinShowNum_InBlackDpShopIdList() {
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setFilterSwitch(true);
        when(mockConfig.getChildrenMinShowNum()).thenReturn(5);
        UnifiedShelfFilterBuilder.filterSecondTabConfig.setBlackSceneCodeList(Lists.newArrayList("scene1", "scene2"));
        when(mockActivityCxt.getSceneCode()).thenReturn("scene3");
        when(mockActivityCxt.getParameters()).thenReturn(java.util.Collections.singletonMap("dpPoiId", 1));
        ShelfFilterNodeVO filterButtonVO = new ShelfFilterNodeVO();

        UnifiedShelfFilterBuilder.allChildrenMinShowNum(mockActivityCxt, mockConfig, filterButtonVO);

        assertEquals(0, filterButtonVO.getMinShowNum());
    }
}
