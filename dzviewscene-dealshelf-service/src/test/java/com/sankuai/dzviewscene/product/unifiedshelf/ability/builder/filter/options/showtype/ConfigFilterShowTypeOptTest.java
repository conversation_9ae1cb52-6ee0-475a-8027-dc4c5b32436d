package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.showtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.showtype.ConfigFilterShowTypeOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.FilterNodeShowTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * ConfigFilterShowTypeOpt的单元测试
 */
public class ConfigFilterShowTypeOptTest {
    private ConfigFilterShowTypeOpt opt;
    private ActivityCxt activityCxt;
    private ConfigFilterShowTypeOpt.Param param;
    private Config config;

    @Before
    public void setUp() {
        opt = new ConfigFilterShowTypeOpt();
        activityCxt = mock(ActivityCxt.class);
        param = mock(ConfigFilterShowTypeOpt.Param.class);
        config = new Config();
    }

    /**
     * 测试命中node2showType配置
     */
    @Test
    public void testCompute_hitNode2ShowType() {
        Map<String, Integer> node2showType = new HashMap<>();
        node2showType.put("testNode", 99);
        config.setNode2showType(node2showType);
        when(param.getIdentityName()).thenReturn("testNode");
        Integer result = opt.compute(activityCxt, param, config);
        assertEquals(99, result.intValue());
    }

    /**
     * 测试tabShowType生效
     */
    @Test
    public void testCompute_tabShowType() {
        config.setTabShowType(88);
        config.setNode2showType(null);
        when(param.getIdentityName()).thenReturn("otherNode");
        when(param.isOptionNode()).thenReturn(false);
        Integer result = opt.compute(activityCxt, param, config);
        assertEquals(88, result.intValue());
    }

    /**
     * 测试默认返回FILTER_GRID_LAYER
     */
    @Test
    public void testCompute_defaultGridLayer() {
        config.setTabShowType(0);
        config.setNode2showType(null);
        when(param.isOptionNode()).thenReturn(true);
        Integer result = opt.compute(activityCxt, param, config);
        assertEquals(FilterNodeShowTypeEnum.FILTER_GRID_LAYER.getType(), result.intValue());
    }

    /**
     * 测试默认返回FILTER_ADAPTIVE_GRID_LAYER
     */
    @Test
    public void testCompute_defaultAdaptiveGridLayer() {
        config.setTabShowType(0);
        config.setNode2showType(null);
        when(param.isOptionNode()).thenReturn(false);
        Integer result = opt.compute(activityCxt, param, config);
        assertEquals(FilterNodeShowTypeEnum.FILTER_ADAPTIVE_GRID_LAYER.getType(), result.intValue());
    }
}
