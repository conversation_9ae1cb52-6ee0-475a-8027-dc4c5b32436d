package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.IconRichLabelTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.*;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfFilterTitleVPTest {

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    /**
     * 测试 buildTextRichLabModel 方法，正常情况
     */
    @Test
    public void testBuildTextRichLabModel_Normal() throws Throwable {
        // arrange
        FilterBtnM filterBtnM = mock(FilterBtnM.class);
        when(filterBtnM.getTitle()).thenReturn("test");
        UnifiedShelfFilterTitleVP.Param param = mock(UnifiedShelfFilterTitleVP.Param.class);
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildTextRichLabModel(param);
        // assert
        assertNotNull(result);
        assertEquals(IconRichLabelTypeEnum.TEXT.getType(), result.getType());
        assertNotNull(result.getText());
        assertEquals("test", result.getText().getText());
    }

    /**
     * 测试 buildTextRichLabModel 方法，param 为 null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildTextRichLabModel_ParamIsNull() throws Throwable {
        // arrange
        UnifiedShelfFilterTitleVP.Param param = null;
        // act
        UnifiedShelfFilterTitleVP.buildTextRichLabModel(param);
    }

    /**
     * 测试 buildTextRichLabModel 方法，param.getFilterBtnM() 为 null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildTextRichLabModel_FilterBtnMIsNull() throws Throwable {
        // arrange
        UnifiedShelfFilterTitleVP.Param param = mock(UnifiedShelfFilterTitleVP.Param.class);
        when(param.getFilterBtnM()).thenReturn(null);
        // act
        UnifiedShelfFilterTitleVP.buildTextRichLabModel(param);
    }

    /**
     * 测试 buildTextRichLabModel 方法，param.getFilterBtnM().getTitle() 为 null
     */
    @Test
    public void testBuildTextRichLabModel_TitleIsNull() throws Throwable {
        // arrange
        FilterBtnM filterBtnM = mock(FilterBtnM.class);
        when(filterBtnM.getTitle()).thenReturn(null);
        UnifiedShelfFilterTitleVP.Param param = mock(UnifiedShelfFilterTitleVP.Param.class);
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildTextRichLabModel(param);
        // assert
        assertNotNull(result);
        assertEquals(IconRichLabelTypeEnum.TEXT.getType(), result.getType());
        assertNotNull(result.getText());
        assertNull(result.getText().getText());
    }

    /**
     * 测试 buildTextModel 方法，输入正常的 text
     */
    @Test
    public void testBuildTextModelNormalText() throws Throwable {
        // arrange
        String text = "test text";
        // act
        RichLabelModel result = UnifiedShelfFilterTitleVP.buildTextModel(text);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
    }

    /**
     * 测试 buildTextModel 方法，输入空字符串
     */
    @Test
    public void testBuildTextModelEmptyText() throws Throwable {
        // arrange
        String text = "";
        // act
        RichLabelModel result = UnifiedShelfFilterTitleVP.buildTextModel(text);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
    }

    /**
     * 测试 buildTextModel 方法，输入 null
     */
    @Test
    public void testBuildTextModelNullText() throws Throwable {
        // arrange
        String text = null;
        // act
        RichLabelModel result = UnifiedShelfFilterTitleVP.buildTextModel(text);
        // assert
        assertNotNull(result);
        assertNull(result.getText());
    }

    /**
     * 测试 buildImageRichLabModel 方法，正常情况
     */
    @Test
    public void testBuildImageRichLabModelNormal() throws Throwable {
        // arrange
        PictureModel pictureModel = new PictureModel();
        pictureModel.setAspectRadio(1.0);
        pictureModel.setPicHeight(200);
        pictureModel.setPicUrl("test.jpg");
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setTitle("测试");
        UnifiedShelfFilterTitleVP.Param param = UnifiedShelfFilterTitleVP.Param.builder().filterBtnM(filterBtnM).build();
        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, param);
        // assert
        assertNotNull(result);
        assertEquals(pictureModel, result.getIcon());
        assertEquals("测试", result.getText().getText());
        assertEquals(1, result.getType());
    }

    /**
     * 测试 buildImageRichLabModel 方法，param 为 null
     */
    @Test
    public void testBuildImageRichLabModelWithNullParam() throws Throwable {
        // arrange
        thrown.expect(NullPointerException.class);
        PictureModel pictureModel = new PictureModel();
        pictureModel.setAspectRadio(1.0);
        pictureModel.setPicHeight(200);
        pictureModel.setPicUrl("test.jpg");
        // act
        UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, null);
    }

    /**
     * 测试 buildImageRichLabModel 方法，param.getFilterBtnM() 为 null
     */
    @Test
    public void testBuildImageRichLabModelWithNullFilterBtnM() throws Throwable {
        // arrange
        thrown.expect(NullPointerException.class);
        PictureModel pictureModel = new PictureModel();
        pictureModel.setAspectRadio(1.0);
        pictureModel.setPicHeight(200);
        pictureModel.setPicUrl("test.jpg");
        FilterBtnM filterBtnM = null;
        UnifiedShelfFilterTitleVP.Param param = UnifiedShelfFilterTitleVP.Param.builder().filterBtnM(filterBtnM).build();
        // act
        UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, param);
    }
}
