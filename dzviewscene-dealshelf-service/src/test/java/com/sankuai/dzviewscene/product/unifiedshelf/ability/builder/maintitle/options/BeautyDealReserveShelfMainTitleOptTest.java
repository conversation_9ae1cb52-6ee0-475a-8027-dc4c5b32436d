package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.options;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfMainTitleVO;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyDealReserveShelfMainTitleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private BeautyDealReserveShelfMainTitleOpt beautyDealReserveShelfMainTitleOpt;

    /**
     * Tests the compute method when config is null.
     */
    @Test
    public void testComputeConfigIsNull() throws Throwable {
        // Arrange
        when(beautyDealReserveShelfMainTitleOpt.compute(any(ActivityCxt.class), any(), any())).thenReturn(null);
        // Act
        ShelfMainTitleVO result = beautyDealReserveShelfMainTitleOpt.compute(context, null, null);
        // Assert
        assertNull(result);
    }

    /**
     * Tests the compute method when config is not null.
     */
    @Test
    public void testComputeConfigIsNotNull() throws Throwable {
        // Arrange
        BeautyDealReserveShelfMainTitleOpt.Config config = new BeautyDealReserveShelfMainTitleOpt.Config();
        // Assuming the Config class has a setter for "title" as an example
        config.setTitle("预订");
        when(beautyDealReserveShelfMainTitleOpt.compute(any(ActivityCxt.class), any(), any())).thenReturn(new ShelfMainTitleVO());
        // Act
        ShelfMainTitleVO result = beautyDealReserveShelfMainTitleOpt.compute(context, null, config);
        // Assert
        assertNotNull(result);
    }
}
