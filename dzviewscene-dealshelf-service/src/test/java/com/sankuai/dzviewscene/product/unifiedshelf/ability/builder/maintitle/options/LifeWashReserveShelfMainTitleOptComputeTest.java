package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.options;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfMainTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.options.LifeWashReserveShelfMainTitleOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class LifeWashReserveShelfMainTitleOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ContextHandlerResult contextHandlerResult;

    private LifeWashReserveShelfMainTitleOpt opt;

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        LifeWashReserveShelfMainTitleOpt opt = new LifeWashReserveShelfMainTitleOpt();
        Config config = null;
        ShelfMainTitleVO result = opt.compute(context, null, config);
        assertNull(result);
    }

    @Test
    public void testComputeTimelinessTextIsEmpty() throws Throwable {
        LifeWashReserveShelfMainTitleOpt opt = new LifeWashReserveShelfMainTitleOpt();
        Config config = new Config();
        config.setTimelinessText(Collections.emptyList());
        ShelfMainTitleVO result = opt.compute(context, null, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeAvgAcceptOderTimeIsZero() throws Throwable {
        LifeWashReserveShelfMainTitleOpt opt = new LifeWashReserveShelfMainTitleOpt();
        Config config = new Config();
        config.setTimelinessText(Arrays.asList("较快确认", "预计15分钟接单", "预计30分钟接单"));
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        ShelfMainTitleVO result = opt.compute(context, null, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeAvgAcceptOderTimeIsLessThan300() throws Throwable {
        LifeWashReserveShelfMainTitleOpt opt = new LifeWashReserveShelfMainTitleOpt();
        Config config = new Config();
        config.setTimelinessText(Arrays.asList("较快确认", "预计15分钟接单", "预计30分钟接单"));
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        ShelfMainTitleVO result = opt.compute(context, null, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeAvgAcceptOderTimeIsLessThan900() throws Throwable {
        LifeWashReserveShelfMainTitleOpt opt = new LifeWashReserveShelfMainTitleOpt();
        Config config = new Config();
        config.setTimelinessText(Arrays.asList("较快确认", "预计15分钟接单", "预计30分钟接单"));
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        ShelfMainTitleVO result = opt.compute(context, null, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeAvgAcceptOderTimeIsGreaterThan900() throws Throwable {
        LifeWashReserveShelfMainTitleOpt opt = new LifeWashReserveShelfMainTitleOpt();
        Config config = new Config();
        config.setTimelinessText(Arrays.asList("较快确认", "预计15分钟接单", "预计30分钟接单"));
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        ShelfMainTitleVO result = opt.compute(context, null, config);
        assertNotNull(result);
    }
}
