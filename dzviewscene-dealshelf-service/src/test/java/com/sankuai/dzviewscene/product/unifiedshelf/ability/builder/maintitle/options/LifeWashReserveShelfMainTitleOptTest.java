package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.options;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfMainTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.vp.UnifiedShelfMainTitleVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class LifeWashReserveShelfMainTitleOptTest {

    @Test
    public void test(){
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> sourceMap = Maps.newHashMap();
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setAvgAcceptOderTime(3L);
        sourceMap.put("ContextHandlerAbility",contextHandlerResult);
        context.setSourceMap(sourceMap);
        LifeWashReserveShelfMainTitleOpt opt = new LifeWashReserveShelfMainTitleOpt();
        LifeWashReserveShelfMainTitleOpt.Config config = new LifeWashReserveShelfMainTitleOpt.Config();
        Param param = Param.builder().build();
        ShelfMainTitleVO compute = opt.compute(context, param, config);
        System.out.println(JSON.toJSONString(compute));
        Assert.notNull(compute);
    }
}
