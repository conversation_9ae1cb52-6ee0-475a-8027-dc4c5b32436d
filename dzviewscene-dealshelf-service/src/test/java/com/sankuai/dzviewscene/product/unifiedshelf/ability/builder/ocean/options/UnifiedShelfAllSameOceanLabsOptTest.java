package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.options;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.options.UnifiedShelfAllSameOceanLabsOpt.Config;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfAllSameOceanLabsOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private UnifiedShelfAllSameOceanLabsOpt.Param param;

    @Mock
    private Config config;

    @Test
    public void testComputeFieldsIsNotEmptyAndCategoryIdAndPoiIdAreFalse() throws Throwable {
        UnifiedShelfAllSameOceanLabsOpt opt = new UnifiedShelfAllSameOceanLabsOpt();
        when(config.getFields()).thenReturn(Arrays.asList("field1", "field2"));
        when(config.isCategoryId()).thenReturn(false);
        when(config.isPoiId()).thenReturn(false);
        Map<String, String> result = opt.compute(context, param, config);
        Map<String, String> expected = new HashMap<>();
        expected.put("field1", "{}");
        expected.put("field2", "{}");
        assertTrue(expected.size() == result.size() && expected.keySet().stream().allMatch(k -> result.containsKey(k) && result.get(k).equals(expected.get(k))));
    }

    @Test
    public void testComputeFieldsIsEmpty() throws Throwable {
        UnifiedShelfAllSameOceanLabsOpt opt = new UnifiedShelfAllSameOceanLabsOpt();
        when(config.getFields()).thenReturn(null);
        Map<String, String> result = opt.compute(context, param, config);
        assertEquals(new HashMap<>(), result);
    }

    @Test
    public void testComputeFieldsIsNotEmptyAndCategoryIdIsTrueAndPoiIdIsFalse() throws Throwable {
        UnifiedShelfAllSameOceanLabsOpt opt = new UnifiedShelfAllSameOceanLabsOpt();
        when(config.getFields()).thenReturn(Arrays.asList("field1", "field2"));
        when(config.isCategoryId()).thenReturn(true);
        when(config.isPoiId()).thenReturn(false);
        Map<String, String> result = opt.compute(context, param, config);
        Map<String, String> expected = new HashMap<>();
        expected.put("field1", "{\"cat_id\":0}");
        expected.put("field2", "{\"cat_id\":0}");
        assertTrue(expected.size() == result.size() && expected.keySet().stream().allMatch(k -> result.containsKey(k) && result.get(k).equals(expected.get(k))));
    }

    @Test
    public void testComputeFieldsIsNotEmptyAndCategoryIdIsFalseAndPoiIdIsTrue() throws Throwable {
        UnifiedShelfAllSameOceanLabsOpt opt = new UnifiedShelfAllSameOceanLabsOpt();
        when(config.getFields()).thenReturn(Arrays.asList("field1", "field2"));
        when(config.isCategoryId()).thenReturn(false);
        when(config.isPoiId()).thenReturn(true);
        Map<String, String> result = opt.compute(context, param, config);
        Map<String, String> expected = new HashMap<>();
        expected.put("field1", "{\"poi_id\":0}");
        expected.put("field2", "{\"poi_id\":0}");
        assertTrue(expected.size() == result.size() && expected.keySet().stream().allMatch(k -> result.containsKey(k) && result.get(k).equals(expected.get(k))));
    }

    @Test
    public void testComputeFieldsIsNotEmptyAndCategoryIdAndPoiIdAreTrue() throws Throwable {
        UnifiedShelfAllSameOceanLabsOpt opt = new UnifiedShelfAllSameOceanLabsOpt();
        when(config.getFields()).thenReturn(Arrays.asList("field1", "field2"));
        when(config.isCategoryId()).thenReturn(true);
        when(config.isPoiId()).thenReturn(true);
        Map<String, String> result = opt.compute(context, param, config);
        Map<String, String> expected = new HashMap<>();
        expected.put("field1", "{\"cat_id\":0,\"poi_id\":0}");
        expected.put("field2", "{\"cat_id\":0,\"poi_id\":0}");
        assertTrue(expected.size() == result.size() && expected.keySet().stream().allMatch(k -> result.containsKey(k) && result.get(k).equals(expected.get(k))));
    }
}
