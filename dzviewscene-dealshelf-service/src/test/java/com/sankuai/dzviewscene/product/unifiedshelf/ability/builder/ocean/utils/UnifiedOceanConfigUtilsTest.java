package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.dp.config.LionObject;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfOceanVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.bean.UnifiedShelfOceanCfgBean;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

public class UnifiedOceanConfigUtilsTest {

    private MockedStatic<UnifiedOceanConfigUtils> mockedUnifiedOceanConfigUtils;

    private MockedStatic<PlatformUtil> mockedPlatformUtil;

    @Before
    public void setUp() throws Exception {
        mockedUnifiedOceanConfigUtils = Mockito.mockStatic(UnifiedOceanConfigUtils.class);
        mockedPlatformUtil = Mockito.mockStatic(PlatformUtil.class);
    }

    @After
    public void tearDown() throws Exception {
        mockedUnifiedOceanConfigUtils.close();
        mockedPlatformUtil.close();
    }

    /**
     * Test getShelfOceanByPlatform when shelfOceanConfigConfigObject is null
     */
    @Test
    public void testGetShelfOceanByPlatform_ShelfOceanConfigConfigObjectNull() throws Throwable {
        mockedUnifiedOceanConfigUtils.when(() -> UnifiedOceanConfigUtils.getShelfOceanByPlatform(anyInt())).thenCallRealMethod();
        // Use reflection to set shelfOceanConfigConfigObject to null
        java.lang.reflect.Field field = UnifiedOceanConfigUtils.class.getDeclaredField("shelfOceanConfigConfigObject");
        field.setAccessible(true);
        field.set(null, null);
        // act
        ShelfOceanVO result = UnifiedOceanConfigUtils.getShelfOceanByPlatform(1);
        // assert
        assertNull(result);
        // Reset the field
        field.set(null, LionObject.create("com.sankuai.dzviewscene.dealshelf.poi.shelf.ocean.config", UnifiedShelfOceanCfgBean.class));
    }

    /**
     * Test getShelfOceanByPlatform when shelfOceanConfigConfigObject.getObject() is null
     */
    @Test
    public void testGetShelfOceanByPlatform_ShelfOceanConfigConfigObjectGetObjectNull() throws Throwable {
        mockedUnifiedOceanConfigUtils.when(() -> UnifiedOceanConfigUtils.getShelfOceanByPlatform(anyInt())).thenCallRealMethod();
        LionObject<UnifiedShelfOceanCfgBean> mockLionObject = mock(LionObject.class);
        when(mockLionObject.getObject()).thenReturn(null);
        // Use reflection to set shelfOceanConfigConfigObject
        java.lang.reflect.Field field = UnifiedOceanConfigUtils.class.getDeclaredField("shelfOceanConfigConfigObject");
        field.setAccessible(true);
        field.set(null, mockLionObject);
        // act
        ShelfOceanVO result = UnifiedOceanConfigUtils.getShelfOceanByPlatform(1);
        // assert
        assertNull(result);
        // Reset the field
        field.set(null, LionObject.create("com.sankuai.dzviewscene.dealshelf.poi.shelf.ocean.config", UnifiedShelfOceanCfgBean.class));
    }

    /**
     * Test getShelfOceanByPlatform when platform is MT
     */
    @Test
    public void testGetShelfOceanByPlatform_PlatformIsMT() throws Throwable {
        mockedUnifiedOceanConfigUtils.when(() -> UnifiedOceanConfigUtils.getShelfOceanByPlatform(anyInt())).thenCallRealMethod();
        mockedPlatformUtil.when(() -> PlatformUtil.isMT(anyInt())).thenReturn(true);
        LionObject<UnifiedShelfOceanCfgBean> mockLionObject = mock(LionObject.class);
        UnifiedShelfOceanCfgBean mockBean = mock(UnifiedShelfOceanCfgBean.class);
        ShelfOceanVO mockMtOcean = mock(ShelfOceanVO.class);
        when(mockLionObject.getObject()).thenReturn(mockBean);
        when(mockBean.getMtOcean()).thenReturn(mockMtOcean);
        // Use reflection to set shelfOceanConfigConfigObject
        java.lang.reflect.Field field = UnifiedOceanConfigUtils.class.getDeclaredField("shelfOceanConfigConfigObject");
        field.setAccessible(true);
        field.set(null, mockLionObject);
        // act
        ShelfOceanVO result = UnifiedOceanConfigUtils.getShelfOceanByPlatform(200);
        // assert
        assertSame(mockMtOcean, result);
        // Reset the field
        field.set(null, LionObject.create("com.sankuai.dzviewscene.dealshelf.poi.shelf.ocean.config", UnifiedShelfOceanCfgBean.class));
    }
}
