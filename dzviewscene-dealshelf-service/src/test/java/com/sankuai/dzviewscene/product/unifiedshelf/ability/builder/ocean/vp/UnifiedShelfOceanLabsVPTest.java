package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.vp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import java.util.Collections;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfOceanLabsVPTest {

    /**
     * 测试 getPoiId 方法，当 platform 等于 MT 时，应返回 mtPoiId
     */
    @Test
    public void testGetPoiIdWhenPlatformIsMT() throws Throwable {
        // arrange
        UnifiedShelfOceanLabsVP.Param param = mock(UnifiedShelfOceanLabsVP.Param.class);
        when(param.getPlatform()).thenReturn(VCPlatformEnum.MT.getType());
        when(param.getMtPoiId()).thenReturn(123L);
        UnifiedShelfOceanLabsVP<Object> vp = new UnifiedShelfOceanLabsVP<Object>() {

            @Override
            protected int getCategoryId(Param param) {
                return 0;
            }

            @Override
            public Map<String, String> compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, Param param, Object o) {
                // Since this is a dummy implementation for testing, return an empty map
                return Collections.emptyMap();
            }
        };
        // act
        long result = vp.getPoiId(param);
        // assert
        assertEquals(123L, result);
    }

    /**
     * 测试 getPoiId 方法，当 platform 不等于 MT 时，应返回 dpPoiId
     */
    @Test
    public void testGetPoiIdWhenPlatformIsNotMT() throws Throwable {
        // arrange
        UnifiedShelfOceanLabsVP.Param param = mock(UnifiedShelfOceanLabsVP.Param.class);
        when(param.getPlatform()).thenReturn(VCPlatformEnum.DP.getType());
        when(param.getDpPoiId()).thenReturn(456L);
        UnifiedShelfOceanLabsVP<Object> vp = new UnifiedShelfOceanLabsVP<Object>() {

            @Override
            protected int getCategoryId(Param param) {
                return 0;
            }

            @Override
            public Map<String, String> compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, Param param, Object o) {
                // Since this is a dummy implementation for testing, return an empty map
                return Collections.emptyMap();
            }
        };
        // act
        long result = vp.getPoiId(param);
        // assert
        assertEquals(456L, result);
    }

    /**
     * 测试 getCategoryId 方法，当 shop 为 null 时
     */
    @Test
    public void testGetCategoryIdWhenShopIsNull() throws Throwable {
        // arrange
        UnifiedShelfOceanLabsVP.Param param = UnifiedShelfOceanLabsVP.Param.builder().shop(null).build();
        // act
        int result = new UnifiedShelfOceanLabsVP() {

            @Override
            public Object compute(com.sankuai.athena.viewscene.framework.ActivityCxt p0, Object p1, Object p2) {
                // No-op implementation for the abstract method
                return null;
            }
        }.getCategoryId(param);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 getCategoryId 方法，当 shop 不为 null 时
     */
    @Test
    public void testGetCategoryIdWhenShopIsNotNull() throws Throwable {
        // arrange
        ShopM shop = new ShopM();
        shop.setCategory(1);
        UnifiedShelfOceanLabsVP.Param param = UnifiedShelfOceanLabsVP.Param.builder().shop(shop).build();
        // act
        int result = new UnifiedShelfOceanLabsVP() {

            @Override
            public Object compute(com.sankuai.athena.viewscene.framework.ActivityCxt p0, Object p1, Object p2) {
                // No-op implementation for the abstract method
                return null;
            }
        }.getCategoryId(param);
        // assert
        assertEquals(1, result);
    }
}
