package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfProductAreaVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreTextVP;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.framework.monitor.FloorItemsMonitor;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSkuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedProductAreaBuilderTest {

    @InjectMocks
    private UnifiedProductAreaBuilder unifiedProductAreaBuilder;

    @Mock
    private FloorItemsMonitor floorItemsMonitor;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private UnifiedProductAreaBuilder.Request request;

    @Mock
    private UnifiedProductAreaBuilder.Config config;

    @Mock
    private ShelfGroupM shelfGroupM;

    @Mock
    private ProductGroupM productGroupM;

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        when(activityCxt.getSource(ShelfMainDataAssembler.CODE)).thenReturn(shelfGroupM);
        when(productGroupM.getProducts()).thenReturn(Lists.newArrayList(new ProductM()));
    }

    /**
     * 测试正常路径：ShelfGroupM 不为空且有产品，groupNames 不为空，buildShelfFilterProductAreaVOs 返回非空列表。
     */
    @Test
    public void testBuildNormalPath() throws Throwable {
        // arrange
        when(shelfGroupM.getProductGroupMs()).thenReturn(Collections.singletonMap("group1", productGroupM));
        when(request.getGroupNames()).thenReturn(Lists.newArrayList("group1"));
        // act
        CompletableFuture<List<ShelfFilterProductAreaVO>> result = unifiedProductAreaBuilder.build(activityCxt, request, config);
        // assert
        assertNotNull(result);
        assertNotNull(result.get());
        assertFalse(result.get().isEmpty());
    }

    /**
     * 测试异常路径：ShelfGroupM 为空或没有产品。
     */
    @Test
    public void testBuildShelfGroupMEmpty() throws Throwable {
        // arrange
        when(shelfGroupM.getProductGroupMs()).thenReturn(null);
        // act
        CompletableFuture<List<ShelfFilterProductAreaVO>> result = unifiedProductAreaBuilder.build(activityCxt, request, config);
        // assert
        assertNotNull(result);
        assertTrue(result.get().isEmpty());
    }

    /**
     * 测试异常路径：groupNames 为空，抛出 BusinessException。
     */
    @Test(expected = BusinessException.class)
    public void testBuildGroupNamesEmpty() throws Throwable {
        // arrange
        when(shelfGroupM.getProductGroupMs()).thenReturn(Collections.singletonMap("group1", productGroupM));
        when(request.getGroupNames()).thenReturn(null);
        // act
        unifiedProductAreaBuilder.build(activityCxt, request, config);
    }

    /**
     * 测试groupByFilter方法，当group2GroupsCompletableFuture为null时
     */
    @Test
    public void testGroupByFilterWithNullCompletableFuture() throws Throwable {
        // arrange
        String groupName = "testGroup";
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterM.setFilters(Lists.newArrayList(filterBtnM));
        ProductGroupM productGroupM = new ProductGroupM();
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        when(activityContext.getAttachment(anyString())).thenReturn(null);
        // act
        Map<Long, List<ProductM>> result = (Map<Long, List<ProductM>>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "groupByFilter", activityContext, groupName, filterM, productGroupM, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试groupByFilter方法，当group2GroupsCompletableFuture不为null但groupName不存在时
     */
    @Test
    public void testGroupByFilterWithGroupNameNotExists() throws Throwable {
        // arrange
        String groupName = "testGroup";
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterM.setFilters(Lists.newArrayList(filterBtnM));
        ProductGroupM productGroupM = new ProductGroupM();
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        when(activityContext.getAttachment(anyString())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        // act
        Map<Long, List<ProductM>> result = (Map<Long, List<ProductM>>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "groupByFilter", activityContext, groupName, filterM, productGroupM, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试groupTotalProductByFilter方法，当groupedProducts为空时
     */
    @Test
    public void testGroupTotalProductByFilterWhenGroupedProductsIsEmpty() throws Throwable {
        // arrange
        Map<Long, List<ProductM>> groupedProducts = Collections.emptyMap();
        ProductGroupM productGroupM = null;
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        // act
        Map<Long, List<ProductM>> result = (Map<Long, List<ProductM>>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "groupTotalProductByFilter", groupedProducts, productGroupM, config);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试groupTotalProductByFilter方法，当productGroupM为空时
     */
    @Test
    public void testGroupTotalProductByFilterWhenProductGroupMIsNull() throws Throwable {
        // arrange
        Map<Long, List<ProductM>> groupedProducts = new HashMap<>();
        groupedProducts.put(1L, Collections.singletonList(new ProductM()));
        ProductGroupM productGroupM = null;
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        // act
        Map<Long, List<ProductM>> result = (Map<Long, List<ProductM>>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "groupTotalProductByFilter", groupedProducts, productGroupM, config);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试groupTotalProductByFilter方法，当productGroupM的products为空时
     */
    @Test
    public void testGroupTotalProductByFilterWhenProductGroupMProductsIsEmpty() throws Throwable {
        // arrange
        Map<Long, List<ProductM>> groupedProducts = new HashMap<>();
        groupedProducts.put(1L, Collections.singletonList(new ProductM()));
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(Collections.emptyList());
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        // act
        Map<Long, List<ProductM>> result = (Map<Long, List<ProductM>>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "groupTotalProductByFilter", groupedProducts, productGroupM, config);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试groupTotalProductByFilter方法，正常情况
     */
    @Test
    public void testGroupTotalProductByFilterNormal() throws Throwable {
        // arrange
        Map<Long, List<ProductM>> groupedProducts = new HashMap<>();
        List<ProductM> products = Collections.singletonList(new ProductM());
        groupedProducts.put(1L, products);
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(products);
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        // act
        Map<Long, List<ProductM>> result = (Map<Long, List<ProductM>>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "groupTotalProductByFilter", groupedProducts, productGroupM, config);
        // assert
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertNotNull(result.get(1L));
    }

    /**
     * 测试当filterProducts为空时，应返回空列表
     */
    @Test
    public void testReadProductFromProductGroupWhenFilterProductsIsEmpty() throws Throwable {
        // arrange
        List<ProductM> paddingProducts = new ArrayList<>();
        List<ProductM> filterProducts = new ArrayList<>();
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        // act
        List<ProductM> result = (List<ProductM>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "readProductFromProductGroup", paddingProducts, filterProducts, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当filterProducts非空但没有匹配的paddingProducts时，应返回空列表
     */
    @Test
    public void testReadProductFromProductGroupWhenNoMatchingPaddingProducts() throws Throwable {
        // arrange
        List<ProductM> paddingProducts = new ArrayList<>();
        List<ProductM> filterProducts = new ArrayList<>();
        filterProducts.add(productM);
        UnifiedProductAreaBuilder.Config config = new UnifiedProductAreaBuilder.Config();
        // act
        List<ProductM> result = (List<ProductM>) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "readProductFromProductGroup", paddingProducts, filterProducts, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试正常路径：构造层次商品列表
     */
    @Test
    public void testBuildProductArea() throws Throwable {
        // arrange
        ProductHierarchyNodeM root = new ProductHierarchyNodeM();
        ProductHierarchyNodeM node1 = new ProductHierarchyNodeM();
        node1.setProductId(1);
        node1.setProductType(ProductTypeEnum.DEAL.getType());
        node1.setParent(root);
        ProductHierarchyNodeM node2 = new ProductHierarchyNodeM();
        node2.setProductId(2);
        node2.setProductType(ProductTypeEnum.DEAL.getType());
        node2.setParent(root);
        ProductHierarchyNodeM node3 = new ProductHierarchyNodeM();
        node3.setProductId(3);
        node3.setProductType(ProductTypeEnum.SKU.getType());
        node3.setParent(node2);
        ProductHierarchyNodeM node4 = new ProductHierarchyNodeM();
        node4.setProductId(4);
        node4.setProductType(ProductTypeEnum.SKU.getType());
        node4.setParent(node2);
        node2.setChildren(Lists.newArrayList(node3, node4));
        root.setChildren(Lists.newArrayList(node1, node2));

        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProductHierarchyRoot(root);
        ProductM productM1 = new ProductM();
        productM1.setProductId(1);
        productM1.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM productM2 = new ProductM();
        productM2.setProductId(2);
        productM2.setProductType(ProductTypeEnum.DEAL.getType());
        ProductSkuM productSkuM1 = new ProductSkuM();
        productSkuM1.setProductId(2);
        productSkuM1.setSkuName("sku1");
        productSkuM1.setSkuId(3);
        ProductSkuM productSkuM2 = new ProductSkuM();
        productSkuM1.setProductId(2);
        productSkuM1.setSkuName("sku2");
        productSkuM1.setSkuId(4);
        productM2.setProductSkuList(Lists.newArrayList(productSkuM1, productSkuM2));
        productGroupM.setProducts(Lists.newArrayList(productM1, productM2));

        when(shelfGroupM.getProductGroupMs()).thenReturn(Collections.singletonMap("group1", productGroupM));
        when(request.getGroupNames()).thenReturn(Collections.singletonList("group1"));
        when(config.isUseProductHierarchy()).thenReturn(true);
        doAnswer(invocation -> {
            // 模拟无返回值的行为
            return null;
        }).when(floorItemsMonitor).doMonitor(eq(activityCxt), any(ShelfItemVO.class));

        // act
        CompletableFuture<List<ShelfFilterProductAreaVO>> result = unifiedProductAreaBuilder.build(activityCxt, request, config);

        // assert
        assertNotNull(result);
        assertFalse(result.get().isEmpty());
    }

    /**
     * 测试moreText方法，正常情况下应返回正确的文本
     */
    @Test
    public void testMoreText() throws Throwable {
        // arrange
        ShelfProductAreaVO shelfProductAreaVO = new ShelfProductAreaVO();
        ShelfItemVO shelfItemVO = new ShelfItemVO();
        shelfItemVO.setSubItems(Lists.newArrayList(new ShelfItemVO(), new ShelfItemVO()));
        shelfItemVO.setDefaultShowNum(2);
        List<ShelfItemVO> items = Lists.newArrayList(new ShelfItemVO(), shelfItemVO);
        shelfProductAreaVO.setItems(items);
        shelfProductAreaVO.setDefaultShowNum(4);
        
        List<ProductM> productMs = Lists.newArrayList(new ProductM(), new ProductM());
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setTotal(10);
        
        ProductAreaMoreTextVP<?> moreTextVP = mock(ProductAreaMoreTextVP.class);
        when(moreTextVP.execute(eq(activityCxt), any(ProductAreaMoreTextVP.Param.class))).thenReturn("更多8个团购");

        PmfExecutionHelper pmfExecutionHelper = mock(PmfExecutionHelper.class);
        ReflectionTestUtils.setField(unifiedProductAreaBuilder, "pmfExecutionHelper", pmfExecutionHelper);
        when(pmfExecutionHelper.findVPoint(eq(activityCxt), anyString(), anyString())).thenReturn(moreTextVP);

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(unifiedProductAreaBuilder, "moreText", 
                activityCxt, shelfProductAreaVO, productMs, 1L, productGroupM);
        
        // assert
        assertEquals("更多8个团购", result);
    }
}
