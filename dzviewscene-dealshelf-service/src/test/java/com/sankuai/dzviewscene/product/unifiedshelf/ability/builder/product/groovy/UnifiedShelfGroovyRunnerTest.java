package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfGroovyRunnerTest {

    private static final String TEST_SK = "test_sk";
    private static final String DEFAULT_SK = "default";
    
    @Mock
    private UnifiedShelfGroovyDouHuSkExc<String> mockExcA;
    @Mock
    private UnifiedShelfGroovyDouHuSkExc<String> mockExcB;
    
    private TestUnifiedShelfGroovyRunner runner;
    private UnifiedShelfGroovyRunnerRequest request;
    
    @Before
    public void setUp() {
        runner = new TestUnifiedShelfGroovyRunner();
        request = new UnifiedShelfGroovyRunnerRequest();
        request.setPreview(false);
    }
    
    /**
     * 测试空请求的情况
     */
    @Test
    public void testGetResult_NullRequest() {
        assertNull(runner.getResult(null));
    }

    /**
     * 测试匹配到斗斛执行器的情况
     */
    @Test
    public void testGetResult_MatchedDouHu() {
        DouHuM douHuM = new DouHuM();
        douHuM.setSk(TEST_SK);
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        request.setDouHuList(douHuList);
        
        when(mockExcA.getResult(any())).thenReturn("test_result");
        
        String result = runner.getResult(request);
        
        assertNotNull(result);
        verify(mockExcA, times(1)).getResult(any());
    }
    
    /**
     * 测试使用默认执行器的情况
     */
    @Test
    public void testGetResult_UseDefault() {
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("non_existent_sk");
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        request.setDouHuList(douHuList);
        
        when(mockExcA.getResult(any())).thenReturn("test_result");
        
        String result = runner.getResult(request);
        
        assertNotNull(result);
        verify(mockExcA, times(1)).getResult(any());
    }

      /**
     * 测试斗斛列表为空的情况
     */
    @Test
    public void testGetResult_EmptyDouHuList() {
        request.setDouHuList(new ArrayList<>());
        when(mockExcA.getResult(any())).thenReturn("test_result");
        
        String result = runner.getResult(request);
        
        assertNotNull(result);
        verify(mockExcA, times(1)).getResult(any());
    }
    
    /**
     * 测试斗斛列表中包含null的情况
     */
    @Test
    public void testGetResult_ContainNullDouHu() {
        DouHuM douHuM = new DouHuM();
        douHuM.setSk(TEST_SK);
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        douHuList.add(null);
        request.setDouHuList(douHuList);
        
        when(mockExcA.getResult(any())).thenReturn("test_result");
        
        String result = runner.getResult(request);
        
        assertNotNull(result);
        verify(mockExcA, times(1)).getResult(any());
    }

    /**
     * 测试斗斛列表
     */
    @Test
    public void testGetResult_hasResult() {
        DouHuM douHuA = new DouHuM();
        douHuA.setSk("douhu_a");
        DouHuM douHuB = new DouHuM();
        douHuB.setSk("douhu_b");
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuA);
        douHuList.add(douHuB);

        UnifiedShelfGroovyRunnerRequest request = new UnifiedShelfGroovyRunnerRequest();
        request.setDouHuList(douHuList);
        
        TestDouHuRunner douhuRunner = new TestDouHuRunner();
        ItemSubTitleVO result = douhuRunner.getResult(request);
        
        assertNotNull(result);
        assertEquals("testA", result.getTags().get(0).getText());
    }
    
    /**
     * 测试斗斛执行器为空的情况
     */
    @Test
    public void testGetResult_EmptyExcMap() {
        when(mockExcA.getApplyDouHuSks()).thenReturn(new ArrayList<>());
        
        String result = runner.getResult(request);
        
        assertNull(result);
    }
    
    /**
     * 测试斗斛执行器中包含null的情况
     */
    @Test
    public void testGetResult_ContainNullExc() {
        when(mockExcA.getApplyDouHuSks()).thenReturn(new ArrayList<>());
        when(mockExcA.getResult(any())).thenReturn("test_result");
        
        String result = runner.getResult(request);
        
        assertNotNull(result);
        verify(mockExcA, times(1)).getResult(any());
    }
    
    /**
     * 测试获取默认key的逻辑
     */
    @Test
    public void testGetDefaultKey() {
        Map<String, UnifiedShelfGroovyDouHuSkExc<String>> douHuExcMap = new HashMap<>();
        douHuExcMap.put("default", mockExcA);
        
        String key = runner.getDefaultKey(request, douHuExcMap);
        assertEquals("default", key);
    }
    
    /**
     * 测试获取以_a结尾的key
     */
    @Test
    public void testGetDefaultKey_EndWithA() {
        Map<String, UnifiedShelfGroovyDouHuSkExc<String>> douHuExcMap = new HashMap<>();
        douHuExcMap.put("test_a", mockExcA);
        
        String key = runner.getDefaultKey(request, douHuExcMap);
        assertEquals("test_a", key);
    }
    
    private class TestUnifiedShelfGroovyRunner extends UnifiedShelfGroovyRunner<String> {
        @Override
        protected List<UnifiedShelfGroovyDouHuSkExc<String>> getDouHuExcList() {
            List<UnifiedShelfGroovyDouHuSkExc<String>> list = new ArrayList<>();
            list.add(mockExcA);
            return list;
        }
    }

    private class TestDouHuAExc implements UnifiedShelfGroovyDouHuSkExc<ItemSubTitleVO> {
        @Override
        public List<String> getApplyDouHuSks() {
            return Lists.newArrayList("douhu_a");
        }

        @Override
        public ItemSubTitleVO getResult(UnifiedShelfGroovyRunnerRequest request) {
            ItemSubTitleVO result = new ItemSubTitleVO();
            result.setJoinType(0);
            
            StyleTextModel styleTextModel = new StyleTextModel();
            styleTextModel.setText("testA");
            styleTextModel.setStyle(TextStyleEnum.TEXT_GRAY.getType());
            result.setTags(Lists.newArrayList(styleTextModel));

            return result;
        }
    }

    private class TestDouHuBExc implements UnifiedShelfGroovyDouHuSkExc<ItemSubTitleVO> {
        @Override
        public List<String> getApplyDouHuSks() {
            return Lists.newArrayList("douhu_b");
        }

        @Override
        public ItemSubTitleVO getResult(UnifiedShelfGroovyRunnerRequest request) {
            ItemSubTitleVO result = new ItemSubTitleVO();
            result.setJoinType(0);
            
            StyleTextModel styleTextModel = new StyleTextModel();
            styleTextModel.setText("testB");
            styleTextModel.setStyle(TextStyleEnum.TEXT_GRAY.getType());
            result.setTags(Lists.newArrayList(styleTextModel));

            return result;
        }
    }

    private class TestDouHuRunner extends UnifiedShelfGroovyRunner<ItemSubTitleVO> {
        @Override
        protected List<UnifiedShelfGroovyDouHuSkExc<ItemSubTitleVO>> getDouHuExcList() {
            return Lists.newArrayList(new TestDouHuAExc(),
                new TestDouHuBExc());
        }
    }

}