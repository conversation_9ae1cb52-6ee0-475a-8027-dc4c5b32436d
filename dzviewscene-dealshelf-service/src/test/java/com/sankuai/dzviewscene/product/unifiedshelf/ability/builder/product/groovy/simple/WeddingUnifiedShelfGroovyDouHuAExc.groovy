package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.simple

import com.google.common.collect.Lists
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.UnifiedShelfGroovyDouHuSkExc
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.UnifiedShelfGroovyRunnerRequest
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum

/**
 * 婚纱摄影货架副标题，斗斛A策略
 */
class WeddingUnifiedShelfGroovyDouHuAExc implements UnifiedShelfGroovyDouHuSkExc<ItemSubTitleVO> {
    @Override
    List<String> getApplyDouHuSks() {
        return Lists.asList("EXP2024100800001_a")
    }

    @Override
    ItemSubTitleVO getResult(UnifiedShelfGroovyRunnerRequest request) {
        def result = new ItemSubTitleVO()
        result.setJoinType(0) // 使用竖线分隔
        def tags = new ArrayList<StyleTextModel>()
        def product = request.productM
        // 获取必要的属性值
        def dressNum = product.getAttrValueFromAllAttrs("dressNum")
        def intensiveRepairNum = product.getAttrValueFromAllAttrs("intensiveRepairNum")
        def photoThemeCount = product.getAttrValueFromAllAttrs("photoThemeCount")
        // 构建副标题内容
        def subtitles = []
        // 添加精修张数和景点数量
        subtitles << "精修${intensiveRepairNum}张"
        // 服装套数
        subtitles << "服装${dressNum}套/人"
        // 景点数量
        subtitles << "景点${photoThemeCount}个"
        // 转换为StyleTextModel列表
        subtitles.each { text ->
            def model = new StyleTextModel()
            model.setText(text)
            model.setStyle(TextStyleEnum.TEXT_GRAY.getType())
            tags.add(model)
        }
        result.setTags(tags)
        return result
    }
}
