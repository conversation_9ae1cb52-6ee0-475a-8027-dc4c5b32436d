package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.activityendtime;

import com.dianping.gmkt.activ.api.enums.ExposureActivityTypeEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemActivityVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class UnifiedShelfItemDefActivityOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private UnifiedShelfItemDefActivityOpt.Param param;
    @Mock
    private ProductM productM;
    private UnifiedShelfItemDefActivityOpt.Config config;
    private UnifiedShelfItemDefActivityOpt unifiedShelfItemDefActivityOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedShelfItemDefActivityOpt = new UnifiedShelfItemDefActivityOpt();
        config = new UnifiedShelfItemDefActivityOpt.Config();
        when(param.getProductM()).thenReturn(productM);
    }

    @Test
    public void test_Skill() throws Throwable {
        ProductActivityM activityM = new ProductActivityM();
        activityM.setShelfActivityType(ExposureActivityTypeEnum.SEC_KILL.getType());
        Map<String, Object> map = new HashMap<>();
        map.put("RainbowSecKillSource", true);
        activityM.setActivityExtraAttrs(map);
        when(productM.getActivities()).thenReturn(Lists.newArrayList(activityM));
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        PromoItemM promoItemM = new PromoItemM();
        PromoItemTextM promoItemTextM = new PromoItemTextM();
        promoItemTextM.setPromoDivideType("SECOND_KILL");
        promoItemM.setPromoItemText(promoItemTextM);
        promoPriceM.setPromoItemList(Lists.newArrayList(promoItemM));
        when(productM.getBestPromoPrice()).thenReturn(promoPriceM);
        // act
        ShelfItemActivityVO result = unifiedShelfItemDefActivityOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
    }
}
