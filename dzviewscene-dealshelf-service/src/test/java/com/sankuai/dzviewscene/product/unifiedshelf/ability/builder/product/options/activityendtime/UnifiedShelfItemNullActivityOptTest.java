package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.activityendtime;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemActivityVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemActivityVP.Param;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertNull;

public class UnifiedShelfItemNullActivityOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;

    private UnifiedShelfItemNullActivityOpt unifiedShelfItemNullActivityOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedShelfItemNullActivityOpt = new UnifiedShelfItemNullActivityOpt();
    }

    /**
     * 测试 compute 方法，期望返回 null
     */
    @Test
    public void testComputeExpectNull() {
        // arrange
        Void someValue = null;

        // act
        ShelfItemActivityVO result = unifiedShelfItemNullActivityOpt.compute(mockActivityCxt, mockParam, someValue);

        // assert
        assertNull("期望返回 null", result);
    }
}
