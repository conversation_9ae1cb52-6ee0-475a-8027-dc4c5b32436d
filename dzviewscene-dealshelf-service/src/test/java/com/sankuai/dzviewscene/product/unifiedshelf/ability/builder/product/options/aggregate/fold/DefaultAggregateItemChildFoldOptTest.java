package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.fold;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemChildFoldVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * DefaultAggregateItemChildFoldOpt的单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class DefaultAggregateItemChildFoldOptTest {

    @InjectMocks
    private DefaultAggregateItemChildFoldOpt childFoldOpt;

    @Mock
    private ActivityCxt activityCxt;

    private AggregateItemChildFoldVP.Param param;
    private DefaultAggregateItemChildFoldOpt.Config config;
    private ShelfItemVO shelfItemVO;
    private ProductHierarchyNodeM nodeM;

    @Before
    public void setUp() {
        // 初始化配置
        config = new DefaultAggregateItemChildFoldOpt.Config();
        
        // 初始化参数
        param = AggregateItemChildFoldVP.Param.builder().build();
        shelfItemVO = new ShelfItemVO();
        nodeM = new ProductHierarchyNodeM();
        
        param.setShelfItemVO(shelfItemVO);
        param.setNodeM(nodeM);
    }

    /**
     * 测试正常场景 - 子项数量大于默认展示数
     */
    @Test
    public void testComputeWithMoreChildren() {
        // arrange
        config.setDefaultShowNum(2);
        shelfItemVO.setSubItems(Lists.newArrayList(
                new ShelfItemVO(),
                new ShelfItemVO(),
                new ShelfItemVO()
        ));

        // act
        childFoldOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(2, shelfItemVO.getDefaultShowNum());
        assertEquals("全部3个套餐", shelfItemVO.getMoreText());
    }

    /**
     * 测试正常场景 - 自定义文本格式
     */
    @Test
    public void testComputeWithCustomTextFormat() {
        // arrange
        config.setDefaultShowNum(2);
        config.setTextFormat("查看全部%d个");
        shelfItemVO.setSubItems(Lists.newArrayList(
                new ShelfItemVO(),
                new ShelfItemVO(),
                new ShelfItemVO()
        ));

        // act
        childFoldOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(2, shelfItemVO.getDefaultShowNum());
        assertEquals("查看全部3个", shelfItemVO.getMoreText());
    }

    /**
     * 测试子项数量小于等于默认展示数的场景
     */
    @Test
    public void testComputeWithLessChildren() {
        // arrange
        config.setDefaultShowNum(3);
        shelfItemVO.setSubItems(Lists.newArrayList(
                new ShelfItemVO(),
                new ShelfItemVO()
        ));

        // act
        childFoldOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(3, shelfItemVO.getDefaultShowNum());
        assertNull(shelfItemVO.getMoreText());
    }

    /**
     * 测试defaultShowNum小于等于0的场景
     */
    @Test
    public void testComputeWithInvalidDefaultShowNum() {
        // arrange
        config.setDefaultShowNum(0);
        shelfItemVO.setSubItems(Lists.newArrayList(
                new ShelfItemVO(),
                new ShelfItemVO(),
                new ShelfItemVO()
        ));

        // act
        Void result = childFoldOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
        assertEquals(0, shelfItemVO.getDefaultShowNum());
        assertNull(shelfItemVO.getMoreText());
    }
}
