package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PicAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * ChildrenHideProcessHandler单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ChildrenHideProcessHandlerTest {

    @InjectMocks
    private ChildrenHideProcessHandler childrenHideProcessHandler;

    @Mock
    private ActivityCxt activityCxt;

    private ShelfItemVO shelfItemVO;

    @Before
    public void setUp() {
        // 初始化参数
        shelfItemVO = new ShelfItemVO();
    }

    /**
     * 测试getName方法
     */
    @Test
    public void testGetName() {
        // act
        String name = childrenHideProcessHandler.getName();
        
        // assert
        assertEquals("ChildrenHideProcessHandler", name);
    }

    /**
     * 测试process方法，当子项为空时
     */
    @Test
    public void testProcessWhenSubItemsEmpty() {
        // arrange
        shelfItemVO.setSubItems(new ArrayList<>());
        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .shelfItemVO(shelfItemVO)
                .build();
        
        // act
        ShelfItemVO result = childrenHideProcessHandler.process(activityCxt, context);
        
        // assert
        assertNotNull(result);
        assertEquals(shelfItemVO, result);
        assertTrue(result.getSubItems().isEmpty());
    }

    /**
     * 测试process方法，当子项为null时
     */
    @Test
    public void testProcessWhenSubItemsNull() {
        // arrange
        shelfItemVO.setSubItems(null);
        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .shelfItemVO(shelfItemVO)
                .build();
        
        // act
        ShelfItemVO result = childrenHideProcessHandler.process(activityCxt, context);
        
        // assert
        assertNotNull(result);
        assertEquals(shelfItemVO, result);
        assertNull(result.getSubItems());
    }

    /**
     * 测试process方法，当只有一个子项时
     */
    @Test
    public void testProcessWhenSingleSubItem() {
        // arrange
        ShelfItemVO subItem = new ShelfItemVO();
        subItem.setHeadPic(buildPicAreaVO());
        ItemSubTitleVO productTags = new ItemSubTitleVO();
        subItem.setProductTags(productTags);
        
        shelfItemVO.setSubItems(Lists.newArrayList(subItem));
        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .shelfItemVO(shelfItemVO)
                .build();
        
        // act
        ShelfItemVO result = childrenHideProcessHandler.process(activityCxt, context);
        
        // assert
        assertNotNull(result);
        assertEquals(shelfItemVO, result);
        assertEquals(1, result.getSubItems().size());
        // 验证单个子项的信息没有被隐藏
        assertNotNull(result.getSubItems().get(0).getHeadPic());
    }

    /**
     * 测试process方法，当有多个子项时
     */
    @Test
    public void testProcessWhenMultipleSubItems() {
        // arrange
        ShelfItemVO subItem1 = new ShelfItemVO();
        subItem1.setHeadPic(buildPicAreaVO());
        ItemSubTitleVO productTags1 = new ItemSubTitleVO();
        subItem1.setProductTags(productTags1);
        
        ShelfItemVO subItem2 = new ShelfItemVO();
        subItem2.setHeadPic(buildPicAreaVO());
        ItemSubTitleVO productTags2 = new ItemSubTitleVO();
        subItem2.setProductTags(productTags2);
        
        shelfItemVO.setSubItems(Lists.newArrayList(subItem1, subItem2));
        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .shelfItemVO(shelfItemVO)
                .build();
        
        // act
        ShelfItemVO result = childrenHideProcessHandler.process(activityCxt, context);
        
        // assert
        assertNotNull(result);
        assertEquals(shelfItemVO, result);
        assertEquals(2, result.getSubItems().size());
        // 验证多个子项的信息已被隐藏
        assertNull(result.getSubItems().get(0).getHeadPic());
        assertNull(result.getSubItems().get(0).getProductTags());
        assertNull(result.getSubItems().get(1).getHeadPic());
        assertNull(result.getSubItems().get(1).getProductTags());
    }

    /**
     * 测试hideInfo方法
     */
    @Test
    public void testHideInfo() {
        // arrange
        ShelfItemVO subItem = new ShelfItemVO();
        subItem.setHeadPic(buildPicAreaVO());
        ItemSubTitleVO productTags = new ItemSubTitleVO();
        subItem.setProductTags(productTags);
        
        // 创建一个包含多个子项的ShelfItemVO
        ShelfItemVO parentItem = new ShelfItemVO();
        parentItem.setSubItems(Lists.newArrayList(subItem, new ShelfItemVO()));
        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .shelfItemVO(parentItem)
                .build();
        
        // act
        ShelfItemVO result = childrenHideProcessHandler.process(activityCxt, context);
        
        // assert
        assertNotNull(result);
        assertEquals(2, result.getSubItems().size());
        // 验证子项的信息已被隐藏
        assertNull(result.getSubItems().get(0).getHeadPic());
        assertNull(result.getSubItems().get(0).getProductTags());
    }

    private PicAreaVO buildPicAreaVO() {
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setDefaultPicUrl("defaultPicUrl");
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl("xxxx");
        picAreaVO.setPic(pictureModel);
        picAreaVO.setFloatTags(Lists.newArrayList());
        return picAreaVO;
    }
}
