package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.price;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class DefaultAggregateItemPriceOptTest {

    @InjectMocks
    private DefaultAggregateItemPriceOpt defaultAggregateItemPriceOpt;

    @Mock
    private ActivityCxt activityCxt;

    private DefaultAggregateItemPriceOpt.Config config;
    private DefaultAggregateItemPriceOpt.Param param;

    @Before
    public void setUp() {
        // 初始化配置和参数
        config = new DefaultAggregateItemPriceOpt.Config();
        param = DefaultAggregateItemPriceOpt.Param.builder().build();
    }

    /**
     * 测试计算最低销售价格
     */
    @Test
    public void testComputeWithMultipleChildItems() {
        // arrange
        ShelfItemVO childItem1 = new ShelfItemVO();
        childItem1.setSalePrice("99.99");

        ShelfItemVO childItem2 = new ShelfItemVO();
        childItem2.setSalePrice("49.99");

        ShelfItemVO childItem3 = new ShelfItemVO();
        childItem3.setSalePrice("79.99");

        ShelfItemVO shelfItemVO = new ShelfItemVO();
        shelfItemVO.setSubItems(Arrays.asList(childItem1, childItem2, childItem3));
        param.setShelfItemVO(shelfItemVO);
        // act
        defaultAggregateItemPriceOpt.compute(activityCxt, param, config);

        // assert
        assertEquals("49.99", shelfItemVO.getSalePrice());
        assertEquals("起", shelfItemVO.getSalePriceSuffix());
    }

    /**
     * 测试没有子项的情况
     */
    @Test
    public void testComputeWithNoChildItems() {
        // arrange

        ShelfItemVO shelfItemVO = new ShelfItemVO();
        param.setShelfItemVO(shelfItemVO);

        // act
        defaultAggregateItemPriceOpt.compute(activityCxt, param, config);

        // assert
        assertNull(shelfItemVO.getSalePrice());
        assertEquals("起", shelfItemVO.getSalePriceSuffix());
    }
}