package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.available;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemavailable.DefaultProductAvailableOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemAvailableVP;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertTrue;

/**
 * 测试DefaultProductAvailableOpt的compute方法
 */
public class DefaultProductAvailableOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private UnifiedShelfItemAvailableVP.Param mockParam;

    private DefaultProductAvailableOpt defaultProductAvailableOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultProductAvailableOpt = new DefaultProductAvailableOpt();
    }

    /**
     * 测试compute方法在正常情况下总是返回true
     */
    @Test
    public void testComputeAlwaysReturnsTrue() {
        // arrange
        // 由于compute方法的实现总是返回true，这里不需要设置mock对象的特定行为

        // act
        Boolean result = defaultProductAvailableOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertTrue("compute方法应该总是返回true", result);
    }
    /**
     * 测试compute方法在unused参数为null时的行为
     */
    @Test
    public void testComputeWithNullUnused() {
        // arrange
        // unused为null的情况下，不需要额外的设置

        // act
        Boolean result = defaultProductAvailableOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertTrue("即使unused参数为null，compute方法也应该返回true", result);
    }

}
