package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.button;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfButtonVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn.PressOnNailItemBtnOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemButtonVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.BtnTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class PressOnNailItemBtnOptTest {

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private UnifiedShelfItemButtonVP.Param mockParam;
    @Mock
    private PressOnNailItemBtnOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private PressOnNailItemBtnOpt pressOnNailItemBtnOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailItemBtnOpt = new PressOnNailItemBtnOpt();
    }

    /**
     * 测试场景：正常情况下，生成按钮信息
     */
    @Test
    public void testComputeNormalCase() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getJumpUrl()).thenReturn("defaultJumpUrl");
        when(mockConfig.getName()).thenReturn("buttonName");
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_APP.getCode());
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList("skuId"));
        HashMap<String, Object> contextParams = new HashMap<>();
        contextParams.put(ShelfActivityConstants.Params.mtPoiIdL, 12345L);
        when(mockContext.getParameters()).thenReturn(contextParams);

        // act
        ShelfButtonVO result = pressOnNailItemBtnOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("buttonName", result.getName());
        assertEquals(BtnTypeEnum.COMMON_BUTTON.getCode(), result.getType());
    }

    /**
     * 测试场景：当SKU列表为空时
     */
    @Test
    public void testComputeWhenSkuListIsEmpty() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getJumpUrl()).thenReturn("defaultJumpUrl");
        when(mockConfig.getName()).thenReturn("buttonName");
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_APP.getCode());
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList());

        // act
        ShelfButtonVO result = pressOnNailItemBtnOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("defaultJumpUrl", result.getJumpUrl());
    }

    /**
     * 测试场景：当用户代理不是MT_APP时
     */
    @Test
    public void testComputeWhenUserAgentIsNotMT_APP() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getJumpUrl()).thenReturn("defaultJumpUrl");
        when(mockConfig.getName()).thenReturn("buttonName");
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(0);
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList("skuId"));

        // act
        ShelfButtonVO result = pressOnNailItemBtnOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("defaultJumpUrl", result.getJumpUrl());
    }

    /**
     * 测试场景：当上下文中没有用户代理信息时
     */
    @Test
    public void testComputeWhenUserAgentIsNull() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getJumpUrl()).thenReturn("defaultJumpUrl");
        when(mockConfig.getName()).thenReturn("buttonName");
        when(mockContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(null);
        when(mockProductM.getSkuIdList()).thenReturn(Arrays.asList("skuId"));

        // act
        ShelfButtonVO result = pressOnNailItemBtnOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("defaultJumpUrl", result.getJumpUrl());
    }
}