package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.DealSecKillUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.BtnTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfButtonVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn.UnifiedShelfItemConfigureBtnOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemButtonVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class UnifiedShelfItemConfigureBtnOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private ProductM mockProductM;
    @Mock
    private UnifiedShelfItemButtonVP.Param mockParam;
    @Mock
    private UnifiedShelfItemConfigureBtnOpt.Config mockConfig;

    private UnifiedShelfItemConfigureBtnOpt underTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        underTest = new UnifiedShelfItemConfigureBtnOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    /**
     * 测试compute方法，当商品有秒杀活动且启用秒杀按钮时
     */
    @Test
    public void testCompute_WithSecKillEnabled() {

        try (MockedStatic<DealSecKillUtils> contextParamBuildUtils = Mockito.mockStatic(DealSecKillUtils.class)){
             contextParamBuildUtils.when(() -> DealSecKillUtils.isSecKillDeal(mockProductM)).thenReturn(true);
             contextParamBuildUtils.when(() -> DealSecKillUtils.hasSecKillPromo(mockProductM)).thenReturn(true);
             // when(mockConfig.isEnableSkill()).thenReturn(true);

             ShelfButtonVO result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

             assertEquals("类型应为秒杀按钮", BtnTypeEnum.SKILL_BUTTON.getCode(), result.getType());
        }

    }

    /**
     * 测试compute方法，当配置了特定按钮类型时
     */
    @Test
    public void testCompute_WithSpecificButtonTypeConfigured() {

        try (MockedStatic<DealSecKillUtils> contextParamBuildUtils = Mockito.mockStatic(DealSecKillUtils.class)) {
            contextParamBuildUtils.when(() -> DealSecKillUtils.isSecKillDeal(mockProductM)).thenReturn(false);
            contextParamBuildUtils.when(() -> DealSecKillUtils.hasSecKillPromo(mockProductM)).thenReturn(false);

            when(mockConfig.getType()).thenReturn(BtnTypeEnum.COMMON_BUTTON.getCode());

            ShelfButtonVO result = underTest.compute(mockActivityCxt, mockParam, mockConfig);
            assertEquals("类型应为自定义按钮", BtnTypeEnum.COMMON_BUTTON.getCode(), result.getType());
        }
        // when(RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(mockProductM)).thenReturn(false);


    }

    /**
     * 测试compute方法，当未配置特定按钮类型且无秒杀活动时
     */
    @Test
    public void testCompute_WithNoSpecificConfiguration() {

        try (MockedStatic<DealSecKillUtils> contextParamBuildUtils = Mockito.mockStatic(DealSecKillUtils.class)) {
            contextParamBuildUtils.when(() -> DealSecKillUtils.isSecKillDeal(mockProductM)).thenReturn(false);
            contextParamBuildUtils.when(() -> DealSecKillUtils.hasSecKillPromo(mockProductM)).thenReturn(false);
            when(mockConfig.getType()).thenReturn(0); // 未配置特定类型

            ShelfButtonVO result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

            assertEquals("类型应为普通按钮", BtnTypeEnum.COMMON_BUTTON.getCode(), result.getType());

        }
    }

    /**
     * 测试compute方法，当配置了跳转URL时
     */
    @Test
    public void testCompute_WithJumpUrlConfigured() {
        String expectedUrl = "http://example.com";
        when(mockConfig.getJumpUrl()).thenReturn(expectedUrl);

        ShelfButtonVO result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

        assertEquals("跳转URL应该匹配配置", expectedUrl, result.getJumpUrl());
    }

    /**
     * 测试compute方法，当启用提单页链接且商品有提单页链接时
     */
    @Test
    public void testCompute_WithOrderUrlEnabledAndProductHasOrderUrl() {
        String expectedUrl = "http://order.example.com";
        when(mockConfig.isEnableOrderUrl()).thenReturn(true);
        when(mockProductM.getOrderUrl()).thenReturn(expectedUrl);

        ShelfButtonVO result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

        assertEquals("跳转URL应为提单页链接", expectedUrl, result.getJumpUrl());
    }

    /**
     * 测试compute方法，当按钮被配置为禁用时
     */
    @Test
    public void testCompute_WithButtonDisabled() {
        when(mockConfig.isDisable()).thenReturn(true);

        ShelfButtonVO result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

        assertTrue("按钮应被禁用", result.isDisable());
    }
}
