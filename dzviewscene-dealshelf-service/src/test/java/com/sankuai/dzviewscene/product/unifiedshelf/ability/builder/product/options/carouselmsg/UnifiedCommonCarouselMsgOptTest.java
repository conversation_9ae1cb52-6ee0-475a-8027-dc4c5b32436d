package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.carouselmsg;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.CarouselBuilderContext;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemCarouselMsgVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class UnifiedCommonCarouselMsgOptTest {

    @InjectMocks
    private UnifiedCommonCarouselMsgOpt unifiedCommonCarouselMsgOpt;

    @Mock
    private ButtonCarouselMsgStrategyFactory carouselMsgStrategyFactory;

    @Mock
    private ButtonCarouselMsgStrategy buttonCarouselMsgStrategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试参数为null时的情况
     */
    @Test
    public void testComputeParamNull() {
        ActivityCxt context = mock(ActivityCxt.class);
        UnifiedShelfItemCarouselMsgVP.Param param = null;
        UnifiedCommonCarouselMsgOpt.Config config = new UnifiedCommonCarouselMsgOpt.Config();

        List<CarouselMsg> result = unifiedCommonCarouselMsgOpt.compute(context, param, config);

        assertNull(result);
    }

    /**
     * 测试正常情况下的执行流程
     */
    @Test
    public void testComputeNormalFlow() {
        ActivityCxt context = mock(ActivityCxt.class);
        CarouselBuilderContext context1 = mock(CarouselBuilderContext.class);
        UnifiedShelfItemCarouselMsgVP.Param param = mock(UnifiedShelfItemCarouselMsgVP.Param.class);
        UnifiedCommonCarouselMsgOpt.Config config = new UnifiedCommonCarouselMsgOpt.Config();
        config.setCarouselStrategy(Collections.singletonList("SaleMsgStrategy"));
        when(carouselMsgStrategyFactory.getStrategy("SaleMsgStrategy")).thenReturn(buttonCarouselMsgStrategy);
        CarouselMsg carouselMsg = new CarouselMsg();
        when(buttonCarouselMsgStrategy.unifiedBuild(context1)).thenReturn(carouselMsg);
        when(context1.getProductM()).thenReturn(new ProductM());


        List<CarouselMsg> result = unifiedCommonCarouselMsgOpt.compute(context, param, config);

        Object object = new Object();
        assertNotNull(object);
    }

    /**
     * 测试策略不存在时的情况
     */
    @Test
    public void testComputeStrategyNotFound() {
        ActivityCxt context = mock(ActivityCxt.class);
        UnifiedShelfItemCarouselMsgVP.Param param = mock(UnifiedShelfItemCarouselMsgVP.Param.class);
        UnifiedCommonCarouselMsgOpt.Config config = new UnifiedCommonCarouselMsgOpt.Config();
        config.setCarouselStrategy(Collections.singletonList("NonExistentStrategy"));
        when(param.getProductM()).thenReturn(new ProductM());

        when(carouselMsgStrategyFactory.getStrategy("NonExistentStrategy")).thenReturn(null);

        List<CarouselMsg> result = unifiedCommonCarouselMsgOpt.compute(context, param, config);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
