package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.defaultshownum;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaDefaultShowNumVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Lists;

import java.util.LinkedHashMap;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class CommonDefaultShowNumOptTest {

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private ProductAreaDefaultShowNumVP.Param mockParam;
    private CommonDefaultShowNumOpt.Config config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        config = new CommonDefaultShowNumOpt.Config();
    }

    /**
     * 测试计算默认展示数量，当规则配置为空时
     */
    @Test
    public void testCompute_WhenRuleShowNumCfgIsEmpty() {
        // arrange
        config.setDefaultShowNum(3);
        config.setRuleShowNumCfg(new LinkedHashMap<>());
        CommonDefaultShowNumOpt opt = new CommonDefaultShowNumOpt();

        // act
        Integer result = opt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(Integer.valueOf(3), result);
    }

    /**
     * 测试计算默认展示数量，配置商品类型展示数量策略
     */
    @Test
    public void testCompute_WhenMatchingProductRule() {
        // arrange
        LinkedHashMap<String, String> ruleShowNumCfg = new LinkedHashMap<>();
        ruleShowNumCfg.put("productTypeRule", "5:5");
        config.setDefaultShowNum(3);
        config.setRuleShowNumCfg(ruleShowNumCfg);
        CommonDefaultShowNumOpt opt = new CommonDefaultShowNumOpt();
        ProductM productM = new ProductM();
        productM.setProductType(5);
        when(mockParam.getProducts()).thenReturn(Lists.newArrayList(productM));
        // act
        Integer result = opt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(Integer.valueOf(5), result);
    }

    /**
     * 测试计算默认展示数量，配置斗斛实验展示数量策略
     */
    @Test
    public void testCompute_WhenMatchingDouHuRule() {
        // arrange
        LinkedHashMap<String, String> ruleShowNumCfg = new LinkedHashMap<>();
        ruleShowNumCfg.put("douHuExpRule", "a:5;b:4");
        config.setDefaultShowNum(3);
        config.setRuleShowNumCfg(ruleShowNumCfg);
        CommonDefaultShowNumOpt opt = new CommonDefaultShowNumOpt();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("a");
        when(mockParam.getDouHuList()).thenReturn(Lists.newArrayList(douHuM));
        // act
        Integer result = opt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(Integer.valueOf(5), result);
    }

    /**
     * 测试计算默认展示数量，配置筛选id展示数量策略
     */
    @Test
    public void testCompute_WhenMatchingFilterRule() {
        // arrange
        LinkedHashMap<String, String> ruleShowNumCfg = new LinkedHashMap<>();
        ruleShowNumCfg.put("filterIdRule", "1:4");
        config.setDefaultShowNum(3);
        config.setRuleShowNumCfg(ruleShowNumCfg);
        CommonDefaultShowNumOpt opt = new CommonDefaultShowNumOpt();
        when(mockParam.getFilterId()).thenReturn(1L);
        // act
        Integer result = opt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(Integer.valueOf(4), result);
    }

    /**
     * 测试计算默认展示数量，配置含交易次卡展示数量策略
     */
    @Test
    public void testCompute_WhenMatchingTimesDealRule() {
        // arrange
        LinkedHashMap<String, String> ruleShowNumCfg = new LinkedHashMap<>();
        ruleShowNumCfg.put("timesDealRule", "5");
        config.setDefaultShowNum(3);
        config.setRuleShowNumCfg(ruleShowNumCfg);
        CommonDefaultShowNumOpt opt = new CommonDefaultShowNumOpt();
        ProductM productM = new ProductM();
        productM.setProductType(1);
        productM.setTradeType(19);
        when(mockParam.getProducts()).thenReturn(Lists.newArrayList(productM));

        // act
        Integer result = opt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(Integer.valueOf(5), result);
    }

    /**
     * 测试计算默认展示数量，配置不可购商品策略
     */
    @Test
    public void testCompute_WhenMatchingUnavailableProductRule() {
        // arrange
        LinkedHashMap<String, String> ruleShowNumCfg = new LinkedHashMap<>();
        ruleShowNumCfg.put("unavailableProductRule", "3");
        config.setDefaultShowNum(3);
        config.setRuleShowNumCfg(ruleShowNumCfg);
        CommonDefaultShowNumOpt opt = new CommonDefaultShowNumOpt();
        ProductM productM1 = new ProductM();
        productM1.setProductType(1);
        productM1.setAttr("BeautyCantRepeatPurchaseProduct", "true");
        ProductM productM2 = new ProductM();
        productM2.setProductType(1);
        ProductM productM3 = new ProductM();
        productM3.setProductType(1);
        productM3.setAttr("BeautyCantRepeatPurchaseProduct", "true");
        ProductM productM4 = new ProductM();
        productM4.setProductType(1);
        when(mockParam.getProducts()).thenReturn(Lists.newArrayList(productM1, productM2, productM3, productM4));

        // act
        Integer result = opt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(Integer.valueOf(2), result);
    }
}
