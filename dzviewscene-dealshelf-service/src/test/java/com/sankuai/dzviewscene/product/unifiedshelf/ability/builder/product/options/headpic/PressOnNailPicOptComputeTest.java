package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.PressOnNailPicOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailPicOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param // Corrected the mock type
    param;

    @Mock
    private Config config;

    @Mock
    private ProductM productM;

    @InjectMocks
    private PressOnNailPicOpt pressOnNailPicOpt;

    @Test
    public void testCompute_ProductPicUrlIsNotEmptyAndMaterialListIsEmpty() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("https://example.com/product_pic.jpg");
        when(productM.getMaterialList()).thenReturn(new ArrayList<>());
        PictureModel result = pressOnNailPicOpt.compute(context, param, config);
        assertEquals("https://example.com/product_pic.jpg", result.getPicUrl());
        assertEquals(1, result.getAspectRadio(), 0);
    }
}
