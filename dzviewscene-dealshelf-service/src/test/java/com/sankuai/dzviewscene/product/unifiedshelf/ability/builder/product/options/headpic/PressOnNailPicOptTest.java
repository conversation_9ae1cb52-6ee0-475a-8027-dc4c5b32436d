package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

public class PressOnNailPicOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private Param param;
    @Mock
    private PressOnNailPicOpt.Config config;
    @Mock
    private ProductM productM;

    private PressOnNailPicOpt pressOnNailPicOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailPicOpt = new PressOnNailPicOpt();
        when(param.getProductM()).thenReturn(productM);
    }

    /**
     * 测试场景：产品图片URL为空，材料列表为空
     */
    @Test
    public void testCompute_ProductPicUrlIsEmptyAndMaterialListIsEmpty() {
        when(productM.getPicUrl()).thenReturn("");
        when(productM.getMaterialList()).thenReturn(new ArrayList<>());

        PictureModel result = pressOnNailPicOpt.compute(context, param, config);

        assertNull(result);
    }

    /**
     * 测试场景：产品图片URL为空，材料列表不为空，但材料图片为空
     */
    @Test
    public void testCompute_ProductPicUrlIsEmptyAndMaterialListIsNotEmptyButMaterialPicIsEmpty() {
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setPic("");
        materialList.add(materialM);

        when(productM.getPicUrl()).thenReturn("");
        when(productM.getMaterialList()).thenReturn(materialList);

        PictureModel result = pressOnNailPicOpt.compute(context, param, config);

        assertNull(result);
    }

    /**
     * 测试场景：产品图片URL为空，材料列表不为空，材料图片不为空
     */
    @Test
    public void testCompute_ProductPicUrlIsEmptyAndMaterialListIsNotEmptyAndMaterialPicIsNotEmpty() {
        List<DealProductMaterialM> materialList = new ArrayList<>();
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setPic("https://example.com/material_pic.jpg");
        materialM.setName("material_name");
        materialList.add(materialM);

        when(productM.getPicUrl()).thenReturn("");
        when(productM.getMaterialList()).thenReturn(materialList);

        PictureModel result = pressOnNailPicOpt.compute(context, param, config);

        assertEquals("https://example.com/material_pic.jpg", result.getPicUrl());
        assertEquals(1, result.getAspectRadio(), 0);
    }

    /**
     * 测试场景：产品图片URL不为空，材料列表为空
     */
    @Test
    public void testCompute_ProductPicUrlIsNotEmptyAndMaterialListIsEmpty() {
        when(productM.getPicUrl()).thenReturn("https://example.com/product_pic.jpg");
        when(productM.getMaterialList()).thenReturn(new ArrayList<>());

        PictureModel result = pressOnNailPicOpt.compute(context, param, config);

        assertEquals("https://example.com/product_pic.jpg", result.getPicUrl());
        assertEquals(1, result.getAspectRadio(), 0);
    }
}

