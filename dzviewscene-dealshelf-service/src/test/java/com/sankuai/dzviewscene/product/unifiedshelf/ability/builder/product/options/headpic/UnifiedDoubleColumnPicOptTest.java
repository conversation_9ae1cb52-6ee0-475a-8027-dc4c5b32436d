package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedDoubleColumnPicOpt的compute方法
 */
public class UnifiedDoubleColumnPicOptTest {

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private Param mockParam;
    @Mock
    private UnifiedDoubleColumnPicOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private UnifiedDoubleColumnPicOpt underTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        underTest = new UnifiedDoubleColumnPicOpt();
    }

    /**
     * 测试当ProductM为null时，应返回null
     */
    @Test
    public void testComputeWhenProductMIsNull() {
        when(mockParam.getProductM()).thenReturn(null);

        PictureModel result = underTest.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试当ProductM的picUrl为空时，应返回null
     */
    @Test
    public void testComputeWhenPicUrlIsEmpty() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("");

        PictureModel result = underTest.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试正常情况下，应正确计算并返回PictureModel
     */
    @Test
    public void testComputeUnderNormalConditions() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        when(mockConfig.getPicWidth()).thenReturn(640);
        when(mockConfig.getPicHeight()).thenReturn(360);

        PictureModel result = underTest.compute(mockContext, mockParam, mockConfig);

        assertNotNull(result);
        assertEquals(1.778, result.getAspectRadio(), 0.001);
    }
}
