package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ExtendImageM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedSingleRowPicOpt的compute方法
 */
public class UnifiedSingleRowPicOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private UnifiedSingleRowPicOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private UnifiedSingleRowPicOpt unifiedSingleRowPicOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedSingleRowPicOpt = new UnifiedSingleRowPicOpt();
    }

    /**
     * 测试当productM为null时返回null
     */
    @Test
    public void testComputeProductMIsNull() {
        when(mockParam.getProductM()).thenReturn(null);

        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试当productM的picUrl为空时返回null
     */
    @Test
    public void testComputePicUrlIsEmpty() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("");

        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试当config为null时使用默认配置
     */
    @Test
    public void testComputeConfigIsNull() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");

        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, new UnifiedSingleRowPicOpt.Config());

        assertNotNull(result);
        assertEquals("https://example.com/pic.jpg", result.getPicUrl());
        assertEquals(1, result.getAspectRadio(), 0);
    }

    /**
     * 测试当calcBySpu为true且满足条件时使用Spu的头图
     */
    @Test
    public void testComputeUseSpuHeadPic() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        when(mockProductM.getDealSpuId()).thenReturn(1L);
        DealProductSpuDTO  spuDTO = new com.sankuai.dztheme.deal.res.DealProductSpuDTO();
        spuDTO.setHeadPic("http://example.com/spu_pic.jpg");
        when(mockProductM.getAttr("topPerformingProduct")).thenReturn("1");
        when(mockProductM.getSpuM()).thenReturn(spuDTO);
        when(mockConfig.isCalcBySpu()).thenReturn(true);
        mockConfig.setCalcBySpu(true);

        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertEquals("https://example.com/spu_pic.jpg", result.getPicUrl());
    }
    @Test
    public void testComputeUseRatioStr() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        List<ExtendImageM> extendImages = new ArrayList<>();
        ExtendImageM image1 = new ExtendImageM("http://example.com/url_ratio1.jpg", "4:3");
        ExtendImageM image2 = new ExtendImageM("http://example.com/url_ratio2.jpg", "3:4");
        extendImages.add(image1);
        extendImages.add(image2);
        when(mockProductM.getExtendImages()).thenReturn(extendImages);

        when(mockConfig.isCalcBySpu()).thenReturn(false);
        when(mockConfig.getWidth()).thenReturn(3);
        when(mockConfig.getHeight()).thenReturn(4);
        when(mockConfig.isUseExtendImages()).thenReturn(true);

        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertEquals("https://example.com/url_ratio2.jpg", result.getPicUrl());
    }
    @Test
    public void testComputeUseDefaultRatio() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        List<ExtendImageM> extendImages = new ArrayList<>();
        ExtendImageM image1 = new ExtendImageM("http://example.com/url_ratio1.jpg", "4:3");
        ExtendImageM image2 = new ExtendImageM("http://example.com/url_ratio2.jpg", "3:4");
        extendImages.add(image1);
        extendImages.add(image2);
        when(mockProductM.getExtendImages()).thenReturn(extendImages);

        when(mockConfig.isCalcBySpu()).thenReturn(false);
        when(mockConfig.getWidth()).thenReturn(1);
        when(mockConfig.getHeight()).thenReturn(1);
        when(mockConfig.isUseExtendImages()).thenReturn(true);

        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertEquals("https://example.com/pic.jpg", result.getPicUrl());
    }
    @Test
    public void testComputeNotUseExtendImage() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");

        when(mockConfig.isCalcBySpu()).thenReturn(false);
        when(mockConfig.getWidth()).thenReturn(3);
        when(mockConfig.getHeight()).thenReturn(4);

        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertEquals("https://example.com/pic.jpg", result.getPicUrl());
    }
}
