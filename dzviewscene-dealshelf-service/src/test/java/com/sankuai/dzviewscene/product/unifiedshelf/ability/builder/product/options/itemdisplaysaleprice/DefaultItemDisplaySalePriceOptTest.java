package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemdisplaysaleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemDisplaySalePriceVP.Param;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.assertEquals;

/**
 * 测试DefaultItemDisplaySalePriceOpt的compute方法
 */
public class DefaultItemDisplaySalePriceOptTest {

    private DefaultItemDisplaySalePriceOpt defaultItemDisplaySalePriceOpt;
    private ActivityCxt mockActivityCxt;
    private Param mockParam;

    @Before
    public void setUp() {
        defaultItemDisplaySalePriceOpt = new DefaultItemDisplaySalePriceOpt();
        mockActivityCxt = Mockito.mock(ActivityCxt.class);
        mockParam = Mockito.mock(Param.class);
    }

    /**
     * 测试compute方法，当salePrice不为空时
     */
    @Test
    public void testComputeWhenSalePriceIsNotNull() {
        // arrange
        String expectedSalePrice = "100";
        Mockito.when(mockParam.getSalePrice()).thenReturn(expectedSalePrice);

        // act
        String result = defaultItemDisplaySalePriceOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertEquals("当salePrice不为空时，应返回正确的salePrice", expectedSalePrice, result);
    }

    /**
     * 测试compute方法，当salePrice为空时
     */
    @Test
    public void testComputeWhenSalePriceIsNull() {
        // arrange
        Mockito.when(mockParam.getSalePrice()).thenReturn(null);

        // act
        String result = defaultItemDisplaySalePriceOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertEquals("当salePrice为空时，应返回null", null, result);
    }
}
