package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemdisplaysaleprice;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemdisplaysaleprice.TimesDealSinglePriceOpt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TimesDealSinglePriceOptComputeTest {

    @InjectMocks
    private TimesDealSinglePriceOpt timesDealSinglePriceOpt;

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        // timesDealSinglePriceOpt = new TimesDealSinglePriceOpt();
        // context = mock(ActivityCxt.class);
        // productM = mock(ProductM.class);
    }

    @Test
    public void testCompute_TimesDealWithEmptySalePrice_ReturnsOriginalSalePrice() throws Throwable {
        when(productM.isTimesDeal()).thenReturn(true);
        TimesDealSinglePriceOpt.Param param = TimesDealSinglePriceOpt.Param.builder().productM(productM).salePrice("").build();
        String result = timesDealSinglePriceOpt.compute(context, param, null);
        assertEquals("", result);
    }

    @Test
    public void testCompute_TimesDealWithNullAttr_ReturnsOriginalSalePrice() throws Throwable {
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn(null);
        TimesDealSinglePriceOpt.Param param = TimesDealSinglePriceOpt.Param.builder().productM(productM).salePrice("100").build();
        String result = timesDealSinglePriceOpt.compute(context, param, null);
        assertEquals("100", result);
    }

    @Test
    public void testCompute_TimesDealWithInvalidAttr_ReturnsOriginalSalePrice() throws Throwable {
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("abc");
        TimesDealSinglePriceOpt.Param param = TimesDealSinglePriceOpt.Param.builder().productM(productM).salePrice("100").build();
        String result = timesDealSinglePriceOpt.compute(context, param, null);
        assertEquals("100", result);
    }
}
