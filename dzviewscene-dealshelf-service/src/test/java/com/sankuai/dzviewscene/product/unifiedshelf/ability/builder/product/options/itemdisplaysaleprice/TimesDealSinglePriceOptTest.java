package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemdisplaysaleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemDisplaySalePriceVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemDisplaySalePriceVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.when;
import static org.junit.Assert.assertEquals;

public class TimesDealSinglePriceOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private ProductM productM;

    private TimesDealSinglePriceOpt timesDealSinglePriceOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        timesDealSinglePriceOpt = new TimesDealSinglePriceOpt();
    }

    /**
     * 测试场景：非多次卡产品，应返回原销售价格
     */
    @Test
    public void testCompute_NonTimesDeal_ReturnsOriginalSalePrice() {
        // arrange
        when(productM.isTimesDeal()).thenReturn(false);
        Param param = UnifiedShelfItemDisplaySalePriceVP.Param.builder().productM(productM).salePrice("100").build();

        // act
        String result = timesDealSinglePriceOpt.compute(context, param, null);

        // assert
        assertEquals("100", result);
    }

    /**
     * 测试场景：多次卡产品但销售价格为空，应返回空字符串
     */
    @Test
    public void testCompute_TimesDealWithEmptySalePrice_ReturnsEmptyString() {
        // arrange
        when(productM.isTimesDeal()).thenReturn(true);
        Param param = UnifiedShelfItemDisplaySalePriceVP.Param.builder().productM(productM).salePrice("").build();

        // act
        String result = timesDealSinglePriceOpt.compute(context, param, null);

        // assert
        assertEquals("", result);
    }

    /**
     * 测试场景：多次卡产品，销售价格非空，但未设置多次卡次数，应返回原销售价格
     */
    @Test
    public void testCompute_TimesDealWithoutTimes_ReturnsOriginalSalePrice() {
        // arrange
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr("SYS_MULTI_SALE_NUMBER")).thenReturn(null);
        Param param = UnifiedShelfItemDisplaySalePriceVP.Param.builder().productM(productM).salePrice("200").build();
        // act
        String result = timesDealSinglePriceOpt.compute(context, param, null);

        // assert
        assertEquals("200", result);
    }

    /**
     * 测试场景：多次卡产品，销售价格非空，设置了多次卡次数，应返回计算后的单次价格
     */
    @Test
    public void testCompute_TimesDealWithTimes_ReturnsCalculatedSinglePrice() {
        // arrange
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("10");
        Param param = UnifiedShelfItemDisplaySalePriceVP.Param.builder().productM(productM).salePrice("1000").build();
        // act
        String result = timesDealSinglePriceOpt.compute(context, param, null);

        // assert
        assertEquals("100", result);
    }
}
