package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemextra;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemExtraVP;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertNull;

/**
 * DefaultItemExtraOpt类的单元测试
 */
public class DefaultItemExtraOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemExtraVP.Param mockParam;

    private DefaultItemExtraOpt defaultItemExtraOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultItemExtraOpt = new DefaultItemExtraOpt();
    }

    /**
     * 测试compute方法，期望返回null
     */
    @Test
    public void testComputeExpectNull() {
        // arrange
        // 由于compute方法不依赖于任何输入参数的具体值，因此不需要对mockActivityCxt和mockParam进行任何特定的设置

        // act
        String result = defaultItemExtraOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertNull("compute方法应该返回null", result);
    }
}
