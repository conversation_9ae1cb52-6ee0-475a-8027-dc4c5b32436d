package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemextra;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemextra.UnifiedConfigItemExtraOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemextra.UnifiedConfigItemExtraOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemExtraVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedConfigItemExtraOptComputeTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    @InjectMocks
    private UnifiedConfigItemExtraOpt unifiedConfigItemExtraOpt;

    /**
     * Test compute method when param or config is null, should throw NullPointerException.
     */
    @Test(expected = NullPointerException.class)
    public void testComputeParamOrConfigIsNull() throws Throwable {
        unifiedConfigItemExtraOpt.compute(activityCxt, null, null);
    }

    /**
     * Test compute method when getExtraJsonByConfig returns null or empty string, should return null.
     */
    @Test
    public void testComputeJsonIsEmpty() throws Throwable {
        Param param = Param.builder().productM(productM).build();
        Config config = new Config();
        String result = unifiedConfigItemExtraOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

    /**
     * Test compute method when getExtraJsonByConfig returns non-empty string, but decoded Map is empty, should return null.
     */
    @Test
    public void testComputeMapIsEmpty() throws Throwable {
        Param param = Param.builder().productM(productM).build();
        Config config = new Config();
        String result = unifiedConfigItemExtraOpt.compute(activityCxt, param, config);
        assertNull(result);
    }
}
