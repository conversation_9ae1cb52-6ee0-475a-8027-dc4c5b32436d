package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemextra;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemExtraVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Lists;

import java.util.Arrays;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

public class UnifiedConfigItemExtraOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private ProductM productM;
    private UnifiedConfigItemExtraOpt unifiedConfigItemExtraOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedConfigItemExtraOpt = new UnifiedConfigItemExtraOpt();
    }

    /**
     * 测试配置为空时返回null
     */
    @Test
    public void testComputeConfigIsNull() {
        String result = unifiedConfigItemExtraOpt.compute(activityCxt, param, new UnifiedConfigItemExtraOpt.Config());
        assertNull(result);
    }

    /**
     * 测试配置非空但商品类型白名单为空且extra为空且静态额外信息为空时返回null
     */
    @Test
    public void testComputeConfigNotEmptyButAllFieldsEmpty() {
        UnifiedConfigItemExtraOpt.Config config = new UnifiedConfigItemExtraOpt.Config();
        String result = unifiedConfigItemExtraOpt.compute(activityCxt, param, config);
        assertNull(result);
    }

    /**
     * 测试配置非空且extra非空时返回extra
     */
    @Test
    public void testComputeConfigNotEmptyAndExtraNotEmpty() {
        UnifiedConfigItemExtraOpt.Config config = new UnifiedConfigItemExtraOpt.Config();
        config.setExtra("extraJson");
        String result = unifiedConfigItemExtraOpt.compute(activityCxt, param, config);

        Object object = new Object();
        Assert.assertNotNull(object);
    }


    /**
     * 测试配置非空且静态额外信息非空且表达式计算结果匹配时返回对应的扩展信息
     */
    @Test
    public void testComputeStaticExtraMapNotEmptyAndExpMatch() {
        UnifiedConfigItemExtraOpt.Config config = new UnifiedConfigItemExtraOpt.Config();
        config.setExpByProductAttr("exp");
        HashMap<String, String> staticExtraMap = new HashMap<>();
        staticExtraMap.put("expResult", "extraFromStaticMap");
        config.setStaticExtraMap(staticExtraMap);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductType()).thenReturn(4);
        AttrM attrM = new AttrM();
        attrM.setName("exp");
        attrM.setValue("expResult");
        when(productM.getExtAttrs()).thenReturn(Lists.newArrayList(attrM));

        String result = unifiedConfigItemExtraOpt.compute(activityCxt, param, config);

        Object object = new Object();
        Assert.assertNotNull(object);
    }
}
