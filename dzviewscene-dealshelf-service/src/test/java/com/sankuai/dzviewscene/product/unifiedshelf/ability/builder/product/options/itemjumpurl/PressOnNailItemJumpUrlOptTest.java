package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemjumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl.PressOnNailItemJumpUrlOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class PressOnNailItemJumpUrlOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private UnifiedShelfItemJumpUrlVP.Param param;
    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试跳转链接不为空且SKU列表不为空的情况
     */
    @Test
    public void testCompute_JumpUrlNotBlankAndSkuListNotEmpty() {
        // arrange
        PressOnNailItemJumpUrlOpt opt = new PressOnNailItemJumpUrlOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(Arrays.asList("SKU123"));

        // act
        String result = opt.compute(context, param, null);

        // assert
        assertEquals("http://example.com&skuinitindex=SKU123", result);
    }

    /**
     * 测试跳转链接不为空但SKU列表为空的情况
     */
    @Test
    public void testCompute_JumpUrlNotBlankAndSkuListEmpty() {
        // arrange
        PressOnNailItemJumpUrlOpt opt = new PressOnNailItemJumpUrlOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(Arrays.asList());

        // act
        String result = opt.compute(context, param, null);

        // assert
        assertEquals("http://example.com", result);
    }

    /**
     * 测试跳转链接为空的情况
     */
    @Test
    public void testCompute_JumpUrlBlank() {
        // arrange
        PressOnNailItemJumpUrlOpt opt = new PressOnNailItemJumpUrlOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("");

        // act
        String result = opt.compute(context, param, null);

        // assert
        assertEquals("", result);
    }
}
