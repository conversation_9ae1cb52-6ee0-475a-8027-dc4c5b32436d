package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemjumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl.UnifiedShelfItemSourceJumpUrlOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemJumpUrlVP.Param;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class UnifiedShelfItemSourceJumpUrlOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private Param param;

    @Mock
    private ProductM productM;

    private UnifiedShelfItemSourceJumpUrlOpt unifiedShelfItemSourceJumpUrlOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedShelfItemSourceJumpUrlOpt = new UnifiedShelfItemSourceJumpUrlOpt();
    }

    /**
     * 测试正常团购场景
     */
    @Test
    public void testCompute_NonCostEffectiveActivity() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://example.com/product");
        // act
        String result = unifiedShelfItemSourceJumpUrlOpt.compute(activityCxt, param, null);
        // assert
        assertEquals("http://example.com/product&source=poi_page", result);
    }

    /**
     * 测试当产品跳转链接为空时
     */
    @Test
    public void testCompute_JumpUrlIsNull() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn(null);
        try(MockedStatic<PerfectActivityBuildUtils> mocked = Mockito.mockStatic(PerfectActivityBuildUtils.class)){
            mocked.when(() -> PerfectActivityBuildUtils.isPpEffectiveActivity(productM)).thenReturn(false);
            // act
            String result = unifiedShelfItemSourceJumpUrlOpt.compute(activityCxt, param, null);
            // assert
            assertEquals(null, result);
        }
    }
}

