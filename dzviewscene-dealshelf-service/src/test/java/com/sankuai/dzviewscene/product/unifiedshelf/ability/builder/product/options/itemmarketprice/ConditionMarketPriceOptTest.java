package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemmarketprice;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemMarketPriceVP.Param;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemmarketprice.ConditionMarketPriceOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * 测试 ConditionMarketPriceOpt.compute 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class ConditionMarketPriceOptTest {

    @InjectMocks
    private ConditionMarketPriceOpt conditionMarketPriceOpt;
    @Mock
    private ActivityCxt mockContext;
    @Mock
    private Param mockParam;
    @Mock
    private Config mockConfig;
    @Mock
    private ProductM productM;

    /**
     * 场景1：config.getTypes() 为 null
     */
    @Test
    public void testCompute_ConfigTypesNull() {
        // arrange
        when(mockConfig.getTypes()).thenReturn(null);
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn("100");

        // act
        String result = conditionMarketPriceOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("100", result);
    }

    /**
     * 场景2：config.getTypes() 为空列表
     */
    @Test
    public void testCompute_ConfigTypesEmpty() {
        // arrange
        when(mockConfig.getTypes()).thenReturn(Collections.emptyList());
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn("200");

        // act
        String result = conditionMarketPriceOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("200", result);
    }

    /**
     * 场景3：config.getTypes() 中的所有 type 都没有对应的 MarketPriceBuilder
     */
    @Test
    public void testCompute_NoMarketPriceBuilder() {
        // arrange
        when(mockConfig.getTypes()).thenReturn(Collections.singletonList("type1"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn("300");

        // act
        String result = conditionMarketPriceOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("300", result);
    }

    /**
     * 场景4：config.getTypes() 中的某些 type 有对应的 MarketPriceBuilder
     */
    @Test
    public void testCompute_WithMarketPriceBuilder() {
        // arrange
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platform", 2);
        when(mockContext.getParameters()).thenReturn(paramMap);
        when(mockParam.getProductM()).thenReturn(productM);
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setNationalSubsidyPrice(BigDecimal.valueOf(400));
        productPromoPriceM.setNationalSubsidyMarketPrice(BigDecimal.valueOf(400));
        when(productM.getPromoPrices()).thenReturn(Lists.newArrayList(productPromoPriceM));
        when(mockConfig.getTypes()).thenReturn(Collections.singletonList("nationalSubsidy"));

        // act
        String result = conditionMarketPriceOpt.compute(mockContext, mockParam, mockConfig);

        // assert
        assertEquals("400", result);
    }
}