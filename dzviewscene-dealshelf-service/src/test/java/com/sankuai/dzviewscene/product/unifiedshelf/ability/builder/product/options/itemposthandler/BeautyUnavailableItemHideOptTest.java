package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemposthandler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfButtonVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemposthandler.BeautyUnavailableItemHideOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemBuildPostHandlerVP.Param;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Lists;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class BeautyUnavailableItemHideOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private Config config;

    private BeautyUnavailableItemHideOpt beautyUnavailableItemHideOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        beautyUnavailableItemHideOpt = new BeautyUnavailableItemHideOpt();
    }

    /**
     * 测试商品不是丽人不可重复购买商品的情况
     */
    @Test
    public void testCompute_NotBeautyCantRepeatPurchaseProduct() {
        // arrange
        try(MockedStatic<ProductMAttrUtils> productMAttrUtils = Mockito.mockStatic(ProductMAttrUtils.class)){
            productMAttrUtils.when(() -> ProductMAttrUtils.isBeautyCantRepeatPurchaseProduct(any())).thenReturn(false);
            when(config.getHideExpSks()).thenReturn(Lists.newArrayList("a"));
            DouHuM douHuM1 = new DouHuM();
            douHuM1.setSk("a");
            ShelfItemVO shelfItemVO = new ShelfItemVO();
            shelfItemVO.setAvailable(true);
            shelfItemVO.setButton(new ShelfButtonVO());
            Param param = Param.builder().shelfItemVO(shelfItemVO).douHuMList(Lists.newArrayList(douHuM1)).build();
            // act
            Void result = beautyUnavailableItemHideOpt.compute(context, param, config);

            // assert
            assertNull(result);
            assertTrue(shelfItemVO.isAvailable());
        }
    }

    /**
     * 测试商品是丽人不可重复购买商品，但不命中隐藏表达式的情况
     */
    @Test
    public void testCompute_BeautyCantRepeatPurchaseProduct_NotHitHideExpSks() {
        // arrange
        try(MockedStatic<ProductMAttrUtils> productMAttrUtils = Mockito.mockStatic(ProductMAttrUtils.class)){
            productMAttrUtils.when(() -> ProductMAttrUtils.isBeautyCantRepeatPurchaseProduct(any())).thenReturn(true);
            when(config.getHideExpSks()).thenReturn(Lists.newArrayList("a"));
            DouHuM douHuM1 = new DouHuM();
            douHuM1.setSk("b");
            ShelfItemVO shelfItemVO = new ShelfItemVO();
            shelfItemVO.setAvailable(true);
            shelfItemVO.setButton(new ShelfButtonVO());
            Param param = Param.builder().shelfItemVO(shelfItemVO).douHuMList(Lists.newArrayList(douHuM1)).build();

            // act
            Void result = beautyUnavailableItemHideOpt.compute(context, param, config);

            // assert
            assertNull(result);
            assertTrue(shelfItemVO.isAvailable());
        }
    }
//
//    /**
//     * 测试商品是丽人不可重复购买商品，命中隐藏表达式情况
//     */
//    @Test
//    public void testCompute_BeautyCantRepeatPurchaseProduct_HitHideExpSks() {
//        // arrange
//        try(MockedStatic<ProductMAttrUtils> productMAttrUtils = Mockito.mockStatic(ProductMAttrUtils.class)) {
//            productMAttrUtils.when(() -> ProductMAttrUtils.isBeautyCantRepeatPurchaseProduct(any())).thenReturn(true);
//            when(config.getHideExpSks()).thenReturn(Lists.newArrayList("a"));
//            DouHuM douHuM1 = new DouHuM();
//            douHuM1.setSk("a");
//            ShelfItemVO shelfItemVO = new ShelfItemVO();
//            shelfItemVO.setAvailable(true);
//            shelfItemVO.setButton(new ShelfButtonVO());
//            Param param = Param.builder().shelfItemVO(shelfItemVO).douHuMList(Lists.newArrayList(douHuM1)).build();
//            // act
//            Void result = beautyUnavailableItemHideOpt.compute(context, param, config);
//
//            // assert
//            assertNull(result);
//            assertFalse(shelfItemVO.isAvailable());
//        }
//    }
}
