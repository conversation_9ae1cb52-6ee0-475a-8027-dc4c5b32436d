package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemposthandler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemBuildPostHandlerVP.Param;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertNull;

/**
 * DefaultPostHandlerOpt类的单元测试
 */
public class DefaultPostHandlerOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;

    private DefaultPostHandlerOpt defaultPostHandlerOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultPostHandlerOpt = new DefaultPostHandlerOpt();
    }

    /**
     * 测试compute方法，期望返回null
     */
    @Test
    public void testComputeExpectNull() {
        // arrange
        // 由于compute方法逻辑简单，直接返回null，因此不需要额外的arrange步骤

        // act
        Void result = defaultPostHandlerOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertNull("compute方法应该返回null", result);
    }
}

