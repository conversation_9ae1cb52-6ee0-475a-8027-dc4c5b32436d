package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagStrategyFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ConfigItemPriceBottomTagOptTest {

    @InjectMocks
    private ConfigItemPriceBottomTagOpt configItemPriceBottomTagOpt;

    @Mock
    private PriceBottomTagStrategyFactory strategyFactory;

    @Test
    public void testCompute_WhenConfigIsNull() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        ConfigItemPriceBottomTagOpt.Param param = ConfigItemPriceBottomTagOpt.Param.builder().build();
        ConfigItemPriceBottomTagOpt.Config config = null;
        List<ShelfTagVO> result = configItemPriceBottomTagOpt.compute(context, param, config);
        Assert.assertNull(result);
    }

    @Test
    public void testCompute_WhenStrategiesIsNull() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        ConfigItemPriceBottomTagOpt.Param param = ConfigItemPriceBottomTagOpt.Param.builder().build();
        ConfigItemPriceBottomTagOpt.Config config = new ConfigItemPriceBottomTagOpt.Config();
        config.setStrategies(null);
        List<ShelfTagVO> result = configItemPriceBottomTagOpt.compute(context, param, config);
        Assert.assertNull(result);
    }

    @Test
    public void testCompute_WhenStrategiesIsEmpty() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        ConfigItemPriceBottomTagOpt.Param param = ConfigItemPriceBottomTagOpt.Param.builder().build();
        ConfigItemPriceBottomTagOpt.Config config = new ConfigItemPriceBottomTagOpt.Config();
        config.setStrategies(new ArrayList<>());
        List<ShelfTagVO> result = configItemPriceBottomTagOpt.compute(context, param, config);
        Assert.assertNull(result);
    }

    @Test
    public void testCompute_WhenValidStrategiesButEmptyConfig() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        ConfigItemPriceBottomTagOpt.Param param = ConfigItemPriceBottomTagOpt.Param.builder().build();
        ConfigItemPriceBottomTagOpt.Config config = new ConfigItemPriceBottomTagOpt.Config();
        List<List<String>> strategies = new ArrayList<>();
        strategies.add(Arrays.asList("strategy1"));
        config.setStrategies(strategies);
        config.setStrategyConfig(new HashMap<>());
        List<ShelfTagVO> result = configItemPriceBottomTagOpt.compute(context, param, config);
        // Adjusted to check for empty list instead of null
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testCompute_WhenValidStrategiesButNullConfig() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        ConfigItemPriceBottomTagOpt.Param param = ConfigItemPriceBottomTagOpt.Param.builder().build();
        ConfigItemPriceBottomTagOpt.Config config = new ConfigItemPriceBottomTagOpt.Config();
        List<List<String>> strategies = new ArrayList<>();
        strategies.add(Arrays.asList("strategy1"));
        config.setStrategies(strategies);
        config.setStrategyConfig(null);
        List<ShelfTagVO> result = configItemPriceBottomTagOpt.compute(context, param, config);
        // Adjusted to check for empty list instead of null
        Assert.assertTrue(result.isEmpty());
    }
}
