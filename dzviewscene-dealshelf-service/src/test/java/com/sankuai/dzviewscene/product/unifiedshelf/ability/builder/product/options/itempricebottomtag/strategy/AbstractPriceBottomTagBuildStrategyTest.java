package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractPriceBottomTagBuildStrategyTest {

    @Mock
    private AbstractPriceBottomTagBuildStrategy abstractPriceBottomTagBuildStrategy;

    /**
     * Test build method when req is null, should return null.
     */
    @Test
    public void testBuildReqIsNull() throws Throwable {
        PriceBottomTagBuildReq req = null;
        when(abstractPriceBottomTagBuildStrategy.build(req)).thenReturn(null);
        ShelfTagVO result = abstractPriceBottomTagBuildStrategy.build(req);
        assertNull(result);
    }

    /**
     * Test build method when req.getProductM() is null, should return null.
     */
    @Test
    public void testBuildProductMIsNull() throws Throwable {
        PriceBottomTagBuildReq req = mock(PriceBottomTagBuildReq.class);
        when(abstractPriceBottomTagBuildStrategy.build(req)).thenReturn(null);
        ShelfTagVO result = abstractPriceBottomTagBuildStrategy.build(req);
        assertNull(result);
    }
}
