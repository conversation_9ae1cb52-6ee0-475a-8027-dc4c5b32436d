package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.common.helper.LionKeys;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class NationSubsidyPromoTagStrategyTest {

    @InjectMocks
    private NationSubsidyPromoTagStrategy strategy;

    @Mock
    private PriceBottomTagBuildReq req;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        strategy = new NationSubsidyPromoTagStrategy();
    }

    /**
     * 测试：当平台不支持国补价时，返回 null
     */
    @Test
    public void testBuildTagPlatformNotSupport() throws Throwable {
        // arrange
        when(req.getPlatform()).thenReturn(1);
//        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.nationalSubsidy.isSupportDp4NationalSubsidy", false)).thenReturn(false);
//        when(PriceUtils.isSupportDp4NationalSubsidy(anyInt())).thenReturn(false);

        // act
        ShelfTagVO result = strategy.buildTag(req);

        // assert
        assertNull(result);
    }

    /**
     * 测试：当产品没有国补价时，返回 null
     */
    @Test
    public void testBuildTagProductNoSubsidy() throws Throwable {
        // arrange
        when(req.getPlatform()).thenReturn(2);
        when(req.getProductM()).thenReturn(productM);
//        when(PriceUtils.isSupportDp4NationalSubsidy(2)).thenReturn(true);
//        when(PriceUtils.hasNationalSubsidy(productM)).thenReturn(false);

        // act
        ShelfTagVO result = strategy.buildTag(req);

        // assert
        assertNull(result);
    }

    /**
     * 测试：正常构建国补价标签
     */
    @Test
    public void testBuildTagSuccess() throws Throwable {
        // arrange
        when(req.getPlatform()).thenReturn(2);
        when(req.getProductM()).thenReturn(productM);
//        when(PriceUtils.isSupportDp4NationalSubsidy(2)).thenReturn(true);
//        when(PriceUtils.hasNationalSubsidy(productM)).thenReturn(true);
//        when(PriceUtils.getNationSubsidyPriceTag(productM)).thenReturn("100");
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setNationalSubsidyPrice(BigDecimal.valueOf(100));
        productPromoPriceM.setPromoPrice(BigDecimal.valueOf(120));
        when(productM.getPromoPrices()).thenReturn(Lists.newArrayList(productPromoPriceM));

        // act
        ShelfTagVO result = strategy.buildTag(req);

        // assert
        assertNotNull(result);
        assertEquals("¥100", result.getText().getText());
        assertEquals("国补价", result.getPreText().getText());
        assertEquals(105,result.getPreText().getStyle());
        assertEquals(ColorUtils.colorFFFFFF, result.getText().getTextColor());
        assertEquals(ColorUtils.color00A72D, result.getText().getBackgroundColor());
    }
}