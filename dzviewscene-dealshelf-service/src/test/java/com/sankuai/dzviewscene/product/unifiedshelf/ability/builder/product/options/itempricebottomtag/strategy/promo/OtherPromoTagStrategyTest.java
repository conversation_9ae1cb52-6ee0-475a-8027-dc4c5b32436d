package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemPromoDetail;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Field;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OtherPromoTagStrategyTest {

    @InjectMocks
    private OtherPromoTagStrategy strategy;

    @Mock
    private PriceBottomTagBuildReq req;

    @Mock
    private ProductM productM;

    @Mock
    private CardM cardM;

    private Map<String, String> promoType2IconNameMap;

    @Before
    public void setUp() throws Exception {
        promoType2IconNameMap = new HashMap<>();
        promoType2IconNameMap.put("50", "Pre Text");
        // Use reflection to set private field
        Field field = OtherPromoTagStrategy.class.getDeclaredField("promoType2IconNameMap");
        field.setAccessible(true);
        field.set(strategy, promoType2IconNameMap);
        when(req.getProductM()).thenReturn(productM);
        when(req.getCardM()).thenReturn(cardM);
    }

    /**
     * Test case: buildTag should return null when isValidOtherPromo returns false due to null promo price
     */
    @Test
    public void testBuildTagWhenPromoIsNull() throws Throwable {
        // arrange
        when(productM.getPromoPrices()).thenReturn(null);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNull(result);
    }

    /**
     * Test case: buildTag should return null when isValidOtherPromo returns false due to invalid promo tag type
     */
    @Test
    public void testBuildTagWhenInvalidPromoTagType() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(0);
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNull(result);
    }

    /**
     * Test case: buildTag should return null when isValidOtherPromo returns false due to member promo type
     */
    @Test
    public void testBuildTagWhenMemberPromoType() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(PromoTagTypeEnum.Member.getCode());
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNull(result);
    }

    /**
     * Test case: buildTag should return null when hitting tag black shop
     */
    @Test
    public void testBuildTagWhenHitBlackShop() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(50);
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        // Setup black shop configuration
        PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
        Map<Integer, List<Long>> blackShopIds = new HashMap<>();
        blackShopIds.put(50, Arrays.asList(123L));
        cfg.setTagBlackDpShopIds(blackShopIds);
        when(req.getCfg()).thenReturn(cfg);
        // Setup context
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.dpPoiIdL, 123L);
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        when(activityCxt.getParameters()).thenReturn(params);
        when(req.getContext()).thenReturn(activityCxt);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNull(result);
    }

    /**
     * Test case: buildTag should return valid ShelfTagVO for normal promo
     */
    @Test
    public void testBuildTagSuccessful() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(50);
        promo.setPromoTag("Test Promo");
        promo.setIcon("test_icon");
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        when(req.getPlatform()).thenReturn(1);
        // Setup empty black shop configuration
        PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
        Map<Integer, List<Long>> blackShopIds = new HashMap<>();
        cfg.setTagBlackDpShopIds(blackShopIds);
        when(req.getCfg()).thenReturn(cfg);
        // Setup context
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.dpPoiIdL, 456L);
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        when(activityCxt.getParameters()).thenReturn(params);
        when(req.getContext()).thenReturn(activityCxt);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNotNull(result);
        assertEquals("Test Promo", result.getName());
        assertNotNull(result.getText());
        assertEquals("Test Promo", result.getText().getText());
    }

    /**
     * Test case: buildTag should handle null configuration cases
     */
    @Test
    public void testBuildTagWithNullConfig() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(50);
        promo.setPromoTag("Test Promo");
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        when(req.getCfg()).thenReturn(null);
        when(req.getPlatform()).thenReturn(1);
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        when(req.getContext()).thenReturn(activityCxt);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNotNull(result);
        assertEquals("Test Promo", result.getName());
    }

    /**
     * Test case: buildTag should handle empty promo tag cases
     */
    @Test
    public void testBuildTagWithEmptyPromoTag() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(50);
        promo.setPromoTag("");
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        when(req.getPlatform()).thenReturn(1);
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        when(req.getContext()).thenReturn(activityCxt);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNotNull(result);
        assertEquals("", result.getName());
        assertNull(result.getText());
    }

    /**
     * Test case: buildTag should return null when isValidOtherPromo returns false
     */
    @Test
    public void testBuildTagWhenIsValidOtherPromoReturnsFalse() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(0);
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNull(result);
    }

    /**
     * Test case: buildTag should return null when isHitTagBlackShop returns true
     */
    @Test
    public void testBuildTagWhenIsHitTagBlackShopReturnsTrue() throws Throwable {
        // arrange
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoTagType(50);
        List<ProductPromoPriceM> promos = Collections.singletonList(promo);
        when(productM.getPromoPrices()).thenReturn(promos);
        // Setup black shop configuration
        PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
        Map<Integer, List<Long>> blackShopIds = new HashMap<>();
        blackShopIds.put(50, Arrays.asList(123L));
        cfg.setTagBlackDpShopIds(blackShopIds);
        when(req.getCfg()).thenReturn(cfg);
        // Setup context with shop id
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.dpPoiIdL, 123L);
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        when(activityCxt.getParameters()).thenReturn(params);
        when(req.getContext()).thenReturn(activityCxt);
        // act
        ShelfTagVO result = strategy.buildTag(req);
        // assert
        assertNull(result);
    }
}
