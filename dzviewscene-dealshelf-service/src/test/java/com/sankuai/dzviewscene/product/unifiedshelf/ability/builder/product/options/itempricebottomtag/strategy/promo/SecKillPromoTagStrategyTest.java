package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfProductMPromoInfoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.math.BigDecimal;
import java.util.ArrayList;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SecKillPromoTagStrategyTest {

    @InjectMocks
    private SecKillPromoTagStrategy strategy;

    /**
     * Test case: hitPromoSimplifyV2 returns true
     * Expected: return null
     */
    @Test
    public void testBuildTag_WhenHitPromoSimplifyV2_ReturnNull() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setContext(new ActivityCxt());
        try (MockedStatic<PromoSimplifyUtils> mockedStatic = Mockito.mockStatic(PromoSimplifyUtils.class)) {
            mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(any(ActivityCxt.class))).thenReturn(true);
            // act
            ShelfTagVO result = strategy.buildTag(req);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test case: getSecKillPromoPriceM returns null
     * Expected: return null
     */
    @Test
    public void testBuildTag_WhenGetSecKillPromoPriceMReturnsNull_ReturnNull() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setContext(new ActivityCxt());
        ProductM productM = new ProductM();
        productM.setPromoPrices(new ArrayList<>());
        req.setProductM(productM);
        try (MockedStatic<PromoSimplifyUtils> mockedPromoSimplify = Mockito.mockStatic(PromoSimplifyUtils.class);
            MockedStatic<RainbowSecKillUtils> mockedRainbow = Mockito.mockStatic(RainbowSecKillUtils.class);
            MockedStatic<UnifiedShelfProductMPromoInfoUtils> mockedUnified = Mockito.mockStatic(UnifiedShelfProductMPromoInfoUtils.class)) {
            mockedPromoSimplify.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(any(ActivityCxt.class))).thenReturn(false);
            mockedRainbow.when(() -> RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(any(ProductM.class))).thenReturn(true);
            mockedUnified.when(() -> UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(any(), any(), any())).thenReturn(null);
            // act
            ShelfTagVO result = strategy.buildTag(req);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test case: Complete flow with valid ShelfTagVO
     * Expected: return ShelfTagVO with text and afterPic set
     */
    @Test
    public void testBuildTag_WhenValidFlow_ReturnShelfTagVO() throws Throwable {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setContext(new ActivityCxt());
        req.setPlatform(1);
        ProductM productM = new ProductM();
        productM.setPromoPrices(new ArrayList<>());
        productM.setMarketPrice("100");
        req.setProductM(productM);
        req.setSalePrice("80");
        ShelfTagVO mockTagVO = new ShelfTagVO();
        mockTagVO.setName("共省20");
        // Ensure afterPic is set
        mockTagVO.setAfterPic(new PictureModel());
        try (MockedStatic<PromoSimplifyUtils> mockedPromoSimplify = Mockito.mockStatic(PromoSimplifyUtils.class);
            MockedStatic<RainbowSecKillUtils> mockedRainbow = Mockito.mockStatic(RainbowSecKillUtils.class);
            MockedStatic<UnifiedShelfProductMPromoInfoUtils> mockedUnified = Mockito.mockStatic(UnifiedShelfProductMPromoInfoUtils.class)) {
            mockedPromoSimplify.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(any(ActivityCxt.class))).thenReturn(false);
            mockedRainbow.when(() -> RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(any(ProductM.class))).thenReturn(true);
            mockedUnified.when(() -> UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(any(), any(), any())).thenReturn(mockTagVO);
            // act
            ShelfTagVO result = strategy.buildTag(req);
            // assert
            assertNotNull(result);
            assertNotNull(result.getText());
            assertNotNull(result.getAfterPic());
        }
    }
}
