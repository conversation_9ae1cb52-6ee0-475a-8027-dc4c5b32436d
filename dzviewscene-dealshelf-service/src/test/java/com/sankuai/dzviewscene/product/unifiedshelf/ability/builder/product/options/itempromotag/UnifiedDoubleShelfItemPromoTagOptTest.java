package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempromotag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPromoTagVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedDoubleShelfItemPromoTagOpt的compute方法
 */
public class UnifiedDoubleShelfItemPromoTagOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private ProductM mockProductM;
    @Mock
    private UnifiedShelfItemPromoTagVP.Param mockParam;
    @Mock
    private UnifiedDoubleShelfItemPromoTagOpt.Config mockConfig;

    private UnifiedDoubleShelfItemPromoTagOpt underTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        underTest = new UnifiedDoubleShelfItemPromoTagOpt();
    }

    /**
     * 测试当销售价格和市场价格都不为null，且折扣值在配置的上限之下时，应返回非空的折扣标签列表
     */
    @Test
    public void testCompute_WithValidDiscount_ReturnsNonNullList() {
        // arrange
        when(mockParam.getSalePrice()).thenReturn("5");
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getMarketPrice()).thenReturn("10");
        when(mockConfig.getMaxDiscount()).thenReturn(9.0);

        // act
        List<RichLabelModel> result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("5.0折", result.get(0).getText());
    }

    /**
     * 测试当销售价格或市场价格为null时，应返回null
     */
    @Test
    public void testCompute_WithNullPrices_ReturnsNull() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getSalePrice()).thenReturn(null);
        // act
        List<RichLabelModel> result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNull(result);
    }

    /**
     * 测试当销售价格或市场价格为0时，应返回null
     */
    @Test
    public void testCompute_WithZeroPrices_ReturnsNull() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getSalePrice()).thenReturn("0");

        // act
        List<RichLabelModel> result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNull(result);
    }

    /**
     * 测试当折扣值超过配置的上限时，应返回null
     */
    @Test
    public void testCompute_WithDiscountAboveLimit_ReturnsNull() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getSalePrice()).thenReturn("1");
        when(mockConfig.getMaxDiscount()).thenReturn(0.5);

        // act
        List<RichLabelModel> result = underTest.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNull(result);
    }
}

