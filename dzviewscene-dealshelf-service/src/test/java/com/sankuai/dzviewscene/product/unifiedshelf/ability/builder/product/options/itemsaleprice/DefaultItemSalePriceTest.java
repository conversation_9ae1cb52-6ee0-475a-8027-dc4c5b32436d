package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSalePriceVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class DefaultItemSalePriceTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private Param param;
    @Mock
    private ProductM productM;
    @Mock
    private CardM cardM;
    @Mock
    private ProductPromoPriceM productPromoPriceM;

    private DefaultItemSalePrice defaultItemSalePrice;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultItemSalePrice = new DefaultItemSalePrice();
        when(param.getProductM()).thenReturn(productM);
        when(param.getCardM()).thenReturn(cardM);
    }

    /**
     * 测试场景：最终优惠价不为空
     */
    @Test
    public void testCompute_UserHasPromoPriceAndTagIsNotBlank() {
        // arrange
        try(MockedStatic<PriceUtils> priceUtils = Mockito.mockStatic(PriceUtils.class)){
            priceUtils.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(productPromoPriceM);
            when(productPromoPriceM.getPromoPriceTag()).thenReturn("185");

            // act
            String result = defaultItemSalePrice.compute(context, param, null);

            // assert
            assertEquals("185", result);
        }
    }

    /**
     * 测试场景：最终优惠价为空，返回基础价格
     */
    @Test
    public void testCompute_UserHasPromoPriceButTagIsBlank() {
        // arrange
        try(MockedStatic<PriceUtils> priceUtils = Mockito.mockStatic(PriceUtils.class)){
            priceUtils.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(productPromoPriceM);
            when(productPromoPriceM.getPromoPriceTag()).thenReturn("");

            when(productM.getBasePriceTag()).thenReturn("130");

            // act
            String result = defaultItemSalePrice.compute(context, param, null);

            // assert
            assertEquals("130", result);
        }
    }

    /**
     * 测试场景：没有优惠
     */
    @Test
    public void testCompute_UserHasNoPromoPrice() {
        // arrange
        try(MockedStatic<PriceUtils> priceUtils = Mockito.mockStatic(PriceUtils.class)){
            priceUtils.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(null);
            when(productM.getBasePriceTag()).thenReturn("130");

            // act
            String result = defaultItemSalePrice.compute(context, param, null);

            // assert
            assertEquals("130", result);
        }
    }
}

