package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSalePriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.math.BigDecimal;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfFreeDealItemSalePriceOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    private com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt.Param param;

    @Before
    public void setUp() {
        param = com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt.Param.builder().productM(productM).build();
    }

    @Test
    public void testComputeProductMIsNull() throws Throwable {
        when(productM.getBasePrice()).thenReturn(null);
        com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt opt = new com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt();
        String result = opt.compute(activityCxt, param, null);
        assertEquals("0", result);
    }

    @Test
    public void testComputeBasePriceIsNull() throws Throwable {
        when(productM.getBasePrice()).thenReturn(null);
        com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt opt = new com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt();
        String result = opt.compute(activityCxt, param, null);
        assertEquals("0", result);
    }

    @Test
    public void testComputeBasePriceIsZero() throws Throwable {
        when(productM.getBasePrice()).thenReturn(BigDecimal.ZERO);
        com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt opt = new com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt();
        String result = opt.compute(activityCxt, param, null);
        assertEquals("0", result);
    }

    @Test
    public void testComputeBasePriceIsNotZero() throws Throwable {
        when(productM.getBasePrice()).thenReturn(BigDecimal.ONE);
        com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt opt = new com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsaleprice.UnifiedShelfFreeDealItemSalePriceOpt();
        String result = opt.compute(activityCxt, param, null);
        assertEquals("1", result);
    }
}
