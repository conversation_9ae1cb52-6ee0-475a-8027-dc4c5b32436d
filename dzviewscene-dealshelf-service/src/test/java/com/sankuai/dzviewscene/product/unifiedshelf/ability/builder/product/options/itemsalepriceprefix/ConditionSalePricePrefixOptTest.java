package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils.ATTR_MINIPROGRAM_LINKED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * ConditionSalePricePrefixOpt.compute 方法的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ConditionSalePricePrefixOptTest {

    @InjectMocks
    private ConditionSalePricePrefixOpt conditionSalePricePrefixOpt;
    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private ProductM productM;
    @Mock
    private ConditionSalePricePrefixOpt.Config config;

    @Before
    public void setUp() {
        conditionSalePricePrefixOpt = new ConditionSalePricePrefixOpt();
        activityCxt = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(Param.class);
        config = new ConditionSalePricePrefixOpt.Config();
    }

    /**
     * 测试 prefixTypes 为空时返回 null
     */
    @Test
    public void testComputeWithEmptyPrefixTypes() {
        config.setPrefixTypes(Collections.emptyList());

        String result = conditionSalePricePrefixOpt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试 prefixTypes 包含不存在的类型时忽略该类型
     */
    @Test
    public void testComputeWithNonexistentPrefixType() {
        config.setPrefixTypes(Collections.singletonList("nonexistent"));

        String result = conditionSalePricePrefixOpt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试正常场景下返回正确的前缀
     */
    @Test
    public void testComputeWithValidPrefixTypeReturnsPrefix() {
        config.setPrefixTypes(Collections.singletonList(ConditionSalePricePrefixOpt.PrefixType.MINIPROGRAM.getCode()));
        Mockito.when(param.getProductM()).thenReturn(productM);
        Mockito.when(productM.getAttr(ATTR_MINIPROGRAM_LINKED)).thenReturn("是");

        String result = conditionSalePricePrefixOpt.compute(activityCxt, param, config);

        assertEquals("焕新价", result);
    }

    /**
     * 测试所有 PrefixTextBuilder 都返回 null 时方法返回 null
     */
    @Test
    public void testComputeWithAllBuildersReturningNull() {
        config.setPrefixTypes(Arrays.asList(ConditionSalePricePrefixOpt.PrefixType.MINIPROGRAM.getCode(), "nonexistent"));
        Mockito.when(param.getProductM()).thenReturn(productM);
        Mockito.when(productM.getAttr(ATTR_MINIPROGRAM_LINKED)).thenReturn("否");

        String result = conditionSalePricePrefixOpt.compute(activityCxt, param, config);

        assertNull(result);
    }
}
