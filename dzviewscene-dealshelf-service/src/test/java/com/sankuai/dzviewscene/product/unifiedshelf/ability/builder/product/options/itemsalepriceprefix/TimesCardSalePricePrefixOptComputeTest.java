package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix.TimesCardSalePricePrefixOpt;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TimesCardSalePricePrefixOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductM productM;

    @Mock
    private TimesCardSalePricePrefixOpt.Param param;

    @InjectMocks
    private TimesCardSalePricePrefixOpt timesCardSalePricePrefixOpt;

    @Test
    public void testCompute_ProductTypeTimeCard_TimesAttrPositive_ReturnsFormatString() throws Throwable {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        when(ProductMAttrUtils.getAttrValue(productM, TimesDealUtil.TIMES_CARD_TIMES)).thenReturn("5");
        when(param.getProductM()).thenReturn(productM);
        String result = timesCardSalePricePrefixOpt.compute(context, param, null);
        assertEquals("共5次", result);
    }

    @Test
    public void testCompute_ProductTypeTimeCard_TimesAttrNonPositive_ReturnsNull() throws Throwable {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        when(ProductMAttrUtils.getAttrValue(productM, TimesDealUtil.TIMES_CARD_TIMES)).thenReturn("0");
        when(param.getProductM()).thenReturn(productM);
        String result = timesCardSalePricePrefixOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testCompute_ProductTypeNotTimeCard_TimesDealTrue_ReturnsSingle() throws Throwable {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.DEAL.getType());
        when(productM.isTimesDeal()).thenReturn(true);
        when(param.getProductM()).thenReturn(productM);
        String result = timesCardSalePricePrefixOpt.compute(context, param, null);
        assertEquals("单次", result);
    }

    @Test
    public void testCompute_ProductTypeNotTimeCard_TimesDealFalse_ReturnsNull() throws Throwable {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.DEAL.getType());
        when(productM.isTimesDeal()).thenReturn(false);
        when(param.getProductM()).thenReturn(productM);
        String result = timesCardSalePricePrefixOpt.compute(context, param, null);
        assertNull(result);
    }
}
