package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

public class TimesCardSalePricePrefixOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private UnifiedShelfSalePricePrefixVP.Param param;
    @Mock
    private ProductM productM;

    private TimesCardSalePricePrefixOpt timesCardSalePricePrefixOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        timesCardSalePricePrefixOpt = new TimesCardSalePricePrefixOpt();
        when(param.getProductM()).thenReturn(productM);
    }

    /**
     * 测试商品类型不是次卡时返回null
     */
    @Test
    public void testCompute_ProductTypeNotTimeCard_ReturnsNull() {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.DEAL.getType());

        String result = timesCardSalePricePrefixOpt.compute(context, param, null);

        assertNull(result);
    }

    /**
     * 测试商品类型是次卡但次数属性为空时返回null
     */
    @Test
    public void testCompute_ProductTypeTimeCard_TimesAttrEmpty_ReturnsNull() {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        when(ProductMAttrUtils.getAttrValue(productM, TimesDealUtil.TIMES_CARD_TIMES)).thenReturn("");

        String result = timesCardSalePricePrefixOpt.compute(context, param, null);

        assertNull(result);
    }

    /**
     * 测试商品类型是次卡但次数属性非数字时返回null
     */
    @Test
    public void testCompute_ProductTypeTimeCard_TimesAttrNotNumber_ReturnsNull() {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        when(ProductMAttrUtils.getAttrValue(productM, TimesDealUtil.TIMES_CARD_TIMES)).thenReturn("abc");

        String result = timesCardSalePricePrefixOpt.compute(context, param, null);

        assertNull(result);
    }

    /**
     * 测试商品类型是次卡且次数属性为0时返回null
     */
    @Test
    public void testCompute_ProductTypeTimeCard_TimesAttrZero_ReturnsNull() {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        when(ProductMAttrUtils.getAttrValue(productM, TimesDealUtil.TIMES_CARD_TIMES)).thenReturn("0");

        String result = timesCardSalePricePrefixOpt.compute(context, param, null);

        assertNull(result);
    }

    /**
     * 测试商品类型是次卡且次数属性为正数时返回正确的前缀
     */
    @Test
    public void testCompute_ProductTypeTimeCard_TimesAttrPositive_ReturnsPrefix() {
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        when(ProductMAttrUtils.getAttrValue(productM, TimesDealUtil.TIMES_CARD_TIMES)).thenReturn("5");

        String result = timesCardSalePricePrefixOpt.compute(context, param, null);

        assertEquals("共5次", result);
    }
}
