package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfTwoStagePaymentSalePricePrefixOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private UnifiedShelfSalePricePrefixVP.Param param;
    @Mock
    private ShelfDouHuFetcher shelfDouHuFetcher;
    @Mock
    private UnifiedShelfTwoStagePaymentSalePricePrefixOpt.Config config;
    @InjectMocks
    private UnifiedShelfTwoStagePaymentSalePricePrefixOpt opt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        opt = new UnifiedShelfTwoStagePaymentSalePricePrefixOpt();
        config = new UnifiedShelfTwoStagePaymentSalePricePrefixOpt.Config();
        config.setTwoStagePaymentAttrValues(Arrays.asList("value1", "value2"));
        config.setExpIds(Arrays.asList("exp1", "exp2"));
    }

    /**
     * 测试命中实验且产品为二段支付交易，应返回预定义的价格前缀
     */
    @Test
    public void testCompute_HitExpAndIsTwoStagePaymentDeal() {
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("exp1");
        List<DouHuM> douHuMList = Collections.singletonList(douHuM);
        when(activityCxt.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);
        ProductM productM = new ProductM();
        productM.setAttr("computer_project_type", "value1");
        when(param.getProductM()).thenReturn(productM);

        String result = opt.compute(activityCxt, param, config);

        assertEquals("预付金", result);
    }

    /**
     * 测试未命中实验，应返回null
     */
    @Test
    public void testCompute_NotHitExp() {
        when(activityCxt.getSource(ShelfDouHuFetcher.CODE)).thenReturn(Collections.emptyList());
        ProductM productM = new ProductM();
        productM.setAttr("computer_project_type", "value1");

        String result = opt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试产品不是二段支付交易，应返回null
     */
    @Test
    public void testCompute_NotTwoStagePaymentDeal() {
        List<DouHuM> douHuMList = Collections.singletonList(new DouHuM());
        when(activityCxt.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);
        ProductM productM = new ProductM();
        productM.setAttr("computer_project_type", "nonMatchingValue");

        String result = opt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试空参数，应返回null
     */
    @Test
    public void testCompute_EmptyParameters() {
        config.setTwoStagePaymentAttrValues(Collections.emptyList());
        config.setExpIds(Collections.emptyList());

        String result = opt.compute(activityCxt, param, config);

        assertNull(result);
    }
}
