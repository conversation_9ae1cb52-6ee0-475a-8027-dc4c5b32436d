package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepricesuffix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePriceSuffixVP.Param;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class ConditionSalePriceSuffixOptTest {

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private Param mockParam;
    @Mock
    private ProductM productM;
    @Mock
    private ConditionSalePriceSuffixOpt.Config mockConfig;

    private ConditionSalePriceSuffixOpt conditionSalePriceSuffixOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        conditionSalePriceSuffixOpt = new ConditionSalePriceSuffixOpt();
    }

    /**
     * 测试compute方法，当config中的suffixTypes为空时应返回null。
     */
    @Test
    public void testComputeWithEmptySuffixTypes() {
        when(mockConfig.getSuffixTypes()).thenReturn(Collections.emptyList());

        String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试compute方法，当builderMap中不存在对应的suffixType时返回null。
     */
    @Test
    public void testComputeWithNonExistentSuffixTextBuilder() {
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("nonExistentSuffixType"));

        String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试suffixType为次卡时
     */
    @Test
    public void testComputeWithTimes() {
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("times"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("3");

        String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

        assertEquals("/3次", result);
    }

    /**
     * 测试suffixType为加项时
     */
    @Test
    public void testComputeWithAdditional() {
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("additional"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.isAdditionalDeal()).thenReturn(true);

        String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

        assertEquals("起", result);
    }

    /**
     * 测试suffixType为团建时
     */
    @Test
    public void testComputeWithCampaign() {
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("campaign"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.getAttr("campaginPriceDesc")).thenReturn("/团");

        String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

        assertEquals("/团", result);
    }

    /**
     * 测试suffixType为多sku时
     */
    @Test
    public void testComputeWithMultiSku() {
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("multiSku"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.getAttr("dealMultiSkuFieldAttr")).thenReturn("true");

        String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

        assertEquals("起", result);
    }

    /**
     * 测试suffixType为连续包月时
     */
    @Test
    public void testComputeWithContinuousMonthly() {
        // 准备测试数据
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("continuousMonthly"));
        when(mockParam.getProductM()).thenReturn(productM);

        // 使用静态方法模拟
        try (org.mockito.MockedStatic<TimesDealUtil> timesDealUtilMockedStatic = org.mockito.Mockito.mockStatic(TimesDealUtil.class)) {
            // 模拟连续包月判断返回true
            timesDealUtilMockedStatic.when(() -> TimesDealUtil.isContinuousMonthly(productM)).thenReturn(true);

            // 执行测试
            String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

            // 验证结果
            assertEquals("/月", result);
        }
    }

    /**
     * 测试suffixType为连续包月时，但商品不是连续包月类型
     */
    @Test
    public void testComputeWithContinuousMonthly_NotContinuousMonthly() {
        // 准备测试数据
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("continuousMonthly"));
        when(mockParam.getProductM()).thenReturn(productM);

        // 使用静态方法模拟
        try (org.mockito.MockedStatic<TimesDealUtil> timesDealUtilMockedStatic = org.mockito.Mockito.mockStatic(TimesDealUtil.class)) {
            // 模拟连续包月判断返回false
            timesDealUtilMockedStatic.when(() -> TimesDealUtil.isContinuousMonthly(productM)).thenReturn(false);

            // 执行测试
            String result = conditionSalePriceSuffixOpt.compute(mockContext, mockParam, mockConfig);

            // 验证结果 - 应该返回null
            assertNull(result);
        }
    }
}
