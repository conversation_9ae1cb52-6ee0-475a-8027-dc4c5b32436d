package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepricesuffix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePriceSuffixVP;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertNull;

/**
 * 测试DefaultItemSalePriceSuffixOpt类的compute方法
 */
public class DefaultItemSalePriceSuffixOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfSalePriceSuffixVP.Param mockParam;

    private DefaultItemSalePriceSuffixOpt defaultItemSalePriceSuffixOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultItemSalePriceSuffixOpt = new DefaultItemSalePriceSuffixOpt();
    }

    /**
     * 测试compute方法在正常情况下返回null
     */
    @Test
    public void testComputeReturnsNull() {
        // arrange
        // 由于compute方法逻辑简单，直接返回null，因此不需要额外的arrange步骤

        // act
        String result = defaultItemSalePriceSuffixOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertNull("compute方法应该返回null", result);
    }
}
