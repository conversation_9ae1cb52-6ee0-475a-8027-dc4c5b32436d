package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemspecialtags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.UnifiedShelfItemConfigSpecialTagOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSpecialTagVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * 测试UnifiedShelfItemConfigSpecialTagOpt的compute方法
 */
public class UnifiedShelfItemConfigSpecialTagOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemSpecialTagVP.Param mockParam;
    @Mock
    private UnifiedShelfItemConfigSpecialTagOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private UnifiedShelfItemConfigSpecialTagOpt target;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        target = new UnifiedShelfItemConfigSpecialTagOpt();
    }

    /**
     * 测试compute方法，当buildLabelList返回非空列表时
     */
    @Test
    public void testComputeWhenBuildLabelListReturnsNonEmptyList() {
        // arrange
        List<ShelfTagVO> expectedList = new ArrayList<>();
        expectedList.add(new ShelfTagVO());
        ProductM mockProductM = new ProductM();
        mockProductM.setAttr("test","value");
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(target.buildLabelList(mockActivityCxt, mockParam, mockConfig)).thenReturn(expectedList);

        UnifiedShelfItemConfigSpecialTagOpt.Config mockConfig = new UnifiedShelfItemConfigSpecialTagOpt.Config();
        mockConfig.setAttrKeys(Lists.newArrayList("test"));
        ActivityCxt mockActivityCxt = new ActivityCxt();
        mockActivityCxt.setSceneCode("test_code");
        // act
        ItemSpecialTagVO result = target.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNotNull("结果不应为null", result);
    }

    /**
     * 测试compute方法，当buildLabelList返回空列表时
     */
    @Test
    public void testComputeWhenBuildLabelListReturnsEmptyList() {
        // arrange
        List<ShelfTagVO> emptyList = new ArrayList<>();
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(target.buildLabelList(mockActivityCxt, mockParam, mockConfig)).thenReturn(emptyList);

        // act
        ItemSpecialTagVO result = target.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNull("当标签列表为空时，结果应为null", result);
    }

    /**
     * 测试compute方法，当buildLabelList返回null时
     */
    @Test
    public void testComputeWhenBuildLabelListReturnsNull() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(target.buildLabelList(mockActivityCxt, mockParam, mockConfig)).thenReturn(null);

        // act
        ItemSpecialTagVO result = target.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNull("当buildLabelList返回null时，结果应为null", result);
    }


    /**
     * 测试compute方法，当Config为null时
     */
    @Test(expected = NullPointerException.class)
    public void testComputeWhenConfigIsNull() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        // 此处不需要模拟buildLabelList的行为，因为Config为null时，compute方法的逻辑可能直接抛出异常

        // act
        target.compute(mockActivityCxt, mockParam, null);

        // assert
        // 期望方法抛出NullPointerException
    }

    /**
     * 测试compute方法，当ActivityCxt为null时
     */
    @Test
    public void testComputeWhenActivityCxtIsNull() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        // 此处不需要模拟buildLabelList的行为，因为ActivityCxt为null时，compute方法的逻辑可能直接抛出异常

        // act
        target.compute(null, mockParam, mockConfig);

        // assert
        // 期望方法抛出NullPointerException
    }

}
