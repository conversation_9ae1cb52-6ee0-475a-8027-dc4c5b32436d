package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemspecialtags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.UnifiedShelfItemDefaultSpecialTagOpt;
import org.junit.Assert;
import org.junit.Test;

public class UnifiedShelfItemDefaultSpecialTagOptTest {


    @Test
    public void test_UnifiedShelfItemDefaultSpecialTagOpt() {

        UnifiedShelfItemDefaultSpecialTagOpt opt = new UnifiedShelfItemDefaultSpecialTagOpt();
        ItemSpecialTagVO compute = opt.compute(new ActivityCxt(), null, null);

        Assert.assertNull(compute);
    }
}
