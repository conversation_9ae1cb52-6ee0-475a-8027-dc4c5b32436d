package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemspecialtags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.UnifiedShelfItemRecommendTagOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSpecialTagVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

/**
 * 测试UnifiedShelfItemRecommendTagOpt的compute方法
 */
public class UnifiedShelfItemRecommendTagOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemSpecialTagVP.Param mockParam;
    @Mock
    private UnifiedShelfItemRecommendTagOpt.Config mockConfig;
    @Mock
    private ShelfItemVO mockShelfItemVO;
    @Mock
    private ProductM mockProductM;

    private UnifiedShelfItemRecommendTagOpt unifiedShelfItemRecommendTagOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedShelfItemRecommendTagOpt = new UnifiedShelfItemRecommendTagOpt();
        when(mockParam.getShelfItemVO()).thenReturn(mockShelfItemVO);
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    /**
     * 测试价格下方有标签时，不展示推荐标签
     */
    @Test
    public void testCompute_WithPriceBottomTags() {
        when(mockShelfItemVO.getPriceBottomTags()).thenReturn(Lists.newArrayList(new ShelfTagVO()));

        ItemSpecialTagVO result = unifiedShelfItemRecommendTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试无推荐标签时，不展示推荐标签
     */
    @Test
    public void testCompute_WithoutRecommendTags() {
        when(mockShelfItemVO.getPriceBottomTags()).thenReturn(Collections.emptyList());
        when(mockProductM.getBasePriceTag()).thenReturn("");

        ItemSpecialTagVO result = unifiedShelfItemRecommendTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试有推荐标签时，展示推荐标签
     */
    @Test
    public void testCompute_WithRecommendTags() {
        when(mockShelfItemVO.getPriceBottomTags()).thenReturn(Collections.emptyList());
        try(MockedStatic<PriceAboveTagsUtils> mockedStatic = Mockito.mockStatic(PriceAboveTagsUtils.class)){
            mockedStatic.when(() -> PriceAboveTagsUtils.getTradeRateTag(mockProductM)).thenReturn("竞争圈标签");
            ItemSpecialTagVO result = unifiedShelfItemRecommendTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

            assertNotNull(result);
            assertFalse(result.getTags().isEmpty());
        }
    }

    /**
     * 测试有无推荐标签但有Ugc标签时，展示ugc标签
     */
    @Test
    public void testCompute_WithUGCTags() {
        when(mockShelfItemVO.getPriceBottomTags()).thenReturn(Collections.emptyList());
        try(MockedStatic<PriceAboveTagsUtils> mockedStatic = Mockito.mockStatic(PriceAboveTagsUtils.class)){
            mockedStatic.when(() -> PriceAboveTagsUtils.getRecommendTag(mockProductM)).thenReturn(Lists.newArrayList("ugc标签"));
            ItemSpecialTagVO result = unifiedShelfItemRecommendTagOpt.compute(mockActivityCxt, mockParam, mockConfig);

            assertNotNull(result);
            assertFalse(result.getTags().isEmpty());
        }
    }
}
