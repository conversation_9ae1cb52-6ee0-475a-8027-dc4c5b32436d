package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.JumpUrlEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils.ATTR_MINIPROGRAM_LINKED;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 测试JumpUrlFactory的getJumpUrl方法
 */
public class JumpUrlFactoryTest {

    private static final String ATTR_MINIPROGRAM_JUMP_URL = "meidiMiniprogramLink";

    private ActivityCxt context;
    private ProductM productM;

    @Before
    public void setUp() {
        context = Mockito.mock(ActivityCxt.class);
        productM = Mockito.mock(ProductM.class);
    }

    /**
     * 测试策略为空时
     */
    @Test
    public void testGetJumpUrlStrategyIsEmpty() {
        String result = JumpUrlFactory.getJumpUrl(context, productM, "");
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试策略为null时
     */
    @Test
    public void testGetJumpUrlStrategyIsNull() {
        String result = JumpUrlFactory.getJumpUrl(context, productM, null);
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试策略无效时
     */
    @Test
    public void testGetJumpUrlStrategyIsInvalid() {
        String result = JumpUrlFactory.getJumpUrl(context, productM, "invalid_strategy");
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试家电小程序跳转，产品属性中包含跳转URL
     */
    @Test
    public void testGetJumpUrlApplianceMiniprogramWithUrl() {
        when(productM.getAttr(ATTR_MINIPROGRAM_JUMP_URL)).thenReturn("miniprogram_link");
        when(productM.getAttr(ATTR_MINIPROGRAM_LINKED)).thenReturn("是");
        String result = JumpUrlFactory.getJumpUrl(context, productM, JumpUrlEnum.APPLIANCE_MINIPROGRAM.getCode());
        assertEquals("miniprogram_link", result);
    }

    /**
     * 测试家电小程序跳转，产品属性中不包含跳转URL
     */
    @Test
    public void testGetJumpUrlApplianceMiniprogramWithoutUrl() {
        when(productM.getJumpUrl()).thenReturn("default_jump_url");
        String result = JumpUrlFactory.getJumpUrl(context, productM, JumpUrlEnum.APPLIANCE_MINIPROGRAM.getCode());
        assertEquals("default_jump_url", result);
    }

    /**
     * 测试回收小程序跳转
     */
    @Test
    public void testGetJumpUrlRecycleMiniprogram() {
        when(productM.getAttr(ATTR_MINIPROGRAM_JUMP_URL)).thenReturn("recycle_miniprogram_link");
        String result = JumpUrlFactory.getJumpUrl(context, productM, JumpUrlEnum.RECYCLE_MINIPROGRAM.getCode());
        assertEquals("recycle_miniprogram_link", result);
    }
}
