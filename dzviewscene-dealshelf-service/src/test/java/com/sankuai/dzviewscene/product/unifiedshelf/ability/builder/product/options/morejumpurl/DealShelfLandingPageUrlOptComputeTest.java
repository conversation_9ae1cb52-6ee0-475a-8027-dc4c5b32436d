package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.morejumpurl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealShelfLandingPageUrlOptComputeTest {

    @Mock
    private ActivityCxt activityCxt;

    @Test(expected = NullPointerException.class)
    public void testCompute_ActivityCxtIsNull() throws Throwable {
        DealShelfLandingPageUrlOpt dealShelfLandingPageUrlOpt = new DealShelfLandingPageUrlOpt();
        dealShelfLandingPageUrlOpt.compute(null, null, null);
    }

    @Test(expected = NullPointerException.class)
    public void testCompute_PaginationIsNull() throws Throwable {
        DealShelfLandingPageUrlOpt dealShelfLandingPageUrlOpt = new DealShelfLandingPageUrlOpt();
        dealShelfLandingPageUrlOpt.compute(activityCxt, null, null);
    }

    @Test
    public void testCompute_PaginationIsOne() throws Throwable {
        DealShelfLandingPageUrlOpt dealShelfLandingPageUrlOpt = new DealShelfLandingPageUrlOpt();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("pagination", "1");
        when(activityCxt.getParameters()).thenReturn(parameters);
        String result = dealShelfLandingPageUrlOpt.compute(activityCxt, null, null);
        assertNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testCompute_PaginationIsNotOne() throws Throwable {
        DealShelfLandingPageUrlOpt dealShelfLandingPageUrlOpt = new DealShelfLandingPageUrlOpt();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("pagination", "0");
        when(activityCxt.getParameters()).thenReturn(parameters);
        dealShelfLandingPageUrlOpt.compute(activityCxt, null, null);
    }
}
