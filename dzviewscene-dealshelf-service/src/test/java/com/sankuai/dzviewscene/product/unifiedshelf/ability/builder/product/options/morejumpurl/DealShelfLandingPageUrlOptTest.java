package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.morejumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.morejumpurl.DealShelfLandingPageUrlOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreJumpUrlVP.Param;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class DealShelfLandingPageUrlOptTest {

    @Mock
    private ActivityCxt activityCxt;
    private DealShelfLandingPageUrlOpt dealShelfLandingPageUrlOpt;
    private Config config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dealShelfLandingPageUrlOpt = new DealShelfLandingPageUrlOpt();
        config = new Config();
    }

    /**
     * 测试场景：落地页有分页，应返回空字符串
     */
    @Test
    public void testCompute_WithPaging() {
        // arrange
        try(MockedStatic<ParamsUtil> paramsUtilMockedStatic = Mockito.mockStatic(ParamsUtil.class)) {
            paramsUtilMockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(true);
            DealShelfLandingPageUrlOpt.Param param = DealShelfLandingPageUrlOpt.Param.builder().build();

            // act
            String result = dealShelfLandingPageUrlOpt.compute(activityCxt, param, config);

            // assert
            assertTrue(StringUtils.isEmpty(result));
        }
    }

    /**
     * 测试货架场景：平台为MT，使用MT URL
     */
    @Test
    public void testCompute_MTPlatformWithDefaultUrl() {
        // arrange
        try(MockedStatic<ParamsUtil> paramsUtilMockedStatic = Mockito.mockStatic(ParamsUtil.class)) {
            paramsUtilMockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(false);
            when(activityCxt.getSceneCode()).thenReturn("beauty");
            DealShelfLandingPageUrlOpt.Param param = DealShelfLandingPageUrlOpt.Param.builder().platform(2).build();

            // act
            String result = dealShelfLandingPageUrlOpt.compute(activityCxt, param, config);

            // assert
            assertTrue(result.startsWith("imeituan:"));
        }
    }

    /**
     * 测试货架场景：平台为DP，使用默认DP URL
     */
    @Test
    public void testCompute_DPPlatformWithCustomUrl() {
        // arrange
        try(MockedStatic<ParamsUtil> paramsUtilMockedStatic = Mockito.mockStatic(ParamsUtil.class)) {
            paramsUtilMockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(false);
            when(activityCxt.getSceneCode()).thenReturn("beauty");
            DealShelfLandingPageUrlOpt.Param param = DealShelfLandingPageUrlOpt.Param.builder().platform(1).build();

            // act
            String result = dealShelfLandingPageUrlOpt.compute(activityCxt, param, config);

            // assert
            assertTrue(result.startsWith("dianping:"));
        }
    }

    /**
     * 测试落地页场景，返回为空
     */
    @Test
    public void testCompute_LanddingPageUrl() {
        // arrange
        try(MockedStatic<ParamsUtil> paramsUtilMockedStatic = Mockito.mockStatic(ParamsUtil.class)) {
            paramsUtilMockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(true);
            when(activityCxt.getSceneCode()).thenReturn("beauty");
            DealShelfLandingPageUrlOpt.Param param = DealShelfLandingPageUrlOpt.Param.builder().platform(2).build();

            // act
            String result = dealShelfLandingPageUrlOpt.compute(activityCxt, param, config);

            // assert
            assertNull(result);
        }
    }
}
