package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.morejumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreJumpUrlVP;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

public class DefaultProductAreaMoreJumpUrlOptTest {

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private ProductAreaMoreJumpUrlVP.Param mockParam;
    private DefaultProductAreaMoreJumpUrlOpt.Config config;

    private DefaultProductAreaMoreJumpUrlOpt defaultProductAreaMoreJumpUrlOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultProductAreaMoreJumpUrlOpt = new DefaultProductAreaMoreJumpUrlOpt();
        config = new DefaultProductAreaMoreJumpUrlOpt.Config();
    }

    /**
     * 测试compute方法，当urlFormat为空时应返回null
     */
    @Test
    public void testComputeWithEmptyUrlFormatReturnsNull() {

        // arrange
        config.setUrlFormat("");

        // act
        String result = defaultProductAreaMoreJumpUrlOpt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(null, result);
    }

    /**
     * 测试compute方法，当不需要url时应返回null
     */
    @Test
    public void testComputeWhenUrlIsNotNeededReturnsNull() {
        // arrange
        config.setUrlFormat("http://example.com");
        config.setLimitProductNums(10);
        when(mockContext.getParam("totalProductNum")).thenReturn(5);

        // act
        String result = defaultProductAreaMoreJumpUrlOpt.compute(mockContext, mockParam, config);

        // assert
        assertEquals(null, result);
    }

    /**
     * 测试compute方法，当url参数为空时应返回格式化字符串
     */
    @Test
    public void testComputeWithEmptyUrlParamsReturnsFormattedString() {
        // arrange
        config.setUrlFormat("http://example.com");
        config.setUrlParamEnums(Arrays.asList());

        // act
        String result = defaultProductAreaMoreJumpUrlOpt.compute(mockContext, mockParam, config);

        // assert
        assertEquals("http://example.com", result);
    }

    /**
     * 测试compute方法，当url参数非空时应返回正确的格式化url
     */
    @Test
    public void testComputeWithUrlParamsReturnsCorrectFormattedUrl() {
        // arrange
        config.setUrlFormat("http://example.com?poiId=%s");
        config.setUrlParamEnums(Arrays.asList("poiId"));
        when(mockParam.getDpPoiIdL()).thenReturn(12345L);

        // act
        String result = defaultProductAreaMoreJumpUrlOpt.compute(mockContext, mockParam, config);

        // assert
        assertEquals("http://example.com?poiId=12345", result);
    }

    /**
     * 测试compute方法，当url为H5时应返回平台化的web url
     */
    @Test
    public void testComputeWithH5UrlReturnsPlatformWebUrl() {
        // arrange
        config.setUrlFormat("http://example.com");
        config.setH5Url(true);
        config.setUrlParamEnums(Arrays.asList("poiId"));
        when(mockParam.getPlatform()).thenReturn(1);
        when(mockParam.getDpPoiIdL()).thenReturn(12345L);

        // act
        String result = defaultProductAreaMoreJumpUrlOpt.compute(mockContext, mockParam, config);

        // assert
        assertTrue(result.startsWith("dianping://web?"));
    }
}
