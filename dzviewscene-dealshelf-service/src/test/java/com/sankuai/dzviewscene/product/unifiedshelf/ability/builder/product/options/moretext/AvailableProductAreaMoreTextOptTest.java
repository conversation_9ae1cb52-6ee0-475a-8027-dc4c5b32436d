package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreTextVP.Param;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class AvailableProductAreaMoreTextOptTest {

    private AvailableProductAreaMoreTextOpt availableProductAreaMoreTextOpt;
    private ActivityCxt context;
    private Param param;
    private AvailableProductAreaMoreTextOpt.Config config;

    @Before
    public void setUp() {
        availableProductAreaMoreTextOpt = new AvailableProductAreaMoreTextOpt();
        context = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(Param.class);
        config = new AvailableProductAreaMoreTextOpt.Config();
    }

    /**
     * 测试场景：当总数小于等于默认显示数量时，应返回空字符串
     */
    @Test
    public void testCompute_WhenTotalNumLessThanOrEqualToDefaultShowNum_ReturnEmptyString() {
        // arrange
        config.setNeedCurrentFilterAllProductNum(false);
        when(param.getItemAreaItemCnt()).thenReturn(5);
        when(param.getDefaultShowNum()).thenReturn(5);

        // act
        String result = availableProductAreaMoreTextOpt.compute(context, param, config);

        // assert
        assertEquals("", result);
    }

    /**
     * 测试场景：当所有商品都不可购时，应返回"展开全部不可购"
     */
    @Test
    public void testCompute_WhenAllProductsCantBuy_ReturnAllCantBuyText() {
        // arrange
        try (MockedStatic<ProductMAttrUtils> productMAttrUtils = Mockito.mockStatic(ProductMAttrUtils.class)){
            List<ProductM> productList = new ArrayList<>();
            ProductM product = Mockito.mock(ProductM.class);
            productList.add(product);
            productList.add(product);
            productList.add(product);
            productList.add(product);
            config.setNeedCurrentFilterAllProductNum(false);
            when(param.getItemAreaItemCnt()).thenReturn(4);
            when(param.getDefaultShowNum()).thenReturn(2);
            when(param.getFloorProductM()).thenReturn(productList);
            productMAttrUtils.when(() -> ProductMAttrUtils.isBeautyCantRepeatPurchaseProduct(product)).thenReturn(true);

            // act
            String result = availableProductAreaMoreTextOpt.compute(context, param, config);

            // assert
            assertEquals("展开全部不可购", result);
        }
    }

    /**
     * 测试场景：当存在可购买商品时，应返回格式化的文案
     */
    @Test
    public void testCompute_WhenAvailableProductsExist_ReturnFormattedText() {
        // arrange
        try (MockedStatic<ProductMAttrUtils> productMAttrUtils = Mockito.mockStatic(ProductMAttrUtils.class)){
            List<ProductM> productList = new ArrayList<>();
            ProductM product = Mockito.mock(ProductM.class);
            productList.add(product);
            productList.add(product);
            productList.add(product);
            config.setNeedCurrentFilterAllProductNum(false);
            config.setTextFormat("更多%d个团购");
            when(param.getItemAreaItemCnt()).thenReturn(4);
            when(param.getDefaultShowNum()).thenReturn(1);
            when(param.getFloorProductM()).thenReturn(productList);
            productMAttrUtils.when(() -> ProductMAttrUtils.isBeautyCantRepeatPurchaseProduct(product)).thenReturn(true);

            // act
            String result = availableProductAreaMoreTextOpt.compute(context, param, config);

            // assert
            assertEquals("更多3个团购", result);
        }
    }
}
