package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext.DefaultProductAreaMoreTextOpt.Config;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductAreaMoreTextOptComputeTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private DefaultProductAreaMoreTextOpt.Param mockParam;

    @Mock
    private Config mockConfig;

    /**
     * 测试 totalNum 小于等于 defaultShowNum 的情况
     */
    @Test
    public void testCompute_TotalNumLessThanOrEqualToDefaultShowNum() throws Throwable {
        DefaultProductAreaMoreTextOpt opt = new DefaultProductAreaMoreTextOpt();
        when(mockParam.getItemAreaItemCnt()).thenReturn(5);
        when(mockParam.getDefaultShowNum()).thenReturn(10);
        String result = opt.compute(mockActivityCxt, mockParam, mockConfig);
        assertEquals("", result);
    }

    /**
     * 测试 totalNum 大于 defaultShowNum，config.isUseTotal() 为 true 的情况
     */
    @Test
    public void testCompute_TotalNumGreaterThanDefaultShowNumAndUseTotal() throws Throwable {
        DefaultProductAreaMoreTextOpt opt = new DefaultProductAreaMoreTextOpt();
        when(mockParam.getItemAreaItemCnt()).thenReturn(15);
        when(mockParam.getDefaultShowNum()).thenReturn(10);
        when(mockConfig.isUseTotal()).thenReturn(true);
        when(mockConfig.getTextFormat()).thenReturn("全部%d个团购");
        String result = opt.compute(mockActivityCxt, mockParam, mockConfig);
        // Adjusted the expected result based on the method's logic
        assertEquals("全部15个团购", result);
    }

    /**
     * 测试 totalNum 大于 defaultShowNum，config.isUseTotal() 为 false 的情况
     */
    @Test
    public void testCompute_TotalNumGreaterThanDefaultShowNumAndNotUseTotal() throws Throwable {
        DefaultProductAreaMoreTextOpt opt = new DefaultProductAreaMoreTextOpt();
        when(mockParam.getItemAreaItemCnt()).thenReturn(15);
        when(mockParam.getDefaultShowNum()).thenReturn(10);
        when(mockConfig.isUseTotal()).thenReturn(false);
        when(mockConfig.getTextFormat()).thenReturn("更多%d个团购");
        String result = opt.compute(mockActivityCxt, mockParam, mockConfig);
        assertEquals("更多5个团购", result);
    }
}
