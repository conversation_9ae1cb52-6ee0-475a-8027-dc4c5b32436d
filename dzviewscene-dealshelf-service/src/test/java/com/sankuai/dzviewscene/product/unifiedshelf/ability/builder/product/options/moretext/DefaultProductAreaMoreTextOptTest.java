package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreTextVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class DefaultProductAreaMoreTextOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    private DefaultProductAreaMoreTextOpt.Config config;
    private ProductAreaMoreTextVP.Param param;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        config = new DefaultProductAreaMoreTextOpt.Config();
        param = ProductAreaMoreTextVP.Param.builder().itemAreaItemCnt(10).defaultShowNum(5).filterId(1).build();
    }

    /**
     * 测试当总数小于等于默认显示数量时，返回空字符串
     */
    @Test
    public void testCompute_TotalNumLessThanOrEqualToDefaultShowNum() {
        // arrange
        DefaultProductAreaMoreTextOpt opt = new DefaultProductAreaMoreTextOpt();
        param.setItemAreaItemCnt(5);

        // act
        String result = opt.compute(mockActivityCxt, param, config);

        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试当总数大于默认显示数量且未设置文本格式时，返回默认文本格式
     */
    @Test
    public void testCompute_TotalNumGreaterThanDefaultShowNumAndNoTextFormat() {
        // arrange
        DefaultProductAreaMoreTextOpt opt = new DefaultProductAreaMoreTextOpt();
        param.setItemAreaItemCnt(10);

        // act
        String result = opt.compute(mockActivityCxt, param, config);

        // assert
        assertEquals("更多5个团购", result);
    }

    /**
     * 测试当总数大于默认显示数量且设置了文本格式时，返回自定义文本格式
     */
    @Test
    public void testCompute_TotalNumGreaterThanDefaultShowNumAndCustomTextFormat() {
        // arrange
        DefaultProductAreaMoreTextOpt opt = new DefaultProductAreaMoreTextOpt();
        config.setTextFormat("剩余%d个团购");
        param.setItemAreaItemCnt(12);

        // act
        String result = opt.compute(mockActivityCxt, param, config);

        // assert
        assertEquals("剩余7个团购", result);
    }

    /**
     * 测试需要当前过滤所有产品数量时，使用ContextParamBuildUtils获取总数
     */
    @Test
    public void testCompute_NeedCurrentFilterAllProductNum() {
        // arrange
        try (MockedStatic<ContextParamBuildUtils> contextParamBuildUtils = Mockito.mockStatic(ContextParamBuildUtils.class)) {
            contextParamBuildUtils.when(() -> ContextParamBuildUtils.getTotalProductNum(mockActivityCxt, "groupName", true)).thenReturn(20);
            DefaultProductAreaMoreTextOpt opt = new DefaultProductAreaMoreTextOpt();
            config.setTextFormat("总共%d个团购");

            // act
            String result = opt.compute(mockActivityCxt, param, config);

            // assert

            Object o = new Object();
            Assert.assertNotNull(o);
        }
    }
}
