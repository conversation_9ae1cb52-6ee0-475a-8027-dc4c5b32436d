package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * FShelfProductAreaMoreTextOpt 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class FShelfProductAreaMoreTextOptTest {

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private FShelfProductAreaMoreTextOpt.Config config;
    @InjectMocks
    private FShelfProductAreaMoreTextOpt fShelfProductAreaMoreTextOpt;
    @Mock
    private FShelfProductAreaMoreTextOpt.Param param;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        fShelfProductAreaMoreTextOpt = new FShelfProductAreaMoreTextOpt();
        config = new FShelfProductAreaMoreTextOpt.Config();
    }

    /**
     * 测试商品总数超过默认显示数量，F型货架，使用总数
     */
    @Test
    public void testComputeFShelfUseTotal() {
        // arrange
        when(mockContext.getParameters()).thenReturn(java.util.Collections.singletonMap("isFShelf", true));
        config.setUseTotal(true);
        config.setFShelfTextFormat("查看更多%d个");
        when(param.getItemAreaItemCnt()).thenReturn(10);
        when(param.getDefaultShowNum()).thenReturn(5);

        // act
        String result = fShelfProductAreaMoreTextOpt.compute(mockContext, param, config);

        // assert
        assertEquals("查看更多10个", result);
    }

    /**
     * 测试商品总数等于默认显示数量，不显示任何文案
     */
    @Test
    public void testComputeNoMoreText() {
        // arrange
//        when(mockContext.getParameters()).thenReturn(java.util.Collections.singletonMap("isFShelf", true));
        config.setUseTotal(true);
        when(param.getItemAreaItemCnt()).thenReturn(5);
        when(param.getDefaultShowNum()).thenReturn(5);

        // act
        String result = fShelfProductAreaMoreTextOpt.compute(mockContext, param, config);

        // assert
        assertEquals("", result);
    }

    /**
     * 测试商品总数超过默认显示数量，非F型货架，使用剩余数
     */
    @Test
    public void testComputeNonFShelfUseRemain() {
        // arrange
        when(mockContext.getParameters()).thenReturn(java.util.Collections.singletonMap("isFShelf", false));
        config.setUseTotal(false);
        config.setTextFormat("查看更多%d个");
        when(param.getItemAreaItemCnt()).thenReturn(10);
        when(param.getDefaultShowNum()).thenReturn(5);

        // act
        String result = fShelfProductAreaMoreTextOpt.compute(mockContext, param, config);

        // assert
        assertEquals("查看更多5个", result);
    }
}
