package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext.SpecialShelfProductAreaMoreTextOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreTextVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SpecialShelfProductAreaMoreTextOptComputeTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private SpecialShelfProductAreaMoreTextOpt.Param param;

    @Test
    public void testCompute_ActivityCxtIsNull() throws Throwable {
        SpecialShelfProductAreaMoreTextOpt specialShelfProductAreaMoreTextOpt = new SpecialShelfProductAreaMoreTextOpt();
        Config config = new Config();
        config.setUseTotal(true);
        config.setShelfFormat("全部%d个团购");
        when(param.getTotalCount()).thenReturn(10);
        when(param.getDefaultShowNum()).thenReturn(5);
        String result = specialShelfProductAreaMoreTextOpt.compute(null, param, config);
        assertEquals("全部10个团购", result);
    }

    @Test
    public void testCompute_ActivityCxtIsNotNullButPaginationIsNotOne() throws Throwable {
        SpecialShelfProductAreaMoreTextOpt specialShelfProductAreaMoreTextOpt = new SpecialShelfProductAreaMoreTextOpt();
        Config config = new Config();
        config.setUseTotal(true);
        config.setShelfFormat("全部%d个团购");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("pagination", "0");
        when(activityCxt.getParameters()).thenReturn(parameters);
        when(param.getTotalCount()).thenReturn(10);
        when(param.getDefaultShowNum()).thenReturn(5);
        String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);
        assertEquals("全部10个团购", result);
    }

    @Test
    public void testCompute_ActivityCxtIsNotNullAndPaginationIsOne() throws Throwable {
        SpecialShelfProductAreaMoreTextOpt specialShelfProductAreaMoreTextOpt = new SpecialShelfProductAreaMoreTextOpt();
        Config config = new Config();
        config.setUseTotal(true);
        config.setShelfFormat("全部%d个团购");
        // Set the floor format to "加载中"
        config.setFloorFormat("加载中");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("pagination", "1");
        when(activityCxt.getParameters()).thenReturn(parameters);
        String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);
        assertEquals("加载中", result);
    }

    @Test
    public void testCompute_TotalCountLessThanOrEqualToDefaultShowNum() throws Throwable {
        SpecialShelfProductAreaMoreTextOpt specialShelfProductAreaMoreTextOpt = new SpecialShelfProductAreaMoreTextOpt();
        Config config = new Config();
        config.setUseTotal(true);
        config.setShelfFormat("全部%d个团购");
        when(param.getTotalCount()).thenReturn(5);
        when(param.getDefaultShowNum()).thenReturn(5);
        String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);
        assertEquals("", result);
    }

    @Test
    public void testCompute_TotalCountGreaterThanDefaultShowNumAndUseTotalIsTrue() throws Throwable {
        SpecialShelfProductAreaMoreTextOpt specialShelfProductAreaMoreTextOpt = new SpecialShelfProductAreaMoreTextOpt();
        Config config = new Config();
        config.setUseTotal(true);
        config.setShelfFormat("全部%d个团购");
        when(param.getTotalCount()).thenReturn(10);
        when(param.getDefaultShowNum()).thenReturn(5);
        String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);
        assertEquals("全部10个团购", result);
    }

    @Test
    public void testCompute_TotalCountGreaterThanDefaultShowNumAndUseTotalIsFalse() throws Throwable {
        SpecialShelfProductAreaMoreTextOpt specialShelfProductAreaMoreTextOpt = new SpecialShelfProductAreaMoreTextOpt();
        Config config = new Config();
        config.setUseTotal(false);
        config.setShelfFormat("更多%d个团购");
        when(param.getTotalCount()).thenReturn(10);
        when(param.getDefaultShowNum()).thenReturn(5);
        String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);
        assertEquals("更多5个团购", result);
    }
}
