package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreTextVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;

/**
 * SpecialShelfProductAreaMoreTextOpt.compute 方法的单元测试
 */
public class SpecialShelfProductAreaMoreTextOptTest {

    @Mock
    private ActivityCxt activityCxt;
    private SpecialShelfProductAreaMoreTextOpt.Config config;
    private ProductAreaMoreTextVP.Param param;

    private SpecialShelfProductAreaMoreTextOpt specialShelfProductAreaMoreTextOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        specialShelfProductAreaMoreTextOpt = new SpecialShelfProductAreaMoreTextOpt();
        config = new SpecialShelfProductAreaMoreTextOpt.Config();
        param = ProductAreaMoreTextVP.Param.builder().build();
    }

    /**
     * 测试场景：落地页有分页，应返回floorFormat
     */
    @Test
    public void testCompute_LandingPageWithPagination() {
        // arrange
        try(MockedStatic<ParamsUtil> mockedStatic = Mockito.mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(true);
            config.setFloorFormat("加载中");

            // act
            String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);

            // assert
            assertEquals("加载中", result);
        }
    }

    /**
     * 测试场景：总数小于等于默认显示数，应返回空字符串
     */
    @Test
    public void testCompute_TotalNumLessThanOrEqualToDefaultShowNum() {
        // arrange
        try(MockedStatic<ParamsUtil> mockedStatic = Mockito.mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(false);
            param.setTotalCount(5);
            param.setDefaultShowNum(5);

            // act
            String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);

            // assert
            assertEquals(StringUtils.EMPTY, result);
        }
    }

    /**
     * 测试场景：使用总数文案，总数大于默认显示数
     */
    @Test
    public void testCompute_UseTotalText() {
        // arrange
        try(MockedStatic<ParamsUtil> mockedStatic = Mockito.mockStatic(ParamsUtil.class)){
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(false);
            param.setTotalCount(10);
            param.setDefaultShowNum(5);
            config.setUseTotal(true);
            config.setShelfFormat("总共%d个项目");

            // act
            String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);

            // assert
            assertEquals("总共10个项目", result);
        }
    }

    /**
     * 测试场景：使用剩余文案，总数大于默认显示数
     */
    @Test
    public void testCompute_UseRemainText() {
        // arrange
        try(MockedStatic<ParamsUtil> mockedStatic = Mockito.mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(activityCxt)).thenReturn(false);
            param.setTotalCount(10);
            param.setDefaultShowNum(5);
            config.setUseTotal(false);
            config.setShelfFormat("还剩%d个项目");

            // act
            String result = specialShelfProductAreaMoreTextOpt.compute(activityCxt, param, config);

            // assert
            assertEquals("还剩5个项目", result);
        }
    }
}
