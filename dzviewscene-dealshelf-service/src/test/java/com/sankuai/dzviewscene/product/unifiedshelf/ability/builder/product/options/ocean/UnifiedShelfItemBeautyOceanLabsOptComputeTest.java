package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean.UnifiedShelfItemBeautyOceanLabsOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemBeautyOceanLabsOptComputeTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean.UnifiedShelfItemBeautyOceanLabsOpt.Param mockParam;

    @Mock
    private Config mockConfig;

    @Mock
    private ProductM mockProductM;

    @Mock
    private ShelfItemVO mockShelfItemVO;

    @Mock
    private ProductPromoPriceM mockPromoPrice;

    @Mock
    private PromoItemM mockPromoItem;

    @InjectMocks
    private UnifiedShelfItemBeautyOceanLabsOpt unifiedShelfItemBeautyOceanLabsOpt;

    /**
     * 测试compute方法，正常情况
     */
    @Test
    public void testComputeNormalCase() throws Throwable {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getShelfItemVO()).thenReturn(mockShelfItemVO);
        when(mockProductM.getBestPromoPrice()).thenReturn(mockPromoPrice);
        when(mockPromoPrice.getPromoItemList()).thenReturn(new ArrayList<>());
        // act
        String result = unifiedShelfItemBeautyOceanLabsOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试compute方法，异常情况，ProductM为null
     */
    @Test(expected = NullPointerException.class)
    public void testComputeExceptionCaseProductMIsNull() throws Throwable {
        // arrange
        // act
        unifiedShelfItemBeautyOceanLabsOpt.compute(mockActivityCxt, mockParam, mockConfig);
    }

    /**
     * 测试compute方法，异常情况，ShelfItemVO为null
     */
    @Test(expected = NullPointerException.class)
    public void testComputeExceptionCaseShelfItemVOIsNull() throws Throwable {
        // arrange
        when(mockParam.getShelfItemVO()).thenReturn(null);
        // act
        unifiedShelfItemBeautyOceanLabsOpt.compute(mockActivityCxt, mockParam, mockConfig);
    }
}
