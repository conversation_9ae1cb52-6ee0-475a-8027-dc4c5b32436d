package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class UnifiedShelfItemBeautyOceanLabsOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private UnifiedShelfItemBeautyOceanLabsOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;
    @Mock
    private ShelfItemVO mockShelfItemVO;

    private UnifiedShelfItemBeautyOceanLabsOpt unifiedShelfItemBeautyOceanLabsOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedShelfItemBeautyOceanLabsOpt = new UnifiedShelfItemBeautyOceanLabsOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getShelfItemVO()).thenReturn(mockShelfItemVO);
    }

    /**
     * 测试compute方法，正常情况
     */
    @Test
    public void testComputeNormalCase() {
        // arrange
        when(mockProductM.getProductType()).thenReturn(1);
        when(mockShelfItemVO.isAvailable()).thenReturn(true);
        when(mockShelfItemVO.getItemId()).thenReturn(123L);

        // act
        String result = unifiedShelfItemBeautyOceanLabsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"sale_status\":\"1\""));
    }

    /**
     * 测试compute方法，商品不可购买情况
     */
    @Test
    public void testComputeProductNotAvailable() {
        // arrange
        when(mockProductM.getProductType()).thenReturn(1);
        when(mockShelfItemVO.isAvailable()).thenReturn(false);
        when(mockShelfItemVO.getItemId()).thenReturn(123L);

        // act
        String result = unifiedShelfItemBeautyOceanLabsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"sale_status\":\"0\""));
    }

    /**
     * 测试compute方法
     */
    @Test
    public void testComputeNonTopDisplayProduct() {
        // arrange
        when(mockProductM.getProductType()).thenReturn(1);
        when(mockShelfItemVO.isAvailable()).thenReturn(true);
        when(mockShelfItemVO.getItemId()).thenReturn(123L);
        when(mockConfig.getModuleName()).thenReturn("团购货架");

        // act
        String result = unifiedShelfItemBeautyOceanLabsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"module_show_type\":\"0\""));
    }
}
