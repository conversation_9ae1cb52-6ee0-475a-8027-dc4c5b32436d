package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedShelfPhotographyOceanLabsOpt的compute方法
 */
public class UnifiedShelfPhotographyOceanLabsOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private Param param;
    @Mock
    private UnifiedShelfPhotographyOceanLabsOpt.Config config;
    @Mock
    private ProductM productM;
    @Mock
    private ShelfItemVO shelfItemVO;

    private UnifiedShelfPhotographyOceanLabsOpt opt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        opt = new UnifiedShelfPhotographyOceanLabsOpt();
    }

    /**
     * 测试compute方法，当启用paddingModulehowTypeLabs配置时
     */
    @Test
    public void testComputeWhenPaddingModulehowTypeLabsEnabled() {
        // arrange
        when(config.isEnablePaddingModulehowTypeLabs()).thenReturn(true);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(anyString())).thenReturn(null);

        // act
        String result = opt.compute(context, param, config);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试compute方法，当启用enableLabelNameLabs配置时
     */
    @Test
    public void testComputeWhenEnableLabelNameLabsEnabled() {
        // arrange
        when(config.isEnableLabelNameLabs()).thenReturn(true);
        when(param.getProductM()).thenReturn(productM);
        when(param.getFilterId()).thenReturn(0L);

        // act
        String result = opt.compute(context, param, config);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试compute方法，当启用enableProductTypeLabs配置时
     */
    @Test
    public void testComputeWhenEnableProductTypeLabsEnabled() {
        // arrange
        when(config.isEnableProductTypeLabs()).thenReturn(true);
        when(config.isEnableLabelNameLabs()).thenReturn(true);
        when(config.isEnablePaddingModulehowTypeLabs()).thenReturn(true);
        when(param.getProductM()).thenReturn(productM);

        // act
        String result = opt.compute(context, param, config);

        // assert
        assertNotNull(result);
    }
}
