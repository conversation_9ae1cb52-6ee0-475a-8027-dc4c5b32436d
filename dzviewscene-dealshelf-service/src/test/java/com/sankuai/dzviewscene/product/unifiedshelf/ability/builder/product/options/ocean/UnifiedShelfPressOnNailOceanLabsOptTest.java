package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class UnifiedShelfPressOnNailOceanLabsOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private ProductM mockProductM;

    private UnifiedShelfPressOnNailOceanLabsOpt optUnderTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        optUnderTest = new UnifiedShelfPressOnNailOceanLabsOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    /**
     * 测试compute方法，当ProductM中的折扣标签和比价标签都不为空时
     */
    @Test
    public void testCompute_WithNonEmptyDiscountAndComparePriceTags() {
        // arrange
        String expectedDiscountTag = "-999";
        when(mockProductM.getAttr(anyString())).thenReturn(expectedDiscountTag, expectedDiscountTag);

        // act
        String result = optUnderTest.compute(mockActivityCxt, mockParam, null);

        // assert
        assertTrue(result.contains(expectedDiscountTag));
    }
}
