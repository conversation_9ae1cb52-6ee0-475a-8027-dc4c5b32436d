package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.postbuildhandler;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.postbuildhandler.MassageSpuAggregateHandlerOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.ItemShowTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.ExposureEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.BeanUtils;
import org.testng.collections.Lists;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * 测试MassageSpuAggregateHandlerOpt的compute方法
 */
public class MassageSpuAggregateHandlerOptTest {

    @Mock
    private MassageSpuAggregateHandlerOpt.Param mockParam;
    @Mock
    private Config mockConfig;

    private ShelfProductAreaVO shelfProductAreaVO;

    private MassageSpuAggregateHandlerOpt handlerOpt;

    private ActivityCxt mockContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        handlerOpt = new MassageSpuAggregateHandlerOpt();
        shelfProductAreaVO = new ShelfProductAreaVO();
        mockContext = new ActivityCxt();
        mockContext.addParam(ShelfActivityConstants.Params.userAgent, 200);
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("test");
        mockContext.addParam(ShelfActivityConstants.Params.douHus, Collections.singletonList(douHuM));

        when(mockParam.getShelfProductAreaVO()).thenReturn(shelfProductAreaVO);
    }

    /**
     * 测试场景：当商品列表为空时，无操作
     */
    @Test
    public void testComputeWhenProductListIsEmpty() {
        shelfProductAreaVO.setItems(Collections.emptyList());

        handlerOpt.compute(mockContext, mockParam, mockConfig);

        assertTrue(shelfProductAreaVO.getItems().isEmpty());
    }

    /**
     * 测试场景：当配置中的DouHu2Style为空时，无操作
     */
    @Test
    public void testComputeWhenDouHu2StyleIsEmpty() {
        shelfProductAreaVO.setItems(Collections.singletonList(new ShelfItemVO()));
        when(mockConfig.getDouHu2Style()).thenReturn(Collections.emptyMap());

        handlerOpt.compute(mockContext, mockParam, mockConfig);

        assertNull(shelfProductAreaVO.getItems().get(0).getSubItems());
    }

    /**
     * 测试场景：当商品没有SPU时，无操作
     */
    @Test
    public void testComputeWhenNoSpu() {
        shelfProductAreaVO.setItems(Collections.singletonList(new ShelfItemVO()));
        Map<String, String> douHu2Style = new HashMap<>();
        douHu2Style.put("test", "1");
        when(mockConfig.getDouHu2Style()).thenReturn(douHu2Style);
        when(mockParam.getProducts()).thenReturn(Collections.singletonList(new ProductM()));

        handlerOpt.compute(mockContext, mockParam, mockConfig);

        assertNull(shelfProductAreaVO.getItems().get(0).getSubItems());
    }

    /**
     * 测试场景：当商品有SPU且为方案1时
     */
    @Test
    public void testComputeWithSpuAndStyle1() {
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setSptSpuId("1111");
        DealProductSpuDTO spuInfo = new DealProductSpuDTO();
        spuInfo.setSpuId(1111);
        spuInfo.setSpuName("足疗30分钟");
        productM.setSpuMList(Lists.newArrayList(spuInfo));
        ShelfItemVO itemVO = new ShelfItemVO();
        itemVO.setItemId(1);
        itemVO.setItemType(ProductTypeEnum.DEAL.getType());
        shelfProductAreaVO.setItems(Lists.newArrayList(itemVO));
        Map<String, String> douHu2Style = new HashMap<>();
        douHu2Style.put("test", "1");
        when(mockConfig.getDouHu2Style()).thenReturn(douHu2Style);
        when(mockParam.getProducts()).thenReturn(Collections.singletonList(productM));

        handlerOpt.compute(mockContext, mockParam, mockConfig);
        assertEquals(shelfProductAreaVO.getItems().get(0).getShowType(), ItemShowTypeEnum.AGGREGATE_CARD.getType());
        assertEquals(shelfProductAreaVO.getItems().get(0).getItemId(), 1111);
        assertNotNull(shelfProductAreaVO.getItems().get(0).getSubItems());
        assertNotNull(shelfProductAreaVO.getItems().get(0).getTitleTag());
    }

    /**
     * 测试场景：当商品有SPU且为方案2时，单SPU
     */
    @Test
    public void testComputeWithSpuAndStyle2() {
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setSptSpuId("1111");
        productM.setTitle("【香薰泡浴】｜110分钟全身｜110分钟全身精油SPA");
        DealProductSpuDTO spuInfo = new DealProductSpuDTO();
        spuInfo.setSpuId(1111);
        spuInfo.setSpuName("足疗30分钟");
        productM.setSpuMList(Lists.newArrayList(spuInfo));
        ShelfItemVO itemVO = new ShelfItemVO();
        itemVO.setItemId(1);
        itemVO.setItemType(ProductTypeEnum.DEAL.getType());
        shelfProductAreaVO.setItems(Lists.newArrayList(itemVO));
        Map<String, String> douHu2Style = new HashMap<>();
        douHu2Style.put("test", "2");
        when(mockConfig.getDouHu2Style()).thenReturn(douHu2Style);
        when(mockParam.getProducts()).thenReturn(Collections.singletonList(productM));

        handlerOpt.compute(mockContext, mockParam, mockConfig);
        assertEquals(shelfProductAreaVO.getItems().get(0).getShowType(), ItemShowTypeEnum.AGGREGATE_CARD.getType());
        assertEquals(shelfProductAreaVO.getItems().get(0).getItemId(), 1111);
        assertNotNull(shelfProductAreaVO.getItems().get(0).getSubItems());
    }

    /**
     * 测试场景：当商品有SPU且为方案3时，多SPU
     */
    @Test
    public void testComputeWithSpuAndStyle3() {
        ProductM productM1 = new ProductM();
        productM1.setProductId(1);
        productM1.setProductType(ProductTypeEnum.DEAL.getType());
        productM1.setSptSpuId("1111");
        DealProductSpuDTO spuInfo = new DealProductSpuDTO();
        spuInfo.setSpuId(1111);
        spuInfo.setSpuName("足疗30分钟");
        productM1.setSpuMList(Lists.newArrayList(spuInfo));
        ProductM productM2 = new ProductM();
        BeanUtils.copyProperties(productM1, productM2);
        productM2.setProductId(2);
        ShelfItemVO itemVO1 = new ShelfItemVO();
        itemVO1.setItemId(1);
        itemVO1.setItemType(ProductTypeEnum.DEAL.getType());
        itemVO1.setHeadPic(new PicAreaVO());
        ShelfItemVO itemVO2 = new ShelfItemVO();
        itemVO2.setItemId(2);
        itemVO2.setItemType(ProductTypeEnum.DEAL.getType());
        itemVO1.setHeadPic(new PicAreaVO());
        shelfProductAreaVO.setItems(Lists.newArrayList(itemVO1, itemVO2));
        Map<String, String> douHu2Style = new HashMap<>();
        douHu2Style.put("test", "2");
        when(mockConfig.getDouHu2Style()).thenReturn(douHu2Style);
        when(mockParam.getProducts()).thenReturn(Lists.newArrayList(productM1, productM2));

        handlerOpt.compute(mockContext, mockParam, mockConfig);
        assertEquals(shelfProductAreaVO.getItems().get(0).getShowType(), ItemShowTypeEnum.AGGREGATE_CARD.getType());
        assertEquals(shelfProductAreaVO.getItems().get(0).getItemId(), 1111);
        assertEquals(shelfProductAreaVO.getItems().get(0).getSubItems().size(), 2);
    }

    /**
     * 测试场景：当商品有SPU且为方案3时
     */
    @Test
    public void testComputeWithSingleSpuAndStyle2() {
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setSptSpuId("1111");
        DealProductSpuDTO spuInfo = new DealProductSpuDTO();
        spuInfo.setSpuId(1111);
        spuInfo.setSpuName("足疗30分钟");
        productM.setSpuMList(Lists.newArrayList(spuInfo));
        productM.setAttr("massageSpuProductTags", "[\"30分钟\",\"含全身按摩\"]");
        ShelfItemVO itemVO = new ShelfItemVO();
        itemVO.setItemId(1);
        itemVO.setItemType(ProductTypeEnum.DEAL.getType());
        shelfProductAreaVO.setItems(Lists.newArrayList(itemVO));
        Map<String, String> douHu2Style = new HashMap<>();
        douHu2Style.put("test", "3");
        when(mockConfig.getDouHu2Style()).thenReturn(douHu2Style);
        when(mockParam.getProducts()).thenReturn(Collections.singletonList(productM));

        handlerOpt.compute(mockContext, mockParam, mockConfig);
        assertEquals(shelfProductAreaVO.getItems().get(0).getShowType(), ItemShowTypeEnum.DEFAULT.getType());
        assertEquals(shelfProductAreaVO.getItems().get(0).getItemId(), 1);
        assertNull(shelfProductAreaVO.getItems().get(0).getSubItems());
        assertNotNull(shelfProductAreaVO.getItems().get(0).getProductTags());
        assertEquals("30分钟", shelfProductAreaVO.getItems().get(0).getProductTags().getTags().get(0).getText());
    }

    /**
     * 测试场景：配置启用后付款标签，但用户未曝光，不应追加标签
     */
    @Test
    public void testAppendAfterPayProductTag_UserNotExposed() {
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setExposure(ExposureEnum.UNEXPOSED.getCode());
        Map<String, Object> sourceMap = Maps.newHashMap();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);

        handlerOpt.appendAfterPayProductTag(mockParam, mockConfig, mockContext,"3");

        Assert.assertNotNull(contextHandlerResult);
    }

    /**
     * 测试场景：配置启用后付款标签，用户已曝光，但商品列表为空，不应追加标签
     */
    @Test
    public void testAppendAfterPayProductTag_EmptyProductList() {
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setExposure(ExposureEnum.EXPOSED.getCode());
        Map<String, Object> sourceMap = Maps.newHashMap();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        when(mockParam.getProducts()).thenReturn(new ArrayList<>());

        handlerOpt.appendAfterPayProductTag(mockParam, mockConfig, mockContext,"3");

        Assert.assertNotNull(contextHandlerResult);
    }

    /**
     * 测试场景：配置启用后付款标签，用户已曝光，存在商品但不符合条件，不应追加标签
     */
    @Test
    public void testAppendAfterPayProductTag_ProductNotMatch() {
        List<ProductM> products = new ArrayList<>();
        ProductM productM = new ProductM();
        productM.setTradeType(19);
        products.add(productM);
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setExposure(ExposureEnum.EXPOSED.getCode());
        Map<String, Object> sourceMap = Maps.newHashMap();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        when(mockParam.getProducts()).thenReturn(products);

        handlerOpt.appendAfterPayProductTag(mockParam, mockConfig, mockContext,"3");

        Assert.assertNotNull(contextHandlerResult);
    }

    /**
     * 测试场景：配置启用后付款标签，用户已曝光，存在符合条件的商品，应追加标签
     */
    @Test
    public void testAppendAfterPayProductTag_ProductMatch() {
        Config config = new Config();
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setExposure(ExposureEnum.EXPOSED.getCode());
        Map<String, Object> sourceMap = Maps.newHashMap();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("exp111");
        douHuMList.add(douHuM);
        sourceMap.put(ShelfActivityConstants.Params.douHus, douHuMList);
        mockContext.setSourceMap(sourceMap);
        mockContext.setParameters(sourceMap);
        List<ProductM> products = new ArrayList<>();
        ProductM productM = new ProductM();
        productM.setTradeType(19);
        products.add(productM);
        productM.setProductId(1);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setAttr("pay_method", "true");
        List<ShelfItemVO> items = new ArrayList<>();
        ShelfItemVO mockShelfItemVO = new ShelfItemVO();
        mockShelfItemVO.setItemId(1L);
        items.add(mockShelfItemVO);
        config.setPreAfterTag("后付款");
        config.setAfterPayExps(Lists.newArrayList("exp111"));
        MassageSpuAggregateHandlerOpt.Param param = MassageSpuAggregateHandlerOpt.Param.builder().build();
        param.setProducts(products);
        ShelfProductAreaVO mockShelfProductAreaVO = new ShelfProductAreaVO();
        mockShelfProductAreaVO.setItems(items);
        param.setShelfProductAreaVO(mockShelfProductAreaVO);

        handlerOpt.appendAfterPayProductTag(param, config, mockContext,"3");

        Assert.assertNotNull(contextHandlerResult);
    }

    @Test
    public void testAppendAfterPayProductTag_ProductMatch1() {
        Config config = new Config();
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setExposure(ExposureEnum.EXPOSED.getCode());
        Map<String, Object> sourceMap = Maps.newHashMap();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("exp111");
        douHuMList.add(douHuM);
        sourceMap.put(ShelfActivityConstants.Params.douHus, douHuMList);
        mockContext.setSourceMap(sourceMap);
        mockContext.setParameters(sourceMap);
        List<ProductM> products = new ArrayList<>();
        ProductM productM = new ProductM();
        productM.setTradeType(19);
        products.add(productM);
        productM.setProductId(1);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setAttr("pay_method", "4");
        productM.setAttr("massageSpuProductTags", "[\"30分钟\",\"含全身按摩\"]");
        List<ShelfItemVO> items = new ArrayList<>();
        ShelfItemVO mockShelfItemVO = new ShelfItemVO();
        mockShelfItemVO.setItemId(1L);
        ItemSubTitleVO itemSubTitleVO = new ItemSubTitleVO();
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setStyle("1");
        styleTextModel.setText("标签1");
        itemSubTitleVO.setTags(Lists.newArrayList(styleTextModel));
        mockShelfItemVO.setProductTags(itemSubTitleVO);
        items.add(mockShelfItemVO);
        config.setPreAfterTag("后付款");
        config.setAfterPayExps(Lists.newArrayList("exp111"));
        MassageSpuAggregateHandlerOpt.Param param = MassageSpuAggregateHandlerOpt.Param.builder().build();
        param.setProducts(products);
        ShelfProductAreaVO mockShelfProductAreaVO = new ShelfProductAreaVO();
        mockShelfProductAreaVO.setItems(items);
        param.setShelfProductAreaVO(mockShelfProductAreaVO);
        handlerOpt.appendAfterPayProductTag(param, config, mockContext,"3");

        Assert.assertNotNull(contextHandlerResult);
    }

}
