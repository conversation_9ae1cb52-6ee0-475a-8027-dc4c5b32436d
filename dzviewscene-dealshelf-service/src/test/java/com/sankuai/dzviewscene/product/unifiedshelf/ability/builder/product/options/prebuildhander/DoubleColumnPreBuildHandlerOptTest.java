package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.prebuildhander;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.prebuildhandler.DoubleColumnPreBuildHandlerOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaPreBuildHandlerVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class DoubleColumnPreBuildHandlerOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private ProductAreaPreBuildHandlerVP.Param param;
    private DoubleColumnPreBuildHandlerOpt.Config config;

    private DoubleColumnPreBuildHandlerOpt doubleColumnPreBuildHandlerOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        doubleColumnPreBuildHandlerOpt = new DoubleColumnPreBuildHandlerOpt();
        config = new DoubleColumnPreBuildHandlerOpt.Config();
    }

    /**
     * 测试场景：当isLimitApp为true且平台不是App时，不进行处理
     */
    @Test
    public void testCompute_LimitAppAndNotAppPlatform() {
        // arrange
        config.setLimitApp(true);
        Map<String, Object> map = new HashMap<>();
        map.put(ShelfActivityConstants.Params.userAgent, 1);
        when(context.getParameters()).thenReturn(map);

        // act
        Void result = doubleColumnPreBuildHandlerOpt.compute(context, param, config);

        // assert
        assertNull(result);
        verify(context, never()).addParam(eq(ShelfActivityConstants.Params.doubleColumnShelf), any());
    }

    /**
     * 测试场景：当商品总数大于等于双列最小数量时，设置双列货架参数
     */
    @Test
    public void testCompute_TotalCountGreaterEqualDoubleColumnMinNum() {
        // arrange
        config.setDoubleColumnMinNum(10);
        when(context.getParameters()).thenReturn(new HashMap<>());
        when(context.getParam(ShelfActivityConstants.Params.totalProductNum)).thenReturn(10);

        // act
        Void result = doubleColumnPreBuildHandlerOpt.compute(context, param, config);

        // assert
        assertNull(result);
        verify(context).addParam(ShelfActivityConstants.Params.doubleColumnShelf, 1);
    }

    /**
     * 测试场景：当商品总数小于双列最小数量时，不设置双列货架参数
     */
    @Test
    public void testCompute_TotalCountLessThanDoubleColumnMinNum() {
        // arrange
        config.setDoubleColumnMinNum(10);
        when(context.getParameters()).thenReturn(new HashMap<>());
        when(context.getParam(ShelfActivityConstants.Params.totalProductNum)).thenReturn(9);

        // act
        Void result = doubleColumnPreBuildHandlerOpt.compute(context, param, config);

        // assert
        assertNull(result);
        verify(context, never()).addParam(eq(ShelfActivityConstants.Params.doubleColumnShelf), any());
    }
}
