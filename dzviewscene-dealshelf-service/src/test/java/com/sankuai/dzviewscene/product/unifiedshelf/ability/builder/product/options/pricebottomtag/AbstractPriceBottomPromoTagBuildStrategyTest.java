package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.pricebottomtag;

import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.AbstractPriceBottomPromoTagBuildStrategy;
import org.junit.Assert;
import org.junit.Test;

public class AbstractPriceBottomPromoTagBuildStrategyTest {

    @Test
    public void test() {
        String commonAfterPic = AbstractPriceBottomPromoTagBuildStrategy.getCommonAfterPic(200);
        Assert.assertNotNull(commonAfterPic);
    }

    @Test
    public void test1() {
        String commonAfterPic = AbstractPriceBottomPromoTagBuildStrategy.getCommonAfterPic(100);
        Assert.assertNotNull(commonAfterPic);
    }

    @Test
    public void test2() {
        String commonAfterPic = AbstractPriceBottomPromoTagBuildStrategy.getMemberAfterPic(100);
        Assert.assertNotNull(commonAfterPic);
    }

    @Test
    public void test3() {
        String commonAfterPic = AbstractPriceBottomPromoTagBuildStrategy.getMemberAfterPic(200);
        Assert.assertNotNull(commonAfterPic);
    }
}
