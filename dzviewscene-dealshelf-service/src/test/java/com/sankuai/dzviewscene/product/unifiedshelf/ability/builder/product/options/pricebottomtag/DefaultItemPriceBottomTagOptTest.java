package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.pricebottomtag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.DefaultItemPriceBottomTagOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPriceBottomTagVP;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.assertNull;

public class DefaultItemPriceBottomTagOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemPriceBottomTagVP.Param mockParam;

    private DefaultItemPriceBottomTagOpt defaultItemPriceBottomTagOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultItemPriceBottomTagOpt = new DefaultItemPriceBottomTagOpt();
    }

    /**
     * 测试 compute 方法在默认情况下返回 null
     */
    @Test
    public void testComputeReturnsNull() {
        // arrange
        // 由于 compute 方法预期返回 null，这里不需要额外的设置

        // act
        List<ShelfTagVO> result = defaultItemPriceBottomTagOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertNull("compute 方法应该返回 null", result);
    }
}
