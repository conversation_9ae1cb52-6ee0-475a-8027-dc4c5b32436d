package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.utils.LionObjectManagerUtils.SubtitleConfig;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 对UnifiedShelfItemPhotographSubTitleOpt类的build方法进行测试
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemPhotographSubTitleOptTest {

    private static final String RELATED_THEME_MODEL = "relateThemeMode";

    private static final String STYLE_CONTENT_COUNT = "photographDealContainPhotoStyleContentCount";

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private UnifiedShelfItemSubTitleVP.Param param;
    @Mock
    private UnifiedShelfItemPhotographSubTitleOpt.Config config;
    @InjectMocks
    private UnifiedShelfItemPhotographSubTitleOpt opt;
    @Mock
    private ProductM productM;

    /**
     * 测试当产品标签为空时的情况
     */
    @Test
    public void testBuildWhenProductTagsAreEmpty() {
        // arrange
        when(param.getProductM()).thenReturn(productM);

        // act
        List<StyleTextModel> result = opt.build(activityCxt, param, config);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当产品标签不为空，但没有匹配的配置时的情况
     */
    @Test
    public void testBuildWhenProductTagsAreNotEmptyButNoMatchingConfig() {
        // arrange
        productM.setProductTagList(new ArrayList<>());
        when(param.getProductM()).thenReturn(productM);
//        when(param.getDouHuMList()).thenReturn(new ArrayList<>());

        // act
        List<StyleTextModel> result = opt.build(activityCxt, param, config);

        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildWhenConfigMatchesAndSearchWordMatchesStyleWords() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(activityCxt.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("风格");
        when(productM.getProductTags()).thenReturn(Lists.newArrayList("1"));

        Map<Integer, List<SubtitleConfig>> categoryId2SubtitleConfigMap = new HashMap<>();
        List<SubtitleConfig> subtitleConfigs = new ArrayList<>();
        SubtitleConfig subtitleConfig = new SubtitleConfig();
        subtitleConfig.setStyleWords(new ArrayList<>());
        subtitleConfigs.add(subtitleConfig);
        categoryId2SubtitleConfigMap.put(1, subtitleConfigs);
        config.setCategoryId2SubtitleConfigMap(categoryId2SubtitleConfigMap);

        // act
        List<StyleTextModel> result = opt.build(activityCxt, param, config);

        // assert
        assertFalse(result.isEmpty());
    }

    /**
     * 测试当有匹配的配置，但搜索词不匹配风格词时的情况
     */
    @Test
    public void testBuildWhenConfigMatchesButSearchWordDoesNotMatchStyleWords() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(activityCxt.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("");
        when(productM.getProductTags()).thenReturn(Lists.newArrayList("1"));

        Map<Integer, List<SubtitleConfig>> categoryId2SubtitleConfigMap = new HashMap<>();
        List<SubtitleConfig> subtitleConfigs = new ArrayList<>();
        SubtitleConfig subtitleConfig = new SubtitleConfig();
        subtitleConfig.setStyleWords(new ArrayList<>());
        subtitleConfigs.add(subtitleConfig);
        categoryId2SubtitleConfigMap.put(1, subtitleConfigs);
        config.setCategoryId2SubtitleConfigMap(categoryId2SubtitleConfigMap);

        // act
        List<StyleTextModel> result = opt.build(activityCxt, param, config);

        // assert
        assertFalse(result.isEmpty());
    }

    @Test
    public void testBuildWhenConfigMatchesButSearchWordDoesNotMatchStyleWords2() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(activityCxt.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("汉服");
        when(productM.getProductTags()).thenReturn(Lists.newArrayList("1"));
        when(productM.getProductTagList()).thenReturn(Lists.newArrayList(new TagM(1, "10063493", "汉服")));
        when(productM.getCategoryId()).thenReturn(504);

        Map<Integer, List<SubtitleConfig>> categoryId2SubtitleConfigMap = new HashMap<>();
        List<SubtitleConfig> subtitleConfigs = new ArrayList<>();
        SubtitleConfig subtitleConfig = new SubtitleConfig();
        subtitleConfig.setStyleWords(new ArrayList<>());
        subtitleConfigs.add(subtitleConfig);
        categoryId2SubtitleConfigMap.put(1, subtitleConfigs);
        config.setCategoryId2SubtitleConfigMap(categoryId2SubtitleConfigMap);

        // act
        List<StyleTextModel> result = opt.build(activityCxt, param, config);

        // assert
        assertFalse(result.isEmpty());
    }

    @Test
    public void testBuildWhenConfigMatchesButSearchWordDoesNotMatchStyleWords3() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(activityCxt.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("汉服");
        when(productM.getProductTags()).thenReturn(Lists.newArrayList("1"));
        when(productM.getProductTagList()).thenReturn(Lists.newArrayList(new TagM(1, "10063493", "汉服")));
        when(productM.getCategoryId()).thenReturn(1);
        when(productM.getAttr(RELATED_THEME_MODEL)).thenReturn("2");

        Map<Integer, List<SubtitleConfig>> categoryId2SubtitleConfigMap = new HashMap<>();
        List<SubtitleConfig> subtitleConfigs = new ArrayList<>();
        SubtitleConfig subtitleConfig = new SubtitleConfig();
        subtitleConfig.setStyleWords(new ArrayList<>());
        subtitleConfigs.add(subtitleConfig);
        categoryId2SubtitleConfigMap.put(1, subtitleConfigs);
        config.setCategoryId2SubtitleConfigMap(categoryId2SubtitleConfigMap);

        // act
        List<StyleTextModel> result = opt.build(activityCxt, param, config);

        // assert
        assertFalse(result.isEmpty());
    }

    @Test
    public void testBuildWhenConfigMatchesButSearchWordDoesNotMatchStyleWords4() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(activityCxt.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("汉服");
        when(productM.getProductTags()).thenReturn(Lists.newArrayList("1"));
        when(productM.getProductTagList()).thenReturn(Lists.newArrayList(new TagM(1, "10063493", "汉服")));
        when(productM.getCategoryId()).thenReturn(1);
        when(productM.getAttr(RELATED_THEME_MODEL)).thenReturn("3");
        when(productM.getAttr(STYLE_CONTENT_COUNT)).thenReturn("2");

        Map<Integer, List<SubtitleConfig>> categoryId2SubtitleConfigMap = new HashMap<>();
        List<SubtitleConfig> subtitleConfigs = new ArrayList<>();
        SubtitleConfig subtitleConfig = new SubtitleConfig();
        subtitleConfig.setStyleWords(new ArrayList<>());
        subtitleConfigs.add(subtitleConfig);
        categoryId2SubtitleConfigMap.put(1, subtitleConfigs);
        config.setCategoryId2SubtitleConfigMap(categoryId2SubtitleConfigMap);

        // act
        List<StyleTextModel> result = opt.build(activityCxt, param, config);

        // assert
        assertFalse(result.isEmpty());
    }
}
