package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.UnifiedShelfItemVoucherSubTitleOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemVoucherSubTitleOptComputeTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    private UnifiedShelfItemVoucherSubTitleOpt opt = new UnifiedShelfItemVoucherSubTitleOpt();

    /**
     * Helper method to create an instance of Param using reflection.
     */
    private UnifiedShelfItemVoucherSubTitleOpt.Param createParamInstance() throws NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException {
        Constructor<UnifiedShelfItemVoucherSubTitleOpt.Param> constructor = UnifiedShelfItemVoucherSubTitleOpt.Param.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    /**
     * 测试 compute 方法，当 ProductM 对象非 null，但 getAttr() 方法返回 null 或空字符串时
     */
    @Test
    public void testComputeWithEmptyVoucherTag() throws Throwable {
        // arrange
        UnifiedShelfItemVoucherSubTitleOpt.Param param = createParamInstance();
        param.setProductM(productM);
        when(productM.getAttr("voucherAttrReservation")).thenReturn("");
        // act
        ItemSubTitleVO result = opt.compute(activityCxt, param, new UnifiedShelfItemVoucherSubTitleOpt.Config());
        // assert
        assertNull(result);
    }

    /**
     * 测试 compute 方法，当 ProductM 对象非 null，getAttr() 方法返回非空字符串时
     */
    @Test
    public void testComputeWithNonEmptyVoucherTag() throws Throwable {
        // arrange
        UnifiedShelfItemVoucherSubTitleOpt.Param param = createParamInstance();
        param.setProductM(productM);
        when(productM.getAttr("voucherAttrReservation")).thenReturn("reservation");
        // act
        ItemSubTitleVO result = opt.compute(activityCxt, param, new UnifiedShelfItemVoucherSubTitleOpt.Config());
        // assert
        assertNotNull(result);
    }
}
