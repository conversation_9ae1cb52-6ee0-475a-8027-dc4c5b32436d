package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedShelfItemVoucherSubTitleOpt的compute方法
 */
public class UnifiedShelfItemVoucherSubTitleOptTest {

    private static final String VOUCHER_ATTR_AVAILABLE_TIME = "voucherAttrAvailableTime";

    private static final String VOUCHER_ATTR_USING_LIMIT = "voucherAttrUsingLimit";

    private static final String VOUCHER_ATTR_ALL_PRODUCT = "voucherAttrAllProduct";

    private static final String VOUCHER_ATTR_RESERVATION = "voucherAttrReservation";

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private ProductM productM;
    @Mock
    private UnifiedShelfItemSubTitleVP.Param param;

    private UnifiedShelfItemVoucherSubTitleOpt opt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        opt = new UnifiedShelfItemVoucherSubTitleOpt();
    }

    /**
     * 当ProductM对象中ATTR_VOUCHER_TAGS属性值非空时
     */
    @Test
    public void testComputeWithNonEmptyVoucherTag() {
        // arrange
        String expectedTag = "优惠券";
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(VOUCHER_ATTR_ALL_PRODUCT)).thenReturn(expectedTag);

        // act
        ItemSubTitleVO result = opt.compute(activityCxt, param, new UnifiedShelfItemVoucherSubTitleOpt.Config());

        // assert
        assertNotNull(result);
        assertEquals(expectedTag, result.getTags().get(0).getText());
    }

    /**
     * 当ProductM对象中ATTR_VOUCHER_TAGS属性值为空时
     */
    @Test
    public void testComputeWithEmptyVoucherTag() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(VOUCHER_ATTR_RESERVATION)).thenReturn("");

        // act
        ItemSubTitleVO result = opt.compute(activityCxt, param, new UnifiedShelfItemVoucherSubTitleOpt.Config());

        // assert
        assertNull(result);
    }
}
