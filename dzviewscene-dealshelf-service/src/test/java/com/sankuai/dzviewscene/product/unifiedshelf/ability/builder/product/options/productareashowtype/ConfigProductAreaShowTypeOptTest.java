package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productareashowtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaShowTypeVP.Param;
import org.junit.Before;
import org.junit.Test;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertEquals;

public class ConfigProductAreaShowTypeOptTest {

    private ConfigProductAreaShowTypeOpt configProductAreaShowTypeOpt;
    private ActivityCxt activityCxt;
    private ConfigProductAreaShowTypeOpt.Config config;
    private Param param;

    @Before
    public void setUp() {
        configProductAreaShowTypeOpt = new ConfigProductAreaShowTypeOpt();
        activityCxt = new ActivityCxt();
        config = new ConfigProductAreaShowTypeOpt.Config();
        param = Param.builder().groupName("testGroup").build();
    }

    /**
     * 测试场景：groupName2ShowType为空，应返回默认展示类型
     */
    @Test
    public void testCompute_WithEmptyGroupName2ShowType_ReturnsDefaultShowType() {
        // arrange
        config.setDefaultShowType(1);
        config.setGroupName2ShowType(new HashMap<>());

        // act
        Integer result = configProductAreaShowTypeOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(Integer.valueOf(1), result);
    }

    /**
     * 测试场景：groupName2ShowType不包含指定groupName，应返回默认展示类型
     */
    @Test
    public void testCompute_WithGroupNameNotInMap_ReturnsDefaultShowType() {
        // arrange
        Map<String, Integer> groupName2ShowType = new HashMap<>();
        groupName2ShowType.put("otherGroup", 2);
        config.setDefaultShowType(1);
        config.setGroupName2ShowType(groupName2ShowType);

        // act
        Integer result = configProductAreaShowTypeOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(Integer.valueOf(1), result);
    }

    /**
     * 测试场景：groupName2ShowType包含指定groupName，应返回对应展示类型
     */
    @Test
    public void testCompute_WithGroupNameInMap_ReturnsSpecificShowType() {
        // arrange
        Map<String, Integer> groupName2ShowType = new HashMap<>();
        groupName2ShowType.put("testGroup", 3);
        config.setDefaultShowType(1);
        config.setGroupName2ShowType(groupName2ShowType);

        // act
        Integer result = configProductAreaShowTypeOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(Integer.valueOf(3), result);
    }
}
