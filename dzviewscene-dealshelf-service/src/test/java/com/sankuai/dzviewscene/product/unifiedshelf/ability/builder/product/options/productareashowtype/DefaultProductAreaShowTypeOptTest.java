package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productareashowtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaShowTypeVP;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;

/**
 * 测试DefaultProductAreaShowTypeOpt的compute方法
 */
public class DefaultProductAreaShowTypeOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private ProductAreaShowTypeVP.Param param;
    @Mock
    private DefaultProductAreaShowTypeOpt.Config config;

    private DefaultProductAreaShowTypeOpt defaultProductAreaShowTypeOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultProductAreaShowTypeOpt = new DefaultProductAreaShowTypeOpt();
    }

    /**
     * 测试compute方法在正常情况下返回0
     */
    @Test
    public void testComputeReturnsZero() {
        // arrange
        // 在这个场景中，由于compute方法的实现不依赖于输入参数的具体值，所以不需要对mock对象进行特别的设置

        // act
        Integer result = defaultProductAreaShowTypeOpt.compute(activityCxt, param, config);

        // assert
        assertEquals("compute方法应该返回0", Integer.valueOf(0), result);
    }
}
