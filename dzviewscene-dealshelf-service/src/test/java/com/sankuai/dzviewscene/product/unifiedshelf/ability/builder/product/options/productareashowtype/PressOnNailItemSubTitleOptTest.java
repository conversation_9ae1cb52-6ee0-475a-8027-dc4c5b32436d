package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productareashowtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.PressOnNailItemSubTitleOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试PressOnNailItemSubTitleOpt的compute方法
 */
public class PressOnNailItemSubTitleOptTest {

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private Param mockParam;
    @Mock
    private PressOnNailItemSubTitleOpt.Config mockConfig;

    private PressOnNailItemSubTitleOpt pressOnNailItemSubTitleOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailItemSubTitleOpt = new PressOnNailItemSubTitleOpt();
    }

    /**
     * 测试当产品信息为空时返回null
     */
    @Test
    public void testComputeWhenProductMIsNull() {
        when(mockParam.getProductM()).thenReturn(null);

        ItemSubTitleVO result = pressOnNailItemSubTitleOpt.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试当产品扩展属性为空时返回null
     */
    @Test
    public void testComputeWhenExtAttrsIsEmpty() {
        ProductM mockProductM = mock(ProductM.class);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getExtAttrs()).thenReturn(new ArrayList<>());

        ItemSubTitleVO result = pressOnNailItemSubTitleOpt.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }

    /**
     * 测试当产品具有到店免费佩戴属性时
     */
    @Test
    public void testComputeWithCanWearAtStore() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("isFreeWearingAtStore", "true"));
        ProductM mockProductM = mock(ProductM.class);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getExtAttrs()).thenReturn(attrs);

        ItemSubTitleVO result = pressOnNailItemSubTitleOpt.compute(mockContext, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.getTags().isEmpty());
        assertEquals("到店免费佩戴", result.getTags().get(0).getText());
    }

    /**
     * 测试当产品具有穿戴甲类型属性时
     */
    @Test
    public void testComputeWithWearingNailsType() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("wearingNailsType", "类型A"));
        ProductM mockProductM = mock(ProductM.class);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getExtAttrs()).thenReturn(attrs);

        ItemSubTitleVO result = pressOnNailItemSubTitleOpt.compute(mockContext, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.getTags().isEmpty());
        assertEquals("类型A", result.getTags().get(0).getText());
    }

    /**
     * 测试当产品具有附加项目属性时
     */
    @Test
    public void testComputeWithNailAdditionalItem() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("nail_additional_item", "[\"附加项目A\"]"));
        ProductM mockProductM = mock(ProductM.class);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getExtAttrs()).thenReturn(attrs);

        ItemSubTitleVO result = pressOnNailItemSubTitleOpt.compute(mockContext, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.getTags().isEmpty());
        assertEquals("附加项目A", result.getTags().get(0).getText());
    }

    /**
     * 测试当产品没有任何标签时返回null
     */
    @Test
    public void testComputeWithNoTags() {
        List<AttrM> attrs = new ArrayList<>();
        ProductM mockProductM = mock(ProductM.class);
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getExtAttrs()).thenReturn(attrs);

        ItemSubTitleVO result = pressOnNailItemSubTitleOpt.compute(mockContext, mockParam, mockConfig);

        assertNull(result);
    }
}
