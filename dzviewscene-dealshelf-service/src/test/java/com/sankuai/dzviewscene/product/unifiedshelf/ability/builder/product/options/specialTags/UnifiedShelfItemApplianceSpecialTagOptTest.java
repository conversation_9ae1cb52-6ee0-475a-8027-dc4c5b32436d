package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * 测试UnifiedShelfItemApplianceSpecialTagOpt的compute方法
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemApplianceSpecialTagOptTest {

    @InjectMocks
    private UnifiedShelfItemApplianceSpecialTagOpt opt;
    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private UnifiedShelfItemApplianceSpecialTagOpt.Config config;
    @Mock
    private ProductSaleM productSaleM;
    @Mock
    private ProductM productM;
    @Mock
    private UnifiedShelfItemApplianceSpecialTagOpt.Param param;

    @Before
    public void setUp() {
        opt = new UnifiedShelfItemApplianceSpecialTagOpt();
        activityCxt = Mockito.mock(ActivityCxt.class);
        config = new UnifiedShelfItemApplianceSpecialTagOpt.Config();
    }

    /**
     * 测试sale为null时
     */
    @Test
    public void testComputeSaleIsNull() {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getSale()).thenReturn(null);

        ItemSpecialTagVO result = opt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试saleTag为空字符串时
     */
    @Test
    public void testComputeSaleTagIsEmpty() {
        ProductSaleM sale = new ProductSaleM();
        sale.setSaleTag("");
        when(param.getProductM()).thenReturn(productM);
        when(productM.getSale()).thenReturn(sale);

        ItemSpecialTagVO result = opt.compute(activityCxt, param, config);

        assertNull(result);
    }

    /**
     * 测试saleTag为非空字符串时
     */
    @Test
    public void testComputeSaleTagIsNotEmpty() {
        ProductSaleM sale = new ProductSaleM();
        sale.setSaleTag("Best Seller");
        when(param.getProductM()).thenReturn(productM);
        when(productM.getSale()).thenReturn(sale);

        ItemSpecialTagVO result = opt.compute(activityCxt, param, config);

        assertNotNull(result);
        assertEquals(1, result.getTags().size());
        assertEquals("Best Seller", result.getTags().get(0).getText().getText());
        assertEquals("#F4F4F4", result.getTags().get(0).getText().getBackgroundColor());
    }
}
