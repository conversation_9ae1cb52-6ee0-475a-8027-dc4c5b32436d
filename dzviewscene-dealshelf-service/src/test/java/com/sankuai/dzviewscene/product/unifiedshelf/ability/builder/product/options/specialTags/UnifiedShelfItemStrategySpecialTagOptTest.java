package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSpecialTagVP.Param;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemStrategySpecialTagOptTest {

    @InjectMocks
    private UnifiedShelfItemStrategySpecialTagOpt unifiedShelfItemStrategySpecialTagOpt;

    @Mock
    private SpecialTagStrategyFactory specialTagStrategyFactory;

    @Mock
    private SpecialTagStrategy specialTagStrategy;

    @Mock
    private Param param;

    @Mock
    private ProductM productM;

    @Mock
    private ActivityCxt context;

    @Mock
    private UnifiedShelfItemStrategySpecialTagOpt.Config config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试F型货架优先级策略，策略返回非空标签列表
     */
    @Test
    public void testGetTagsByStrategyFShelfWithNonEmptyTags() throws Throwable {
        // arrange
        config.setStrategyByPriority(Collections.singletonList("strategy1"));
        when(param.getProductM()).thenReturn(productM);
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(ShelfActivityConstants.Params.isFShelf, true);
        when(context.getParameters()).thenReturn(parameters);
        when(config.getStrategyByPriority()).thenReturn(Collections.singletonList("strategy1"));
        when(specialTagStrategyFactory.getSpecialTagStrategy("strategy1")).thenReturn(specialTagStrategy);
        when(specialTagStrategy.build(any(SpecialTagBuildReq.class))).thenReturn(Collections.singletonList(new ShelfTagVO()));

        // act
        List<ShelfTagVO> result = (List<ShelfTagVO>) ReflectionTestUtils.invokeMethod(unifiedShelfItemStrategySpecialTagOpt, "getTagsByStrategy", context, param, config);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(specialTagStrategyFactory, times(1)).getSpecialTagStrategy("strategy1");
        verify(specialTagStrategy, times(1)).build(any(SpecialTagBuildReq.class));
    }

    /**
     * 测试F型货架优先级策略，策略返回空标签列表
     */
    @Test
    public void testGetTagsByStrategyFShelfWithEmptyTags() throws Throwable {
        // arrange
        config.setStrategyByPriority(Collections.singletonList("strategy1"));
        when(param.getProductM()).thenReturn(productM);
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(ShelfActivityConstants.Params.isFShelf, true);
        when(context.getParameters()).thenReturn(parameters);
        when(config.getStrategyByPriority()).thenReturn(Collections.singletonList("strategy1"));
        when(specialTagStrategyFactory.getSpecialTagStrategy("strategy1")).thenReturn(specialTagStrategy);
        when(specialTagStrategy.build(any(SpecialTagBuildReq.class))).thenReturn(Collections.emptyList());

        // act
        List<ShelfTagVO> result = (List<ShelfTagVO>) ReflectionTestUtils.invokeMethod(unifiedShelfItemStrategySpecialTagOpt, "getTagsByStrategy", context, param, config);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(specialTagStrategyFactory, times(1)).getSpecialTagStrategy("strategy1");
        verify(specialTagStrategy, times(1)).build(any(SpecialTagBuildReq.class));
    }
}
