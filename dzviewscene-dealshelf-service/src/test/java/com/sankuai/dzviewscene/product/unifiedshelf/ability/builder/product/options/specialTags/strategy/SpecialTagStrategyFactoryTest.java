package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy;

import org.apache.commons.collections.MapUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import shaded.org.elasticsearch.common.collect.Map;

import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecialTagStrategyFactoryTest {

    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private SpecialTagStrategy specialTagStrategy;
    
    private SpecialTagStrategyFactory specialTagStrategyFactory;

    @Before
    public void setUp() throws Exception {
        specialTagStrategyFactory = new SpecialTagStrategyFactory();
        specialTagStrategyFactory.setApplicationContext(applicationContext);
    }

    /**
     * 测试正常情况下获取策略实例
     */
    @Test
    public void testGetSpecialTagStrategyNormalCase() throws Throwable {
        // arrange
        when(applicationContext.getBeansOfType(SpecialTagStrategy.class))
                .thenReturn(Map.of("testStrategy", specialTagStrategy));
        when(specialTagStrategy.getName()).thenReturn("testStrategy");
        specialTagStrategyFactory.afterPropertiesSet();

        // act
        SpecialTagStrategy result = specialTagStrategyFactory.getSpecialTagStrategy("testStrategy");

        // assert
        assertNotNull(result);
        assertEquals(specialTagStrategy, result);
    }

    /**
     * 测试当策略不存在时
     */
    @Test
    public void testGetSpecialTagStrategyWhenStrategyDoesNotExist() throws Throwable {
        // arrange
        when(applicationContext.getBeansOfType(SpecialTagStrategy.class))
                .thenReturn(Map.of());
        specialTagStrategyFactory.afterPropertiesSet();

        // act
        SpecialTagStrategy result = specialTagStrategyFactory.getSpecialTagStrategy("nonExistingStrategy");

        // assert
        assertNull(result);
    }

    /**
     * 测试当Spring容器中没有SpecialTagStrategy类型的Bean时
     */
    @Test
    public void testGetSpecialTagStrategyWhenNoStrategiesInContext() throws Throwable {
        // arrange
        when(applicationContext.getBeansOfType(SpecialTagStrategy.class))
                .thenReturn(Map.of());
        specialTagStrategyFactory.afterPropertiesSet();

        // act
        SpecialTagStrategy result = specialTagStrategyFactory.getSpecialTagStrategy("testStrategy");

        // assert
        assertNull(result);
    }
}
