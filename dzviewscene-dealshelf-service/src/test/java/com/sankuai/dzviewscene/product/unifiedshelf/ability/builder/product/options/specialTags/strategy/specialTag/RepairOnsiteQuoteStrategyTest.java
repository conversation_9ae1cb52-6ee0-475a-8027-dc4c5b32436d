package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.specialTag;

import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.List;

import static org.junit.Assert.*;

public class RepairOnsiteQuoteStrategyTest {

    private RepairOnsiteQuoteStrategy strategy;
    private SpecialTagBuildReq req;
    private ProductM productM;

    @Before
    public void setUp() {
        strategy = new RepairOnsiteQuoteStrategy();
        req = new SpecialTagBuildReq();
        productM = Mockito.mock(ProductM.class);
    }

    /**
     * 测试场景：当 ProductM 为 null 时
     */
    @Test
    public void testBuildWhenProductMIsNull() {
        req.setProductM(null);

        List<ShelfTagVO> result = strategy.build(req);

        assertNull(result);
    }

    /**
     * 测试场景：当支付方式匹配且服务类型匹配时
     */
    @Test
    public void testBuildWhenPayMethodAndServiceTypeMatch() {
        Mockito.when(productM.getAttr("pay_method")).thenReturn("2");
        Mockito.when(productM.getAttr("computer_project_type")).thenReturn("上门费");
        req.setProductM(productM);

        List<ShelfTagVO> result = strategy.build(req);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("维修费待师傅上门后报价", result.get(0).getText().getText());
    }

    /**
     * 测试场景：当支付方式不匹配时
     */
    @Test
    public void testBuildWhenPayMethodDoesNotMatch() {
        Mockito.when(productM.getAttr("pay_method")).thenReturn("1");
        Mockito.when(productM.getAttr("computer_project_type")).thenReturn("上门费");
        req.setProductM(productM);

        List<ShelfTagVO> result = strategy.build(req);

        assertNull(result);
    }

    /**
     * 测试场景：当服务类型不匹配时
     */
    @Test
    public void testBuildWhenServiceTypeDoesNotMatch() {
        Mockito.when(productM.getAttr("pay_method")).thenReturn("2");
        Mockito.when(productM.getAttr("computer_project_type")).thenReturn("非上门费");
        req.setProductM(productM);

        List<ShelfTagVO> result = strategy.build(req);

        assertNull(result);
    }

    /**
     * 测试场景：当支付方式和服务类型都不匹配时
     */
    @Test
    public void testBuildWhenNeitherPayMethodNorServiceTypeMatch() {
        Mockito.when(productM.getAttr("pay_method")).thenReturn("1");
        Mockito.when(productM.getAttr("computer_project_type")).thenReturn("非上门费");
        req.setProductM(productM);

        List<ShelfTagVO> result = strategy.build(req);

        assertNull(result);
    }
}
