package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemTitleVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class PressOnNailItemTitleOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private ProductM mockProductM;
    @Mock
    private PressOnNailItemTitleOpt.Config mockConfig;

    private PressOnNailItemTitleOpt pressOnNailItemTitleOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailItemTitleOpt = new PressOnNailItemTitleOpt();
    }

    /**
     * 测试compute方法，当config禁用高亮且产品材料列表为空时
     */
    @Test
    public void testCompute_DisableHighlightAndEmptyMaterialList() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getMaterialList()).thenReturn(new ArrayList<>());
        when(mockConfig.isDisableHighlight()).thenReturn(true);

        // act
        List<StyleTextModel> result = pressOnNailItemTitleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNull(result.get(0).getText());
    }

    /**
     * 测试compute方法，当config禁用高亮且产品材料列表不为空，但材料名称为空时
     */
    @Test
    public void testCompute_DisableHighlightAndNonEmptyMaterialListWithEmptyName() {
        // arrange
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("");
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getMaterialList()).thenReturn(Arrays.asList(materialM));
        when(mockConfig.isDisableHighlight()).thenReturn(true);

        // act
        List<StyleTextModel> result = pressOnNailItemTitleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertNull(result.get(0).getText());
    }

    /**
     * 测试compute方法，当config启用高亮且产品材料列表不为空，且材料名称不为空时
     */
    @Test
    public void testCompute_EnableHighlightAndNonEmptyMaterialListWithNonEmptyName() {
        // arrange
        DealProductMaterialM materialM = new DealProductMaterialM();
        materialM.setName("MaterialName");
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getTitle()).thenReturn("ProductTitle");
        when(mockProductM.getMaterialList()).thenReturn(Arrays.asList(materialM));
        when(mockConfig.isDisableHighlight()).thenReturn(false);

        // 模拟process4Highlight方法的行为，这里简化为直接返回一个非空列表
        // 注意：实际测试中需要根据process4Highlight的实现来调整
        List<StyleTextModel> highlightTextModels = Arrays.asList(new StyleTextModel());
        // 这里简化处理，直接返回非空列表表示有高亮文本
        // act
        List<StyleTextModel> result = pressOnNailItemTitleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // assert
        assertEquals("结果应为非空列表", 1, result.size());
    }
}
