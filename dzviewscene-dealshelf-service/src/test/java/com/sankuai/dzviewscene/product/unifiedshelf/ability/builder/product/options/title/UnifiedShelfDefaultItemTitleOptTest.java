package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfDefaultItemTitleOptTest {

    @Mock
    private UnifiedShelfItemDefaultTitleOpt.Config config;

    @Mock
    private ActivityCxt context;

    @Mock
    private UnifiedShelfItemDefaultTitleOpt.Param param;

    @InjectMocks
    private UnifiedShelfItemDefaultTitleOpt defaultItemTitleOpt;

    @Test
    public void test_title() {
        ProductM productM = new ProductM();
        productM.setTitle("标题");
        when(param.getProductM()).thenReturn(productM);

        List<StyleTextModel> compute = defaultItemTitleOpt.compute(context, param, config);
        Assert.assertNotNull(compute);
    }
}
