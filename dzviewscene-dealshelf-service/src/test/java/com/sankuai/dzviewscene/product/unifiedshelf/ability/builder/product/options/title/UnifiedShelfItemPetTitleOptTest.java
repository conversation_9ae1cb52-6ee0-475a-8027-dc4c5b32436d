package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

/**
 * 测试UnifiedShelfItemPetTitleOpt的compute方法
 */
public class UnifiedShelfItemPetTitleOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemTitleVP.Param mockParam;
    @Mock
    private UnifiedShelfItemPetTitleOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private UnifiedShelfItemPetTitleOpt unifiedShelfItemPetTitleOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        unifiedShelfItemPetTitleOpt = new UnifiedShelfItemPetTitleOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    /**
     * 测试compute方法，当config禁用高亮且标题为空时
     */
    @Test
    public void testCompute_DisableHighlightAndEmptyTitle() {
        when(mockConfig.isDisableHighlight()).thenReturn(true);
        when(mockProductM.getTitle()).thenReturn("");

        List<StyleTextModel> result = unifiedShelfItemPetTitleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertTrue(result.get(0).getText().isEmpty());
    }

    /**
     * 测试compute方法，当config禁用高亮且标题非空时
     */
    @Test
    public void testCompute_DisableHighlightAndNonEmptyTitle() {
        when(mockConfig.isDisableHighlight()).thenReturn(true);
        when(mockProductM.getTitle()).thenReturn("Pet Title");

        List<StyleTextModel> result = unifiedShelfItemPetTitleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("Pet Title", result.get(0).getText());
    }

    /**
     * 测试compute方法，当config启用高亮但未找到高亮文本时
     */
    @Test
    public void testCompute_EnableHighlightButNoHighlightText() {
        when(mockConfig.isDisableHighlight()).thenReturn(false);
        when(mockProductM.getTitle()).thenReturn("Pet Title");
        when(mockProductM.getAttr("service_type")).thenReturn("洗澡");

        List<StyleTextModel> result = unifiedShelfItemPetTitleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("Pet Title", result.get(0).getText());
    }

    @Test
    public void testCompute_FilterId() {
        when(mockConfig.isDisableHighlight()).thenReturn(false);
        when(mockProductM.getTitle()).thenReturn("Pet Title");
        when(mockProductM.getAttr("service_type")).thenReturn("洗澡");
        when(mockParam.getFilterId()).thenReturn(1l);
        when(mockConfig.getNoServiceTypeFilterIds()).thenReturn(Collections.singletonList(1l));

        List<StyleTextModel> result = unifiedShelfItemPetTitleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("Pet Title", result.get(0).getText());
    }

}
