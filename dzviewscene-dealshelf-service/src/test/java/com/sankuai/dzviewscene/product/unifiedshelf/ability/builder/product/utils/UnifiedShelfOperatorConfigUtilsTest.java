package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils;

import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.UncheckedExecutionException;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfCategoryAndVersionDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigSource;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStableTag;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 类的描述
 *
 * @auther: liweilong06
 * @date: 2024/12/12 3:31下午
 */
public class UnifiedShelfOperatorConfigUtilsTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试executeGroovy方法，当groovyShell参数为空时
     */
    @Test
    public void testExecuteGroovyWithEmptyGroovyShell() {
        // arrange
        Map<String, Object> params = new HashMap<>();

        // act
        Object result = UnifiedShelfOperatorConfigUtils.executeGroovy(null, params);

        // assert
        assertNull("当groovyShell为空时，应返回null", result);
    }

    /**
     * 测试executeGroovy方法，当params参数为空时
     */
    @Test
    public void testExecuteGroovyWithEmptyParams() {
        // arrange
        String groovyShell = "return 'Hello World'";
        Map<String, Object> params = new HashMap<>();

        // act
        Object result = UnifiedShelfOperatorConfigUtils.executeGroovy(groovyShell, params);

        // assert
        assertNull("当params为空时，应返回null", result);
    }

    /**
     * 测试executeGroovy方法，正常情况
     */
    @Test
    public void testExecuteGroovyNormal() {
        // arrange
        String groovyShell = "return 'Hello World'";
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");


        // act
        Object result = UnifiedShelfOperatorConfigUtils.executeGroovy(groovyShell, params);

        // assert
        assertEquals("在正常情况下，应返回正确的结果", "Hello World", result);
    }

    /**
     * 测试executeGroovy方法，异常引入
     */
    @Test(expected = UncheckedExecutionException.class)
    public void testExecuteGroovyAsUnsafeImport() {
        // arrange
        String groovyShell = "import org.mockito.MockedStatic;\n return \"123\"";
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");


        // act
        UnifiedShelfOperatorConfigUtils.executeGroovy(groovyShell, params);

        // 期望抛出异常
    }

    /**
     * 测试executeGroovy方法，异常引入
     */
    @Test(expected = SecurityException.class)
    public void testExecuteGroovyAsUnsafeCode() {
        // arrange
        String groovyShell = "System.exit(0)";
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");


        // act
        UnifiedShelfOperatorConfigUtils.executeGroovy(groovyShell, params);

        // 期望抛出异常
    }

    /**
     * 测试executeGroovy方法，验证Runtime.getRuntime().execute()执行时抛出SecurityException
     */
    @Test(expected = SecurityException.class)
    public void testExecuteGroovyWithRuntimeExecute() {
        // arrange
        String groovyShell = "Runtime.getRuntime().execute('ls')";
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");

        // act
        UnifiedShelfOperatorConfigUtils.executeGroovy(groovyShell, params);

        // 期望抛出SecurityException
    }

    @Test
    public void testGetOperatorConfigAttrs() {
        // arrange
        Set<String> expectedAttrs = new HashSet<>(Arrays.asList("attr1", "attr2"));
        Map<StoreKey, List<OperatorShelfConfigDTO>> configMap = new HashMap<>();
        OperatorShelfConfigDTO configDTO = new OperatorShelfConfigDTO();
        configDTO.setNeedAttrs(Arrays.asList("attr1", "attr2"));
        configDTO.setStrategyUnit(OperatorShelfConfigStrategyUnit.subTitle);
        configMap.put(new StoreKey("category", "key"), Collections.singletonList(configDTO));
        CompletableFuture<Map<StoreKey, List<OperatorShelfConfigDTO>>> future = CompletableFuture.completedFuture(configMap);

        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM, future);

        // act
        Set<String> actualAttrs = UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext);

        // assert
        assertEquals(expectedAttrs, actualAttrs);
    }

    @Test
    public void testGetOperatorConfigAttrsWhenFutureIsNull() {
        // arrange
        when(activityContext.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(null);

        // act
        Set<String> actualAttrs = UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext);

        // assert
        assertTrue(actualAttrs.isEmpty());
    }

    @Test
    public void testGetOperatorConfigAttrsWhenConfigMapIsEmpty() {
        // arrange
        CompletableFuture<Map<StoreKey, List<OperatorShelfConfigDTO>>> future = CompletableFuture.completedFuture(new HashMap<>());
        when(activityContext.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(future);

        // act
        Set<String> actualAttrs = UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext);

        // assert
        assertTrue(actualAttrs.isEmpty());
    }

    @Test
    public void testAddOperatorShelfConfig() {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        List<StoreKey> storeKeys = Arrays.asList(new StoreKey("shelf_operator_preview_config", "agent", "subtitle", "1"),
                new StoreKey("shelf_operator_config", "1#2#3"));
        Map<StoreKey, Object> expectedConfigMap = new HashMap<>();
        expectedConfigMap.put(storeKeys.get(0), Collections.singletonList(new OperatorShelfConfigDTO()));
        expectedConfigMap.put(storeKeys.get(1), Collections.singletonList(new OperatorShelfConfigDTO()));
        parameters.put(ShelfActivityConstants.Params.operatorPreviewConfigTags, Lists.newArrayList("agent#subtitle#1"));
        ShopM shopM = new ShopM();
        shopM.setBackCategory(Lists.newArrayList(1,2,3));
        parameters.put(ShelfActivityConstants.Ctx.ctxShop, shopM);

        try (MockedStatic<AthenaBeanFactory> athenaBeanFactoryMockedStatic = mockStatic(AthenaBeanFactory.class)) {

            RedisStoreClient redisStoreClient = mock(RedisStoreClient.class);
            athenaBeanFactoryMockedStatic.when(() -> AthenaBeanFactory.getBean(RedisStoreClient.class)).thenReturn(redisStoreClient);
            when(redisStoreClient.multiGet(anyListOf(StoreKey.class))).thenReturn(expectedConfigMap);

            // act
            Map<StoreKey, List<OperatorShelfConfigDTO>> actualConfigMap = UnifiedShelfOperatorConfigUtils.addOperatorShelfConfig(parameters);

            // assert
            assertEquals(expectedConfigMap, actualConfigMap);
        }
    }

    @Test
    public void testAddOperatorShelfConfigWhenExceptionOccurs() {
        // arrange
        Map<String, Object> parameters = new HashMap<>();

        try (MockedStatic<AthenaBeanFactory> athenaBeanFactoryMockedStatic = mockStatic(AthenaBeanFactory.class)) {
            RedisStoreClient redisStoreClient = mock(RedisStoreClient.class);
            athenaBeanFactoryMockedStatic.when(() -> AthenaBeanFactory.getBean(RedisStoreClient.class)).thenReturn(redisStoreClient);
            when(redisStoreClient.multiGet(anyList())).thenThrow(RuntimeException.class);

            // act
            Map<StoreKey, List<OperatorShelfConfigDTO>> actualConfigMap = UnifiedShelfOperatorConfigUtils.addOperatorShelfConfig(parameters);

            // assert
            assertTrue(actualConfigMap.isEmpty());
        }
    }

    @Test
    public void testGetShelfOperatorConfig() {
        // arrange
        ProductM productM = new ProductM();
        productM.setCategoryId(1);
        productM.setServiceTypeId(2L);
        productM.setAttr(UnifiedShelfOperatorConfigUtils.SERVICE_TYPE_LEAF_ID, "3");

        OperatorShelfConfigDTO expectedConfig = new OperatorShelfConfigDTO();
        expectedConfig.setShopBackCategories(Arrays.asList(1));
        expectedConfig.setProductCategories(Arrays.asList(801L, 842L, 841L));
        expectedConfig.setStrategyUnit(OperatorShelfConfigStrategyUnit.shelfNav);
        expectedConfig.setStableTag(OperatorShelfConfigStableTag.online);
        expectedConfig.setDpExperimentId("1");
        expectedConfig.setVersion("2");

        OperatorShelfConfigDTO notFirst = new OperatorShelfConfigDTO();
        notFirst.setShopBackCategories(Arrays.asList(1));
        notFirst.setProductCategories(Arrays.asList(801L, 842L, 841L));
        notFirst.setStrategyUnit(OperatorShelfConfigStrategyUnit.shelfNav);
        notFirst.setStableTag(OperatorShelfConfigStableTag.online);
        notFirst.setDpExperimentId("1");
        notFirst.setVersion("1");

        Map<StoreKey, List<OperatorShelfConfigDTO>> configMap = new HashMap<>();
        configMap.put(new StoreKey("category", "3", "48", "V2"), Collections.singletonList(expectedConfig));
        configMap.put(new StoreKey("category", "3", "V2"), Collections.singletonList(notFirst));

        CompletableFuture<Map<StoreKey, List<OperatorShelfConfigDTO>>> future = CompletableFuture.completedFuture(configMap);

        try (MockedStatic<UnifiedShelfOperatorConfigContent> contentMockedStatic = Mockito.mockStatic(UnifiedShelfOperatorConfigContent.class)) {
            List<OperatorShelfCategoryAndVersionDTO> configs = Lists.newArrayList(buildCategoryVersionDTO("V2"));
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getOperatorShelfSubTitleVersionConfig()).thenReturn(configs);
            when(activityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(future);
            when(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(buildShopM());

            // act
            OperatorShelfConfigDTO actualConfig = UnifiedShelfOperatorConfigUtils.getShelfOperatorConfig(activityCxt, productM, OperatorShelfConfigStrategyUnit.shelfNav, false);

            // assert
            assertEquals(expectedConfig, actualConfig);
        } catch (Exception e) {
            // Noting
        }
    }

    private ShopM buildShopM() {
        ShopM shopM = new ShopM();
        shopM.setShopId(1);
        shopM.setCityId(1);
        shopM.setBackCategory(Lists.newArrayList(48));
        return shopM;
    }

    private OperatorShelfCategoryAndVersionDTO buildCategoryVersionDTO(String version) {
        OperatorShelfCategoryAndVersionDTO categoryDTO = new OperatorShelfCategoryAndVersionDTO();
        categoryDTO.setShopBackCategory(48);
        categoryDTO.setVersion(version);
        categoryDTO.setProductCategoryList(Lists.newArrayList(841L));
        return categoryDTO;
    }

    @Test
    public void testGetReleaseShelfOperatorConfig() {
        // arrange
        ProductM productM = new ProductM();
        productM.setCategoryId(1);
        productM.setServiceTypeId(2L);
        productM.setAttr(UnifiedShelfOperatorConfigUtils.SERVICE_TYPE_LEAF_ID, "3");

        OperatorShelfConfigDTO expectedConfig = new OperatorShelfConfigDTO();
        expectedConfig.setVersion("1");
        expectedConfig.setProductCategories(Arrays.asList(801L, 842L, 841L));
        expectedConfig.setStrategyUnit(OperatorShelfConfigStrategyUnit.shelfNav);
        expectedConfig.setStableTag(OperatorShelfConfigStableTag.online);

        OperatorShelfConfigDTO notFirst = new OperatorShelfConfigDTO();
        notFirst.setProductCategories(Arrays.asList(801L, 842L, 841L));
        notFirst.setStrategyUnit(OperatorShelfConfigStrategyUnit.shelfNav);
        notFirst.setStableTag(OperatorShelfConfigStableTag.online);
        notFirst.setVersion("2");
        notFirst.setDpExperimentId("1");

        Map<StoreKey, List<OperatorShelfConfigDTO>> configMap = new HashMap<>();
        configMap.put(new StoreKey("category", "3", "48", "V2"), Collections.singletonList(expectedConfig));
        configMap.put(new StoreKey("category", "3", "V2"), Collections.singletonList(notFirst));


        CompletableFuture<Map<StoreKey, List<OperatorShelfConfigDTO>>> future = CompletableFuture.completedFuture(configMap);

        try (MockedStatic<UnifiedShelfOperatorConfigContent> contentMockedStatic = Mockito.mockStatic(UnifiedShelfOperatorConfigContent.class)) {
            List<OperatorShelfCategoryAndVersionDTO> configs = Lists.newArrayList(buildCategoryVersionDTO("2"));
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getOperatorShelfSubTitleVersionConfig()).thenReturn(configs);
            when(activityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(future);
            when(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(buildShopM());

            // act
            OperatorShelfConfigDTO actualConfig = UnifiedShelfOperatorConfigUtils.getShelfOperatorConfig(activityCxt, productM, OperatorShelfConfigStrategyUnit.shelfNav, true);

            // assert
            assertEquals(expectedConfig, actualConfig);
        } catch (Exception e) {
            // Noting
        }
    }

    @Test
    public void testGetShelfOperatorConfigWhenFutureIsNull() {
        // arrange
        ProductM productM = new ProductM();
        when(activityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(null);

        // act
        OperatorShelfConfigDTO actualConfig = UnifiedShelfOperatorConfigUtils.getShelfOperatorConfig(activityCxt, productM, OperatorShelfConfigStrategyUnit.shelfNav, false);

        // assert
        assertNull(actualConfig);
    }

    @Test
    public void testGetShelfOperatorConfigWhenConfigMapIsEmpty() {
        // arrange
        ProductM productM = new ProductM();
        CompletableFuture<Map<StoreKey, List<OperatorShelfConfigDTO>>> future = CompletableFuture.completedFuture(new HashMap<>());
        when(activityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(future);

        // act
        OperatorShelfConfigDTO actualConfig = UnifiedShelfOperatorConfigUtils.getShelfOperatorConfig(activityCxt, productM, OperatorShelfConfigStrategyUnit.shelfNav, false);

        // assert
        assertNull(actualConfig);
    }

    @Test
    public void testAddMockDouHuResult() {
        // arrange
        List<String> mockDouHuResult = Arrays.asList("exp1_a", "exp2_b");
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.mockDouHuResult)).thenReturn(mockDouHuResult);
        when(activityCxt.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);

        // act
        UnifiedShelfOperatorConfigUtils.addMockDouHuResult(activityCxt);

        // assert
        assertEquals(2, douHuMList.size());
        assertEquals("exp1", douHuMList.get(0).getExpId());
        assertEquals("exp2", douHuMList.get(1).getExpId());

    }

    @Test
    public void testAddMockDouHuResultWhenMockDouHuResultIsEmpty() {
        // arrange
        when(activityCxt.getParam(ShelfActivityConstants.Params.mockDouHuResult)).thenReturn(Collections.emptyList());

        // act
        UnifiedShelfOperatorConfigUtils.addMockDouHuResult(activityCxt);

        // assert
        verify(activityCxt, never()).getSource(anyString());
    }

    @Test
    public void testAddMockDouHuResultWhenValidateDouHuFails() {
        // arrange
        List<String> mockDouHuResult = Arrays.asList("invalid_mock");
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.mockDouHuResult)).thenReturn(mockDouHuResult);
        when(activityCxt.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);

        // act
        UnifiedShelfOperatorConfigUtils.addMockDouHuResult(activityCxt);

        // assert
        assertTrue(CollectionUtils.isEmpty(douHuMList));
    }

    @Test
    public void testShouldHasOperatorConfigWhenContextOrProductMIsNull() {
        // act & assert
        assertFalse(UnifiedShelfOperatorConfigUtils.shouldHasOperatorConfig(null, productM));
        assertFalse(UnifiedShelfOperatorConfigUtils.shouldHasOperatorConfig(activityCxt, null));
    }

    @Test
    public void testShouldHasOperatorConfigWhenOperatorShelfCategoryListIsEmpty() {
        // arrange
        try (MockedStatic<UnifiedShelfOperatorConfigContent> contentMockedStatic = Mockito.mockStatic(UnifiedShelfOperatorConfigContent.class)) {
            List<OperatorShelfCategoryAndVersionDTO> configs = Lists.newArrayList();
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getOperatorShelfSubTitleVersionConfig()).thenReturn(configs);

            // act
            boolean result = UnifiedShelfOperatorConfigUtils.shouldHasOperatorConfig(activityCxt, productM);

            // assert
            assertFalse(result);
        }
    }

    @Test
    public void testShouldHasOperatorConfigWhenShopBackCategoryListOrProductBackCategoryListIsEmpty() {
        // arrange
        List<OperatorShelfCategoryAndVersionDTO> operatorShelfCategoryList = new ArrayList<>();
        operatorShelfCategoryList.add(new OperatorShelfCategoryAndVersionDTO());
        try (MockedStatic<UnifiedShelfOperatorConfigContent> contentMockedStatic = Mockito.mockStatic(UnifiedShelfOperatorConfigContent.class)) {
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getOperatorShelfSubTitleVersionConfig()).thenReturn(operatorShelfCategoryList);

            // act
            boolean result = UnifiedShelfOperatorConfigUtils.shouldHasOperatorConfig(activityCxt, productM);

            // assert
            assertFalse(result);
        }
    }

    @Test
    public void testShouldHasOperatorConfigWhenShopBackCategoryAndProductBackCategoryMatch() {
        // arrange
        ShopM shopM = new ShopM();
        shopM.setBackCategory(Lists.newArrayList(1,2,48));
        when(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);

        when(productM.getCategoryId()).thenReturn(2);
        when(productM.getServiceTypeId()).thenReturn(1L);
        when(productM.getAttr(anyString())).thenReturn("3");

        try (MockedStatic<UnifiedShelfOperatorConfigContent> contentMockedStatic = Mockito.mockStatic(UnifiedShelfOperatorConfigContent.class)) {
            List<OperatorShelfCategoryAndVersionDTO> configs = Lists.newArrayList(buildCategoryVersionDTO("3"));
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getOperatorShelfSubTitleVersionConfig()).thenReturn(configs);
            // act
            boolean result = UnifiedShelfOperatorConfigUtils.shouldHasOperatorConfig(activityCxt, productM);

            // assert
            assertTrue(result);
        }
    }

    @Test
    public void testShouldHasOperatorConfigWhenShopBackCategoryAndProductBackCategoryDoNotMatch() {
        // arrange
        List<OperatorShelfCategoryAndVersionDTO> operatorShelfCategoryList = new ArrayList<>();
        OperatorShelfCategoryAndVersionDTO OperatorShelfCategoryAndVersionDTO = new OperatorShelfCategoryAndVersionDTO();
        OperatorShelfCategoryAndVersionDTO.setShopBackCategory(1);
        OperatorShelfCategoryAndVersionDTO.setVersion("3");
        OperatorShelfCategoryAndVersionDTO.setProductCategoryList(Lists.newArrayList(2L));
        operatorShelfCategoryList.add(OperatorShelfCategoryAndVersionDTO);

        try (MockedStatic<UnifiedShelfOperatorConfigContent> contentMockedStatic = Mockito.mockStatic(UnifiedShelfOperatorConfigContent.class)) {
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getOperatorShelfSubTitleVersionConfig()).thenReturn(operatorShelfCategoryList);

            // act
            boolean result = UnifiedShelfOperatorConfigUtils.shouldHasOperatorConfig(activityCxt, productM);

            // assert
            assertFalse(result);
        }
    }

    @Test
    @Ignore
    public void test_readFromJson() throws Exception  {
        String paramJson = FileUtils.readFileToString(new File("/Users/<USER>/temp/meijie_param_2.json"), "UTF-8");
        if(paramJson.startsWith("\ufeff")){
            paramJson=paramJson.substring(1);
        }
        Map<String, Object> paramMap = JacksonUtils.deserialize(paramJson, Map.class);
        String shell = FileUtils.readFileToString(new File("/Users/<USER>/temp/BeautyMeiJiaMeiJieTags.groovy"), "UTF-8");
        OperatorShelfConfigDTO configDTO = new OperatorShelfConfigDTO();
        configDTO.setGroovyContent(shell);
        String groovyContent = UnifiedShelfOperatorConfigContent.getSubTitleWholeShellContent(configDTO);
        Object result = UnifiedShelfOperatorConfigUtils.executeGroovy(groovyContent, paramMap);
        System.out.println("运行结果是：" + JsonCodec.encodeWithUTF8(result));
    }

    @Test
    @Ignore
    public void test_createConfigDTOs() throws Exception {
        List<Integer> dealCategoryIds = Lists.newArrayList(48,46,52,122006,51,100014,50,44,47,43,49,45);
        List<Long> dealCategoryIdsLong = dealCategoryIds.stream().map(value -> Long.valueOf("84" + value) ).collect(Collectors.toList());
        OperatorShelfConfigDTO configDTO = new OperatorShelfConfigDTO();
        configDTO.setNeedAttrs(getNeedAttrs());
        configDTO.setProductCategories(dealCategoryIdsLong);
        configDTO.setShopBackCategories(Lists.newArrayList(453));
        configDTO.setStableTag(OperatorShelfConfigStableTag.online);
        configDTO.setStrategyUnit(OperatorShelfConfigStrategyUnit.subTitle);
        configDTO.setStrategyId(-1);
        configDTO.setStrategyUnitId(-1);
        configDTO.setGroovyContent(FileUtils.readFileToString(new File("/Users/<USER>/temp/PetSubtitleDocument.groovy"), "UTF-8"));
        System.out.println(JacksonUtils.serialize(Lists.newArrayList(configDTO)));
    }

    @NotNull
    private ArrayList<String> getNeedAttrs() {
        return Lists.newArrayList("attr_count_card_tag",
                "attr_over_night_tag",
                "attr_joy_service_duration_tag",
                "bodyRegion",
                "serviceBodyRange",
                "moxibustionMethod",
                "unclassifiedTools",
                "moxibustionTool",
                "moxibustionMaterial",
                "disposableMaterial");
    }

}
