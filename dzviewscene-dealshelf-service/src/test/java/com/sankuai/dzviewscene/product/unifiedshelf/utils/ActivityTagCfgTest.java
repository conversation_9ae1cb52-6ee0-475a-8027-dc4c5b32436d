package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy.ActivityTagCfg;
import org.junit.Test;

import static org.junit.Assert.assertNotNull;

public class ActivityTagCfgTest {

    @Test
    public void test_() {

        ActivityTagCfg cfg = new ActivityTagCfg();
        cfg.setActivityTypes(Lists.newArrayList());
        cfg.setActivityScene(Lists.newArrayList());
        cfg.setIsPreheat(false);

        assertNotNull(JSON.toJSONString(cfg));
        assertNotNull(JSON.toJSONString(cfg.getActivityScene()));
        assertNotNull(JSON.toJSONString(cfg.getActivityTypes()));
        assertNotNull(JSON.toJSONString(cfg.getIsPreheat()));

    }
}
