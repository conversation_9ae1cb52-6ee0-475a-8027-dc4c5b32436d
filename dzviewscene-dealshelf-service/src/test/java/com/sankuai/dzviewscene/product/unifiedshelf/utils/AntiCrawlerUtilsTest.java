package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class AntiCrawlerUtilsTest {

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = Mockito.mockStatic(Lion.class);
    }

    @After
    public void tearDown() throws Exception {
        lionMockedStatic.close();
    }

    /**
     * 测试总开关关闭时，方法直接返回，不进行任何处理
     */
    @Test
    public void testHideProductKeyInfo4UnifiedShelf_SwitchOff() {

        lionMockedStatic.when(() -> Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.anti.crawler.hide.info.switch", false)).thenReturn(false);

        List<ShelfFilterProductAreaVO> filterBtnIdAndProAreasVOS = new ArrayList<>();
        // act
        AntiCrawlerUtils.hideProductKeyInfo4UnifiedShelf(filterBtnIdAndProAreasVOS, 0, "testScene");

        // assert
        assertTrue("当总开关关闭时，方法应直接返回，不做任何处理", filterBtnIdAndProAreasVOS.isEmpty());
    }


    /**
     * 测试正常情况下，商品关键信息被隐藏
     */
    @Test
    public void testHideProductKeyInfo4UnifiedShelf_NormalCondition() {
        // arrange
        lionMockedStatic.when(() -> Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.anti.crawler.hide.info.switch", false)).thenReturn(true);

        ShelfItemVO shelfItemVO = new ShelfItemVO();
        shelfItemVO.setSalePrice("100");
        ShelfProductAreaVO shelfProductAreaVO = new ShelfProductAreaVO();
        shelfProductAreaVO.setItems(Lists.newArrayList(shelfItemVO));
        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        shelfFilterProductAreaVO.setProductAreas(Lists.newArrayList(shelfProductAreaVO));
        List<ShelfFilterProductAreaVO> filterBtnIdAndProAreasVOS = Lists.newArrayList(shelfFilterProductAreaVO);

        // act
        AntiCrawlerUtils.hideProductKeyInfo4UnifiedShelf(filterBtnIdAndProAreasVOS, 0, "testScene");

        // assert
        assertNotNull("商品关键信息应被隐藏", filterBtnIdAndProAreasVOS.get(0).getProductAreas().get(0).getItems().get(0).getSalePrice());
        assertNotEquals("100", filterBtnIdAndProAreasVOS.get(0).getProductAreas().get(0).getItems().get(0).getSalePrice());
    }

    /**
     * 测试正常情况下，商品关键信息被隐藏
     */
    @Test
    public void testHideProductKeyInfo4UnifiedShelf_2() {
        boolean b = AntiCrawlerUtils.clearUnifiedShelf();
        Object o = new Object();
        Assert.assertNotNull(o);
    }

    /**
     * 测试正常情况下，商品关键信息被隐藏
     */
    @Test
    public void testloginLog() {
        AntiCrawlerUtils.loginLog(0, "test");
        Object o = new Object();
        Assert.assertNotNull(o);
    }

    /**
     * 测试正常情况下，商品关键信息被隐藏
     */
    @Test
    public void testloginLog2() {
        AntiCrawlerUtils.loginLog(1, "test");
        Object o = new Object();
        Assert.assertNotNull(o);
    }

    /**
     * 测试总开关关闭时，方法直接返回，不进行任何处理
     */
    @Test
    public void testHideProductKeyInfo4UnifiedShelf_SwitchOff2() {

        lionMockedStatic.when(() -> Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.clearUnifiedShelf", false)).thenReturn(true);

        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        // act
        AntiCrawlerUtils.clearUnifiedShelf(shelfFilterProductAreaVO, 0, "testScene",false);
        Object o = new Object();
        Assert.assertNotNull(o);
    }

    /**
     * 测试总开关关闭时，方法直接返回，不进行任何处理
     */
    @Test
    public void testHideProductKeyInfo4UnifiedShelf_SwitchOff5() {

        lionMockedStatic.when(() -> Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.clearUnifiedShelf", false)).thenReturn(false);

        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        // act
        AntiCrawlerUtils.clearUnifiedShelf(shelfFilterProductAreaVO, 0, "testScene",false);
        Object o = new Object();
        Assert.assertNotNull(o);
    }


    /**
     * 测试总开关关闭时，方法直接返回，不进行任何处理
     */
    @Test
    public void testHideProductKeyInfo4UnifiedShelf_SwitchOff3() {

        lionMockedStatic.when(() -> Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.clearUnifiedShelf", false)).thenReturn(true);

        UnifiedShelfResponse unifiedShelfResponse = new UnifiedShelfResponse();
        // act
        AntiCrawlerUtils.clearUnifiedShelf(unifiedShelfResponse, 0, "testScene",false);
        Object o = new Object();
        Assert.assertNotNull(o);
    }


    /**
     * 测试总开关关闭时，方法直接返回，不进行任何处理
     */
    @Test
    public void testHideProductKeyInfo4UnifiedShelf_SwitchOff4() {

        lionMockedStatic.when(() -> Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.clearUnifiedShelf", false)).thenReturn(false);

        UnifiedShelfResponse unifiedShelfResponse = new UnifiedShelfResponse();
        // act
        AntiCrawlerUtils.clearUnifiedShelf(unifiedShelfResponse, 0, "testScene",false);
        Object o = new Object();
        Assert.assertNotNull(o);
    }

}
