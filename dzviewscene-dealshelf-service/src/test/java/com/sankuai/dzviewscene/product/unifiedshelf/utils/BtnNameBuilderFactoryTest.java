package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.DealSecKillUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn.BtnNameBuilderFactory;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * BtnNameBuilderFactory单元测试
 */
public class BtnNameBuilderFactoryTest {


    /**
     * 测试getButtonName方法，当btnNameCfg为空时
     */
    @Test
    public void testGetButtonNameWithEmptyConfig() {
        // arrange
        Map<String, String> btnNameCfg = new HashMap<>();

        ActivityCxt activityCxt = new ActivityCxt();
        ProductM productM = new ProductM();
        // act
        String result = BtnNameBuilderFactory.getButtonName(activityCxt, productM, btnNameCfg);

        // assert
        assertNull(result);
    }


    @Test
    public void test_SEC_KILL() {

        ActivityCxt activityCxt = new ActivityCxt();
        ProductM productM = new ProductM();

        try (MockedStatic<DealSecKillUtils> mockedStatic = Mockito.mockStatic(DealSecKillUtils.class)) {
            mockedStatic.when(() -> DealSecKillUtils.isSecKillDeal(productM)).thenReturn(true);
            mockedStatic.when(() -> DealSecKillUtils.hasSecKillPromo(productM)).thenReturn(true);
            // arrange
            Map<String, String> btnNameCfg = new HashMap<>();

            // act
            String result = BtnNameBuilderFactory.getButtonName(activityCxt, productM, btnNameCfg);

            // assert
            assertNotNull(result);
            assertEquals(result, "秒杀");
        }
    }

    @Test
    public void test_PRE_SHOW() {

        ActivityCxt activityCxt = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setAttr("warmUpStartTimeAttr", String.valueOf(System.currentTimeMillis()));

        // arrange
        Map<String, String> btnNameCfg = new HashMap<>();
        btnNameCfg.put("preShow", "preShow");

        // act
        String result = BtnNameBuilderFactory.getButtonName(activityCxt, productM, btnNameCfg);

        // assert
        Object o = new Object();
        Assert.assertNotNull(o);
    }

    @Test
    public void test_coupon() {

        ActivityCxt activityCxt = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setAttr("common_consumer_coupon_assign", "false");
        // arrange
        Map<String, String> btnNameCfg = new HashMap<>();
        btnNameCfg.put("coupon", "coupon");

        // act
        String result = BtnNameBuilderFactory.getButtonName(activityCxt, productM, btnNameCfg);

        // assert
        assertNotNull(result);
    }

    @Test
    public void test_PRE_SALE() {

        ActivityCxt activityCxt = new ActivityCxt();
        ProductM productM = new ProductM();

        try (MockedStatic<PreSaleUtils> mockedStatic = Mockito.mockStatic(PreSaleUtils.class)) {
            mockedStatic.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(true);
            // arrange
            Map<String, String> btnNameCfg = new HashMap<>();
            btnNameCfg.put("preSale", "preSale");

            // act
            String result = BtnNameBuilderFactory.getButtonName(activityCxt, productM, btnNameCfg);

            // assert
            assertNotNull(result);
            assertEquals(result, "preSale");
        }
    }

}
