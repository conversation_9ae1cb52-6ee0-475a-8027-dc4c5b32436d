package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.degrade.util.JsonCodec;
import com.dianping.product.shelf.common.enums.ProductBaseInfoExtraKeyEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.shelf.utils.CommonProductTagUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.MockedStatic;

import java.util.HashMap;

import static org.mockito.Mockito.mockStatic;

public class CommonProductTagUtilsTest {

    @Test
    public void test_appendCommonProductTag() {
        try (MockedStatic<LionConfigHelper> mockedStatic = mockStatic(LionConfigHelper.class)) {
            HashMap<String, String> lionMap = new HashMap<>();
            lionMap.put("startTime","2024-01-28 00:00:00");
            lionMap.put("endTime","2056-01-28 00:00:00");
            mockedStatic.when(() -> LionConfigHelper.getSpringNoAvailable()).thenReturn(lionMap);
            DzItemVO itemVO = new DzItemVO();
            ActivityCxt activityCxt = new ActivityCxt();
            RichLabelVO richLabelVO = new RichLabelVO(11,"#777777","副标题");
            itemVO.setProductRichTags(Lists.newArrayList(richLabelVO));
            ProductM productM = new ProductM();
            productM.setAttr("HolidayClosedBottomProduct", "true");
            String sceneCode = "shoppingmall_deal_shelf";
            CommonProductTagUtils.appendCommonProductTag(itemVO, productM, activityCxt);
            System.out.println("富文本前加不可用："+ JSON.toJSONString(itemVO.getProductRichTags()));
            Assert.assertTrue(itemVO.getProductRichTags().size() == 2);

            itemVO.setProductRichTags(Lists.newArrayList());
            itemVO.setProductTags(Lists.newArrayList("副标题"));
            CommonProductTagUtils.appendCommonProductTag(itemVO, productM, activityCxt);
            System.out.println("普通文本前加不可用："+JSON.toJSONString(itemVO.getProductRichTags()));
            Assert.assertTrue(itemVO.getProductRichTags().size() == 2);

            itemVO.setProductRichTags(Lists.newArrayList());
            itemVO.setProductTags(Lists.newArrayList(JsonCodec.encode(richLabelVO)));
            CommonProductTagUtils.appendCommonProductTag(itemVO, productM, activityCxt);
            System.out.println("JSON后普通文本前加不可用："+JSON.toJSONString(itemVO.getProductTags()));
            Assert.assertTrue(itemVO.getProductTags().size() == 2);
            activityCxt.setSceneCode(sceneCode);
            itemVO.setProductRichTags(Lists.newArrayList());
            itemVO.setProductTags(Lists.newArrayList("副标题"));
            CommonProductTagUtils.appendCommonProductTag(itemVO, productM, activityCxt);
            Assert.assertTrue(itemVO.getProductTags().size() == 1);
        }

    }

    @Test
    public void test_checkShopId() {
        HashMap<String, String> lionMap = new HashMap<>();
        lionMap.put("whiteMtShopIds","88888,99999");
        lionMap.put("whiteDpShopIds","11111,22222");
        //美团在白名单内返回true
        boolean b = CommonProductTagUtils.checkShopId(88888, 2,lionMap);
        Assert.assertTrue(b);
        //点评不在白名单内返回false
        b = CommonProductTagUtils.checkShopId(88888, 1,lionMap);
        Assert.assertTrue(!b);
        //美团不在白名单范围内false
        b = CommonProductTagUtils.checkShopId(11111, 2,lionMap);
        Assert.assertTrue(!b);
        //点评在白名单内返回true
        b = CommonProductTagUtils.checkShopId(11111, 1,lionMap);
        Assert.assertTrue(b);
    }

    @Test
    public void test_checkTime() {
        HashMap<String, String> lionMap = new HashMap<>();
        boolean b = CommonProductTagUtils.checkTime(lionMap);
        //没有配置时间返回false
        Assert.assertTrue(!b);
        lionMap.put("startTime","2025-01-28 00:00:00");
        lionMap.put("endTime","2025-02-05 00:00:00");
        b = CommonProductTagUtils.checkTime(lionMap);
        //不在时间范围内返回false
        Assert.assertTrue(!b);
        lionMap.put("startTime","2024-01-28 00:00:00");
        lionMap.put("endTime","2056-01-28 00:00:00");
        b = CommonProductTagUtils.checkTime(lionMap);
        //在时间范围内返回true
        Assert.assertTrue(b);
    }

    @Test
    public void test_appendUnifiedCommonProductTag() {
        try (MockedStatic<LionConfigHelper> mockedStatic = mockStatic(LionConfigHelper.class)) {
            HashMap<String, String> lionMap = new HashMap<>();
            lionMap.put("startTime","2024-01-28 00:00:00");
            lionMap.put("endTime","2056-01-28 00:00:00");
            mockedStatic.when(() -> LionConfigHelper.getSpringNoAvailable()).thenReturn(lionMap);
            ActivityCxt activityCxt = new ActivityCxt();
            ShelfItemVO shelfItemVO = new ShelfItemVO();
            ProductM productM = new ProductM();
            productM.setAttr("HolidayClosedBottomProduct", "true");
            CommonProductTagUtils.appendUnifiedCommonProductTag(shelfItemVO, productM,activityCxt);
            System.out.println("一致性货架副标题前加不可用："+ JSON.toJSONString(shelfItemVO.getProductTags()));
            Assert.assertTrue(shelfItemVO.getProductTags().getTags().size() == 1);
            ItemSubTitleVO productTags = new ItemSubTitleVO();
            productTags.setJoinType(1);
            StyleTextModel styleTextModel = new StyleTextModel();
            styleTextModel.setText("副标题1");
            styleTextModel.setStyle("textGray");
            productTags.setTags(Lists.newArrayList(styleTextModel));
            shelfItemVO.setProductTags(productTags);
            CommonProductTagUtils.appendUnifiedCommonProductTag(shelfItemVO, productM,activityCxt);
            System.out.println("一致性货架副标题前加不可用："+JSON.toJSONString(shelfItemVO.getProductTags()));
            Assert.assertTrue(shelfItemVO.getProductTags().getTags().size() == 2);
        }

    }
}
