package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealGroupThemeHandler;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.GroupPaddingHandler;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigUtils;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

public class DealGroupThemeHandlerTest {

    private DealGroupThemeHandler handler;

    private Method appendOperatorConfigs;

    @Mock
    private ActivityContext activityContext;

    @Before
    public void setUp() throws NoSuchMethodException {
        handler = new DealGroupThemeHandler();
        appendOperatorConfigs = DealGroupThemeHandler.class.getDeclaredMethod("appendOperatorConfigs", ActivityContext.class, Map.class);
        appendOperatorConfigs.setAccessible(true);
    }

    @Test
    public void test_needPricePageSource() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        ActivityContext context = new ActivityContext();
        context.setParameters(new HashMap<>());
        context.getParameters().put(ShelfActivityConstants.Params.spaceKey, UnifiedShelfActivityCtxBuilder.UNIFIED_SHELF_SPACE_KEY);

        DealGroupThemeHandler dealGroupThemeHandler = new DealGroupThemeHandler();

        Method needPricePageSource = dealGroupThemeHandler.getClass().getDeclaredMethod("needPricePageSource", ActivityContext.class);
        needPricePageSource.setAccessible(true);

        boolean needed = (boolean) needPricePageSource.invoke(dealGroupThemeHandler, context);
        Assert.assertTrue(needed);

    }

    /**
     * 测试appendOperatorConfigs方法，当appendAttrs为空时，不进行任何操作
     */
    @Test
    public void testAppendOperatorConfigsWhenAppendAttrsIsEmpty() throws InvocationTargetException, IllegalAccessException {
        // arrange
        Map<String, Object> extParams = new HashMap<>();
        extParams.put("attributeKeys", new ArrayList<>());
        try (MockedStatic<UnifiedShelfOperatorConfigUtils> mockedStatic = mockStatic(UnifiedShelfOperatorConfigUtils.class)) {
            mockedStatic.when(() -> UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext)).thenReturn(new HashSet<>());

            // act
            appendOperatorConfigs.invoke(handler, activityContext, extParams);

            // assert
            assertTrue(((List) extParams.get("attributeKeys")).isEmpty());
        }
    }

    /**
     * 测试appendOperatorConfigs方法，当appendAttrs非空且extParams中attributeKeys包含所有appendAttrs时，不添加新元素
     */
    @Test
    public void testAppendOperatorConfigsWhenAppendAttrsIsNotEmptyAndAllPresent() throws InvocationTargetException, IllegalAccessException {
        // arrange
        Map<String, Object> extParams = new HashMap<>();
        List<String> attributeKeys = new ArrayList<>(Arrays.asList("attr1", "attr2"));
        extParams.put("attributeKeys", attributeKeys);
        Set<String> appendAttrs = new HashSet<>(Arrays.asList("attr1", "attr2"));
        try (MockedStatic<UnifiedShelfOperatorConfigUtils> mockedStatic = mockStatic(UnifiedShelfOperatorConfigUtils.class)) {
            mockedStatic.when(() -> UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext)).thenReturn(appendAttrs);

            // act
            appendOperatorConfigs.invoke(handler, activityContext, extParams);

            // assert
            assertEquals(3, ((List) extParams.get("attributeKeys")).size());
            assertTrue(((List) extParams.get("attributeKeys")).containsAll(appendAttrs));
        }
    }

    /**
     * 测试appendOperatorConfigs方法，当appendAttrs非空且extParams中attributeKeys不包含所有appendAttrs时，添加缺失的元素
     */
    @Test
    public void testAppendOperatorConfigsWhenAppendAttrsIsNotEmptyAndNotAllPresent() throws InvocationTargetException, IllegalAccessException {
        // arrange
        Map<String, Object> extParams = new HashMap<>();
        List<String> attributeKeys = new ArrayList<>(Collections.singletonList("attr1"));
        extParams.put("attributeKeys", attributeKeys);
        Set<String> appendAttrs = new HashSet<>(Arrays.asList("attr1", "attr2"));
        try (MockedStatic<UnifiedShelfOperatorConfigUtils> mockedStatic = mockStatic(UnifiedShelfOperatorConfigUtils.class)) {
            mockedStatic.when(() -> UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext)).thenReturn(appendAttrs);

            // act
            appendOperatorConfigs.invoke(handler, activityContext, extParams);

            // assert
            assertEquals(3, ((List) extParams.get("attributeKeys")).size());
            assertTrue(((List) extParams.get("attributeKeys")).containsAll(appendAttrs));
        }
    }

    /**
     * 测试appendOperatorConfigs方法，当发生异常时，不对extParams进行任何修改
     */
    @Test
    public void testAppendOperatorConfigsWhenExceptionOccurs() {
        // arrange
        Map<String, Object> extParams = new HashMap<>();
        List<String> attributeKeys = new ArrayList<>(Collections.singletonList("attr1"));
        extParams.put("attributeKeys", attributeKeys);
        try (MockedStatic<UnifiedShelfOperatorConfigUtils> mockedStatic = mockStatic(UnifiedShelfOperatorConfigUtils.class)) {
            mockedStatic.when(() -> UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext)).thenThrow(RuntimeException.class);

            // act
            try {
                appendOperatorConfigs.invoke(handler, activityContext, extParams);
            } catch (Exception ignored) {
            }

            // assert
            assertEquals(1, ((List) extParams.get("attributeKeys")).size());
            assertTrue(((List) extParams.get("attributeKeys")).contains("attr1"));
        }
    }
}
