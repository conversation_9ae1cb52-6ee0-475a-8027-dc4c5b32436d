package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.shelf.business.utils.DzItemVOBuildUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class DzItemVOBuildUtilsTest {


    /**
     * 测试标题和关键词为空时返回null
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlight2_TitleAndKeywordAreEmpty() {
        // arrange
        String title = "";
        String keyword = "";

        // act
        List<StyleTextModel> result = DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(title, keyword);

        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试标题包含关键词时返回高亮文本
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlight2_TitleContainsKeyword() {
        // arrange
        String title = "This is a Test";
        String keyword = "Test";

        // act
        List<StyleTextModel> result = DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(title, keyword);

        // assert
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isEmpty());
        Assert.assertTrue(StringUtils.equalsIgnoreCase(result.get(result.size() - 1).getText(), keyword));
        Assert.assertEquals(TextStyleEnum.TEXT_HIGHLIGHT.getType(), result.get(result.size() - 1).getStyle());
    }

    /**
     * 测试标题不包含关键词时返回null
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlight2_TitleDoesNotContainKeyword() {
        // arrange
        String title = "This is a Test";
        String keyword = "Example";

        // act
        List<StyleTextModel> result = DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(title, keyword);

        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试关键词为空但标题不为空时返回null
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlight2_KeywordIsEmpty() {
        // arrange
        String title = "This is a Test";
        String keyword = "";

        // act
        List<StyleTextModel> result = DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(title, keyword);

        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试标题为空但关键词不为空时返回null
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlight2_TitleIsEmpty() {
        // arrange
        String title = "";
        String keyword = "Test";

        // act
        List<StyleTextModel> result = DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(title, keyword);

        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试标题和关键词大小写不敏感时返回高亮文本
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlight2_CaseInsensitive() {
        // arrange
        String title = "This is a Test";
        String keyword = "test";

        // act
        List<StyleTextModel> result = DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(title, keyword);

        // assert
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isEmpty());
        Assert.assertTrue(StringUtils.equalsIgnoreCase(result.get(result.size() - 1).getText(), keyword));
        Assert.assertEquals(TextStyleEnum.TEXT_HIGHLIGHT.getType(), result.get(result.size() - 1).getStyle());
    }
}
