package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class FilterUtilsTest {

    private List<FilterBtnM> filterBtnMList;

    @Before
    public void setUp() {
        filterBtnMList = new ArrayList<>();
    }

    /**
     * 测试场景：当filterBtnMList为空时，应返回0
     */
    @Test
    public void testFindFilterParentId_EmptyList() {
        long result = FilterUtils.findFilterParentId(null, 1L);
        assertEquals(0L, result);
    }

    /**
     * 测试场景：当filterBtnMList不为空，但没有找到匹配的filterId时，应返回0
     */
    @Test
    public void testFindFilterParentId_NoMatch() {
        FilterBtnM mockFilterBtnM = Mockito.mock(FilterBtnM.class);
        when(mockFilterBtnM.getChildren()).thenReturn(new ArrayList<>());
        filterBtnMList.add(mockFilterBtnM);

        long result = FilterUtils.findFilterParentId(filterBtnMList, 1L);
        assertEquals(0L, result);
    }

    /**
     * 测试场景：当filterBtnMList不为空，且找到匹配的filterId时，应返回正确的parentId
     */
    @Test
    public void testFindFilterParentId_MatchFound() {
        FilterBtnM child = Mockito.mock(FilterBtnM.class);
        when(child.getFilterId()).thenReturn(2L);

        FilterBtnM parent = Mockito.mock(FilterBtnM.class);
        when(parent.getFilterId()).thenReturn(1L);
        when(parent.getChildren()).thenReturn(Arrays.asList(child));

        filterBtnMList.add(parent);

        long result = FilterUtils.findFilterParentId(filterBtnMList, 2L);
        assertEquals(1L, result);
    }

    /**
     * 测试场景：当filterBtnMList中存在多个FilterBtnM，且在最后一个FilterBtnM中找到匹配的filterId时，应返回正确的parentId
     */
    @Test
    public void testFindFilterParentId_MatchInLast() {
        FilterBtnM child = Mockito.mock(FilterBtnM.class);
        when(child.getFilterId()).thenReturn(3L);

        FilterBtnM parent1 = Mockito.mock(FilterBtnM.class);
        when(parent1.getChildren()).thenReturn(new ArrayList<>());

        FilterBtnM parent2 = Mockito.mock(FilterBtnM.class);
        when(parent2.getFilterId()).thenReturn(2L);
        when(parent2.getChildren()).thenReturn(Arrays.asList(child));

        filterBtnMList.addAll(Arrays.asList(parent1, parent2));

        long result = FilterUtils.findFilterParentId(filterBtnMList, 3L);
        assertEquals(2L, result);
    }
}
