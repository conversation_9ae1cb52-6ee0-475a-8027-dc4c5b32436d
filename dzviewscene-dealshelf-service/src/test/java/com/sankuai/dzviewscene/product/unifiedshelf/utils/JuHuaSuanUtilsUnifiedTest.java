package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dztheme.deal.dto.enums.DealActivityTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemActivityVO;
import com.sankuai.dzviewscene.product.shelf.utils.JuHuaSuanUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

public class JuHuaSuanUtilsUnifiedTest {

    /**
     * 测试产品没有活动时返回null
     */
    @Test
    public void testBuildJuHuaSuanEndTimeShelfItemActivityVO_NoActivities() {
        ProductActivityM productActivityM = new ProductActivityM();
        ProductM productM = new ProductM();
        // arrange
        productM.setActivities(new ArrayList<>());

        // act
        ShelfItemActivityVO result = JuHuaSuanUtils.buildJuHuaSuanEndTimeShelfItemActivityVO(productM);

        // assert
        assertNull(result);
    }

    /**
     * 测试产品有聚划算活动且活动结束时间大于0时返回正确的ShelfItemActivityVO
     */
    @Test
    public void testBuildJuHuaSuanEndTimeShelfItemActivityVO_ActivityMatch() {
        // arrange
        ProductActivityM productActivityM = new ProductActivityM();
        ProductM productM = new ProductM();
        productActivityM.setShelfActivityType(DealActivityTypeEnum.JU_HUA_SUAN.getType());
        productActivityM.setActivityEndTime(1000L);
        List<ProductActivityM> activities = new ArrayList<>();
        activities.add(productActivityM);
        productM.setActivities(activities);

        // act
        ShelfItemActivityVO result = JuHuaSuanUtils.buildJuHuaSuanEndTimeShelfItemActivityVO(productM);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试产品有多个活动包含聚划算活动时返回最后一个聚划算活动的结束时间
     */
    @Test
    public void testBuildJuHuaSuanEndTimeShelfItemActivityVO_MultipleActivities() {
        ProductM productM = new ProductM();

        // arrange
        ProductActivityM productActivityM1 = new ProductActivityM();
        productActivityM1.setShelfActivityType(DealActivityTypeEnum.JU_HUA_SUAN.getType());
        productActivityM1.setActivityEndTime(1000L);

        ProductActivityM productActivityM2 = new ProductActivityM();
        productActivityM2.setShelfActivityType(DealActivityTypeEnum.JU_HUA_SUAN.getType());
        productActivityM2.setActivityEndTime(2000L);

        List<ProductActivityM> activities = new ArrayList<>();
        activities.add(productActivityM1);
        activities.add(productActivityM2);
        productM.setActivities(activities);

        // act
        ShelfItemActivityVO result = JuHuaSuanUtils.buildJuHuaSuanEndTimeShelfItemActivityVO(productM);

        // assert
        assertNotNull(result);
        // 验证返回的是最后一个聚划算活动的结束时间
    }
}
