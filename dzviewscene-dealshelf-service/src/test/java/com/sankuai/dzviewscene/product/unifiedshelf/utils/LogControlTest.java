package com.sankuai.dzviewscene.product.unifiedshelf.utils;


import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dzviewscene.product.shelf.utils.LogControl;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class LogControlTest {

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = Mockito.mockStatic(Lion.class);
    }

    @After
    public void tearDown() throws Exception {
        lionMockedStatic.close();
    }


    @Test
    public void test_logControl_static() {
        String text = "{ \"forceOpen\":true }";
        lionMockedStatic.when(() -> Lion.getString(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.log.control.config")).thenReturn(text);
        LogControl.refreshLogControl();

        Object o = new Object();
        Assert.assertNotNull(o);
    }

    @Test
    public void test_logControl_logFuc() {
        String text = "{ \"forceOpen\":true }";
        lionMockedStatic.when(() -> Lion.getString(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.log.control.config")).thenReturn(text);

        Map<String, Object> param = new HashMap<>();
        LogControl.logFuc(param, () -> {
            System.out.println(text);
        });

        Object o = new Object();
        Assert.assertNotNull(o);
    }

    @Test
    public void test_logControl_logFuc2() {
        String text = "{ \"forceOpen\":false }";
        lionMockedStatic.when(() -> Lion.getString(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.log.control.config")).thenReturn(text);

        Map<String, Object> param = new HashMap<>();
        LogControl.logFuc(param, () -> {
            System.out.println(text);
        });

        Object o = new Object();
        Assert.assertNotNull(o);
    }
}
