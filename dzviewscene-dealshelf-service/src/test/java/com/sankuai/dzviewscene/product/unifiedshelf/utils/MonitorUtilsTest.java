package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import org.junit.Assert;
import org.junit.Test;

public class MonitorUtilsTest {

    @Test
    public void monitorTest() {
        ActivityContextRequest activityContextRequest = new ActivityContextRequest();
        MonitorUtils.monitor(activityContextRequest, "test");

        Object o = new Object();
        Assert.assertNotNull(o);
    }
}
