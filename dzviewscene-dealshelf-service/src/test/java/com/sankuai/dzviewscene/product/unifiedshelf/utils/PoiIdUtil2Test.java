package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.assertEquals;

public class PoiIdUtil2Test {

    private UnifiedShelfFilterBuilder.Request request;

    @Before
    public void setUp() {
        request = Mockito.mock(UnifiedShelfFilterBuilder.Request.class);
    }

    /**
     * 测试getDpPoiIdL方法，当dpPoiIdL不为0时
     */
    @Test
    public void testGetDpPoiIdLWhenDpPoiIdLIsNotZero() {
        Mockito.when(request.getDpPoiIdL()).thenReturn(123L);
        Mockito.when(request.getDpPoiId()).thenReturn(0);

        Long result = PoiIdUtil.getDpPoiIdL(request);

        assertEquals(Long.valueOf(123), result);
    }

    /**
     * 测试getDpPoiIdL方法，当dpPoiIdL为0且dpPoiId不为0时
     */
    @Test
    public void testGetDpPoiIdLWhenDpPoiIdLIsZeroAndDpPoiIdIsNotZero() {
        Mockito.when(request.getDpPoiIdL()).thenReturn(0L);
        Mockito.when(request.getDpPoiId()).thenReturn(456);

        Long result = PoiIdUtil.getDpPoiIdL(request);

        assertEquals(Long.valueOf(456), result);
    }

    /**
     * 测试getDpPoiIdL方法，当dpPoiIdL和dpPoiId都为0时
     */
    @Test
    public void testGetDpPoiIdLWhenDpPoiIdLAndDpPoiIdAreZero() {
        Mockito.when(request.getDpPoiIdL()).thenReturn(0L);
        Mockito.when(request.getDpPoiId()).thenReturn(0);

        Long result = PoiIdUtil.getDpPoiIdL(request);

        assertEquals(Long.valueOf(0), result);
    }

    /**
     * 测试getDpPoiIdL方法，当dpPoiIdL不为0时
     */
    @Test
    public void testMTSHOP() {
        Mockito.when(request.getMtPoiIdL()).thenReturn(123L);

        Long result = PoiIdUtil.getMtPoiIdL(request);

        assertEquals(Long.valueOf(123), result);
    }

}
