package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import static org.junit.Assert.assertEquals;

import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.UnifiedShelfOceanBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class PoiIdUtilTest {

    private UnifiedShelfOceanBuilder.Request request;

    @Before
    public void setUp() {
        request = Mockito.mock(UnifiedShelfOceanBuilder.Request.class);
    }

    /**
     * 测试getDpPoiIdL方法，当dpPoiIdL不为0时
     */
    @Test
    public void testGetDpPoiIdLWhenDpPoiIdLIsNotZero() {
        Mockito.when(request.getDpPoiIdL()).thenReturn(100L);
        Mockito.when(request.getDpPoiId()).thenReturn(0);
        Long result = PoiIdUtil.getDpPoiIdL(request);
        assertEquals(Long.valueOf(100L), result);
    }

    /**
     * 测试getDpPoiIdL方法，当dpPoiIdL为0且dpPoiId不为0时
     */
    @Test
    public void testGetDpPoiIdLWhenDpPoiIdLIsZeroAndDpPoiIdIsNotZero() {
        Mockito.when(request.getDpPoiIdL()).thenReturn(0L);
        Mockito.when(request.getDpPoiId()).thenReturn(50);
        Long result = PoiIdUtil.getDpPoiIdL(request);
        assertEquals(Long.valueOf(50L), result);
    }

    /**
     * 测试getDpPoiIdL方法，当dpPoiIdL和dpPoiId都为0时
     */
    @Test
    public void testGetDpPoiIdLWhenDpPoiIdLAndDpPoiIdAreZero() {
        Mockito.when(request.getDpPoiIdL()).thenReturn(0L);
        Mockito.when(request.getDpPoiId()).thenReturn(0);
        Long result = PoiIdUtil.getDpPoiIdL(request);
        assertEquals(Long.valueOf(0L), result);
    }

    /**
     * 测试getMtPoiIdL方法，当mtPoiIdL不为0时
     */
    @Test
    public void testGetMtPoiIdLWhenMtPoiIdLIsNotZero() {
        Mockito.when(request.getMtPoiIdL()).thenReturn(12345L);
        Mockito.when(request.getMtPoiId()).thenReturn(0);
        Long result = PoiIdUtil.getMtPoiIdL(request);
        assertEquals(Long.valueOf(12345), result);
    }

    /**
     * 测试getMtPoiIdL方法，当mtPoiIdL为0且mtPoiId不为0时
     */
    @Test
    public void testGetMtPoiIdLWhenMtPoiIdLIsZeroAndMtPoiIdIsNotZero() {
        Mockito.when(request.getMtPoiIdL()).thenReturn(0L);
        Mockito.when(request.getMtPoiId()).thenReturn(6789);
        Long result = PoiIdUtil.getMtPoiIdL(request);
        assertEquals(Long.valueOf(6789), result);
    }

    /**
     * 测试getMtPoiIdL方法，当mtPoiIdL和mtPoiId都为0时
     */
    @Test
    public void testGetMtPoiIdLWhenMtPoiIdLAndMtPoiIdAreZero() {
        Mockito.when(request.getMtPoiIdL()).thenReturn(0L);
        Mockito.when(request.getMtPoiId()).thenReturn(0);
        Long result = PoiIdUtil.getMtPoiIdL(request);
        assertEquals(Long.valueOf(0), result);
    }

}
