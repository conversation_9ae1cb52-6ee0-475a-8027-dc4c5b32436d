package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.shelf.business.utils.DzItemVOBuildUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ProductTitleHighLightUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.*;

public class ProductTitleHighLightUtilsTest {

    @Mock
    private Cat catMock;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试标题为空时的情况
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_TitleNull() throws Throwable {
        // arrange
        String title = null;
        String keyWord = "测试";

        // act
        List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWord);

        // assert
        assertNull("当标题为空时，应返回null", result);
    }

    /**
     * 测试关键词为空时的情况
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_KeyWordNull() throws Throwable {
        // arrange
        String title = "测试标题";
        String keyWord = null;

        // act
        List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWord);

        // assert
        assertNull("当关键词为空时，应返回null", result);
    }

    /**
     * 测试标题和关键词均为空时的情况
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_BothNull() throws Throwable {
        // arrange
        String title = null;
        String keyWord = null;

        // act
        List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWord);

        // assert
        assertNull("当标题和关键词均为空时，应返回null", result);
    }

    /**
     * 测试内部方法抛出异常时的情况
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_Exception() throws Throwable {
        // arrange
        String title = "测试标题";
        String keyWord = "测试";

        try (MockedStatic<DzItemVOBuildUtils> mockedStatic = Mockito.mockStatic(DzItemVOBuildUtils.class)) {
            mockedStatic.when(() -> DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(title, keyWord)).thenThrow(new RuntimeException("测试异常"));

            // act
            List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWord);

            // assert
            assertNull("当内部方法抛出异常时，应返回null", result);
        }
    }
}
