package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.dianping.gmkt.activ.api.enums.ExposureActivityTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ShelfPromoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemActivityVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Lists;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class RainbowSecKillUtilsTest {


    /**
     * 测试当产品不是彩虹秒杀团单时，返回null
     */
    @Test
    public void testGetSecKillShelfItemActivityVOWithTimePreStr_ProductNotRainbowSecKill() {
        ProductM productM = new ProductM();
        // arrange
        String timePreStr = "仅剩";
        try (MockedStatic<RainbowSecKillUtils> mockedStatic = Mockito.mockStatic(RainbowSecKillUtils.class)) {
            mockedStatic.when(() -> RainbowSecKillUtils.isRainbowSecKillDeal(productM)).thenReturn(false);

            // act
            ShelfItemActivityVO result = RainbowSecKillUtils.getSecKillShelfItemActivityVOWithTimePreStr(productM, timePreStr);

            // assert
            assertNull(result);
        }
    }

    /**
     * 测试当产品是彩虹秒杀团单但没有活动时，返回null
     */
    @Test
    public void testGetSecKillShelfItemActivityVOWithTimePreStr_ProductRainbowSecKillNoActivity() {
        ProductM productM = new ProductM();
        // arrange
        String timePreStr = "仅剩";
        List<ProductActivityM> activities = new ArrayList<>();
        productM.setActivities(activities);
        try (MockedStatic<RainbowSecKillUtils> mockedStatic = Mockito.mockStatic(RainbowSecKillUtils.class)) {
            mockedStatic.when(() -> RainbowSecKillUtils.isRainbowSecKillDeal(productM)).thenReturn(true);

            // act
            ShelfItemActivityVO result = RainbowSecKillUtils.getSecKillShelfItemActivityVOWithTimePreStr(productM, timePreStr);

            // assert
            assertNull(result);
        }
    }

    /**
     * 测试当产品是彩虹秒杀团单且有活动时，返回正确的ShelfItemActivityVO
     */
    @Test
    public void testGetSecKillShelfItemActivityVOWithTimePreStr_ProductRainbowSecKillWithActivity() {
        // arrange

        List<ProductPromoPriceM> promoPrices = Lists.newArrayList();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoType(PromoTypeEnum.NORMAL_PROMO.getType());
        promoPrice1.setPromoTagType(PromoTagTypeEnum.Merchant_Member.getCode());
        promoPrice1.setPromoPrice(new BigDecimal(66));
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoTypeCode(PromoTypeEnum.NORMAL_PROMO.getType());
        promoPrice1.setPromoItemList(Lists.newArrayList(promoItemM));
        promoPrices.add(promoPrice1);

        String timePreStr = "仅剩";
        ProductActivityM activity = new ProductActivityM();
        activity.setRemainingTime(100);
        activity.setShelfActivityType(ExposureActivityTypeEnum.COST_EFFECTIVE_SECKILL.getType());
        List<ProductActivityM> activities = new ArrayList<>();
        activities.add(activity);
        ProductM productM = new ProductM();
        productM.setActivities(activities);
        productM.setPromoPrices(promoPrices);

        // act
        ShelfItemActivityVO result = RainbowSecKillUtils.getSecKillShelfItemActivityVOWithTimePreStr(productM, timePreStr);

        // assert
        assertNotNull(result);
        assertEquals("仅剩", result.getPreText());
        assertEquals(100000, result.getActivityEndTime());
    }

    /**
     * 测试当产品是彩虹秒杀团单且有活动时，返回正确的ShelfItemActivityVO
     */
    @Test
    public void testGetSecKillShelfItemActivityVOWithTimePreStr_ProductRainbowSecKillWithActivity2() {
        // arrange

        List<ProductPromoPriceM> promoPrices = Lists.newArrayList();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoType(PromoTypeEnum.NORMAL_PROMO.getType());
        promoPrice1.setPromoTagType(PromoTagTypeEnum.Merchant_Member.getCode());
        promoPrice1.setPromoPrice(new BigDecimal(66));
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoTypeCode(PromoTypeEnum.NORMAL_PROMO.getType());
        promoPrice1.setPromoItemList(Lists.newArrayList(promoItemM));
        promoPrices.add(promoPrice1);

        String timePreStr = "仅剩";
        ProductActivityM activity = new ProductActivityM();
        activity.setRemainingTime(100);
        activity.setShelfActivityType(ExposureActivityTypeEnum.COST_EFFECTIVE_SECKILL.getType());
        List<ProductActivityM> activities = new ArrayList<>();
        activities.add(activity);
        ProductM productM = new ProductM();
        productM.setActivities(activities);
        productM.setPromoPrices(promoPrices);

        // act
        ShelfItemActivityVO result = ShelfPromoUtils.buildDirectPromoShelfItemActivityVO(productM, true);

        // assert
        assertNotNull(result);
    }
}
