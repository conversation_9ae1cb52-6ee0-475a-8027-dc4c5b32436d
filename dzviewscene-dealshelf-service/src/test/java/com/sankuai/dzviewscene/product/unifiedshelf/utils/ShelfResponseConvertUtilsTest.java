package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.FileUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PicAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterNodeVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

public class ShelfResponseConvertUtilsTest {

    @Test
    public void test_cleanDegradeData() {

        String string = FileUtil.file2str("unified_shelf.json");

        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

        UnifiedShelfResponse copResponse = ShelfResponseConvertUtils.cleanDegradeData(response);

        Assert.assertNotNull(copResponse);
        Assert.assertEquals(response.getFilterIdAndProductAreas().size(), copResponse.getFilterIdAndProductAreas().size());
        Assert.assertEquals(response.getFilterIdAndProductAreas().get(0).getProductAreas().size(), copResponse.getFilterIdAndProductAreas().get(0).getProductAreas().size());
        Assert.assertEquals(response.getFilterIdAndProductAreas().get(0).getProductAreas().get(0).getItems().size(), copResponse.getFilterIdAndProductAreas().get(0).getProductAreas().get(0).getItems().size());
    }

    @Test
    public void test_copyFilterProductAreaVO() {

        String string = FileUtil.file2str("unified_shelf.json");

        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

        ShelfFilterProductAreaVO productAreaVO = response.getFilterIdAndProductAreas().get(0);

        ShelfFilterProductAreaVO copy = ShelfResponseConvertUtils.copyFilterProductAreaVO(productAreaVO);

        Assert.assertNotNull(copy);
        Assert.assertEquals(response.getFilterIdAndProductAreas().get(0).getProductAreas().get(0).getItems().size(), copy.getProductAreas().get(0).getItems().size());
    }

    @Test
    public void test_copy_empty() {

        ShelfResponseConvertUtils.cleanDegradeData(new UnifiedShelfResponse());
    }

    @Test
    public void test_copy_empty2() {

        ShelfResponseConvertUtils.copyFilterProductAreaVO(new ShelfFilterProductAreaVO());
    }

    @Test
    public void test_copy_empty3() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        ShelfResponseConvertUtils shelfResponseConvertUtils = new ShelfResponseConvertUtils();
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("copyFilterProductAreaVOs", List.class);
            method.setAccessible(true);
            method.invoke(shelfResponseConvertUtils, Lists.newArrayList());
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("copyShelfProductAreaVOs", List.class);
            method.setAccessible(true);
            method.invoke(shelfResponseConvertUtils, Lists.newArrayList());
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("copyShelfProductItemVOs", List.class);
            method.setAccessible(true);
            method.invoke(shelfResponseConvertUtils, Lists.newArrayList());
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("getPic", PicAreaVO.class);
            method.setAccessible(true);
            PicAreaVO picAreaVO = null;
            method.invoke(shelfResponseConvertUtils, picAreaVO);
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("getSelectedFilterId", ShelfFilterNodeVO.class);
            method.setAccessible(true);
            ShelfFilterNodeVO picAreaVO = null;
            method.invoke(shelfResponseConvertUtils, picAreaVO);
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("getSalePrice", String.class, String.class);
            method.setAccessible(true);
            String salePrice = null;
            String basePrice = null;
            method.invoke(shelfResponseConvertUtils, basePrice, salePrice);
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("getSalePrice", String.class, String.class);
            method.setAccessible(true);
            String salePrice = "test";
            String basePrice = null;
            method.invoke(shelfResponseConvertUtils, basePrice, salePrice);
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("getSalePrice", String.class, String.class);
            method.setAccessible(true);
            String basePrice = "test";
            String salePrice = null;
            method.invoke(shelfResponseConvertUtils, basePrice, salePrice);
        }
        {
            Method method = shelfResponseConvertUtils.getClass().getDeclaredMethod("getSalePrice", String.class, String.class);
            method.setAccessible(true);
            String basePrice = "test";
            String salePrice = "test";
            method.invoke(shelfResponseConvertUtils, basePrice, salePrice);
        }

        Object object = new Object();
        Assert.assertNotNull(object);
    }
}
