package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Lists;

/**
 * 测试ShopCategoryConfigUtils类中convertTrace方法的不同场景
 */
public class ShopCategoryConfigUtilsTest {

    @Mock
    private Cat catMock;

    /**
     * 测试convertTrace方法，当输入列表为空时
     */
    @Test
    public void testConvertTraceEmptyList() {
        // arrange
        List<Integer> shopCategoryId = Collections.emptyList();
        // act
        String result = ShopCategoryConfigUtils.convertTrace(shopCategoryId);
        // assert
        Assert.assertNull("当输入列表为空时，应返回null", result);
    }

    /**
     * 测试convertTrace方法，当输入列表包含非空元素时
     */
    @Test
    public void testConvertTraceWithNonNullElements() {
        // arrange
        List<Integer> shopCategoryId = Arrays.asList(1, 2, 3);
        // act
        String result = ShopCategoryConfigUtils.convertTrace(shopCategoryId);
        // assert
        Assert.assertEquals("当输入列表包含非空元素时，应正确连接元素", "1-2-3", result);
    }

    /**
     * 测试convertTrace方法，当输入列表包含null元素时
     */
    @Test
    public void testConvertTraceWithNullElements() {
        // arrange
        List<Integer> shopCategoryId = Arrays.asList(1, null, 3);
        // act
        String result = ShopCategoryConfigUtils.convertTrace(shopCategoryId);
        // assert
        Assert.assertEquals("当输入列表包含null元素时，应忽略null并正确连接其它元素", "1-3", result);
    }

    /**
     * 测试convertTrace方法，当输入列表包含全部为null的元素时
     */
    @Test
    public void testConvertTraceAllNullElements() {
        // arrange
        List<Integer> shopCategoryId = Lists.newArrayList();
        // act
        String result = ShopCategoryConfigUtils.convertTrace(shopCategoryId);
        // assert
        Assert.assertNull("当输入列表包含全部为null的元素时，应返回null", result);
    }

    /**
     * 测试convertTrace方法，当输入列表只包含一个非空元素时
     */
    @Test
    public void testConvertTraceSingleNonNullElement() {
        // arrange
        List<Integer> shopCategoryId = Collections.singletonList(1);
        // act
        String result = ShopCategoryConfigUtils.convertTrace(shopCategoryId);
        // assert
        Assert.assertEquals("当输入列表只包含一个非空元素时，应正确返回该元素的字符串表示", "1", result);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试空列表输入
     */
    @Test
    public void testConvertMultiListByIndexEmptyList() {
        List<Integer> shopCategoryIdList = Collections.emptyList();
        List<List<Integer>> result = ShopCategoryConfigUtils.convertMultiListByIndex(shopCategoryIdList);
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试单个元素列表
     */
    @Test
    public void testConvertMultiListByIndexSingleElement() {
        List<Integer> shopCategoryIdList = Collections.singletonList(1);
        List<List<Integer>> expected = Collections.singletonList(Collections.singletonList(1));
        List<List<Integer>> result = ShopCategoryConfigUtils.convertMultiListByIndex(shopCategoryIdList);
        assertEquals("结果应为包含单个元素的列表", expected, result);
    }

    /**
     * 测试多个元素列表
     */
    @Test
    public void testConvertMultiListByIndexMultipleElements() {
        List<Integer> shopCategoryIdList = Arrays.asList(1, 2, 3);
        List<List<Integer>> expected = Arrays.asList(Collections.singletonList(1), Arrays.asList(1, 2), Arrays.asList(1, 2, 3));
        List<List<Integer>> result = ShopCategoryConfigUtils.convertMultiListByIndex(shopCategoryIdList);
        assertEquals("结果应为逐步累加的列表", expected, result);
    }

    /**
     * 测试convertMultiTraceByShopCategory方法，输入为空列表
     */
    @Test
    public void testConvertMultiTraceByShopCategoryEmptyList() {
        // arrange
        List<Integer> shopCategoryId = Collections.emptyList();
        // act
        List<String> result = ShopCategoryConfigUtils.convertMultiTraceByShopCategory(shopCategoryId);
        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试convertMultiTraceByShopCategory方法，输入为包含单个元素的列表
     */
    @Test
    public void testConvertMultiTraceByShopCategorySingleElement() {
        // arrange
        List<Integer> shopCategoryId = Arrays.asList(1);
        // act
        List<String> result = ShopCategoryConfigUtils.convertMultiTraceByShopCategory(shopCategoryId);
        // assert
        assertEquals("结果列表应包含一个元素", 1, result.size());
        assertEquals("结果应为\"1\"", "1", result.get(0));
    }

    /**
     * 测试convertMultiTraceByShopCategory方法，输入为包含多个元素的列表
     */
    @Test
    public void testConvertMultiTraceByShopCategoryMultipleElements() {
        // arrange
        List<Integer> shopCategoryId = Arrays.asList(1, 2, 3);
        // act
        List<String> result = ShopCategoryConfigUtils.convertMultiTraceByShopCategory(shopCategoryId);
        // assert
        assertEquals("结果列表应包含三个元素", 3, result.size());
        assertEquals("第一个元素应为\"1\"", "1", result.get(0));
        assertEquals("第二个元素应为\"1-2\"", "1-2", result.get(1));
        assertEquals("第三个元素应为\"1-2-3\"", "1-2-3", result.get(2));
    }

    /**
     * 测试 nullOrInvalid 方法，当 shopCategoryConfig 为 null 时
     */
    @Test
    public void testNullOrInvalidWhenConfigIsNull() {
        // arrange
        ShopCategoryConfig shopCategoryConfig = null;
        // act
        boolean result = ShopCategoryConfigUtils.nullOrInvalid(shopCategoryConfig);
        // assert
        assertTrue("当 shopCategoryConfig 为 null 时，应返回 true", result);
    }

    /**
     * 测试 nullOrInvalid 方法，当 configList 和 defaultConfig 都为空时
     */
    @Test
    public void testNullOrInvalidWhenConfigListAndDefaultConfigAreEmpty() {
        // arrange
        ShopCategoryConfig shopCategoryConfig = new ShopCategoryConfig();
        shopCategoryConfig.setConfigList(Collections.emptyList());
        shopCategoryConfig.setDefaultConfig("");
        // act
        boolean result = ShopCategoryConfigUtils.nullOrInvalid(shopCategoryConfig);
        // assert
        assertTrue("当 configList 和 defaultConfig 都为空时，应返回 true", result);
    }

    /**
     * 测试 nullOrInvalid 方法，当 configList 不为空但 defaultConfig 为空时
     */
    @Test
    public void testNullOrInvalidWhenConfigListIsNotEmptyAndDefaultConfigIsEmpty() {
        // arrange
        ShopCategoryConfig shopCategoryConfig = new ShopCategoryConfig();
        shopCategoryConfig.setConfigList(Collections.singletonList(new ShopCategoryConfig.ShopCategoryCellConfig()));
        shopCategoryConfig.setDefaultConfig("");
        // act
        boolean result = ShopCategoryConfigUtils.nullOrInvalid(shopCategoryConfig);
        // assert
        assertFalse("当 configList 不为空但 defaultConfig 为空时，应返回 false", result);
    }

    /**
     * 测试 nullOrInvalid 方法，当 configList 为空但 defaultConfig 不为空时
     */
    @Test
    public void testNullOrInvalidWhenConfigListIsEmptyAndDefaultConfigIsNotEmpty() {
        // arrange
        ShopCategoryConfig shopCategoryConfig = new ShopCategoryConfig();
        shopCategoryConfig.setConfigList(Collections.emptyList());
        shopCategoryConfig.setDefaultConfig("default");
        // act
        boolean result = ShopCategoryConfigUtils.nullOrInvalid(shopCategoryConfig);
        // assert
        assertFalse("当 configList 为空但 defaultConfig 不为空时，应返回 false", result);
    }

    /**
     * 测试 nullOrInvalid 方法，当 configList 和 defaultConfig 都不为空时
     */
    @Test
    public void testNullOrInvalidWhenConfigListAndDefaultConfigAreNotEmpty() {
        // arrange
        ShopCategoryConfig shopCategoryConfig = new ShopCategoryConfig();
        shopCategoryConfig.setConfigList(Collections.singletonList(new ShopCategoryConfig.ShopCategoryCellConfig()));
        shopCategoryConfig.setDefaultConfig("default");
        // act
        boolean result = ShopCategoryConfigUtils.nullOrInvalid(shopCategoryConfig);
        // assert
        assertFalse("当 configList 和 defaultConfig 都不为空时，应返回 false", result);
    }

    /**
     * 测试getHitConfig方法，当config为null时应返回null
     */
    @Test
    public void testGetHitConfigConfigIsNull() {
        String result = ShopCategoryConfigUtils.getHitConfig(Collections.singletonList(1), null);
        assertEquals(null, result);
    }

    /**
     * 测试getHitConfig方法，当config有效但configList为空且defaultConfig为空时应返回null
     */
    @Test
    public void testGetHitConfigConfigListAndDefaultConfigAreEmpty() {
        ShopCategoryConfig config = new ShopCategoryConfig();
        String result = ShopCategoryConfigUtils.getHitConfig(Collections.singletonList(1), config);
        assertEquals(null, result);
    }

    /**
     * 测试getHitConfig方法，当shopCategoryTree为空且config有默认配置时应返回默认配置
     */
    @Test
    public void testGetHitConfigShopCategoryTreeIsEmpty() {
        ShopCategoryConfig config = new ShopCategoryConfig();
        config.setDefaultConfig("default");
        String result = ShopCategoryConfigUtils.getHitConfig(Collections.emptyList(), config);
        assertEquals("default", result);
    }

    /**
     * 测试getHitConfig方法，当shopCategoryTree和configList不为空，但没有匹配项时应返回默认配置
     */
    @Test
    public void testGetHitConfigNoMatchFound() {
        ShopCategoryConfig config = new ShopCategoryConfig();
        config.setDefaultConfig("default");
        ShopCategoryConfig.ShopCategoryCellConfig cellConfig = new ShopCategoryConfig.ShopCategoryCellConfig();
        cellConfig.setBackCategory(Collections.singletonList(2));
        cellConfig.setConfig("config");
        config.setConfigList(Collections.singletonList(cellConfig));
        String result = ShopCategoryConfigUtils.getHitConfig(Collections.singletonList(1), config);
        assertEquals("default", result);
    }

    /**
     * 测试getHitConfig方法，当shopCategoryTree和configList匹配时应返回匹配的配置
     */
    @Test
    public void testGetHitConfigMatchFound() {
        ShopCategoryConfig config = new ShopCategoryConfig();
        config.setDefaultConfig("default");
        ShopCategoryConfig.ShopCategoryCellConfig cellConfig = new ShopCategoryConfig.ShopCategoryCellConfig();
        cellConfig.setBackCategory(Collections.singletonList(1));
        cellConfig.setConfig("matchedConfig");
        config.setConfigList(Collections.singletonList(cellConfig));
        String result = ShopCategoryConfigUtils.getHitConfig(Collections.singletonList(1), config);
        assertEquals("matchedConfig", result);
    }

    /**
     * 测试getHitConfig方法，当有多个匹配时应返回最长路径对应的配置
     */
    @Test
    public void testGetHitConfigMultipleMatches() {
        ShopCategoryConfig config = new ShopCategoryConfig();
        config.setDefaultConfig("default");
        ShopCategoryConfig.ShopCategoryCellConfig cellConfig1 = new ShopCategoryConfig.ShopCategoryCellConfig();
        cellConfig1.setBackCategory(Arrays.asList(1, 2));
        cellConfig1.setConfig("longestMatchConfig");
        ShopCategoryConfig.ShopCategoryCellConfig cellConfig2 = new ShopCategoryConfig.ShopCategoryCellConfig();
        cellConfig2.setBackCategory(Collections.singletonList(1));
        cellConfig2.setConfig("shortMatchConfig");
        config.setConfigList(Arrays.asList(cellConfig1, cellConfig2));
        String result = ShopCategoryConfigUtils.getHitConfig(Arrays.asList(1, 2), config);
        assertEquals("longestMatchConfig", result);
    }

    /**
     * 测试 parseVal 方法，输入为空字符串和null类对象
     */
    @Test
    public void testParseValEmptyStringAndNullClass() {
        // arrange
        String hitConfig = "";
        Class<?> tClass = null;

        // act
        Object result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 parseVal 方法，输入为非空字符串和String类对象
     */
    @Test
    public void testParseValNonEmptyStringAndStringClass() {
        // arrange
        String hitConfig = "testString";
        Class<String> tClass = String.class;

        // act
        String result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertEquals("testString", result);
    }

    /**
     * 测试 parseVal 方法，输入为整数字符串和Integer类对象
     */
    @Test
    public void testParseValIntegerStringAndIntegerClass() {
        // arrange
        String hitConfig = "123";
        Class<Integer> tClass = Integer.class;

        // act
        Integer result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertEquals(Integer.valueOf(123), result);
    }

    /**
     * 测试 parseVal 方法，输入为长整型字符串和Long类对象
     */
    @Test
    public void testParseValLongStringAndLongClass() {
        // arrange
        String hitConfig = "1234567890";
        Class<Long> tClass = Long.class;

        // act
        Long result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertEquals(Long.valueOf(1234567890), result);
    }

    /**
     * 测试 parseVal 方法，输入为浮点数字符串和Double类对象
     */
    @Test
    public void testParseValDoubleStringAndDoubleClass() {
        // arrange
        String hitConfig = "123.45";
        Class<Double> tClass = Double.class;

        // act
        Double result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertEquals(Double.valueOf(123.45), result, 0.0);
    }

    /**
     * 测试 parseVal 方法，输入为浮点数字符串和BigDecimal类对象
     */
    @Test
    public void testParseValDoubleStringAndBigDecimalClass() {
        // arrange
        String hitConfig = "123.45";
        Class<BigDecimal> tClass = BigDecimal.class;

        // act
        BigDecimal result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertEquals(BigDecimal.valueOf(123.45), result);
    }

    /**
     * 测试 parseVal 方法，输入为布尔值字符串和Boolean类对象
     */
    @Test
    public void testParseValBooleanStringAndBooleanClass() {
        // arrange
        String hitConfig = "true";
        Class<Boolean> tClass = Boolean.class;

        // act
        Boolean result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试 parseVal 方法，输入为JSON字符串和自定义类对象
     */
    @Test
    public void testParseValJsonStringAndCustomClass() {
        // arrange
        String hitConfig = "{\"name\":\"testName\",\"value\":123}";
        Class<CustomClass> tClass = CustomClass.class;

        // act
        CustomClass result = ShopCategoryConfigUtils.parseVal(hitConfig, tClass);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals("testName", result.getName());
        Assert.assertEquals(123, result.getValue());
    }

    // 自定义类，用于测试 JSON 解析
    public static class CustomClass {
        private String name;
        private int value;

        // getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }
    }
}
