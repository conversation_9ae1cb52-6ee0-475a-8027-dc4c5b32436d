package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.meituan.mtrace.Tracer;
import com.sankuai.FileUtil;
import com.sankuai.athena.stability.faulttolerance.exception.FaultToleranceException;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.athena.viewscene.framework.pmf.monitor.error.ExecuteError;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.shelf.faulttolerance.utils.TriggerFaultTolerantUtils;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class TriggerFaultTolerantUtils2Test {

    @Mock
    private ExecuteError executeError;
    @Mock
    private UnifiedShelfResponse unifiedShelfResponse;

    /**
     * 测试结果有效时不触发容错
     */
    @Test
    public void testTriggerFaultTolerantByUnExpectResultWithValidResult2() {

        try (MockedStatic<Tracer> mockedStatic = Mockito.mockStatic(Tracer.class)) {

            mockedStatic.when(() -> Tracer.getAllContext()).thenReturn(null);
            // Tracer.putContext("ft.exception.prefix_compositeAtomService_queryDealProductTheme", null);
            String string = FileUtil.file2str("unified_shelf.json");

            UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

            // act
            TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(executeError, response);
        }
    }

    @Test(expected = FaultToleranceException.class)
    public void testTriggerFaultTolerantByHasDealThemeException2() {
        Tracer.putContext("ft.exception.prefix_compositeAtomService_queryDealProductTheme", "true");
        // act
        TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(executeError, unifiedShelfResponse);
    }

    /**
     * 测试发生异常时触发容错
     */
    @Test(expected = FaultToleranceException.class)
    public void testTriggerFaultTolerantByUnExpectResultWithException2() {
        // arrange

        try (MockedStatic<UnifiedShelfModelUtils> mockedStatic = Mockito.mockStatic(UnifiedShelfModelUtils.class);
             MockedStatic<ExceptionTracer> tracerMockedStatic = Mockito.mockStatic(ExceptionTracer.class)) {
            mockedStatic.when(() -> UnifiedShelfModelUtils.hasProducts(unifiedShelfResponse)).thenReturn(false);
            tracerMockedStatic.when(() -> ExceptionTracer.hasException()).thenReturn(true);

            // act
            TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(executeError, unifiedShelfResponse);

            // assert is handled by the expected exception
        }
    }

    /**
     * 测试发生异常时触发容错
     */
    @Test(expected = FaultToleranceException.class)
    public void testTriggerFaultTolerantByUnExpectResultWithException3() {
        // arrange

        try (MockedStatic<UnifiedShelfModelUtils> mockedStatic = Mockito.mockStatic(UnifiedShelfModelUtils.class);
             MockedStatic<ExceptionTracer> exceptionTracerMockedStatic = Mockito.mockStatic(ExceptionTracer.class);
             MockedStatic<Tracer> tracerMockedStatic = Mockito.mockStatic(Tracer.class)) {

            tracerMockedStatic.when(() -> Tracer.getAllContext()).thenReturn(null);
            mockedStatic.when(() -> UnifiedShelfModelUtils.hasProducts(unifiedShelfResponse)).thenReturn(false);
            exceptionTracerMockedStatic.when(() -> ExceptionTracer.hasException()).thenReturn(true);

            // act
            TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(executeError, unifiedShelfResponse);

            // assert is handled by the expected exception
        }
    }
}
