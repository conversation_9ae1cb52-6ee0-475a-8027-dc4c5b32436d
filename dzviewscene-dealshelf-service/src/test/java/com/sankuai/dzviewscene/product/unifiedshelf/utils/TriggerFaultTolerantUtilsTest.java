package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.meituan.mtrace.Tracer;
import com.sankuai.athena.stability.faulttolerance.exception.FaultToleranceException;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.athena.viewscene.framework.pmf.monitor.error.ExecuteError;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.shelf.faulttolerance.utils.TriggerFaultTolerantUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * 测试TriggerFaultTolerantUtils的triggerFaultTolerantByUnExpectResult方法
 */
public class TriggerFaultTolerantUtilsTest {

    @Mock
    private ExecuteError executeError;
    @Mock
    private ShelfFilterProductAreaVO filterBtnIdAndProAreasVO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试结果有效时不触发容错
     */
    @Test
    public void testTriggerFaultTolerantByUnExpectResultWithValidResult() {
        // Tracer.putContext("ft.exception.prefix_compositeAtomService_queryDealProductTheme", null);
        try (MockedStatic<UnifiedShelfModelUtils> mockedStatic = Mockito.mockStatic(UnifiedShelfModelUtils.class);
             MockedStatic<Tracer> tracerMockedStatic = Mockito.mockStatic(Tracer.class)) {
            tracerMockedStatic.when(() -> Tracer.getAllContext()).thenReturn(null);
            mockedStatic.when(() -> UnifiedShelfModelUtils.productAreasIsNotEmpty(filterBtnIdAndProAreasVO)).thenReturn(true);

            // act
            TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(executeError, filterBtnIdAndProAreasVO);

            // assert no exception is thrown
        }
    }

    @Test(expected = FaultToleranceException.class)
    public void testTriggerFaultTolerantByHasDealThemeException() {
        Tracer.putContext("ft.exception.prefix_compositeAtomService_queryDealProductTheme", "true");
        // act
        TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(executeError, filterBtnIdAndProAreasVO);
    }

    /**
     * 测试发生异常时触发容错
     */
    @Test(expected = FaultToleranceException.class)
    public void testTriggerFaultTolerantByUnExpectResultWithException() {
        // arrange

        try (MockedStatic<UnifiedShelfModelUtils> mockedStatic = Mockito.mockStatic(UnifiedShelfModelUtils.class);
             MockedStatic<ExceptionTracer> tracerMockedStatic = Mockito.mockStatic(ExceptionTracer.class)) {
            mockedStatic.when(() -> UnifiedShelfModelUtils.productAreasIsNotEmpty(filterBtnIdAndProAreasVO)).thenReturn(false);
            tracerMockedStatic.when(() -> ExceptionTracer.hasException()).thenReturn(true);

            // act
            TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(executeError, filterBtnIdAndProAreasVO);

            // assert is handled by the expected exception
        }
    }

}
