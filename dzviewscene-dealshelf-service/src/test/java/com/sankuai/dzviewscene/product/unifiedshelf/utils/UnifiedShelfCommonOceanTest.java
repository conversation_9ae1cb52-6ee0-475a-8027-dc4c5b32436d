package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.sankuai.FileUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfOceanVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils.UnifiedShelfCommonOcean;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import org.junit.Assert;
import org.junit.Test;

import java.util.concurrent.CompletableFuture;

public class UnifiedShelfCommonOceanTest extends UnifiedShelfCommonTest {

    @Test
    public void test_paddingCommonOcean() {
        UnifiedShelfResponse response = JSON.parseObject(FileUtil.file2str("unified_shelf.json"), UnifiedShelfResponse.class);
        ShelfOceanVO shelfOceanVO = JSON.parseObject(FileUtil.file2str("ocean.json"), ShelfOceanVO.class);
        ActivityContext context = new ActivityContext();
        context.getParameters().put(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());

        ShelfGroupM shelfGroupM = initShelfGroupM();

        context.setMainData(CompletableFuture.completedFuture(shelfGroupM));

        ShelfOceanVO shelfOceanVO1 = UnifiedShelfCommonOcean.paddingCommonOcean(shelfOceanVO, response, context);
        Assert.assertNotNull(shelfOceanVO1);
    }

}
