package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.FileUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;

import java.util.HashMap;
import java.util.List;

public class UnifiedShelfCommonTest {

    public static ShelfGroupM initShelfGroupM() {

        ProductGroupM productGroupM = JSON.parseObject(FileUtil.file2str("product_group.json"), ProductGroupM.class);
        FilterM filterM = JSON.parseObject(FileUtil.file2str("filter.json"), FilterM.class);
        List<DouHuM> douHuMS = JSON.parseArray(FileUtil.file2str("douhu.json"), DouHuM.class);

        ShelfGroupM shelfGroupM = new ShelfGroupM();

        shelfGroupM.setProductGroupMs(new HashMap<>());
        shelfGroupM.setFilterMs(new HashMap<>());

        shelfGroupM.getDouHus().addAll(douHuMS);
        shelfGroupM.getProductGroupMs().put("团购", productGroupM);
        shelfGroupM.getFilterMs().put("团购", filterM);
        // System.out.println(JSON.toJSONString(shelfGroupM));
        return shelfGroupM;
    }

    public static ShelfGroupM initShelfGroupMMulti() {

        ProductGroupM productGroupM = JSON.parseObject(FileUtil.file2str("product_group.json"), ProductGroupM.class);
        FilterM filterM = JSON.parseObject(FileUtil.file2str("filter.json"), FilterM.class);
        List<DouHuM> douHuMS = JSON.parseArray(FileUtil.file2str("douhu.json"), DouHuM.class);

        ShelfGroupM shelfGroupM = new ShelfGroupM();

        shelfGroupM.setProductGroupMs(new HashMap<>());
        shelfGroupM.setFilterMs(new HashMap<>());

        shelfGroupM.getDouHus().addAll(douHuMS);
        shelfGroupM.getProductGroupMs().put("团购", productGroupM);
        shelfGroupM.getProductGroupMs().put("超团", productGroupM);
        shelfGroupM.getFilterMs().put("团购", filterM);
        shelfGroupM.getFilterMs().put("超团", filterM);
        // System.out.println(JSON.toJSONString(shelfGroupM));
        return shelfGroupM;
    }

    public static UnifiedProductAreaBuilder.Request initReq() {

        String shop = "{\"shopId\":132234798,\"longShopId\":132234798,\"shopName\":\"镁连社·美联社\",\"shopType\":50,\"useType\":1,\"category\":157,\"lat\":31.274683,\"lng\":121.519067,\"cityId\":1,\"shopUuid\":\"H1fQdj8AYzUea3Of\"}\n";
        UnifiedProductAreaBuilder.Request deserialize = new UnifiedProductAreaBuilder.Request();
        deserialize.setCtxShop(JSON.parseObject(shop, ShopM.class));
        deserialize.setGroupNames(Lists.newArrayList("团购"));
        deserialize.setSelectedFilterId(200128875);
        deserialize.setDpPoiIdL(132234798);
        deserialize.setMtPoiIdL(195223233);
        deserialize.setPlatform(2);
        deserialize.setUserAgent(200);
        deserialize.setDpCityId(1);
        deserialize.setMtCityId(10);
        // System.out.println(JSON.toJSONString(deserialize));
        return deserialize;
    }

    public static UnifiedProductAreaBuilder.Request initReqMulti() {

        String shop = "{\"shopId\":132234798,\"longShopId\":132234798,\"shopName\":\"镁连社·美联社\",\"shopType\":50,\"useType\":1,\"category\":157,\"lat\":31.274683,\"lng\":121.519067,\"cityId\":1,\"shopUuid\":\"H1fQdj8AYzUea3Of\"}\n";
        UnifiedProductAreaBuilder.Request deserialize = new UnifiedProductAreaBuilder.Request();
        deserialize.setCtxShop(JSON.parseObject(shop, ShopM.class));
        deserialize.setGroupNames(Lists.newArrayList("团购", "超团"));
        deserialize.setSelectedFilterId(200128875);
        deserialize.setDpPoiIdL(132234798);
        deserialize.setMtPoiIdL(195223233);
        deserialize.setPlatform(2);
        deserialize.setUserAgent(200);
        deserialize.setDpCityId(1);
        deserialize.setMtCityId(10);
        // System.out.println(JSON.toJSONString(deserialize));
        return deserialize;
    }
}
