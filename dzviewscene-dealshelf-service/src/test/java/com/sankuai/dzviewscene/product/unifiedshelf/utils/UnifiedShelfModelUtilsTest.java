package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.FileUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import org.junit.Assert;
import org.junit.Test;

public class UnifiedShelfModelUtilsTest {


    @Test
    public void test_hasFilters() {

        String string = FileUtil.file2str("unified_shelf.json");

        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

        boolean copResponse = UnifiedShelfModelUtils.hasFilters(response);

        Assert.assertTrue(copResponse);
    }


    @Test
    public void test_hasFilters2() {

        UnifiedShelfResponse response = new UnifiedShelfResponse();
        ShelfFilterVO shelfFilterVO = new ShelfFilterVO();
        shelfFilterVO.setFilterRoot(new ShelfFilterNodeVO());
        response.setFilter(shelfFilterVO);

        boolean copResponse = UnifiedShelfModelUtils.hasFilters(response);

        Assert.assertFalse(copResponse);
    }


    @Test
    public void testhasProducts() {

        String string = FileUtil.file2str("unified_shelf.json");

        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

        boolean copResponse = UnifiedShelfModelUtils.hasProducts(response);

        Assert.assertTrue(copResponse);
    }

    @Test
    public void test_hasNoProducts() {

        String string = FileUtil.file2str("unified_shelf.json");

        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

        boolean copResponse = UnifiedShelfModelUtils.hasNoProducts(response);

        Assert.assertFalse(copResponse);
    }

    @Test
    public void test_hasNoProducts2() {

        UnifiedShelfResponse response = new UnifiedShelfResponse();

        boolean copResponse = UnifiedShelfModelUtils.hasNoProducts(response);

        Assert.assertTrue(copResponse);
    }

    @Test
    public void test_hasNoProducts3() {

        UnifiedShelfResponse response = new UnifiedShelfResponse();

        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        response.setFilterIdAndProductAreas(Lists.newArrayList(shelfFilterProductAreaVO));


        boolean copResponse = UnifiedShelfModelUtils.hasNoProducts(response);

        Assert.assertTrue(copResponse);
    }

    @Test
    public void test_hasNoProducts4() {

        UnifiedShelfResponse response = new UnifiedShelfResponse();

        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        ShelfProductAreaVO shelfProductAreaVO = new ShelfProductAreaVO();
        shelfFilterProductAreaVO.setProductAreas(Lists.newArrayList(shelfProductAreaVO));
        response.setFilterIdAndProductAreas(Lists.newArrayList(shelfFilterProductAreaVO));

        boolean copResponse = UnifiedShelfModelUtils.hasNoProducts(response);

        Assert.assertTrue(copResponse);
    }

    @Test
    public void test_hasNoProducts5() {

        UnifiedShelfResponse response = new UnifiedShelfResponse();

        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        ShelfProductAreaVO shelfProductAreaVO = new ShelfProductAreaVO();
        shelfFilterProductAreaVO.setProductAreas(Lists.newArrayList(shelfProductAreaVO));
        response.setFilterIdAndProductAreas(Lists.newArrayList(shelfFilterProductAreaVO));

        boolean copResponse = UnifiedShelfModelUtils.hasNoProducts(response);

        Assert.assertTrue(copResponse);
    }


    @Test
    public void test_productAreasIsNotEmpty() {

        String string = FileUtil.file2str("unified_shelf.json");

        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

        ShelfFilterProductAreaVO shelfFilterProductAreaVO = response.getFilterIdAndProductAreas().get(0);
        boolean copResponse = UnifiedShelfModelUtils.productAreasIsNotEmpty(shelfFilterProductAreaVO);

        Assert.assertTrue(copResponse);
    }


    @Test
    public void test_productAreaIsNotEmpty() {

        String string = FileUtil.file2str("unified_shelf.json");

        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);

        ShelfProductAreaVO shelfProductAreaVO = response.getFilterIdAndProductAreas().get(0).getProductAreas().get(0);
        boolean copResponse = UnifiedShelfModelUtils.productAreaIsNotEmpty(shelfProductAreaVO);

        Assert.assertTrue(copResponse);
    }


}
