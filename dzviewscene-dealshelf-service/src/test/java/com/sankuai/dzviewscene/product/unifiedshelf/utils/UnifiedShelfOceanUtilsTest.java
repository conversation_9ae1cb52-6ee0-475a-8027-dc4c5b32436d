package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfOceanVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.bean.UnifiedShelfOceanCfgBean;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.bean.UnifiedShelfOceanConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils.UnifiedOceanConfigUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.SceneCfgBean;
import org.junit.Assert;
import org.junit.Test;

public class UnifiedShelfOceanUtilsTest {

    @Test
    public void test_UnifiedShelfOceanCfgBean() {

        UnifiedShelfOceanCfgBean shelfOceanCfgBean = new UnifiedShelfOceanCfgBean();
        shelfOceanCfgBean.setDpOcean(new ShelfOceanVO());
        shelfOceanCfgBean.setDpOcean(new ShelfOceanVO());
        System.out.println(JSON.toJSONString(shelfOceanCfgBean));

        Object o = new Object();
        Assert.assertNotNull(o);
    }

    @Test
    public void test_UnifiedShelfOceanConfig2() {
        UnifiedShelfOceanConfig.ShelfOceanConfigBean shelfOceanCfgBean = new UnifiedShelfOceanConfig.ShelfOceanConfigBean();
        shelfOceanCfgBean.setDpOcean(new ShelfOceanVO());
        shelfOceanCfgBean.setDpOcean(new ShelfOceanVO());
        System.out.println(JSON.toJSONString(shelfOceanCfgBean));

        Object o = new Object();
        Assert.assertNotNull(o);
    }

    @Test
    public void test_UnifiedShelfOceanConfig3() {


        SceneCfgBean test = UnifiedOceanConfigUtils.getOceanConfigBySceneCode("test");

        System.out.println(JSON.toJSONString(test));
        Object o = new Object();
        Assert.assertNotNull(o);
    }

}
