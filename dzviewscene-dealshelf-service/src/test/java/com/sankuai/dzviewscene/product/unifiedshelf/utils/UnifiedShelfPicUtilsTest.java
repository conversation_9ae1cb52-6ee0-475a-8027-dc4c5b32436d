package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.sankuai.dzviewscene.product.shelf.utils.UnifiedShelfPicUtils;
import org.junit.Assert;
import org.junit.Test;

public class UnifiedShelfPicUtilsTest {

    /**
     * 测试toHttpsUrl方法，当key不包含"?"时
     */
    @Test
    public void testToHttpsUrlWhenKeyDoesNotContainQuestionMark() {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 200;
        PictureURLBuilders.ScaleType scaleType = PictureURLBuilders.ScaleType.Scale;

        // act
        String result = UnifiedShelfPicUtils.toHttpsUrl(key, width, height, scaleType);

        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试toHttpsUrl方法，当key包含"?"且split后长度为2时
     */
    @Test
    public void testToHttpsUrlWhenKeyContainsQuestionMarkAndSplitLengthIsTwo() {
        // arrange
        String key = "testKey?token=123";
        int width = 100;
        int height = 200;
        PictureURLBuilders.ScaleType scaleType = PictureURLBuilders.ScaleType.Cut;

        // act
        String result = UnifiedShelfPicUtils.toHttpsUrl(key, width, height, scaleType);

        // assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("testKey"));
        Assert.assertTrue(result.contains("token=123"));
    }

    /**
     * 测试toHttpsUrl方法，当key包含"?"但split后长度不为2时
     */
    @Test
    public void testToHttpsUrlWhenKeyContainsQuestionMarkButSplitLengthIsNotTwo() {
        // arrange
        String key = "testKey?";
        int width = 100;
        int height = 200;
        PictureURLBuilders.ScaleType scaleType = PictureURLBuilders.ScaleType.Scale;

        // act
        String result = UnifiedShelfPicUtils.toHttpsUrl(key, width, height, scaleType);

        // assert
        Assert.assertEquals(key, result);
    }

    /**
     * 测试toHttpsUrl方法，当URL包含"s3plus-img"时
     */
    @Test
    public void testToHttpsUrlWhenUrlContainsS3plusImg() {
        // arrange
        String key = "s3plus-img?key";
        int width = 100;
        int height = 200;
        PictureURLBuilders.ScaleType scaleType = PictureURLBuilders.ScaleType.Scale;

        // act
        String result = UnifiedShelfPicUtils.toHttpsUrl(key, width, height, scaleType);

        // assert
        Assert.assertNotNull(result);
        Assert.assertFalse(result.contains("%7Cwatermark%3D0"));
    }
}
