package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfProductMPromoInfoUtilsTest {

    @Test
    public void getSecKillPromoPriceM() {
        ProductPromoPriceM productPromoPriceM1 = new ProductPromoPriceM();
        productPromoPriceM1.setPromoTagType(PromoTagTypeEnum.NewUser.getCode());
        ShelfTagVO dzTagVO1 = UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(Lists.newArrayList(productPromoPriceM1), "100", "80");


        ProductPromoPriceM productPromoPriceM2 = new ProductPromoPriceM();
        productPromoPriceM2.setPromoTagType(PromoTagTypeEnum.PreSale_Member.getCode());
        ShelfTagVO dzTagVO2 = UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(Lists.newArrayList(productPromoPriceM2), "100", "80");

//        Assert.assertTrue(dzTagVO1 != null && dzTagVO1.getName().startsWith("新客共省¥20"));
        Assert.assertNotNull(dzTagVO1);
        Assert.assertNotNull(dzTagVO2);
    }

    @Test
    public void getSalePrice() {
        Assert.assertEquals(UnifiedShelfProductMPromoInfoUtils.getSalePrice("100"), new BigDecimal("100"));
        Assert.assertEquals(UnifiedShelfProductMPromoInfoUtils.getSalePrice(""), new BigDecimal("0"));
    }


    /**
     * 测试场景：当promoPriceMS为空时，应返回null
     */
    @Test
    public void testGetSecKillPromoPriceM_WithEmptyPromoPriceMS() {
        // arrange
        List<ProductPromoPriceM> promoPriceMS = new ArrayList<>();

        // act
        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(promoPriceMS, "100", "80");

        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试场景：当marketPrice为空时，应返回null
     */
    @Test
    public void testGetSecKillPromoPriceM_WithEmptyMarketPrice() {
        // arrange
        List<ProductPromoPriceM> promoPriceMS = new ArrayList<>();
        promoPriceMS.add(Mockito.mock(ProductPromoPriceM.class));

        // act
        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(promoPriceMS, "", "80");

        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试场景：当salePrice为空时，应正确计算优惠金额并返回新客共省标签
     */
    @Test
    public void testGetSecKillPromoPriceM_WithEmptySalePrice() {
        // arrange
        List<ProductPromoPriceM> promoPriceMS = new ArrayList<>();
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoType(PromoTagTypeEnum.NewUser.getCode());
        promoPriceM.setPromoTagType(PromoTagTypeEnum.NewUser.getCode());
        promoPriceMS.add(promoPriceM);

        // act
        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(promoPriceMS, "100", "");

        // assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getName().contains("新客共省"));
    }

    /**
     * 测试场景：当存在秒杀+新客优惠时，应正确返回新客共省标签
     */
    @Test
    public void testGetSecKillPromoPriceM_WithSecKillAndNewUserPromo() {
        // arrange
        List<ProductPromoPriceM> promoPriceMS = new ArrayList<>();
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoType(PromoTagTypeEnum.NewUser.getCode());
        promoPriceM.setPromoTagType(PromoTagTypeEnum.NewUser.getCode());
        promoPriceMS.add(promoPriceM);

        // act
        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(promoPriceMS, "100", "80");

        // assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getName().contains("新客共省"));
    }

    /**
     * 测试场景：当存在秒杀优惠但不包含新客优惠时，应正确返回共省标签
     */
    @Test
    public void testGetSecKillPromoPriceM_WithOnlySecKillPromo() {
        // arrange
        List<ProductPromoPriceM> promoPriceMS = new ArrayList<>();
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoType(PromoTagTypeEnum.Official_Subsidies_NewUser.getCode());
        promoPriceM.setPromoTagType(PromoTagTypeEnum.Official_Subsidies_NewUser.getCode());
        promoPriceMS.add(promoPriceM);

        // act
        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.getSecKillPromoPriceM(promoPriceMS, "100", "90");

        // assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getName().contains("共省"));
    }


    private ProductM productM;
    private ProductPromoPriceM promoPriceM;

    @Before
    public void setUp() {
        productM = new ProductM();
        promoPriceM = Mockito.mock(ProductPromoPriceM.class);
    }

    /**
     * 测试场景：当productM的marketPrice为空时，应返回null
     */
    @Test
    public void testBuildMemberTag_MarketPriceIsEmpty() {
        productM.setMarketPrice("");

        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.buildMemberTag(productM, "100", promoPriceM);

        assertNull(result);
    }

    /**
     * 测试场景：当salePrice为空时，应计算共省金额为marketPrice
     */
    @Test
    public void testBuildMemberTag_SalePriceIsEmpty() {
        productM.setMarketPrice("200");
        productM.setPromoPrices(new ArrayList<>());

        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.buildMemberTag(productM, "", promoPriceM);

        assertNotNull(result);
        assertEquals("共省200", result.getName());
    }

    /**
     * 测试场景：当salePrice非空且productM包含默认促销价格时，应正确计算共省金额
     */
    @Test
    public void testBuildMemberTag_WithDefaultPromoPrice() {
        productM.setMarketPrice("300");
        ArrayList<ProductPromoPriceM> promoPrices = new ArrayList<>();
        promoPrices.add(promoPriceM);
        productM.setPromoPrices(promoPrices);

        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.buildMemberTag(productM, "100", null);

        assertNotNull(result);
        assertEquals("共省200", result.getName());
    }

    /**
     * 测试场景：当salePrice非空且productM不包含默认促销价格但提供了promoPriceM时，应正确计算共省金额
     */
    @Test
    public void testBuildMemberTag_WithProvidedPromoPriceM() {
        productM.setMarketPrice("400");
        productM.setPromoPrices(new ArrayList<>());

        ShelfTagVO result = UnifiedShelfProductMPromoInfoUtils.buildMemberTag(productM, "200", promoPriceM);

        assertNotNull(result);
        assertEquals("共省200", result.getName());
    }

}
