package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.MagicalMemberPromoTagStrategy;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class UnifiedShelfPromoUtilsTest {

    private Method validateCouponShelfVersionForMtWxXCX;

    @Before
    public void setUp() throws NoSuchMethodException {
        validateCouponShelfVersionForMtWxXCX = UnifiedShelfPromoUtils.class.getDeclaredMethod("validateCouponShelfVersionForMtWxXCX", Integer.class, Integer.class);
        validateCouponShelfVersionForMtWxXCX.setAccessible(true);
    }

    @Test
    public void test_buildAfterInflateCouponInfo() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        Method buildAfterInflateCouponInfo = unifiedShelfPromoUtils.getClass().getDeclaredMethod("buildAfterInflateCouponInfo", PromoItemM.class);
        buildAfterInflateCouponInfo.setAccessible(true);

        PromoItemM promoItemM = new PromoItemM();

        Object invoke = buildAfterInflateCouponInfo.invoke(unifiedShelfPromoUtils, promoItemM);
        Assert.assertNotNull(invoke);

    }

    @Test
    public void test_buildAfterInflateCouponInfo2() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        Method buildAfterInflateCouponInfo = unifiedShelfPromoUtils.getClass().getDeclaredMethod("buildAfterInflateCouponInfo", PromoItemM.class);
        buildAfterInflateCouponInfo.setAccessible(true);

        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromotionExplanatoryTags(Lists.newArrayList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode()));

        Object invoke = buildAfterInflateCouponInfo.invoke(unifiedShelfPromoUtils, promoItemM);
        Assert.assertNotNull(invoke);

    }

    @Test
    public void test_buildAfterInflateCouponInfo3() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        Method buildAfterInflateCouponInfo = unifiedShelfPromoUtils.getClass().getDeclaredMethod("buildAfterInflateCouponInfo", PromoItemM.class);
        buildAfterInflateCouponInfo.setAccessible(true);

        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromotionExplanatoryTags(Lists.newArrayList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode()));

        Object invoke = buildAfterInflateCouponInfo.invoke(unifiedShelfPromoUtils, promoItemM);
        Assert.assertNotNull(invoke);

    }

    @Test
    public void test_getPromoTag() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        Method getPromoTag = unifiedShelfPromoUtils.getClass().getDeclaredMethod("getPromoTag", ProductPromoPriceM.class);
        getPromoTag.setAccessible(true);

        ProductPromoPriceM promoItemM = new ProductPromoPriceM();

        Object invoke = getPromoTag.invoke(unifiedShelfPromoUtils, promoItemM);
        Assert.assertNull(invoke);
    }

    @Test
    public void test_getPromoTag3() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        Method getPromoTag = unifiedShelfPromoUtils.getClass().getDeclaredMethod("getPromoTag", ProductPromoPriceM.class);
        getPromoTag.setAccessible(true);

        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();

        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromotionExplanatoryTags(Lists.newArrayList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode()));
        Map<String, String> maps = new HashMap<>();
        maps.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        promoItemM.setPromotionOtherInfoMap(maps);
        productPromoPriceM.setPromoItemList(Lists.newArrayList(promoItemM));

        Map<String, String> map = new HashMap<>();
        map.put("promotionDetailMagicalMemberCouponText", "promotionDetailMagicalMemberCouponText");
        productPromoPriceM.setExtendDisplayInfo(map);

        Object invoke = getPromoTag.invoke(unifiedShelfPromoUtils, productPromoPriceM);
        Assert.assertNotNull(invoke);

    }

    /**
     * 测试当平台为null时，应该返回true
     */
    @Test
    public void test_validateCouponShelfVersionForMtWxXCX_PlatformIsNull() {
        // arrange
        Integer platform = null;
        Integer shelfVersion = 100;
        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        // act
        boolean result = false;
        try {
            result = (Boolean)validateCouponShelfVersionForMtWxXCX.invoke(unifiedShelfPromoUtils, platform, shelfVersion);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试当平台不是MT_XCX时，应该返回true
     */
    @Test
    public void test_validateCouponShelfVersionForMtWxXCX_PlatformIsNotMtXcx() {
        // arrange
        Integer platform = VCClientTypeEnum.DP_XCX.getCode();
        Integer shelfVersion = 100;
        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        // act
        boolean result = false;
        try {
            result = (Boolean)validateCouponShelfVersionForMtWxXCX.invoke(unifiedShelfPromoUtils, platform, shelfVersion);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试当平台是MT_XCX且货架版本号为null时，应该返回false
     */
    @Test
    public void test_validateCouponShelfVersionForMtWxXCX_ShelfVersionIsNull() {
        // arrange
        Integer platform = VCClientTypeEnum.MT_XCX.getCode();
        Integer shelfVersion = null;
        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        // act
        boolean result = false;
        try {
            result = (Boolean)validateCouponShelfVersionForMtWxXCX.invoke(unifiedShelfPromoUtils, platform, shelfVersion);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试当平台是MT_XCX且货架版本号小于MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL时，应该返回false
     */
    @Test
    public void test_validateCouponShelfVersionForMtWxXCX_ShelfVersionIsLessThanLimit() {
        // arrange
        Integer platform = VCClientTypeEnum.MT_XCX.getCode();
        Integer shelfVersion = 111 - 1; // 假设MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL为112
        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        // act
        boolean result = false;
        try {
            result = (Boolean)validateCouponShelfVersionForMtWxXCX.invoke(unifiedShelfPromoUtils, platform, shelfVersion);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试当平台是MT_XCX且货架版本号等于MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL时，应该返回true
     */
    @Test
    public void test_validateCouponShelfVersionForMtWxXCX_ShelfVersionIsEqualToLimit() {
        // arrange
        Integer platform = VCClientTypeEnum.MT_XCX.getCode();
        Integer shelfVersion = 112; // 假设MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL为112
        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        // act
        boolean result = false;
        try {
            result = (Boolean)validateCouponShelfVersionForMtWxXCX.invoke(unifiedShelfPromoUtils, platform, shelfVersion);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试当平台是MT_XCX且货架版本号大于MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL时，应该返回true
     */
    @Test
    public void test_validateCouponShelfVersionForMtWxXCX_ShelfVersionIsGreaterThanLimit() {
        // arrange
        Integer platform = VCClientTypeEnum.MT_XCX.getCode();
        Integer shelfVersion = 112 + 1; // 假设MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL为112
        UnifiedShelfPromoUtils unifiedShelfPromoUtils = new UnifiedShelfPromoUtils();

        // act
        boolean result = false;
        try {
            result = (Boolean)validateCouponShelfVersionForMtWxXCX.invoke(unifiedShelfPromoUtils, platform, shelfVersion);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        // assert
        Assert.assertTrue(result);
    }
}
