package com.sankuai.dzviewscene.product.utils;

import com.dianping.lion.client.util.JsonUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Assert;
import org.testng.annotations.Test;
import org.testng.collections.Maps;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class ParamsUtilTest {

    @Test
    public static void isEmpty() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        map.put("test2",null);

        assertFalse(ParamsUtil.isEmpty(map,"test1"));
        assertTrue(ParamsUtil.isEmpty(map,"test2"));
    }

    @Test
    public static void getValue_Map() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");

        Assert.assertEquals(ParamsUtil.getValue(map,"test1", ""),"test1");
        Assert.assertEquals(ParamsUtil.getValue(map,"test2", "test2"),"test2");
    }

    @Test
    public static void getValue_ActivityCxt() {
        ActivityCxt activityCxt = new ActivityCxt();
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        activityCxt.setParameters(map);

        Assert.assertEquals(ParamsUtil.getValue(activityCxt,"test1", ""),"test1");
        Assert.assertEquals(ParamsUtil.getValue(activityCxt,"test2", "test2"),"test2");
    }

    @Test
    public static void getIntSafely_ActivityCxt() {
        ActivityCxt activityCxt = new ActivityCxt();
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        map.put("test2","100");
        activityCxt.setParameters(map);

        Assert.assertEquals(ParamsUtil.getIntSafely(activityCxt,"test1"),0);
        Assert.assertEquals(ParamsUtil.getIntSafely(activityCxt,"test2"),100);
    }

    @Test
    public static void getLongSafely_ActivityCxt() {
        ActivityCxt activityCxt = new ActivityCxt();
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        map.put("test2","6541612346461164461");
        activityCxt.setParameters(map);

        Assert.assertEquals(ParamsUtil.getLongSafely(activityCxt,"test1"),0);
        Assert.assertEquals(ParamsUtil.getLongSafely(activityCxt,"test2"),6541612346461164461L);
    }

    @Test
    public static void getLongSafely_Map() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        map.put("test2","6541612346461164461");

        Assert.assertEquals(ParamsUtil.getLongSafely(map,"test1"),0);
        Assert.assertEquals(ParamsUtil.getLongSafely(map,"test2"),6541612346461164461L);
    }

    @Test
    public static void getDoubleSafely_ActivityCxt() {
        ActivityCxt activityCxt = new ActivityCxt();
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        map.put("test2","6541612.3464611");
        activityCxt.setParameters(map);

        Assert.assertEquals(ParamsUtil.getDoubleSafely(activityCxt,"test1"),0,0.000001);
        Assert.assertEquals(ParamsUtil.getDoubleSafely(activityCxt,"test2"),6541612.3464611, 0.000001);
    }

    @Test
    public static void getIntSafely_Map() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        map.put("test2","100");

        Assert.assertEquals(ParamsUtil.getIntSafely(map,"test1"),0);
        Assert.assertEquals(ParamsUtil.getIntSafely(map,"test2"),100);
    }

    @Test
    public static void getParamsFromExtraMap() throws IOException {
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        String mapStr = JsonUtils.toJson(map);

        Assert.assertEquals(ParamsUtil.getParamsFromExtraMap(mapStr,"test1", ""),"test1");
        Assert.assertEquals(ParamsUtil.getParamsFromExtraMap(mapStr,"test2", "test2"),"test2");
    }

    @Test
    public static void getStringSafely_ActivityCxt(){
        ActivityCxt activityCxt = new ActivityCxt();
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");
        activityCxt.setParameters(map);

        Assert.assertEquals(ParamsUtil.getStringSafely(activityCxt,"test1"),"test1");
        Assert.assertEquals(ParamsUtil.getStringSafely(activityCxt,"test2"),"");
    }

    @Test
    public static void getStringSafely_Map(){
        Map<String, Object> map = Maps.newHashMap();
        map.put("test1","test1");

        Assert.assertEquals(ParamsUtil.getStringSafely(map,"test1"),"test1");
        Assert.assertEquals(ParamsUtil.getStringSafely(map,"test2"),"");
    }

    /**
     * 测试judgeDealShelfHasPage方法
     */
    @Test
    public void testJudgeDealShelfHasPage() {
        Map<String, Object> hashMap = new HashMap<String, Object>() {{
            put(ShelfActivityConstants.Params.pagination, "1");
        }};
        ActivityCxt activityCxt = new ActivityCxt();
        activityCxt.setParameters(hashMap);
        boolean result = ParamsUtil.judgeDealShelfHasPage(activityCxt);
        assertTrue("当pagination=1时，应返回true", result);
    }

    /**
     * 测试getBooleanSafely方法，当params为null时应返回false
     */
    @Test
    public void testGetBooleanSafelyParamsNull() {
        // arrange
        Map<String, Object> params = null;

        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");

        // assert
        Assert.assertFalse("当params为null时，应返回false", result);
    }

    /**
     * 测试getBooleanSafely方法，当params为空时应返回false
     */
    @Test
    public void testGetBooleanSafelyParamsEmpty() {
        // arrange
        Map<String, Object> params = new HashMap<>();

        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");

        // assert
        Assert.assertFalse("当params为空时，应返回false", result);
    }

    /**
     * 测试getBooleanSafely方法，当params中不包含指定key时应返回false
     */
    @Test
    public void testGetBooleanSafelyKeyNotPresent() {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("anotherKey", true);

        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");

        // assert
        Assert.assertFalse("当params中不包含指定key时，应返回false", result);
    }

    /**
     * 测试getBooleanSafely方法，当params中指定key的值为Boolean类型且为true时应返回true
     */
    @Test
    public void testGetBooleanSafelyValueTrue() {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("testKey", true);

        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");

        // assert
        Assert.assertTrue("当params中指定key的值为Boolean类型且为true时，应返回true", result);
    }

    /**
     * 测试getBooleanSafely方法，当params中指定key的值为Boolean类型且为false时应返回false
     */
    @Test
    public void testGetBooleanSafelyValueFalse() {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("testKey", false);

        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");

        // assert
        Assert.assertFalse("当params中指定key的值为Boolean类型且为false时，应返回false", result);
    }

    /**
     * 测试getBooleanSafely方法，当params中指定key的值不是Boolean类型时应返回false
     */
    @Test
    public void testGetBooleanSafelyValueNotBoolean() {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("testKey", "notABoolean");

        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");

        // assert
        Assert.assertFalse("当params中指定key的值不是Boolean类型时，应返回false", result);
    }
}
