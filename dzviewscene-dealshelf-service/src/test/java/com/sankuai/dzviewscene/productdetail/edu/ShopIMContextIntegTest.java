package com.sankuai.dzviewscene.productdetail.edu;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzviewscene.shelf.business.detail.edu.EduCourseDetailContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2020/10/29 10:15 上午
 */
@Ignore
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.shelf.business.detail.edu.context","com.sankuai.dzviewscene.nr.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class ShopIMContextIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(ShopIMContextIntegTest.class);

    @Resource
    private ShopIMContext context;

    @RpcClient(url = "com.sankuai.dzim.cliententry.ClientEntryService")
    private ClientEntryService clientEntryService;

    static {
        TestBeanFactory.registerBean("shopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-core.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_online() {
        ActivityContext activityContext = buildContext();
        Map<String, String> result = context.contextExt(activityContext).join();
        logger.info("结果为：" + JsonCodec.encodeWithUTF8(result));
    }

    private ActivityContext buildContext() {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.platform, 101);
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, 1591904784);
        activityContext.addParam(ShelfActivityConstants.Params.clientType, "android");
        return activityContext;
    }


    @Environment(AthenaEnv.Product)
    //@Test
    public void test_onlineAsBatch() {
        logger.info("结果为：" + clientEntryService.getClientEntry(buildClientEntryReqDTO(ClientTypeEnum.dp_mainApp_ios.getType())));
        logger.info("结果为：" + clientEntryService.getClientEntry(buildClientEntryReqDTO(ClientTypeEnum.dp_mainApp_android.getType())));
        logger.info("结果为：" + clientEntryService.getClientEntry(buildClientEntryReqDTO(ClientTypeEnum.dp_wap.getType())));
        logger.info("结果为：" + clientEntryService.getClientEntry(buildClientEntryReqDTO(ClientTypeEnum.mt_mainApp_ios.getType())));
        logger.info("结果为：" + clientEntryService.getClientEntry(buildClientEntryReqDTO(ClientTypeEnum.mt_mainApp_android.getType())));
        logger.info("结果为：" + clientEntryService.getClientEntry(buildClientEntryReqDTO(ClientTypeEnum.mt_wap.getType())));
    }

    private ClientEntryReqDTO buildClientEntryReqDTO(int clientType) {
        ClientEntryReqDTO reqDTO = new ClientEntryReqDTO();
        reqDTO.setDpPoiId(2532814);
        reqDTO.setClientType(clientType);
        reqDTO.setShopType(EduCourseDetailContext.EDU_SHOP_TYPE);
        return reqDTO;
    }

}
