package com.sankuai.dzviewscene.productdetail.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DzShopVOUtilsTest {

    @Mock
    private ProductM productM;

    @Mock
    private ActivityContext context;

    @Mock
    private ShopM shopM;

    private Object originalUrlConfig;

    private Field urlConfigField;

    @Before
    public void setUp() throws Exception {
        // Get the UrlConfig field
        urlConfigField = DzShopVOUtils.class.getDeclaredField("urlConfig");
        urlConfigField.setAccessible(true);
        // Store the original urlConfig
        originalUrlConfig = urlConfigField.get(null);
        // Create a new UrlConfig instance using reflection
        Class<?> urlConfigClass = Class.forName("com.sankuai.dzviewscene.productdetail.util.DzShopVOUtils$UrlConfig");
        Constructor<?> constructor = urlConfigClass.getDeclaredConstructor();
        constructor.setAccessible(true);
        Object newUrlConfig = constructor.newInstance();
        // Set URL values in the new UrlConfig instance
        Field mtZjXcxShopUrlField = urlConfigClass.getDeclaredField("mtZjXcxShopUrl");
        mtZjXcxShopUrlField.setAccessible(true);
        mtZjXcxShopUrlField.set(newUrlConfig, "https://mt.zjxcx.shop/%d");
        Field mtIShopUrlField = urlConfigClass.getDeclaredField("mtIShopUrl");
        mtIShopUrlField.setAccessible(true);
        mtIShopUrlField.set(newUrlConfig, "https://i.mt.shop/%d");
        Field dpMShopUrlField = urlConfigClass.getDeclaredField("dpMShopUrl");
        dpMShopUrlField.setAccessible(true);
        dpMShopUrlField.set(newUrlConfig, "https://m.dp.shop/%d");
        Field mtAppShopUrlField = urlConfigClass.getDeclaredField("mtAppShopUrl");
        mtAppShopUrlField.setAccessible(true);
        mtAppShopUrlField.set(newUrlConfig, "https://app.mt.shop/%d");
        Field dpAppShopUrlField = urlConfigClass.getDeclaredField("dpAppShopUrl");
        dpAppShopUrlField.setAccessible(true);
        dpAppShopUrlField.set(newUrlConfig, "https://app.dp.shop/%d/%s");
        // Set the new UrlConfig instance
        urlConfigField.set(null, newUrlConfig);
    }

    @After
    public void tearDown() throws Exception {
        // Reset to original urlConfig
        urlConfigField.set(null, originalUrlConfig);
    }

    @Test
    public void testBuildShopUrl_ShopMsEmpty() throws Throwable {
        when(productM.getShopMs()).thenReturn(Collections.emptyList());
        String result = DzShopVOUtils.buildShopUrl(productM, context);
        assertNull(result);
    }

    @Test
    public void testBuildShopUrl_ShopMsFirstElementNull() throws Throwable {
        when(productM.getShopMs()).thenReturn(Collections.singletonList(null));
        String result = DzShopVOUtils.buildShopUrl(productM, context);
        assertNull(result);
    }

    @Test
    public void testBuildShopUrl_UserAgentIsMtZjXcx() throws Throwable {
        when(productM.getShopMs()).thenReturn(Collections.singletonList(shopM));
        when(context.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_ZJ_XCX.getCode());
        when(shopM.getLongShopId()).thenReturn(123456L);
        String result = DzShopVOUtils.buildShopUrl(productM, context);
        assertNotNull(result);
        assertEquals("https://mt.zjxcx.shop/123456", result);
    }

    @Test
    public void testBuildShopUrl_UserAgentNotApp() throws Throwable {
        when(productM.getShopMs()).thenReturn(Collections.singletonList(shopM));
        when(context.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.DP_M.getCode());
        when(context.getParam(ShelfActivityConstants.Params.platform)).thenReturn(2);
        when(shopM.getLongShopId()).thenReturn(123456L);
        String result = DzShopVOUtils.buildShopUrl(productM, context);
        assertNotNull(result);
        assertEquals("https://i.mt.shop/123456", result);
    }

    @Test
    public void testBuildShopUrl_PlatformIsMT() throws Throwable {
        when(productM.getShopMs()).thenReturn(Collections.singletonList(shopM));
        when(context.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.MT_APP.getCode());
        when(context.getParam(ShelfActivityConstants.Params.platform)).thenReturn(2);
        when(shopM.getLongShopId()).thenReturn(123456L);
        String result = DzShopVOUtils.buildShopUrl(productM, context);
        assertNotNull(result);
        assertEquals("https://app.mt.shop/123456", result);
    }

    @Test
    public void testBuildShopUrl_PlatformNotMT() throws Throwable {
        when(productM.getShopMs()).thenReturn(Collections.singletonList(shopM));
        when(context.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(VCClientTypeEnum.DP_APP.getCode());
        when(context.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(shopM.getLongShopId()).thenReturn(123456L);
        when(shopM.getShopUuid()).thenReturn("shopUuid");
        String result = DzShopVOUtils.buildShopUrl(productM, context);
        assertNotNull(result);
        assertEquals("https://app.dp.shop/123456/shopUuid", result);
    }

    /**
     * 测试buildShopMapUrl方法，当shopMs为空时，应返回null
     */
    @Test
    public void testBuildShopMapUrlWhenShopMsIsNull() throws Throwable {
        // arrange
        when(productM.getShopMs()).thenReturn(null);
        // act
        String result = DzShopVOUtils.buildShopMapUrl(productM, context, "urlParam");
        // assert
        assertNull(result);
    }

    /**
     * 测试buildShopMapUrl方法，当shopMs的第一个元素为null时，应返回null
     */
    @Test
    public void testBuildShopMapUrlWhenFirstShopIsNull() throws Throwable {
        // arrange
        when(productM.getShopMs()).thenReturn(Arrays.asList(null, shopM));
        // act
        String result = DzShopVOUtils.buildShopMapUrl(productM, context, "urlParam");
        // assert
        assertNull(result);
    }

    /**
     * 测试buildShopMapUrl方法，当用户不在APP端时，应返回buildMapJumpUrlForWx的URL
     */
    @Test
    public void testBuildShopMapUrlWhenUserNotInApp() throws Throwable {
        // arrange
        when(productM.getShopMs()).thenReturn(Collections.singletonList(shopM));
        when(context.getParam(ShelfActivityConstants.Params.platform)).thenReturn(0);
        when(context.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(0);
        // act
        String result = DzShopVOUtils.buildShopMapUrl(productM, context, "urlParam");
        // assert
        assertNotNull(result);
    }

    /**
     * 测试buildShopMapUrl方法，当用户在APP端时，应返回buildMapJumpUrlForDp的URL
     */
    @Test
    public void testBuildShopMapUrlWhenUserInApp() throws Throwable {
        // arrange
        when(productM.getShopMs()).thenReturn(Collections.singletonList(shopM));
        when(context.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(context.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(1);
        // act
        String result = DzShopVOUtils.buildShopMapUrl(productM, context, "urlParam");
        // assert
        assertNotNull(result);
    }
}
