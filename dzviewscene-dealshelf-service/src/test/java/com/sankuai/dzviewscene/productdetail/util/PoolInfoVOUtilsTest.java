package com.sankuai.dzviewscene.productdetail.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.detail.vo.backroom.BackroomPoolInfoVO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PoolInfoVOUtilsTest {

    @Mock
    private ProductM productM;

    @Test(expected = NullPointerException.class)
    public void testBuildPoolInfoVONullProductM() throws Throwable {
        PoolInfoVOUtils.buildPoolInfoVO(null);
    }

    @Test
    public void testBuildPoolInfoVONullAttr() throws Throwable {
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_SIMPLE_RULE.getKey())).thenReturn(null);
        BackroomPoolInfoVO result = PoolInfoVOUtils.buildPoolInfoVO(productM);
        assertNull(result);
    }

    @Test
    public void testBuildPoolInfoVOEmptyAttr() throws Throwable {
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_SIMPLE_RULE.getKey())).thenReturn("");
        BackroomPoolInfoVO result = PoolInfoVOUtils.buildPoolInfoVO(productM);
        assertNull(result);
    }

    @Test
    public void testBuildPoolInfoVONonEmptyAttr() throws Throwable {
        // Using JSON array format for simple rules
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_SIMPLE_RULE.getKey())).thenReturn("[\"rule1\",\"rule2\"]");
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_RULE_INFO.getKey())).thenReturn("{\"title\":\"test\"}");
        BackroomPoolInfoVO result = PoolInfoVOUtils.buildPoolInfoVO(productM);
        assertNotNull(result);
        assertEquals(Arrays.asList("rule1", "rule2"), result.getPoolSimpleRule());
        assertNotNull(result.getPoolDetailRule());
        assertEquals("test", result.getPoolDetailRule().getTitle());
    }

    @Test
    public void testBuildPoolInfoVONullPoolRuleInfo() throws Throwable {
        // Using JSON array format for simple rules
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_SIMPLE_RULE.getKey())).thenReturn("[\"rule1\",\"rule2\"]");
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_RULE_INFO.getKey())).thenReturn(null);
        BackroomPoolInfoVO result = PoolInfoVOUtils.buildPoolInfoVO(productM);
        assertNotNull(result);
        assertEquals(Arrays.asList("rule1", "rule2"), result.getPoolSimpleRule());
        assertNull(result.getPoolDetailRule());
    }

    @Test
    public void testBuildPoolInfoVONonNullPoolRuleInfo() throws Throwable {
        // Using JSON array format for simple rules
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_SIMPLE_RULE.getKey())).thenReturn("[\"rule1\",\"rule2\"]");
        when(productM.getAttr(GeneralProductAttrEnum.ATTR_ROLEPLAY_POOL_RULE_INFO.getKey())).thenReturn("{\"title\":\"test\"}");
        BackroomPoolInfoVO result = PoolInfoVOUtils.buildPoolInfoVO(productM);
        assertNotNull(result);
        assertEquals(Arrays.asList("rule1", "rule2"), result.getPoolSimpleRule());
        assertNotNull(result.getPoolDetailRule());
        assertEquals("test", result.getPoolDetailRule().getTitle());
    }
}
