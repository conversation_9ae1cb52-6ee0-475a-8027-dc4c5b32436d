package com.sankuai.dzviewscene.productdetail.util;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.mockito.Mockito;

/**
 * Test class for TimesDealUtil.hitTimesStyle2 method
 */
public class TimesDealUtilHitTimesStyle2Test {

    /**
     * Test when sk contains "a", should return true
     */
    @Test
    public void testHitTimesStyle2_WhenSkContainsA() {
        // arrange
        ProductM timesDealProductM = Mockito.mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        String sk = "abc";
        // act
        boolean result = TimesDealUtil.hitTimesStyle2(timesDealProductM, sk, allProducts);
        // assert
        assertTrue(result);
    }

    /**
     * Test when sk contains "b" and related times deal is empty
     */
    @Test
    public void testHitTimesStyle2_WhenSkContainsBAndEmptyRelatedTimesDeal() {
        // arrange
        ProductM timesDealProductM = Mockito.mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        when(timesDealProductM.getRelatedTimesDeal()).thenReturn(new ArrayList<>());
        Map<Integer, ProductM> allProducts = new HashMap<>();
        String sk = "b123";
        // act
        boolean result = TimesDealUtil.hitTimesStyle2(timesDealProductM, sk, allProducts);
        // assert
        assertTrue(result);
    }

    /**
     * Test when sk contains "b" and has related times deal but no single time deal
     */
    @Test
    public void testHitTimesStyle2_WhenSkContainsBAndNoSingleTimeDeal() {
        // arrange
        ProductM timesDealProductM = Mockito.mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        List<Integer> relatedDeals = new ArrayList<>();
        relatedDeals.add(1);
        when(timesDealProductM.getRelatedTimesDeal()).thenReturn(relatedDeals);
        when(timesDealProductM.getProductId()).thenReturn(1);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        ProductM relatedProduct = Mockito.mock(ProductM.class);
        when(relatedProduct.isTimesDeal()).thenReturn(true);
        allProducts.put(1, relatedProduct);
        String sk = "b123";
        // act
        boolean result = TimesDealUtil.hitTimesStyle2(timesDealProductM, sk, allProducts);
        // assert
        assertTrue(result);
    }

    /**
     * Test when sk contains neither "a" nor "b", should return false
     */
    @Test
    public void testHitTimesStyle2_WhenSkContainsNeitherANorB() {
        // arrange
        ProductM timesDealProductM = Mockito.mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        String sk = "xyz";
        // act
        boolean result = TimesDealUtil.hitTimesStyle2(timesDealProductM, sk, allProducts);
        // assert
        assertFalse(result);
    }
}
