package com.sankuai.dzviewscene.productdetail.util;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.dianping.zebra.util.StringUtils;
import com.dp.arts.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionKeys;

import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.MedicalDealAttrUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuGroupModuleConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuItemValueConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.GlassesV1ProductTagsOpt;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.testng.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @author: created by hang.yu on 2024/1/25 15:25
 */
@RunWith(MockitoJUnitRunner.class)
public class TimesDealUtilTest {

    /**
     * 测试isAfterPayAnXinXueTimeCard方法 - 正常场景：满足所有条件
     */
    @Test
    public void testIsAfterPayAnXinXueTimeCard_AllConditionsMet() {
        // 准备测试数据
        ProductM productM = new ProductM();
        productM.setProductType(ProductTypeEnum.TIME_CARD.getType());
        productM.setAttr("attr_creditPayPreAuthTag", "tag1");
        productM.setAttr("attr_guaranteeTagCodes", "[1001]");
        
        List<String> afterPayTagList = Arrays.asList("tag1", "tag2");
        List<Integer> anXinTagCodes = Arrays.asList(1001, 1002);
        long anXinProductTagId = 1001L;

        // 执行测试
        boolean result = TimesDealUtil.isAfterPayAnXinXueTimeCard(productM, afterPayTagList, anXinTagCodes, anXinProductTagId);

        // 验证结果
        assertTrue(result);
    }

    /**
     * 测试isAfterPayAnXinXueTimeCard方法 - productM为null的场景
     */
    @Test
    public void testIsAfterPayAnXinXueTimeCard_NullProduct() {
        List<String> afterPayTagList = Arrays.asList("tag1", "tag2");
        List<Integer> anXinTagCodes = Arrays.asList(1001, 1002);
        long anXinProductTagId = 1001L;

        // 执行测试
        boolean result = TimesDealUtil.isAfterPayAnXinXueTimeCard(null, afterPayTagList, anXinTagCodes, anXinProductTagId);

        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试isAfterPayAnXinXueTimeCard方法 - 不满足先用后付条件的场景
     */
    @Test
    public void testIsAfterPayAnXinXueTimeCard_NoAfterPay() {
        // 准备测试数据
        ProductM productM = new ProductM();
        productM.setProductType(ProductTypeEnum.TIME_CARD.getType());
        // 不设置creditPayPreAuthTag，使其不满足先用后付条件
        productM.setAttr("attr_guaranteeTagCodes", "[1001]");
        
        List<String> afterPayTagList = Arrays.asList("tag1", "tag2");
        List<Integer> anXinTagCodes = Arrays.asList(1001, 1002);
        long anXinProductTagId = 1001L;

        // 执行测试
        boolean result = TimesDealUtil.isAfterPayAnXinXueTimeCard(productM, afterPayTagList, anXinTagCodes, anXinProductTagId);

        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试isAfterPayAnXinXueTimeCard方法 - 不是次卡的场景
     */
    @Test
    public void testIsAfterPayAnXinXueTimeCard_NotTimeCard() {
        // 准备测试数据
        ProductM productM = new ProductM();
        productM.setProductType(ProductTypeEnum.DEAL.getType()); // 设置为普通商品类型
        productM.setAttr("attr_creditPayPreAuthTag", "tag1");
        productM.setAttr("attr_guaranteeTagCodes", "[1001]");
        
        List<String> afterPayTagList = Arrays.asList("tag1", "tag2");
        List<Integer> anXinTagCodes = Arrays.asList(1001, 1002);
        long anXinProductTagId = 1001L;

        // 执行测试
        boolean result = TimesDealUtil.isAfterPayAnXinXueTimeCard(productM, afterPayTagList, anXinTagCodes, anXinProductTagId);

        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试isAfterPayAnXinXueTimeCard方法 - 不是安心学标签的场景
     */
    @Test
    public void testIsAfterPayAnXinXueTimeCard_NoAnXinTag() {
        // 准备测试数据
        ProductM productM = new ProductM();
        productM.setProductType(ProductTypeEnum.TIME_CARD.getType());
        productM.setAttr("attr_creditPayPreAuthTag", "tag1");
        productM.setAttr("attr_guaranteeTagCodes", "[2001]"); // 设置一个非安心学标签
        
        List<String> afterPayTagList = Arrays.asList("tag1", "tag2");
        List<Integer> anXinTagCodes = Arrays.asList(1001, 1002);
        long anXinProductTagId = 1001L;

        // 执行测试
        boolean result = TimesDealUtil.isAfterPayAnXinXueTimeCard(productM, afterPayTagList, anXinTagCodes, anXinProductTagId);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testGlassesV2() {
        SkuConfig testSkuConfig = new SkuConfig();
        testSkuConfig.setType(Integer.valueOf(123));
        testSkuConfig.setTitleConfig(null);
        testSkuConfig.setSubTitleConfig(null);
        testSkuConfig.setSkuItemConfigList(Lists.newArrayList());
        SkuItemValueConfig randomPriceConfig = new SkuItemValueConfig();
        randomPriceConfig.setBeforeText("123");
        randomPriceConfig.setMidText("1234");
        testSkuConfig.setPriceItemConfig(randomPriceConfig);
        Assert.assertNotNull(testSkuConfig.getPriceItemConfig());
    }

    @Test
    public void testRandomGlassType() {
        /**
         * 测试任选配镜类型
         */
        GlassesV1ProductTagsOpt glassesV1ProductTagsOpt = new GlassesV1ProductTagsOpt();
        ProductM productM = new ProductM();
        AttrM lenAPR = new AttrM("lensAvailablePriceRange", "567");
        AttrM APR = new AttrM("availablePriceRange", "321");
        List<AttrM> extAttrs = Lists.newArrayList();
        extAttrs.add(lenAPR);
        extAttrs.add(APR);
        productM.setExtAttrs(extAttrs);
        List<String> ans = glassesV1ProductTagsOpt.buildRandomGlassesTag(productM, null);
        Assert.assertNotNull(ans);
    }

    @Test
    public void testWrapPriceConfig() {
        Map<String, String> name2ValueMap = new HashMap<String, String>();
        name2ValueMap.put("testPrice", "123");
        name2ValueMap.put("title", "aha");
        SkuConfig skuConfig = new SkuConfig();
        ValueConfig valueConfig = new ValueConfig();
        valueConfig.setProcessType(1);
        valueConfig.setKey("testPrice");
        valueConfig.setFormat("This is your price : %s");
        ValueConfig titleConfig = new ValueConfig();
        titleConfig.setKey("title");
        titleConfig.setFormat("This is your title : %s");
        titleConfig.setProcessType(5);
        List<ValueConfig> valueKeys = new ArrayList<>();
        valueKeys.add(valueConfig);
        SkuItemValueConfig randomPriceConfig = new SkuItemValueConfig();
        randomPriceConfig.setValueKeys(valueKeys);
        skuConfig.setPriceItemConfig(randomPriceConfig);
        SkuItemValueConfig titleItemConfig = new SkuItemValueConfig();
        List<ValueConfig> titleConfigs = new ArrayList<>();
        titleConfigs.add(titleConfig);
        titleItemConfig.setValueKeys(titleConfigs);
        skuConfig.setTitleConfig(titleItemConfig);
        SkuGroupModuleConfig skuGroupModuleConfig = new SkuGroupModuleConfig();
        ArrayList<SkuConfig> skuConfigs = new ArrayList<>();
        skuConfigs.add(skuConfig);
        skuGroupModuleConfig.setSkuConfigList(skuConfigs);
        List<SkuGroupModuleConfig> skuGroupModuleConfigList = new ArrayList<>();
        skuGroupModuleConfigList.add(skuGroupModuleConfig);
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = MedicalDealAttrUtils.buildDealSkusByAttrs(name2ValueMap, skuGroupModuleConfigList);
        Assert.assertNotNull(dealSkuGroupModuleVOS);
    }

    @Test
    public void isTimesDeal1() {
        boolean timesDeal = TimesDealUtil.isTimesDeal(null);
        Assert.assertFalse(timesDeal);
    }

    @Test
    public void isTimesDeal2() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setTradeType(10);
        boolean timesDeal = TimesDealUtil.isTimesDeal(dealDetailInfoModel);
        Assert.assertFalse(timesDeal);
    }

    @Test
    public void isTimesDeal3() {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setTradeType(19);
        boolean timesDeal = TimesDealUtil.isTimesDeal(dealDetailInfoModel);
        Assert.assertTrue(timesDeal);
    }

    @Test
    public void getTimesTitle1() {
        String timesTitle = TimesDealUtil.getTimesTitle(null);
        Assert.assertNull(timesTitle);
    }

    @Test
    public void getTimesTitle2() {
        List<AttrM> dealAttrs = Lists.newArrayList();
        AttrM e = new AttrM();
        e.setName("sys_multi_sale_number");
        e.setValue(null);
        dealAttrs.add(e);
        String timesTitle = TimesDealUtil.getTimesTitle(dealAttrs);
        Assert.assertNull(timesTitle);
    }

    @Test
    public void getTimesTitle3() {
        List<AttrM> dealAttrs = Lists.newArrayList();
        AttrM e = new AttrM();
        e.setName("sys_multi_sale_number");
        e.setValue("aa");
        dealAttrs.add(e);
        String timesTitle = TimesDealUtil.getTimesTitle(dealAttrs);
        Assert.assertNull(timesTitle);
    }

    @Test
    public void getTimesTitle4() {
        List<AttrM> dealAttrs = Lists.newArrayList();
        AttrM e = new AttrM();
        e.setName("sys_multi_sale_number");
        e.setValue("10");
        dealAttrs.add(e);
        String timesTitle = TimesDealUtil.getTimesTitle(dealAttrs);
        Assert.assertEquals("每次套餐详情（共10次）", timesTitle);
    }

    @Test
    public void testGetProductSinglePrice() {
        BigDecimal productSinglePrice = TimesDealUtil.getProductSinglePrice(buildProductM());
        Assert.assertEquals(productSinglePrice.stripTrailingZeros().toPlainString(), "3");
    }

    private static ProductM buildProductM() {
        ProductM productM = new ProductM();
        productM.setTradeType(19);
        productM.setAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER, "3");
        productM.setSalePrice(BigDecimal.valueOf(9));
        return productM;
    }

    @Test
    public void testHitTimesStyle1() {
        boolean b = TimesDealUtil.hitTimesStyle1(buildProductM(), "c");
        Assert.assertTrue(b);
    }

    @Test
    public void testHitTimesStyle2() {
        boolean b = TimesDealUtil.hitTimesStyle2(buildProductM(), "a", null);
        Assert.assertTrue(b);
    }

    @Test
    public void testHitTimesStyle3() {
        boolean b = TimesDealUtil.hitTimesStyle3(buildProductM(), "b", null);
        Assert.assertFalse(b);
    }

    @Test
    public void testGetProductSinglePrice2() {
        BigDecimal productSinglePrice = TimesDealUtil.getProductSinglePrice(buildProductM(), 3);
        Assert.assertEquals(productSinglePrice.intValue(), 3);
    }

    /**
     * Test getSinglePrice with division by zero to trigger exception
     */
    @Test
    public void testGetSinglePriceDivisionByZero() {
        // arrange
        BigDecimal salePrice = new BigDecimal("100.00");
        String times = "0";
        // act
        String result = TimesDealUtil.getSinglePrice(salePrice, times);
        // assert
        assertNull(result);
    }

    /**
     * Test getSinglePrice with normal valid inputs
     */
    @Test
    public void testGetSinglePriceNormalCase() {
        // arrange
        BigDecimal salePrice = new BigDecimal("100.00");
        String times = "3";
        // act
        String result = TimesDealUtil.getSinglePrice(salePrice, times);
        // assert
        assertEquals("33.34", result);
    }

    /**
     * Test getSinglePrice with null sale price
     */
    @Test
    public void testGetSinglePriceNullSalePrice() {
        // arrange
        BigDecimal salePrice = null;
        String times = "3";
        // act
        String result = TimesDealUtil.getSinglePrice(salePrice, times);
        // assert
        assertNull(result);
    }

    /**
     * Test getSinglePrice with invalid times string
     */
    @Test
    public void testGetSinglePriceInvalidTimes() {
        // arrange
        BigDecimal salePrice = new BigDecimal("100.00");
        String times = "abc";
        // act
        String result = TimesDealUtil.getSinglePrice(salePrice, times);
        // assert
        assertNull(result);
    }

    /**
     * Test when product is not a times deal, should return original sale price
     */
    @Test
    public void testGetProductSinglePrice_NotTimesDeal() {
        // arrange
        ProductM productM = mock(ProductM.class);
        BigDecimal salePrice = new BigDecimal("100.00");
        when(productM.isTimesDeal()).thenReturn(false);
        when(productM.getSalePrice()).thenReturn(salePrice);
        // act
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM, 2);
        // assert
        assertEquals(salePrice, result);
    }

    /**
     * Test normal division case for times deal
     */
    @Test
    public void testGetProductSinglePrice_TimesDeal_NormalDivision() {
        // arrange
        ProductM productM = mock(ProductM.class);
        BigDecimal salePrice = new BigDecimal("100.00");
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getSalePrice()).thenReturn(salePrice);
        // act
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM, 2);
        // assert
        assertEquals(new BigDecimal("50.00"), result);
    }

    /**
     * Test exception handling when division fails
     */
    @Test
    public void testGetProductSinglePrice_TimesDeal_DivisionError() {
        // arrange
        ProductM productM = mock(ProductM.class);
        BigDecimal salePrice = new BigDecimal("100.00");
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getSalePrice()).thenReturn(salePrice);
        // act
        // Division by zero will cause exception
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM, 0);
        // assert
        assertEquals(salePrice, result);
    }

    /**
     * Test case when product is not a times deal
     */
    @Test
    public void testHitTimesStyle3_NotTimesDeal() {
        // arrange
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.isTimesDeal()).thenReturn(false);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        // act
        boolean result = TimesDealUtil.hitTimesStyle3(productM, "b", allProducts);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when sk is blank
     */
    @Test
    public void testHitTimesStyle3_BlankSk() {
        // arrange
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.isTimesDeal()).thenReturn(true);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        // act
        boolean result = TimesDealUtil.hitTimesStyle3(productM, "", allProducts);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when all conditions are met and should return true
     */
    @Test
    public void testHitTimesStyle3_AllConditionsMet() {
        // arrange
        ProductM timesDealProductM = Mockito.mock(ProductM.class);
        ProductM singleProductM = Mockito.mock(ProductM.class);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        allProducts.put(1, singleProductM);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        when(timesDealProductM.getRelatedTimesDeal()).thenReturn(Arrays.asList(1));
        when(singleProductM.isTimesDeal()).thenReturn(false);
        when(timesDealProductM.getProductId()).thenReturn(2);
        allProducts.put(2, timesDealProductM);
        // act
        boolean result = TimesDealUtil.hitTimesStyle3(timesDealProductM, "b", allProducts);
        // assert
        assertTrue(result);
    }

    /**
     * Test case when containsSingleTimeDeal returns false
     */
    @Test
    public void testHitTimesStyle3_NoSingleTimeDeal() {
        // arrange
        ProductM timesDealProductM = Mockito.mock(ProductM.class);
        ProductM relatedProductM = Mockito.mock(ProductM.class);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        allProducts.put(1, relatedProductM);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        when(timesDealProductM.getRelatedTimesDeal()).thenReturn(Arrays.asList(1));
        // Related product is also a times deal
        when(relatedProductM.isTimesDeal()).thenReturn(true);
        // act
        boolean result = TimesDealUtil.hitTimesStyle3(timesDealProductM, "b", allProducts);
        // assert
        assertFalse(result);
    }

    /**
     * Test when product is not a times deal
     */
    @Test
    public void testGetProductSinglePriceWhenNotTimesDeal() {
        // arrange
        ProductM productM = mock(ProductM.class);
        when(productM.isTimesDeal()).thenReturn(false);
        // act
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM);
        // assert
        assertNull(result);
    }

    /**
     * Test when times attribute is blank
     */
    @Test
    public void testGetProductSinglePriceWhenTimesBlank() {
        // arrange
        ProductM productM = mock(ProductM.class);
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("");
        // act
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM);
        // assert
        assertNull(result);
    }

    /**
     * Test when times attribute is not digits
     */
    @Test
    public void testGetProductSinglePriceWhenTimesNotDigits() {
        // arrange
        ProductM productM = mock(ProductM.class);
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("abc");
        // act
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM);
        // assert
        assertNull(result);
    }

    /**
     * Test when division throws exception
     */
    @Test
    public void testGetProductSinglePriceWhenDivisionException() {
        // arrange
        ProductM productM = mock(ProductM.class);
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("0");
        when(productM.getSalePrice()).thenReturn(new BigDecimal("100"));
        // act
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM);
        // assert
        assertNull(result);
    }

    /**
     * Test successful calculation of single price
     */
    @Test
    public void testGetProductSinglePriceSuccess() {
        // arrange
        ProductM productM = mock(ProductM.class);
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("2");
        when(productM.getSalePrice()).thenReturn(new BigDecimal("100"));
        // act
        BigDecimal result = TimesDealUtil.getProductSinglePrice(productM);
        // assert
        assertEquals(new BigDecimal("50.00"), result);
    }

    @Test
    public void testTimesDealHangUpDownNotTimesDeal() throws Throwable {
        ProductM timesDealProductM = mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(false);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        assertFalse(TimesDealUtil.timesDealHangUpDown(timesDealProductM, allProducts));
    }

    @Test
    public void testTimesDealHangUpDownNoSingleTimeDeal() throws Throwable {
        ProductM timesDealProductM = mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        assertFalse(TimesDealUtil.timesDealHangUpDown(timesDealProductM, allProducts));
    }

    @Test
    public void testTimesDealHangUpDownNullProductSinglePrice() throws Throwable {
        ProductM timesDealProductM = mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        ProductM singleTimeDeal = mock(ProductM.class);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        allProducts.put(1, singleTimeDeal);
        assertFalse(TimesDealUtil.timesDealHangUpDown(timesDealProductM, allProducts));
    }

    @Test
    public void testTimesDealHangUpDownNullSinglePrice() throws Throwable {
        ProductM timesDealProductM = mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        ProductM singleTimeDeal = mock(ProductM.class);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        allProducts.put(1, singleTimeDeal);
        assertFalse(TimesDealUtil.timesDealHangUpDown(timesDealProductM, allProducts));
    }

    @Test
    public void testTimesDealHangUpDownSinglePriceLessThanProductSinglePrice() throws Throwable {
        ProductM timesDealProductM = mock(ProductM.class);
        when(timesDealProductM.isTimesDeal()).thenReturn(true);
        ProductM singleTimeDeal = mock(ProductM.class);
        Map<Integer, ProductM> allProducts = new HashMap<>();
        allProducts.put(1, singleTimeDeal);
        assertFalse(TimesDealUtil.timesDealHangUpDown(timesDealProductM, allProducts));
    }

    @Test
    public void testGenMultiTimesDealRichTitle() {
        try (MockedStatic<Lion> lion = Mockito.mockStatic(Lion.class)) {
            lion.when(() -> Lion.getList(anyString(), anyString(), any())).thenReturn(
                    com.google.common.collect.Lists.newArrayList("EXP2024122400001_c", "EXP2024122400002_c"));
            lion.when(() -> Lion.getBoolean(LionKeys.APP_KEY, LionKeys.TIMES_DEAL_OPTIMIZE_SWITCH, false))
                    .thenReturn(true);
            ProductM productM = JacksonUtils.deserialize(productMJson, ProductM.class);
            List<DouHuM> douHuMList = JacksonUtils.deserialize(douhuDataJson, List.class);
            ActivityCxt context = Mockito.mock(ActivityCxt.class);
            when(context.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);
            String result = TimesDealUtil.genMultiTimesDealRichTitle(context, productM, "/次");
            assert StringUtils.isNotBlank(result);
        }

    }

    @Test
    public void testGenSingleTimesDealRichTitle() {
        try (MockedStatic<Lion> lion = Mockito.mockStatic(Lion.class)) {
            lion.when(() -> Lion.getList(anyString(), anyString(), any())).thenReturn(
                    com.google.common.collect.Lists.newArrayList("EXP2024122400001_c", "EXP2024122400002_c"));
            lion.when(() -> Lion.getBoolean(LionKeys.APP_KEY, LionKeys.TIMES_DEAL_OPTIMIZE_SWITCH, false))
                    .thenReturn(true);
            ProductM productM = JacksonUtils.deserialize(productMJson, ProductM.class);
            List<DouHuM> douHuMList = JacksonUtils.deserialize(douhuDataJson, List.class);
            ActivityCxt context = Mockito.mock(ActivityCxt.class);
            when(context.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);
            String result = TimesDealUtil.genSingleTimesDealRichTitle(productM, context);
            assert StringUtils.isNotBlank(result);
        }

    }

    @Test
    public void testSetPricePromoTagFlag() {
        try (MockedStatic<Lion> lion = Mockito.mockStatic(Lion.class)) {
            lion.when(() -> Lion.getList(anyString(), anyString(), any())).thenReturn(
                    com.google.common.collect.Lists.newArrayList("EXP2024122400001_c", "EXP2024122400002_c"));
            lion.when(() -> Lion.getBoolean(LionKeys.APP_KEY, LionKeys.TIMES_DEAL_OPTIMIZE_SWITCH, false))
                    .thenReturn(true);
            List<ProductM> productMs = JacksonUtils.deserialize(productMsJson, List.class);
            List<DouHuM> douHuMList = JacksonUtils.deserialize(douhuDataJson, List.class);
            ActivityCxt context = Mockito.mock(ActivityCxt.class);
            when(context.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);
            TimesDealUtil.setPricePromoTagFlag(productMs, context, Lists.newArrayList());
            assert CollectionUtils.isNotEmpty(productMs);
        }

    }

    private static final String productMJson = "{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":0,\"productId\":1035747853,\"id\":null,\"categoryId\":0,\"categoryName\":null,\"spuType\":0,\"title\":\"10次卡--\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1035747853&poiid=436822180&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUKl3xCcPkuXeB9-u17IquUDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPAKpO-G1CVde4lKQZusmR5NttY2y4AdsduvBCw9Fe1CF8wkjJvxn9ajTitmtUGeBBhHZeM0BrRFl30Up1JUrkW_GOB3wnuZWYuDAmv3-ISc3LFRNtnvRLqupd7k2AbJB1XUlKQ3sRJvypMckLu84KPiQP3D2Xp3oiJt6OC7jbWaeU4KmRumEl-ABxy4nCbTOXsBoMtmN_pLcgReVR8PQ0r2xAdZVzIlaVZwBOQcZMw5H5DXNZpiBDSWGcVa3PJlHXTZRPgcFnIkUm70kYrkjiVQmj7VljsHY09Xorq0PFQx3Te--ytXE0JWX_2gy3gZNn6fT4GPj9YDBtO5MzKHblm9ymOOe5lrpxbhmeWqRzvENg\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":0,\"saleTag\":\"已售0\"},\"stock\":null,\"basePriceTag\":null,\"basePriceDesc\":null,\"basePrice\":null,\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"promoPrices\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM\",\"promoType\":0,\"promoPrice\":[\"java.math.BigDecimal\",1400],\"promoTag\":\"特惠促销共省¥480\",\"promoTagPrefix\":null,\"promoPriceTag\":\"1400\",\"marketPrice\":null,\"discount\":[\"java.math.BigDecimal\",0.75],\"discountTag\":null,\"availableTime\":null,\"userHasCard\":false,\"totalPromoPrice\":[\"java.math.BigDecimal\",480],\"totalPromoPriceTag\":\"-¥480\",\"promoItemList\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM\",\"promoId\":1035747853,\"promoTypeCode\":11,\"promoType\":\"团购优惠\",\"desc\":\"\",\"promoTag\":\"-¥480\",\"promoPrice\":[\"java.math.BigDecimal\",480],\"canAssign\":false,\"sourceType\":1,\"promoIdentity\":null,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"endTime\":0,\"remainStock\":0,\"effectiveEndTime\":0,\"couponGroupId\":null,\"couponId\":null,\"amount\":[\"java.math.BigDecimal\",480],\"minConsumptionAmount\":null,\"promotionExplanatoryTags\":null,\"promotionOtherInfoMap\":null,\"promoItemText\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemTextM\",\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null,\"title\":\"团购优惠\",\"subTitle\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"promoDivideTypeDesc\":null},\"newUser\":false}]],\"coupons\":null,\"startTime\":0,\"endTime\":0,\"promoQuantityLimit\":0,\"icon\":\"\",\"iconText\":null,\"promoTagType\":10,\"singlePrice\":[\"java.math.BigDecimal\",140],\"pricePromoInfoMap\":{\"@class\":\"java.util.HashMap\"},\"extendDisplayInfo\":{\"@class\":\"java.util.HashMap\"}}]],\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1035747853,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1035747853,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\团\\\\购\\\\详\\\\情\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"1400.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"188.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104542,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\足\\\\疗\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":3022,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceProcessArrayNew\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\流\\\\程\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2441,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceDurationInt\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\时\\\\长\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3015,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceMaterialAndTool\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3018,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathBucket\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\桶\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3019,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathMaterial\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\包\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3030,\\\\\\\"attrName\\\\\\\":\\\\\\\"freeFood\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\免\\\\费\\\\餐\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3109,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceBodyRange\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\具\\\\体\\\\服\\\\务\\\\部\\\\位\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404171,\\\\\\\"attrName\\\\\\\":\\\\\\\"Fruit\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404173,\\\\\\\"attrName\\\\\\\":\\\\\\\"TeaWater\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\茶\\\\水\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404176,\\\\\\\"attrName\\\\\\\":\\\\\\\"Snack\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2730,\\\\\\\"attrName\\\\\\\":\\\\\\\"skuCateId\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\项\\\\目\\\\分\\\\类\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":402,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1035747853,\\\"title\\\":\\\"团购详情\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"1400.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\",\\\"rawAttrValue\\\":\\\"选择服务工具/材料\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\",\\\"rawAttrValue\\\":\\\"木桶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\",\\\"rawAttrValue\\\":\\\"草本包\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\",\\\"rawAttrValue\\\":\\\"茶点水果\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\",\\\"rawAttrValue\\\":\\\"足部\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\",\\\"rawAttrValue\\\":\\\"水果拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\",\\\"rawAttrValue\\\":\\\"养生茶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\",\\\"rawAttrValue\\\":\\\"零食拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\",\\\"rawAttrValue\\\":\\\"2104542\\\",\\\"unit\\\":null,\\\"valueType\\\":402,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104542,\\\"cnName\\\":\\\"足疗\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1035747853,\\\"title\\\":\\\"团购详情\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"1400.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\"},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\"},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\"},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\"},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\"},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\"},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\"},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\"},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\"},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"attr_search_hidden_status\",\"value\":\"false\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStatusAttr\",\"value\":\"true\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"productType\",\"value\":\"deal\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"足疗\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"sys_multi_sale_number\",\"value\":\"10\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":19,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",1400],\"timesDealQueryFlag\":true,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"childProducts\":null,\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":2,\"actProductId\":1035747853,\"actProductType\":0,\"additionalDeal\":false,\"timesDeal\":true,\"dealSpuId\":0,\"top\":false,\"unifyProduct\":false}";

    private static final String douhuDataJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM\",\"abtest\":\"{}\",\"code\":\"-9\",\"msg\":null,\"expId\":\"exp003218\",\"sk\":null,\"abQueryId\":\"43b3bff8-39ac-4352-ad57-1337d9f94c7e\",\"bucket\":null,\"others\":{\"@class\":\"java.util.HashMap\"}},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM\",\"abtest\":\"{\\\"query_id\\\":\\\"9ac44df3-caea-413b-869c-e1800e1f0992\\\",\\\"ab_id\\\":\\\"EXP2024122400001_c\\\"}\",\"code\":\"200\",\"msg\":null,\"expId\":\"EXP2024122400001\",\"sk\":\"EXP2024122400001_c\",\"abQueryId\":\"9ac44df3-caea-413b-869c-e1800e1f0992\",\"bucket\":\"-2\",\"others\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"expVersion\\\"}\":\"v3\"}}]]";

    /**
     * 测试isContinuousMonthly方法 - 当商品为null时
     */
    @Test
    public void testIsContinuousMonthly_NullProduct() {
        // arrange
        ProductM productM = null;
        
        // act
        boolean result = TimesDealUtil.isContinuousMonthly(productM);
        
        // assert
        assertFalse(result);
    }
    
    /**
     * 测试isContinuousMonthly方法 - 当sys_multi_sale_type属性为null时
     */
    @Test
    public void testIsContinuousMonthly_NullAttr() {
        // arrange
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr(anyString())).thenReturn(null);
        
        // act
        boolean result = TimesDealUtil.isContinuousMonthly(productM);
        
        // assert
        assertFalse(result);
    }
    
    /**
     * 测试isContinuousMonthly方法 - 当sys_multi_sale_type属性不等于4时
     */
    @Test
    public void testIsContinuousMonthly_NotContinuousMonthly() {
        // arrange
        ProductM productM = mock(ProductM.class);
        // 使用Mockito.any()匹配任何字符串参数，但返回值为"1"，不是"4"
        when(productM.getAttr(anyString())).thenReturn("1");
        
        // act
        boolean result = TimesDealUtil.isContinuousMonthly(productM);
        
        // assert
        assertFalse(result);
    }
    
    /**
     * 测试isContinuousMonthly方法 - 当sys_multi_sale_type属性等于4时
     */
    @Test
    public void testIsContinuousMonthly_IsContinuousMonthly() {
        // arrange
        ProductM productM = mock(ProductM.class);
        // 使用Mockito.any()匹配任何字符串参数，但返回值为"4"，表示连续包月
        when(productM.getAttr(anyString())).thenReturn("是");
        
        // act
        boolean result = TimesDealUtil.isContinuousMonthly(productM);
        
        // assert
        assertTrue(result);
    }
    
    private static final String productMsJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":0,\"productId\":1035747853,\"id\":null,\"categoryId\":0,\"categoryName\":null,\"spuType\":0,\"title\":\"10次卡--\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1035747853&poiid=436822180&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUKl3xCcPkuXeB9-u17IquUDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPAKpO-G1CVde4lKQZusmR5NttY2y4AdsduvBCw9Fe1CF8wkjJvxn9ajTitmtUGeBBhHZeM0BrRFl30Up1JUrkW_xvZEONeTAdxEeimRilzUGVWo8CrobIduiX_PffiMd0-T5v2g8YznU76CyVB-EI7-bGSy0eqFVKOn4gM7O7KvfsmfJO8807mNeRr-3Ytpij-cnP5rKqbjbi05qL2lQo_z5iF-Y4BmiSQD1Ccu0S3q3FH_ioGBW2dZEJfzsAcERjNDC3CxamFqKafZou8N5B2m15iXiHkuZ4Kf9jW2md-zmm3m08mwwh92axivMgXAy2wvOv_OK8U2f52C5oBAQjpUNB38Xxj3xsyGGS5q7uWnmw\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":0,\"saleTag\":\"已售0\"},\"stock\":null,\"basePriceTag\":null,\"basePriceDesc\":null,\"basePrice\":null,\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1035747853,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1035747853,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\团\\\\购\\\\详\\\\情\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"1400.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"188.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104542,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\足\\\\疗\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":3022,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceProcessArrayNew\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\流\\\\程\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2441,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceDurationInt\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\时\\\\长\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3015,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceMaterialAndTool\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3018,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathBucket\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\桶\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3019,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathMaterial\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\包\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3030,\\\\\\\"attrName\\\\\\\":\\\\\\\"freeFood\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\免\\\\费\\\\餐\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3109,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceBodyRange\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\具\\\\体\\\\服\\\\务\\\\部\\\\位\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404171,\\\\\\\"attrName\\\\\\\":\\\\\\\"Fruit\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404173,\\\\\\\"attrName\\\\\\\":\\\\\\\"TeaWater\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\茶\\\\水\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404176,\\\\\\\"attrName\\\\\\\":\\\\\\\"Snack\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2730,\\\\\\\"attrName\\\\\\\":\\\\\\\"skuCateId\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\项\\\\目\\\\分\\\\类\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":402,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1035747853,\\\"title\\\":\\\"团购详情\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"1400.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\",\\\"rawAttrValue\\\":\\\"选择服务工具/材料\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\",\\\"rawAttrValue\\\":\\\"木桶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\",\\\"rawAttrValue\\\":\\\"草本包\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\",\\\"rawAttrValue\\\":\\\"茶点水果\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\",\\\"rawAttrValue\\\":\\\"足部\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\",\\\"rawAttrValue\\\":\\\"水果拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\",\\\"rawAttrValue\\\":\\\"养生茶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\",\\\"rawAttrValue\\\":\\\"零食拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\",\\\"rawAttrValue\\\":\\\"2104542\\\",\\\"unit\\\":null,\\\"valueType\\\":402,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104542,\\\"cnName\\\":\\\"足疗\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1035747853,\\\"title\\\":\\\"团购详情\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"1400.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\"},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\"},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\"},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\"},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\"},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\"},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\"},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\"},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\"},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"attr_search_hidden_status\",\"value\":\"false\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStatusAttr\",\"value\":\"true\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"productType\",\"value\":\"deal\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"足疗\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"sys_multi_sale_number\",\"value\":\"10\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":19,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",1400],\"timesDealQueryFlag\":true,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"childProducts\":null,\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":0,\"actProductId\":1035747853,\"actProductType\":0,\"additionalDeal\":false,\"timesDeal\":true,\"dealSpuId\":0,\"top\":false,\"unifyProduct\":false},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":1,\"productId\":1035698763,\"id\":null,\"categoryId\":0,\"categoryName\":null,\"spuType\":0,\"title\":\"【热点】多次卡测试5.0\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1035698763&poiid=436822180&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUKl3xCcPkuXeB9-u17IquUDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPCvfGYrvsINTwREjEB-NU9gWdOs_1wtu_w0jDUaxQEVFXrrmBSa0esvQg2Y1UIX5xOgx_Wm55qcpPRmYkzTh4vmOIX1aP1RpOIUK9NCOWkerzZ9xzSNDQ5QB0UjyJlt-eLMJIyb8Z_Wo04rZrVBngQYwA7-L3MfqluHckama2VNK6B2AwcumFvzxPakJ7ElH84jrEZ68zUesl-srbNAIgOd4sZgGCk7qGQsvkk6P1DPojwsctrMEI1yj2e01VVVjKPBorp5kPRWHjLNn6tp5_HegiL4ui-80d85hBxk4rI3Pr9OQXu52I5I-IvQLlTJEF1i1ZK0n_dHExiJ3K-u_VqiT26M3rUz53wjzkBdjX8k5A\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":64,\"saleTag\":\"已售64\"},\"stock\":null,\"basePriceTag\":null,\"basePriceDesc\":null,\"basePrice\":null,\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1035698763,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1035698763,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\团\\\\购\\\\详\\\\情\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"233.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"188.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104542,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\足\\\\疗\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":3022,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceProcessArrayNew\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\流\\\\程\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2441,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceDurationInt\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\时\\\\长\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3015,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceMaterialAndTool\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3018,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathBucket\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\桶\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3019,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathMaterial\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\包\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3030,\\\\\\\"attrName\\\\\\\":\\\\\\\"freeFood\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\免\\\\费\\\\餐\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3109,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceBodyRange\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\具\\\\体\\\\服\\\\务\\\\部\\\\位\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404171,\\\\\\\"attrName\\\\\\\":\\\\\\\"Fruit\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404173,\\\\\\\"attrName\\\\\\\":\\\\\\\"TeaWater\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\茶\\\\水\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404176,\\\\\\\"attrName\\\\\\\":\\\\\\\"Snack\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2730,\\\\\\\"attrName\\\\\\\":\\\\\\\"skuCateId\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\项\\\\目\\\\分\\\\类\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":402,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1035698763,\\\"title\\\":\\\"团购详情\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"233.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\",\\\"rawAttrValue\\\":\\\"选择服务工具/材料\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\",\\\"rawAttrValue\\\":\\\"木桶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\",\\\"rawAttrValue\\\":\\\"草本包\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\",\\\"rawAttrValue\\\":\\\"茶点水果\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\",\\\"rawAttrValue\\\":\\\"足部\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\",\\\"rawAttrValue\\\":\\\"水果拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\",\\\"rawAttrValue\\\":\\\"养生茶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\",\\\"rawAttrValue\\\":\\\"零食拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\",\\\"rawAttrValue\\\":\\\"2104542\\\",\\\"unit\\\":null,\\\"valueType\\\":402,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104542,\\\"cnName\\\":\\\"足疗\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1035698763,\\\"title\\\":\\\"团购详情\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"233.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\"},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\"},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\"},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\"},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\"},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\"},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\"},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\"},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\"},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"attr_search_hidden_status\",\"value\":\"false\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStatusAttr\",\"value\":\"true\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"productType\",\"value\":\"deal\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"足疗\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"sys_multi_sale_number\",\"value\":\"6\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":19,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",233],\"timesDealQueryFlag\":false,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"childProducts\":null,\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":0,\"actProductId\":1035698763,\"actProductType\":1,\"additionalDeal\":false,\"timesDeal\":true,\"dealSpuId\":0,\"top\":false,\"unifyProduct\":false},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":1,\"productId\":1023296754,\"id\":null,\"categoryId\":0,\"categoryName\":null,\"spuType\":0,\"title\":\"【热点】cs洗脚｜60分钟足疗\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1023296754&poiid=436822180&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUKl3xCcPkuXeB9-u17IquUDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPAKpO-G1CVde4lKQZusmR5NttY2y4AdsduvBCw9Fe1CF8wkjJvxn9ajTitmtUGeBBhHZeM0BrRFl30Up1JUrkW_xvZEONeTAdxEeimRilzUGVWo8CrobIduiX_PffiMd0-T5v2g8YznU76CyVB-EI7-bGSy0eqFVKOn4gM7O7KvfsmfJO8807mNeRr-3Ytpij-cnP5rKqbjbi05qL2lQo_z5iF-Y4BmiSQD1Ccu0S3q3FH_ioGBW2dZEJfzsAcERjNDC3CxamFqKafZou8N5B2m15iXiHkuZ4Kf9jW2md-zmm3m08mwwh92axivMgXAy2wvOv_OK8U2f52C5oBAQjpUNB38Xxj3xsyGGS5q7uWnmw\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":4,\"saleTag\":\"已售4\"},\"stock\":null,\"basePriceTag\":null,\"basePriceDesc\":null,\"basePrice\":null,\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1023296754,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1023296754,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\团\\\\购\\\\详\\\\情\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"99.00\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"188.00\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104542,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\足\\\\疗\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":188.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":3022,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceProcessArrayNew\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\流\\\\程\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\洗\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":10},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\底\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":50}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\洗\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":10},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\底\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":50}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2441,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceDurationInt\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\时\\\\长\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3109,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceBodyRange\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\具\\\\体\\\\服\\\\务\\\\部\\\\位\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1023296754,\\\"title\\\":\\\"团购详情\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"99.00\\\",\\\"marketPrice\\\":\\\"188.00\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":188.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"洗脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":10},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足底按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":50}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"洗脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":10},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足底按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":50}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\",\\\"rawAttrValue\\\":\\\"足部\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104542,\\\"cnName\\\":\\\"足疗\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1023296754,\\\"title\\\":\\\"团购详情\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"99.00\\\",\\\"marketPrice\\\":\\\"188.00\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":188.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"洗脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":10},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足底按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":50}]\\\"},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"attr_search_hidden_status\",\"value\":\"false\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStatusAttr\",\"value\":\"true\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"productType\",\"value\":\"deal\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"足疗\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":3,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",98.2],\"timesDealQueryFlag\":false,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"childProducts\":null,\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":0,\"actProductId\":1023296754,\"actProductType\":1,\"additionalDeal\":false,\"timesDeal\":false,\"dealSpuId\":0,\"top\":false,\"unifyProduct\":false},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":1,\"productId\":1023259531,\"id\":null,\"categoryId\":0,\"categoryName\":null,\"spuType\":0,\"title\":\"【热点】cs-多次卡60分钟｜60分钟足疗\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1023259531&poiid=436822180&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUKl3xCcPkuXeB9-u17IquUDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPCvfGYrvsINTwREjEB-NU9gWdOs_1wtu_w0jDUaxQEVFXrrmBSa0esvQg2Y1UIX5xOgx_Wm55qcpPRmYkzTh4vmOIX1aP1RpOIUK9NCOWkerzZ9xzSNDQ5QB0UjyJlt-eLMJIyb8Z_Wo04rZrVBngQYwA7-L3MfqluHckama2VNK6B2AwcumFvzxPakJ7ElH84jrEZ68zUesl-srbNAIgOd4sZgGCk7qGQsvkk6P1DPojwsctrMEI1yj2e01VVVjKPBorp5kPRWHjLNn6tp5_HegiL4ui-80d85hBxk4rI3Pr9OQXu52I5I-IvQLlTJEF1i1ZK0n_dHExiJ3K-u_VqiT26M3rUz53wjzkBdjX8k5A\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":3,\"saleTag\":\"已售3\"},\"stock\":null,\"basePriceTag\":null,\"basePriceDesc\":null,\"basePrice\":null,\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1023259531,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1023259531,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\团\\\\购\\\\详\\\\情\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"198.00\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"188.00\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104542,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\足\\\\疗\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":188.0,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":3022,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceProcessArrayNew\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\流\\\\程\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\洗\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":10},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\底\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":50}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\洗\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":10},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\底\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":50}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2441,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceDurationInt\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\时\\\\长\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3109,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceBodyRange\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\具\\\\体\\\\服\\\\务\\\\部\\\\位\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1023259531,\\\"title\\\":\\\"团购详情\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"198.00\\\",\\\"marketPrice\\\":\\\"188.00\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":188.0,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"洗脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":10},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足底按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":50}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"洗脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":10},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足底按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":50}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\",\\\"rawAttrValue\\\":\\\"足部\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104542,\\\"cnName\\\":\\\"足疗\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1023259531,\\\"title\\\":\\\"团购详情\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"198.00\\\",\\\"marketPrice\\\":\\\"188.00\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":188.0,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"洗脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":10},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足底按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":50}]\\\"},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"attr_search_hidden_status\",\"value\":\"false\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStatusAttr\",\"value\":\"true\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"productType\",\"value\":\"deal\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"足疗\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"sys_multi_sale_number\",\"value\":\"2\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":19,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",198],\"timesDealQueryFlag\":false,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"childProducts\":null,\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":0,\"actProductId\":1023259531,\"actProductType\":1,\"additionalDeal\":false,\"timesDeal\":true,\"dealSpuId\":0,\"top\":false,\"unifyProduct\":false}]]";

}
