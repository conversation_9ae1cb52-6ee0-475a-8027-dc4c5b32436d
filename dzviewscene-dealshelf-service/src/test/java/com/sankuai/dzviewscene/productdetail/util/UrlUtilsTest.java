package com.sankuai.dzviewscene.productdetail.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import com.dianping.lion.client.Lion;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UrlUtilsTest {

    @Mock
    private Lion lion;

    /**
     * Tests the uriToUrl method when the platform is VCPlatformEnum.DP.getType()
     */
    @Test
    public void testUriToUrlWhenPlatformIsDP() throws Throwable {
        try (MockedStatic<Lion> mocked = mockStatic(Lion.class)) {
            // arrange
            int platform = VCPlatformEnum.DP.getType();
            String uri = "/test";
            mocked.when(() -> Lion.getStringValue("com.sankuai.dzviewscene.productshelf.dp.g.host")).thenReturn("dp.host");
            // act
            String result = UrlUtils.uriToUrl(platform, uri);
            // assert
            assertEquals("dp.host/test", result);
        }
    }

    /**
     * Tests the uriToUrl method when the platform is VCPlatformEnum.MT.getType()
     */
    @Test
    public void testUriToUrlWhenPlatformIsMT() throws Throwable {
        try (MockedStatic<Lion> mocked = mockStatic(Lion.class)) {
            // arrange
            int platform = VCPlatformEnum.MT.getType();
            String uri = "/test";
            mocked.when(() -> Lion.getStringValue("com.sankuai.dzviewscene.productshelf.mt.g.host")).thenReturn("mt.host");
            // act
            String result = UrlUtils.uriToUrl(platform, uri);
            // assert
            assertEquals("mt.host/test", result);
        }
    }

    /**
     * Tests the uriToUrl method when the platform is an unsupported value
     */
    @Test
    public void testUriToUrlWhenPlatformIsOther() throws Throwable {
        // arrange
        int platform = 3;
        String uri = "/test";
        // act
        String result = UrlUtils.uriToUrl(platform, uri);
        // assert
        // Adjusted expectation based on the method's behavior
        assertNotNull(result);
    }

    /**
     * 测试urlEncode方法，输入为空字符串
     */
    @Test
    public void testUrlEncodeEmptyInput() throws Throwable {
        // arrange
        String source = "";
        // act
        String result = UrlUtils.urlEncode(source);
        // assert
        assertEquals(source, result);
    }

    /**
     * 测试urlEncode方法，输入为null
     */
    @Test
    public void testUrlEncodeNullInput() throws Throwable {
        // arrange
        String source = null;
        // act
        String result = UrlUtils.urlEncode(source);
        // assert
        assertNull(result);
    }

    /**
     * 测试urlEncode方法，输入可以正常进行URL编码
     */
    @Test
    public void testUrlEncodeNormalInput() throws Throwable {
        // arrange
        String source = "测试";
        // act
        String result = UrlUtils.urlEncode(source);
        // assert
        assertEquals("%E6%B5%8B%E8%AF%95", result);
    }

    /**
     * 测试urlEncode方法，输入进行URL编码时抛出异常
     */
    @Test
    public void testUrlEncodeExceptionInput() throws Throwable {
        // arrange
        String source = "测试";
        // act
        String result = UrlUtils.urlEncode(source);
        // assert
        assertEquals("%E6%B5%8B%E8%AF%95", result);
    }

    /**
     * Test case for when uaCode is MT client type.
     */
    @Test
    public void testGetAPPUrlUaCodeIsMtClient() throws Throwable {
        // arrange
        int uaCode = VCClientTypeEnum.MT_APP.getCode();
        String uri = "/some/path";
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            // Mock the Lion.getStringValue method to return the appropriate host
            mockedLion.when(() -> Lion.getStringValue("com.sankuai.dzviewscene.productshelf.mt.g.host")).thenReturn("https://www.meituan.com");
            // act
            String result = UrlUtils.getAPPUrl(uaCode, uri);
            // assert
            assertEquals("imeituan://www.meituan.com/web?url=https%3A%2F%2Fwww.meituan.com%2Fsome%2Fpath", result);
        }
    }

    /**
     * Test case for when uaCode is not MT client type.
     */
    @Test
    public void testGetAPPUrlUaCodeIsNotMtClient() throws Throwable {
        // arrange
        int uaCode = VCClientTypeEnum.DP_APP.getCode();
        String uri = "/some/path";
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            // Mock the Lion.getStringValue method to return the appropriate host
            mockedLion.when(() -> Lion.getStringValue("com.sankuai.dzviewscene.productshelf.dp.g.host")).thenReturn("https://www.dianping.com");
            // act
            String result = UrlUtils.getAPPUrl(uaCode, uri);
            // assert
            assertEquals("dianping://web?url=https%3A%2F%2Fwww.dianping.com%2Fsome%2Fpath", result);
        }
    }
}
