package com.sankuai.dzviewscene.productshelf.filterlist;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.AppContextConfiguration;
import com.sankuai.dzviewscene.FilterProductListQueryService;
import com.sankuai.dzviewscene.req.FilterProductListRequest;
import com.sankuai.dzviewscene.res.FilterProductListResponse;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.testng.collections.CollectionUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/11/23
 */
@Ignore("没有可执行的方法")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppContextConfiguration.class)
public class FilterProductListRPCServiceUnitTest {

    @Autowired
    private FilterProductListQueryService filterProductListQueryService;

    //@Test
    public void multiGetFilterProductsTest() {
        while (true) {
            FilterProductListRequest request = new FilterProductListRequest();
            request.setSceneCode("medical_productdetail_deal_vaccine_list");
            request.setCityId(1);
            request.setLat(23d);
            request.setLng(116d);
            request.setPlatform(100);
            request.setFilterId(100041620);
            FilterProductListResponse response = filterProductListQueryService.multiGetFilterProducts(request);
            Assert.assertTrue( 1 == 1);
        }
    }
}
