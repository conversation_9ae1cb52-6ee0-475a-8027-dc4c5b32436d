package com.sankuai.dzviewscene.productshelf.filterlist.roleplay;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.AppContextConfiguration;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.AggOverallReviewModel;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.request.ContentBaseRequest;
import com.sankuai.beautycontent.beautylaunchapi.model.enums.ConditionEnum;
import com.sankuai.beautycontent.beautylaunchapi.model.enums.ContentSceneEnum;
import com.sankuai.beautycontent.beautylaunchapi.model.enums.PlatEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.business.filterlist.utils.GrayControlUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.platform.detail.vo.roleplay.CommentVO;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFilterProductListVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.SCPResourceAttrEnum;
import com.sankuai.mpproduct.publish.common.enums.IdItemEnum;
import com.sankuai.mpproduct.publish.common.enums.OwnerEnum;
import com.sankuai.mpproduct.trade.api.enums.ResourceQueryStrategy;
import com.sankuai.mpproduct.trade.api.model.ResourceIdDTO;
import com.sankuai.mpproduct.trade.api.request.ResourceBatchQueryRequest;
import com.sankuai.mpproduct.trade.api.response.ResourceBatchQueryResponse;
import com.sankuai.user.collection.client.CollTypeEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 剧本杀单元测试
 */
@Ignore("没有可执行的方法")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppContextConfiguration.class)
public class FilterProductListApiTest {


    @Resource
    private ActivityEngine activityEngine;

    @Resource
    private CompositeAtomService compositeAtomService;


    //@Test
    public void test_atomService() {

        boolean hit = GrayControlUtils.hitGrayCity(7);

        CompletableFuture<Map<Long, Integer>> future2 = compositeAtomService.getMtCollectedCount(CollTypeEnum.TOUTIAO_COLL, Lists.newArrayList(5304586L,5301635L,5302663L,112L), (short) 15);
        Map<Long, Integer> map1 = future2.join();

        CompletableFuture<Map<String, Boolean>> future1 = compositeAtomService.getDpFavorStatus(Lists.newArrayList("5301635", "5304586", "5302663", "5303673"), 73, 860124758L);

        Map<String, Boolean> join = future1.join();

        CompletableFuture<Map<Long, AggOverallReviewModel>> mapCompletableFuture = compositeAtomService.queryReview(buildReviewQueryRequest(Lists.newArrayList(5304586L)));

        Map<Long, AggOverallReviewModel> map = mapCompletableFuture.join();

        CompletableFuture<ResourceBatchQueryResponse> future = compositeAtomService.batchQueryResource
                (buildResourceQueryRequest(Lists.newArrayList(5301635L, 5304586L, 5302663L, 5303673L)));
        ResourceBatchQueryResponse response = future.join();

    }

    private ResourceBatchQueryRequest buildResourceQueryRequest(List<Long> resourceIds){
        ResourceBatchQueryRequest request = new ResourceBatchQueryRequest();
        Set<ResourceIdDTO> resourceIdDTOS = Sets.newHashSet();
        resourceIds.forEach(resourceId->{
            ResourceIdDTO resourceIdDTO = new ResourceIdDTO();
            resourceIdDTO.setId(resourceId);
            resourceIdDTO.setType(IdItemEnum.GENERAL_DESKTOP_ROLE_PLAYING_GAME);
            resourceIdDTOS.add(resourceIdDTO);
        });
        Set<ResourceQueryStrategy> queryStrategies = Sets.newHashSet(ResourceQueryStrategy.BASIC, ResourceQueryStrategy.EXT);
        request.setOwnerId(OwnerEnum.NIB_GENERAL.getValue());
        request.setResourceIds(resourceIdDTOS);
        request.setQueryStrategies(queryStrategies);
        return request;
    }

    private ContentBaseRequest buildReviewQueryRequest(List<Long> resourceIds){
        int platform =1;
        ContentBaseRequest request = new ContentBaseRequest();
        request.setCityId(0);
        request.setPlatform(PlatformUtil.isMT(platform)? PlatEnum.MT.type:PlatEnum.DP.type);
        request.setSceneKey(ContentSceneEnum.JUBENSHA_LIST_STAR.extEnDesc);
        request.addExtParam(ConditionEnum.JU_BEN_SHA_DRAMA_IDS, resourceIds);
        return request;
    }

    //列表页测试
    //@Test
    public void test() {

        Object result = executeByActivityEngine(buildActivityRequest());
        System.out.println(result);

    }

    private ActivityRequest buildActivityRequest() {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(FilterListActivity.ACTIVITY_FILTER_LIST_CODE);
        paddingParams(activityRequest.getParams());
        return activityRequest;
    }

    private void paddingParams(Map<String, Object> params) {
        int platform = 201;
        String sceneCode1 = "roleplay_channel_standardproduct_list"; //热门剧本入口
        String sceneCode2 = "roleplay_landing_filterandlist"; //剧本列表筛选加首屏
        String sceneCode3 = "inactive_roleplay_landing_standardproduct_list"; //桌面剧本信息流
        String sceneCode4 = "inactive_roleplay_recommend_list"; //剧本推荐
        String sceneCode5 = "active_roleplay_landing_product_list"; //实景剧本信息流
        String sceneCode6 = "inactive_roleplay_standardproduct_detail_pin"; //剧本详情页在拼场次模块
        String sceneCode7 = "inactive_roleplay_standardproduct_detail_pin_landing"; //剧本详情页在拼场次落地页

        params.put(FilterListActivityConstants.Params.sceneCode, sceneCode1);
        params.put(FilterListActivityConstants.Params.userAgent, platform);
        params.put(FilterListActivityConstants.Params.lat, 31.230708);
        params.put(FilterListActivityConstants.Params.lng, 121.472916);
        params.put(FilterListActivityConstants.FilterParams.filterId, 5304586);

        params.put(FilterListActivityConstants.Params.traceMark, "1");
//        params.put(FilterListActivityConstants.Params.extra, "{\"activityId\":138,\"endTime\":1611050400000,\"floorId\":1017988,\"lastScene\":false,\"preScene\":false,\"productCategoryIds\":[304],\"sceneId\":138,\"startTime\":1611021600000}");
       // params.put(FilterListActivityConstants.Params.extra, "{\"activityId\":68,\"endTime\":1611662399000,\"floorId\":1140502,\"lastScene\":false,\"productCategoryIds\":[304],\"sceneId\":450,\"startTime\":1611658800000}");

        //////////////////////平台相关参数//////////////////////
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            params.put(FilterListActivityConstants.Params.platform, VCPlatformEnum.MT.getType());
            params.put(FilterListActivityConstants.Params.mtCityId, 8000);
            params.put(FilterListActivityConstants.Params.mtUserId, 0L);
            params.put(FilterListActivityConstants.Params.categoryId, 21576);
        } else {
            params.put(FilterListActivityConstants.Params.platform, VCPlatformEnum.DP.getType());
            //params.put(FilterListActivityConstants.Params.dpCityId, 4432);
            params.put(FilterListActivityConstants.Params.dpCityId, 1);
            params.put(FilterListActivityConstants.Params.dpUserId, 0L);

        }
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<DzFilterProductListVO> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }

        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        return activityResponse.getResult().join();
    }
}
