package com.sankuai.dzviewscene.productshelf.gateways.api;

import com.dianping.beauty.zone.common.adapter.ItemSearchAdapter;
import com.dianping.beauty.zone.common.parse.ItemDefaultParser;
import com.dianping.beauty.zone.common.parse.SingleParserFactory;
import com.dianping.beauty.zone.common.parse.reader.AnnotationReaderFactory;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.framework.DefaultActivityEngine;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.list.ProductListActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;

@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene", "com.sankuai.dzviewscene.nr.atom", "com.sankuai.dzviewscene.productshelf.nr", "com.sankuai.athena", "com.dianping.vc.sdk", "com.dianping.beauty.zone.common.parse.reader"})
public class ListApiTest {

    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.registerBean("itemSearchAdapter", ItemSearchAdapter.class);
        TestBeanFactory.registerBean("itemDefaultParser", ItemDefaultParser.class);
        TestBeanFactory.registerBean("singleParserFactory", SingleParserFactory.class);
        TestBeanFactory.registerBean("annotationReaderFactory", AnnotationReaderFactory.class);
        // TestBeanFactory.registerBean("annotationReaders", List<AnnotationReader>.class);
    }

    @Resource
    private ActivityEngine activityEngine;

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void test() {
        for (int i = 0; i <= 10; i++) {
            ActivityRequest activityRequest = buildActivityRequest();

            Object object = executeByActivityEngine(activityRequest);
            System.out.println(object);
        }
    }

    private ActivityRequest buildActivityRequest() {
        int platform = 100;
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.addParam(FilterListActivityConstants.Params.sceneCode, "cosmetology_landing_plastic_surgery_prepay_remote_prepaylist");
        activityRequest.setActivityCode(ProductListActivity.ACTIVITY_PRODUCT_LIST_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(FilterListActivityConstants.Params.floorId, 1174310);
        activityRequest.addParam(FilterListActivityConstants.Params.pageSize, 5);
        activityRequest.addParam(FilterListActivityConstants.Params.extra, true);
        activityRequest.addParam(FilterListActivityConstants.Params.sortStrategy, 14);
        activityRequest.addParam(FilterListActivityConstants.Params.lng, 121.472916);
        activityRequest.addParam(FilterListActivityConstants.Params.lat, 31.230708);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, 10);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, 23952254);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, 1);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, 92726325);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        return activityRequest;
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }

        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        return activityResponse.getResult().join();
    }
}
