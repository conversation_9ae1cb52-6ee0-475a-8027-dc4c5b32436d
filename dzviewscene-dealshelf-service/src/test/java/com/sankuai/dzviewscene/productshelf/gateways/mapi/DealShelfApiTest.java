package com.sankuai.dzviewscene.productshelf.gateways.mapi;

import com.dianping.beauty.zone.common.adapter.ItemSearchAdapter;
import com.dianping.beauty.zone.common.parse.ItemDefaultParser;
import com.dianping.beauty.zone.common.parse.SingleParserFactory;
import com.dianping.beauty.zone.common.parse.reader.AnnotationReaderFactory;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceEngine;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceExecutionEngine;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.faulttolerance.ActivityContextRequestBuilder;
import com.sankuai.dzviewscene.shelf.faulttolerance.dealshelf.DealShelfFTConfiguration;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.framework.DefaultActivityEngine;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;

@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene", "com.sankuai.dzviewscene.nr.atom", "com.sankuai.dzviewscene.productshelf.nr", "com.sankuai.athena", "com.dianping.vc.sdk", "com.dianping.beauty.zone.common.parse.reader"})
public class DealShelfApiTest {

    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.registerBean("itemSearchAdapter", ItemSearchAdapter.class);
        TestBeanFactory.registerBean("itemDefaultParser", ItemDefaultParser.class);
        TestBeanFactory.registerBean("singleParserFactory", SingleParserFactory.class);
        TestBeanFactory.registerBean("annotationReaderFactory", AnnotationReaderFactory.class);
        // TestBeanFactory.registerBean("annotationReaders", List<AnnotationReader>.class);
    }

    @Resource
    private ActivityEngine activityEngine;

    private FaultToleranceEngine faultToleranceEngine = new FaultToleranceExecutionEngine();

    @Resource
    private DealShelfFTConfiguration dealShelfFaultToleranceConfiguration;

    @Resource
    private ActivityContextRequestBuilder activityContextRequestBuilder;

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void test() {
        for (int i = 0; i <= 20; i++) {
            ActivityRequest activityRequest = buildActivityRequest();

            Object object = executeByActivityEngine(activityRequest);
            System.out.println(object);
        }
    }

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void testDealShelfDegrade() {
        for (int i = 0; i <= 20; i++) {
            ActivityContextRequest request = activityContextRequestBuilder.buildActivityContextRequest(getActivityContextRequest());
            DzShelfResponseVO dealShelfResponseVO = faultToleranceEngine.execute(request, dealShelfFaultToleranceConfiguration);
            System.out.println(dealShelfResponseVO);
        }
    }

    private ActivityContextRequest getActivityContextRequest() {
        ActivityContextRequest request = new ActivityContextRequest();
        request.setShopId(1464093879);
        request.setPlatform(100);
        request.setCityId(1);
        return request;
    }

    private ActivityRequest buildActivityRequest() {
        int platform = 100;
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, "12.35.6");
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, "fdasdafads");
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, 10);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, 23952254);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, 1);
            activityRequest.addParam(ShelfActivityConstants.Params.dpUserId, 1216431730L);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, 1339170441);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, "ios");
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, "life_psychological_poi_deal_filter_products_shelf");
        activityRequest.addParam(ShelfActivityConstants.Params.keyword, "亲子");
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        return activityRequest;
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }

        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        return activityResponse.getResult().join();
    }
}
