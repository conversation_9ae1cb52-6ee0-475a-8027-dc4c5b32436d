package com.sankuai.dzviewscene.productshelf.gateways.mapi;

import com.dianping.beauty.zone.common.adapter.ItemSearchAdapter;
import com.dianping.beauty.zone.common.parse.ItemDefaultParser;
import com.dianping.beauty.zone.common.parse.SingleParserFactory;
import com.dianping.beauty.zone.common.parse.reader.AnnotationReaderFactory;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.framework.DefaultActivityEngine;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;

@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene", "com.sankuai.athena", "com.dianping.vc.sdk", "com.dianping.beauty.zone.common.parse.reader"})
public class ProductShelfApiTest {

    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.registerBean("itemSearchAdapter", ItemSearchAdapter.class);
        TestBeanFactory.registerBean("itemDefaultParser", ItemDefaultParser.class);
        TestBeanFactory.registerBean("singleParserFactory", SingleParserFactory.class);
        TestBeanFactory.registerBean("annotationReaderFactory", AnnotationReaderFactory.class);
        // TestBeanFactory.registerBean("annotationReaders", List<AnnotationReader>.class);
    }

    @Resource
    private ActivityEngine activityEngine;

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void test() {
        ActivityRequest activityRequest = buildActivityRequest();
        Object object = executeByActivityEngine(activityRequest);
        System.out.println("结果为：" + JsonCodec.encodeWithUTF8(object));
    }

    private ActivityRequest buildActivityRequest() {
        int platform = 100;
        int shopid = 1731552270;
        String moduleName = null;
        String sceneCode = null;
        String shopuuid = null;
        String unionId = null;
        String deviceId = null;
        String client = "ios";
        String searchkeyword = null;

        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.moduleName, moduleName);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, sceneCode);
        activityRequest.addParam(ShelfActivityConstants.Params.shopUuid, shopuuid);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, unionId);
        activityRequest.addParam(ShelfActivityConstants.Params.deviceId, deviceId);
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, client);
        activityRequest.addParam(ShelfActivityConstants.Params.keyword, searchkeyword);
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.productFilter);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, PlatformUtil.getPlatform(platform));
        if (PlatformUtil.isMT(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, 10);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, shopid);
//            activityRequest.addParam(ShelfActivityConstants.Params.mtUserId, 0);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, 1);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, shopid);
//            activityRequest.addParam(ShelfActivityConstants.Params.dpUserId, 0);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        return activityRequest;
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }

        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        return activityResponse.getResult().join();
    }
}
