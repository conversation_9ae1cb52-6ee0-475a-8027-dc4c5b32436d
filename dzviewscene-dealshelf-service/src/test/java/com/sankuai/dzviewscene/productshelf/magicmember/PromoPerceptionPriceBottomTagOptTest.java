package com.sankuai.dzviewscene.productshelf.magicmember;


import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.AppContextConfiguration;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag.PromoPerceptionPriceBottomTagOpt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo.MagicalMemberPromoTagBuildStrategy;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.testng.collections.Maps;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = AppContextConfiguration.class)
public class PromoPerceptionPriceBottomTagOptTest {

//    @Mock
//    private MagicalMemberPromoTagBuildStrategy magicalMemberPromoTagBuildStrategy;

//    @InjectMocks
//    @Resource
//    private PromoPerceptionPriceBottomTagOpt promoPerceptionPriceBottomTagOpt;

    @Test
    public void testPromoPerceptionPriceBottomTagOptWithMagicalMember() throws NoSuchFieldException, IllegalAccessException {
        // 初始化上下文和配置
        ActivityCxt context = new ActivityCxt();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(ShelfActivityConstants.Params.userAgent, 200);
        parameters.put(ShelfActivityConstants.Params.appVersion, "10.20.400");
        context.setParameters(parameters);

        PromoPerceptionPriceBottomTagOpt.Config config = new PromoPerceptionPriceBottomTagOpt.Config();

        PriceBottomTagBuildCfg priceBottomTagBuildCfg = new PriceBottomTagBuildCfg();
        priceBottomTagBuildCfg.setDpRadio(3.25);
        priceBottomTagBuildCfg.setMtRadio(3.25);
        priceBottomTagBuildCfg.setNibBiz("nib.general.medical_cosmetic");
        config.setMagicalMemberTagConfig(priceBottomTagBuildCfg);
        config.setPopType(3);

        // 构建测试商品信息
        ProductM productM = new ProductM();
        productM.setMarketPrice("102");

        // 构建促销价格信息，包括神会员促销
        ProductPromoPriceM magicalMemberPromo = new ProductPromoPriceM();
        magicalMemberPromo.setPromoType(PromoTypeEnum.DIRECT_PROMO.getType()); // 神会员促销类型
        magicalMemberPromo.setPromoPrice(new BigDecimal("99.99")); // 假设的神会员促销价格

        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromotionExplanatoryTags(Lists.newArrayList(3));
        Map<String, String> otherInfoMap = Maps.newHashMap();
        otherInfoMap.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "true");
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "false");
        otherInfoMap.put(PromotionPropertyEnum.TSP_COUPON_ID.getValue(), "111");
        otherInfoMap.put(PromotionPropertyEnum.TSP_COUPON_GROUP_ID.getValue(), "222");
        otherInfoMap.put(PromotionPropertyEnum.BIZ_TOKEN.getValue(), "xseikwoe");
        otherInfoMap.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "1");
        promoItemM.setPromotionOtherInfoMap(otherInfoMap);
        promoItemM.setAmount(new BigDecimal("10"));
        magicalMemberPromo.setPromoItemList(Lists.newArrayList(promoItemM));

        // 将促销信息添加到商品中
        productM.setPromoPrices(Lists.newArrayList(magicalMemberPromo));

        // 构建参数
        ProductPriceBottomTagVP.Param param = ProductPriceBottomTagVP.Param.builder()
                .productM(productM)
                .platform(VCPlatformEnum.MT.getType())
                .salePrice("100")
                .build();

        // 执行测试方法
        PromoPerceptionPriceBottomTagOpt promoPerceptionPriceBottomTagOpt = new PromoPerceptionPriceBottomTagOpt();
        Field field = PromoPerceptionPriceBottomTagOpt.class.getDeclaredField("magicalMemberPromoTagBuildStrategy");
        field.setAccessible(true);
        field.set(promoPerceptionPriceBottomTagOpt, new MagicalMemberPromoTagBuildStrategy());

        List<DzTagVO> resultTags = promoPerceptionPriceBottomTagOpt.compute(context, param, config);


        // 验证结果
        assertNotNull(resultTags); // 确保结果非空
        assertFalse(resultTags.isEmpty()); // 确保有标签生成
        DzTagVO magicalMemberTag = resultTags.get(0); // 获取第一个标签，假设是神会员标签
        assertEquals(DzPromoUtils.MAGICAL_MEMBER_TAG_NAME, magicalMemberTag.getName()); // 验证标签文本是否为神会员
        assertNotNull(magicalMemberTag.getPromoDetail()); // 验证标签文本是否为神会员
    }

}
