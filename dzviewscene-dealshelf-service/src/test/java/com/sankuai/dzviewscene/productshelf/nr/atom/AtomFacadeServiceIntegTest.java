package com.sankuai.dzviewscene.productshelf.nr.atom;

import com.meituan.carnation.industry.dto.MedicineCityRequest;
import com.meituan.carnation.industry.dto.brand.BrandItemDTO;
import com.meituan.carnation.industry.dto.brand.BrandQueryRequest;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.productshelf.nr.assemble.biz.FilterType;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.FilterTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFilterBtnVO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;

import java.util.List;

/**
 * @auther: liweilong06
 * @date: 2020/3/22 10:47 下午
 */
@Ignore
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.productshelf.nr.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AtomFacadeServiceIntegTest {

    static {
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
    }

    private boolean brandResultNotNull(com.meituan.carnation.industry.dto.IResponse<List<BrandItemDTO>> result) {
        return result != null && result.isSuccess() && result.getResult() != null && CollectionUtils.isNotEmpty(result.getResult());
    }

    private BrandQueryRequest buildBrandRequest() {
        BrandQueryRequest brandQueryRequest = new BrandQueryRequest();
        brandQueryRequest.setCityId(10);
        brandQueryRequest.setPlatformType(2);
        brandQueryRequest.setTagId(1063);
        return brandQueryRequest;
    }

    private DzFilterBtnVO buildMoreFilterBtn(String filterName, FilterType filterType, long filterId) {
        return buildMoreFilterBtn(filterName, filterType, filterId, filterType.name() + "#" + filterId + "#" + filterName);
    }

    private DzFilterBtnVO buildMoreFilterBtn(String filterName, FilterType filterType, long filterId, String extra) {
        DzFilterBtnVO filter = new DzFilterBtnVO();
        filter.setId(filterId);
        filter.setType(FilterTypeEnum.More.id);
        filter.setName(filterName);
        filter.setSelectable(true);
        filter.setMultiSelect(false);
        filter.setExtra(extra);
        return filter;
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_01_remoteCity() {
        MedicineCityRequest cityRequest = new MedicineCityRequest();
        cityRequest.setCityId(1);
        cityRequest.setNearestOpenCityNum(5);
        cityRequest.setPlatform(1);
        cityRequest.setQueryNearestOpenCity(true);
//        com.meituan.carnation.industry.dto.IResponse<MedicineCityDTO> result = atomFacadeService.multiGetMedicalOpenCity(cityRequest).join();
//        logger.info("结果为：" + JsonCodec.encodeWithUTF8(result));
    }

}
