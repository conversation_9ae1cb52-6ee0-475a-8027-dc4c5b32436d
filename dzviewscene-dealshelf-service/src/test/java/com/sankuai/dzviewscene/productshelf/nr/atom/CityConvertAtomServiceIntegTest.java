package com.sankuai.dzviewscene.productshelf.nr.atom;

import com.dianping.gis.remote.dto.*;
import com.dianping.gis.remote.enums.RegionType;
import com.dianping.gis.remote.service.CityInfoService;
import com.dianping.gis.remote.service.HotRegionStationService;
import com.dianping.gis.remote.service.MtForeignCityService;
import com.dianping.gis.remote.service.RegionInfoService;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.api.request.QueryActivityRequest;
import com.dianping.gmkt.activity.api.response.QueryActivityResponse;
import com.dianping.gmkt.activity.api.service.DealActivityQueryService;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.poi.areacommon.AreaCommonService;
import com.dianping.tpfun.product.api.data.ProductDataTableViewService;
import com.dianping.tpfun.product.api.data.model.ProductMetaAttrDataTableVO;
import com.dianping.tpfun.product.api.data.request.QueryDataTableRequest;
import com.dianping.tpfun.product.api.meta.enums.MetaEntityTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.service.mobile.group.geo.bean.AreaInfo;
import com.meituan.service.mobile.group.geo.bean.SubwayLine;
import com.meituan.service.mobile.group.geo.bean.SubwayStation;
import com.meituan.service.mobile.group.geo.service.AreaService;
import com.meituan.service.mobile.group.geo.service.SubwayService;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.FilterTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFilterBtnVO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @auther: liweilong06
 * @date: 2020/3/3 1:21 下午
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dztheme.common.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class CityConvertAtomServiceIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(CityConvertAtomServiceIntegTest.class);

    @RpcClient(url = "http://service.dianping.com/com.dianping.poi.areacommon.AreaCommonService_1.0.0")
    private AreaCommonService areaCommonService;
    @RpcClient(url = "http://service.dianping.com/gisService/cityInfoService_1.0.0")
    private CityInfoService cityInfoService;
    @RpcClient(url = "http://service.dianping.com/gisService/regionInfoService_1.0.0")
    private RegionInfoService regionInfoService;
    @RpcClient(url = "http://service.dianping.com/gisService/mtForeignCityService_1.0.0")
    private MtForeignCityService mtForeignCityService;
    @RpcClient(url = "http://service.dianping.com/gisService/hotRegionStationService_1.0.0")
    private HotRegionStationService hotRegionStationService;
    @Resource
    private AreaService areaService;
    @Resource
    private SubwayService subwayService;

    @RpcClient(url = "http://service.dianping.com/gmkt_activity_service/DealActivityQueryService_0.0.1")
    private DealActivityQueryService dealActivityQueryService;

    @RpcClient(url = "http://service.dianping.com/tpfunService/productDataTableViewService_1.0.0")
    private ProductDataTableViewService productDataTableViewService;

    static {
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
    }

    private static Integer dpCityId = 5;

    private static Integer mtCityId = 10;

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_01_getMtCityId() {
        Integer mtCityId = areaCommonService.getMtCityByDpCity(dpCityId);
        logger.info("点评城市" + dpCityId + "转换结果为：" + mtCityId);
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_05_areaInfo() {
        // TODO 行政区信息
        RegionInfoDTO result = regionInfoService.loadRegionInfo(10);
        logger.info("结果为：" + JsonCodec.encode(result));
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_05_area() {
        // TODO 行政区
        List<RegionInfoDTO> result = regionInfoService.findRegionListByCityId(1, RegionType.District, 0, 1000);
        logger.info("结果为：" + JsonCodec.encode(result));
        // TODO 商圈
        List<RegionInfoDTO> dic = regionInfoService.findRegionListByCityId(1, RegionType.BusinessDistrict, 0, 1000);
        logger.info("结果为：" + JsonCodec.encode(dic));
        // TODO 子城市
        List<CityInfoDTO> citys = cityInfoService.getChildCityList(1, true);
        logger.info("结果为：" + JsonCodec.encode(citys));
        // TODO 地铁站
        List<RegionInfoDTO> info = regionInfoService.findChildRegionList(10, RegionType.BusinessDistrict, true, 0, 50);
        logger.info("结果为：" + JsonCodec.encode(info));
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_05_childCity() {
        // TODO 子城市
        List<CityInfoDTO> citys = cityInfoService.getChildCityList(1, true);
        logger.info("结果为：" + JsonCodec.encode(citys));
        // TODO 行政区
        List<RegionInfoDTO> result = regionInfoService.findRegionListByCityId(citys.get(0).getCityId(), RegionType.District, 0, 1000);
        logger.info("结果为：" + JsonCodec.encode(result));
        // TODO 商圈
        List<RegionInfoDTO> dic = regionInfoService.findRegionListByCityId(citys.get(0).getCityId(), RegionType.BusinessDistrict, 0, 1000);
        logger.info("结果为：" + JsonCodec.encode(dic));
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_05_hot() {
        // TODO 热门商圈
        HotRegionStationDTO result = hotRegionStationService.getHotRegionAndStation(1);
        logger.info("结果为：" + JsonCodec.encode(result));
        List<RegionInfoDTO> regionInfoDTOS = regionInfoService.findRegions( result.getHotRegions());
        logger.info("结果为：" + JsonCodec.encode(regionInfoDTOS));
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_05_subStation() {
        // TODO 地铁线
        List<RegionInfoDTO> result = regionInfoService.findRegionListByCityId(1, RegionType.MetroLine, 0, 50);
        logger.info("结果为：" + JsonCodec.encode(result));
        // TODO 地铁站
        List<RegionInfoDTO> station = regionInfoService.findChildRegionList(1325, RegionType.MetroStation, true, 0, 50);
        logger.info("结果为：" + JsonCodec.encode(station));
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_06_getSubwayLine() {
        List<SubwayDTO> result = regionInfoService.findCitySubways(1);
        logger.info("结果为：" + JsonCodec.encode(result));
    }


    @Environment(AthenaEnv.Test)
    //@Test
    public void test_06_getSubwayStation() {
        List<StationDTO> result = mtForeignCityService.findStationsByLineId(1326);
        logger.info("结果为：" + JsonCodec.encode(result));
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_mt_city() {
        // TODO 商圈
        long startTime = System.currentTimeMillis();
        List<AreaInfo> result = areaService.listByCity(10);
        logger.info("结果为：" + JsonCodec.encode(result));
        // 热门商圈
        List<AreaInfo> hot = result.stream().filter(areaInfo -> areaInfo.getHot()).collect(Collectors.toList());
        logger.info("结果为：" + JsonCodec.encode(hot));
        long endTime = System.currentTimeMillis();
        logger.info("耗时为：" + (endTime - startTime) + "ms");
    }


    @Environment(AthenaEnv.Product)
    //@Test
    public void test_mt_city_info() {
        DzFilterBtnVO dzFilterBtnVO = buildMtAreaFilter();
        int a = 0;
    }


    @Environment(AthenaEnv.Product)
    //@Test
    public void test_mt_subway_info() {
        DzFilterBtnVO dzFilterBtnVO = buildMtSubwayFilter(10);
        int a = 0;
    }

    private DzFilterBtnVO buildMtSubwayFilter(int cityId) {
        List<SubwayLine> subwayLines = subwayService.listSubwayLineByCity(cityId);
        if (CollectionUtils.isEmpty(subwayLines)) {
            return null;
        }
        DzFilterBtnVO returnValue = buildSingleFilterVO(0, "地铁", FilterTypeEnum.SubwayLine);
        // 地铁站
        for (SubwayLine subwayLine : subwayLines) {
            DzFilterBtnVO subwayLineFilter = buildSingleFilterVO(subwayLine.getId(), subwayLine.getName(), FilterTypeEnum.SubwayLine);
            // 地铁线增加地铁站子节点
            addChild(subwayLineFilter, buildMtSubwayStation(subwayLine.getId()));
            // 将地铁线加入到返回结果中
            addChild(returnValue, subwayLineFilter);
        }
        return returnValue;
    }

    private List<DzFilterBtnVO> buildMtSubwayStation(int subwayLineId) {
        List<SubwayStation> subwayStations = subwayService.listSubwayStation(subwayLineId);
        if (CollectionUtils.isEmpty(subwayStations)) {
            return null;
        }
        return subwayStations.stream()
                .map(subwayStation -> buildSingleFilterVO(subwayStation.getId(), subwayStation.getName(), FilterTypeEnum.SubwayStation))
                .collect(Collectors.toList());
    }

    private DzFilterBtnVO buildMtAreaFilter() {
        List<AreaInfo> areaInfos = areaService.listByCity(10);
        DzFilterBtnVO returnValue = buildSingleFilterVO(0, "商圈", FilterTypeEnum.Region);
        // 附近
        addChild(returnValue, buildMtNearbyFilter());
        if (CollectionUtils.isEmpty(areaInfos)) {
            return returnValue;
        }
        // 推家商圈
        addChild(returnValue, buildMtRecommendRegionFilter(areaInfos));
        // 各个地区
        addChild(returnValue, buildMtTotalRegionFilter(areaInfos));
        return returnValue;
    }

    private void addChild(DzFilterBtnVO returnValue, DzFilterBtnVO child) {
        if (returnValue == null || child == null) {
            return;
        }
        if (returnValue.getChildren() == null) {
            returnValue.setChildren(new ArrayList<>());
        }
        returnValue.getChildren().add(child);
    }

    private DzFilterBtnVO buildMtRecommendRegionFilter(List<AreaInfo> areaInfos) {
        List<AreaInfo> hot = areaInfos.stream().filter(areaInfo -> areaInfo.getHot()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hot)) {
            return null;
        }
        List<DzFilterBtnVO> filterVOList = hot.stream()
                .map(areaInfo -> buildSingleFilterVO(areaInfo.getId(), areaInfo.getName(), FilterTypeEnum.Region))
                .collect(Collectors.toList());
        DzFilterBtnVO returnValue = buildSingleFilterVO(0, "推荐商圈", FilterTypeEnum.Region);
        returnValue.setChildren(filterVOList);
        return returnValue;
    }

    private List<DzFilterBtnVO> buildMtTotalRegionFilter(List<AreaInfo> areaInfos) {
        // 首先组装顶级行政区
        List<DzFilterBtnVO> returnValue = createMtRegionFirstFloor(areaInfos);
        // 组装子选项
        areaInfos.stream()
                .filter(areaInfo -> areaInfo.getDistrictId() != 0)
                .forEach(areaInfo -> addChildByParentId(returnValue, areaInfo));
        return returnValue;
    }

    private List<DzFilterBtnVO> createMtRegionFirstFloor(List<AreaInfo> areaInfos) {
        return areaInfos.stream()
                .filter(areaInfo -> areaInfo.getDistrictId() == 0)
                .map(areaInfo -> buildSingleFilterVO(areaInfo.getId(), areaInfo.getName(), FilterTypeEnum.Region))
                .collect(Collectors.toList());
    }

    private void addChildByParentId(List<DzFilterBtnVO> returnValue, AreaInfo areaInfo) {
        // 理论上只有一个父节点
        List<DzFilterBtnVO> parents = returnValue.stream()
                .filter(dzFilterVO -> dzFilterVO.getId() == areaInfo.getDistrictId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parents)) {
            return;
        }
        addChild(parents.get(0), buildSingleFilterVO(areaInfo.getId(), areaInfo.getName(), FilterTypeEnum.Region));
    }


    private DzFilterBtnVO buildMtNearbyFilter() {
        List<DzFilterBtnVO> filterVOList = new ArrayList<>();
        filterVOList.add(buildSingleFilterVO(-1, "附近", FilterTypeEnum.Distance));
        filterVOList.add(buildSingleFilterVO(1000, "1km", FilterTypeEnum.Distance));
        filterVOList.add(buildSingleFilterVO(3000, "3km", FilterTypeEnum.Distance));
        filterVOList.add(buildSingleFilterVO(5000, "5km", FilterTypeEnum.Distance));
        filterVOList.add(buildSingleFilterVO(10000, "10km", FilterTypeEnum.Distance));
        filterVOList.add(buildSingleFilterVO(-2, "全城", FilterTypeEnum.Distance));

        DzFilterBtnVO returnValue = buildSingleFilterVO(0, "附近", FilterTypeEnum.Distance);
        returnValue.setChildren(filterVOList);
        return returnValue;
    }


    private void addChild(DzFilterBtnVO returnValue, List<DzFilterBtnVO> children) {
        if (returnValue == null || CollectionUtils.isEmpty(children)) {
            return;
        }
        if (returnValue.getChildren() == null) {
            returnValue.setChildren(new ArrayList<>());
        }
        returnValue.getChildren().addAll(children);
    }


    private DzFilterBtnVO buildSingleFilterVO(int id, String name, FilterTypeEnum filterType) {
        DzFilterBtnVO filterVO = new DzFilterBtnVO();
        filterVO.setId(id);
        filterVO.setName(name);
        filterVO.setSelectable(true);
        filterVO.setType(filterType.id);
        return filterVO;
    }


    @Environment(AthenaEnv.Test)
    //@Test
    public void test_mt_subway() {
        long startTime = System.currentTimeMillis();
        // 地铁线
        List<SubwayLine> result = subwayService.listSubwayLineByCity(10);
        logger.info("结果为：" + JsonCodec.encode(result));
        // 地铁站
        for (SubwayLine subwayLine : result) {
            List<SubwayStation> stations = subwayService.listSubwayStation(result.get(0).getId());
        }
        long endTime = System.currentTimeMillis();
        logger.info("耗时为：" + (endTime - startTime) + "ms");
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_activity() {
        long startTime = System.currentTimeMillis();
        // {"result":"000000","resultString":"\u6210\u529f","categoryPic":"https://p1.meituan.net/dprainbow/f5caa55e5beac012921d0bddddacfd234235.png","categoryText":"\u6297\u8870\u6708|\u9650\u65f66\u6298\u8d77","picWidth":229,"picHeight":36,"pageId":2291954,"itemActivityInfoDTOs":null,"longTitleActivityDTO":{"valid":true,"mainTitle":"\u6297\u8870\u6708","titleColorBefore":"#ff363c","titleColorAfter":"#7100e4","leftTitleBefore":"\u70b9\u51fb\u7b5b\u9009","leftTitleAfter":"\u5df2\u7b5b\u9009","rightTitleBefore":"\u6d3b\u52a8\u5546\u6237","rightTitleAfter":"\u6d3b\u52a8\u5546\u6237"}}
        // 活动
        QueryActivityRequest request = new QueryActivityRequest();
        request.setCategory(20423);
        request.setPlatform(AppPlatform.MT);
        request.setChannel(1);
        request.setMtCity(10);
        request.setSource(RequestSource.SearchListTitle);
        QueryActivityResponse result = dealActivityQueryService.queryPageByCategory(request);
        logger.info("结果为：" + JsonCodec.encode(result));
        long endTime = System.currentTimeMillis();
        logger.info("耗时为：" + (endTime - startTime) + "ms");
    }


    @Environment(AthenaEnv.Product)
    //@Test
    public void test_filter() {
        long startTime = System.currentTimeMillis();
        long spuType = 2015L;
        long metaAttrId = 975L;
        // {"result":"000000","resultString":"\u6210\u529f","categoryPic":"https://p1.meituan.net/dprainbow/f5caa55e5beac012921d0bddddacfd234235.png","categoryText":"\u6297\u8870\u6708|\u9650\u65f66\u6298\u8d77","picWidth":229,"picHeight":36,"pageId":2291954,"itemActivityInfoDTOs":null,"longTitleActivityDTO":{"valid":true,"mainTitle":"\u6297\u8870\u6708","titleColorBefore":"#ff363c","titleColorAfter":"#7100e4","leftTitleBefore":"\u70b9\u51fb\u7b5b\u9009","leftTitleAfter":"\u5df2\u7b5b\u9009","rightTitleBefore":"\u6d3b\u52a8\u5546\u6237","rightTitleAfter":"\u6d3b\u52a8\u5546\u6237"}}
        QueryDataTableRequest request = new QueryDataTableRequest();
        request.setSpuType(spuType);
        request.setMetaAttrId(metaAttrId);
        for (MetaEntityTypeEnum metaEntityTypeEnum : MetaEntityTypeEnum.values()) {
            try {
                request.setEntityType(metaEntityTypeEnum.value);
                ProductMetaAttrDataTableVO result = productDataTableViewService.queryDataTableVO(request);
                logger.info(metaEntityTypeEnum.name() + "结果为：" + JsonCodec.encode(result));
                Thread.sleep(200);
            } catch (Exception e) {
                //e.printStackTrace();
                logger.info(metaEntityTypeEnum.name() + "读取失败：request=" + JsonCodec.encode(request));
                logger.info(e.getMessage());
            }
        }
        long endTime = System.currentTimeMillis();
        logger.info("耗时为：" + (endTime - startTime) + "ms");

        Map<Long,ProductMetaAttrDataTableVO> valueResult = productDataTableViewService.getDataTableVOMapBySpuTypeAndEntityType(spuType, MetaEntityTypeEnum.GOODS.value);
        logger.info("结果为：" + JsonCodec.encode(valueResult));
    }


}
