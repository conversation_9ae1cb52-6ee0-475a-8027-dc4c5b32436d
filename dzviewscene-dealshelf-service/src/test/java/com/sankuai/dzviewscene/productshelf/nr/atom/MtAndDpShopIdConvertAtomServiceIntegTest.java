package com.sankuai.dzviewscene.productshelf.nr.atom;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.google.common.collect.Lists;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * @auther: liweilong06
 * @date: 2020/3/22 10:47 下午
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.productshelf.nr.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class MtAndDpShopIdConvertAtomServiceIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(MtAndDpShopIdConvertAtomServiceIntegTest.class);

    static {
        TestBeanFactory.registerBean("shopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
    }

    @Autowired
    private AtomFacadeService mtAndDpShopIdConvertAtomService;

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_01_queryMtByDpShopIds() throws Exception {
        List<Integer> shopIds = new ArrayList<>();
        shopIds.add(446691893);
        Map<Integer, List<Integer>> idMap = mtAndDpShopIdConvertAtomService.queryMtByDpShopIds(shopIds).join();
        logger.info("获取到的结果为：" + JsonCodec.encode(idMap));
        Assert.assertTrue(MapUtils.isNotEmpty(idMap));
        Map<Integer, Integer> dpMtIdMap = new HashMap<>();
        for (Integer dpId : idMap.keySet()) {
            dpMtIdMap.put(dpId, idMap.get(dpId).get(0));
        }
        logger.info("获取到的美团点评IdMap为：" + JsonCodec.encode(dpMtIdMap));
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_02_queryDpByMtShopIds() {
        List<Integer> shopIds = new ArrayList<>();
        shopIds.add(4099235);
        shopIds.add(385624);
        shopIds.add(4430216);
        Map<Integer, List<Integer>> idMap = mtAndDpShopIdConvertAtomService.queryDpByMtShopIds(shopIds).join();
        logger.info("获取到的结果为：" + JsonCodec.encode(idMap));
        Assert.assertTrue(MapUtils.isNotEmpty(idMap));
        Map<Integer, Integer> dpMtIdMap = new HashMap<>();
        for (Integer mtId : idMap.keySet()) {
            dpMtIdMap.put(idMap.get(mtId).get(0), mtId);
        }
        logger.info("获取到的美团点评IdMap为：" + JsonCodec.encode(dpMtIdMap));
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_02_queryDpByMtShopIdsForPro() throws Exception {
        List<Integer> shopIds = new ArrayList<>();
        shopIds.add(164613609);
        Map<Integer, List<Integer>> idMap = mtAndDpShopIdConvertAtomService.queryDpByMtShopIds(shopIds).join();
        logger.info("获取到的结果为：" + JsonCodec.encode(idMap));
        Assert.assertTrue(MapUtils.isNotEmpty(idMap));
        Map<Integer, Integer> dpMtIdMap = new HashMap<>();
        for (Integer mtId : idMap.keySet()) {
            dpMtIdMap.put(idMap.get(mtId).get(0), mtId);
        }
        logger.info("获取到的美团点评IdMap为：" + JsonCodec.encode(dpMtIdMap));
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_03_queryMtByDpShopIdsForPro() throws Exception {
        List<Integer> shopIds = new ArrayList<>();
        shopIds.add(1885742585);
        Map<Integer, List<Integer>> idMap = mtAndDpShopIdConvertAtomService.queryMtByDpShopIds(shopIds).join();
        logger.info("获取到的结果为：" + JsonCodec.encode(idMap));
        Assert.assertTrue(MapUtils.isNotEmpty(idMap));
        Map<Integer, Integer> dpMtIdMap = new HashMap<>();
        for (Integer dpId : idMap.keySet()) {
            dpMtIdMap.put(dpId, idMap.get(dpId).get(0));
        }
        logger.info("获取到的dp->mt为：" + JsonCodec.encode(dpMtIdMap));
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_04_getMtIdByDpId() {
        List<Integer> shopIds = new ArrayList<>();
        shopIds.addAll(Arrays.asList(111575143, 1224980430,97827467));

        List<List<Integer>> shopIdList = Lists.partition(shopIds, 40);
        Map<Integer, Integer> dpToMtShopIds = new HashMap<>();

        for (List<Integer> perShopId : shopIdList) {
            List<Integer> actRequestIds = new ArrayList<>();
            actRequestIds.addAll(perShopId);
            Map<Integer, List<Integer>> idMap = mtAndDpShopIdConvertAtomService.queryMtByDpShopIds(actRequestIds).join();
            for (Integer dpShopId : idMap.keySet()) {
                dpToMtShopIds.put(dpShopId, idMap.get(dpShopId).get(0));
            }
        }

        StringBuffer strB = new StringBuffer("dpId,mtId");
        dpToMtShopIds.forEach((dpId, mtId) -> {
            strB.append(dpId).append(",").append(mtId).append("###");
        });

        logger.info("获取到的Dp团单Id为：" + strB.toString());
    }

}
