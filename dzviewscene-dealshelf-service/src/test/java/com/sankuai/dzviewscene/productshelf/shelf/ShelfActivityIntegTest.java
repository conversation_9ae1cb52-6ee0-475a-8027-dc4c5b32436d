package com.sankuai.dzviewscene.productshelf.shelf;

import com.dianping.gateway.client.debug.DEBUG;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.google.gson.Gson;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.framework.DefaultActivityEngine;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFilterProductListVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 货架联合测试
 */
@Ignore("没有可执行的方法")
@Slf4j
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene", "com.sankuai.athena","com.dianping.vc.sdk"})
public class ShelfActivityIntegTest {

    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-core.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
    }

    @Resource
    private ActivityEngine activityEngine;

    private Gson gson = new Gson();

    @Environment(value = AthenaEnv.Test, swimlane = "waimai-eci-210819-161931-325")
    //@Test
    public void test() {
        for(int i =0 ; i <= 10; i++ ) {
            ActivityRequest activityRequest = buildActivityRequest();
            log.info("请求为：" + gson.toJson(activityRequest));
            Object result = executeByActivityEngine(activityRequest);
            log.info("结果为：" + gson.toJson(result));
        }
    }

    private ActivityRequest buildActivityRequest() {
        int platform = 100;
        int cityId = 2;
        //15044130
        int shopId = 15044130;
        long userId = 0L;

        String sceneCode = "medical_poi_pharmacy_products_shelf";
        String searchkeyword = null;
        String shopuuid = "H9vyFnIsGeud46Rp";
        String unionId = null;
        String deviceId = "dsfsdfsdfsdfs";
        String client = "android";
        String version = "10.40.10";


        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.productFilter);
        activityRequest.addParam(ShelfActivityConstants.Params.extra, "{\"filterBtnIndex\":\"2\",\"productIndex\":\"3\",\"wmShopId\":\"685787\",\"productId\":\"79244776\",\"outOfDelivery\":\"false\",\"filterBtnJumpUrl\":\"dianping://waimai.dianping.com/takeout/supermarket/foods?poi_id=924127875322462\",\"selectedFilterName\":\"\\u65b0\\u603b\\u5206\\u6d4b\\u8bd5\\u5206\\u7c7b0\",\"skuIds\":[\"213931733\"],\"userId\":0,\"selectedFilterId\":\"150061501\",\"wmPlatform\":1,\"selectedFilterType\":\"1\",\"status\":\"1\"}");
        activityRequest.addParam(ShelfActivityConstants.Params.lat, 31.1);
        activityRequest.addParam(ShelfActivityConstants.Params.lng, 121.1);

        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, sceneCode);
        activityRequest.addParam(ShelfActivityConstants.Params.keyword, searchkeyword);
        activityRequest.addParam(ShelfActivityConstants.Params.shopUuid, shopuuid);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, unionId);
        activityRequest.addParam(ShelfActivityConstants.Params.deviceId, deviceId);
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, client);
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, version);
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, cityId);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, shopId);
            activityRequest.addParam(ShelfActivityConstants.Params.mtUserId, userId);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, cityId);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, shopId);
            activityRequest.addParam(ShelfActivityConstants.Params.dpUserId, userId);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        return activityRequest;
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }
        // 2. 框架执行trace信息加入debug
        addTrace2DEBUG(activityResponse);
        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        return activityResponse.getResult().join();
    }

    private void addTrace2DEBUG(ActivityResponse<DzShelfResponseVO> activityResponse) {
        DEBUG.log("traces", activityResponse.getTraceElements());
    }



}
