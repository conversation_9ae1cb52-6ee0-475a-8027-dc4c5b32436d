package com.sankuai.dzviewscene.productshelf.vu.biz;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TrialClassSearchConfig;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TrialClassShelfConfig;
import com.sankuai.dzviewscene.productshelf.vu.biz.enums.TrialClassSearchResultEnum;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2023/6/18 下午7:32
 */
public class TrialClassShelfConfigUnitTest {

    //@Test
    public void test_getTotalJson() {
        TrialClassShelfConfig config = new TrialClassShelfConfig();
        config.setBarTitle("试听课");
        config.setBarDpTitleIcon("https://p1.meituan.net/travelcube/0ecaafaf3725f076f8cd1b254b8059822063.png");
        config.setBarMtTitleIcon("https://p1.meituan.net/travelcube/0ecaafaf3725f076f8cd1b254b8059822063.png");
        config.setBarButtonName("报名试听");
        config.setBarPostTitle("送精美礼品");
        config.setPopWindowTitle("试听课报名");
        config.setPopWindowTitleTips(Lists.newArrayList("仅限新客报名试听"));
        config.setPopWindowAreaTitle("以下科目任选试听1节");
        System.out.println(JsonCodec.encodeWithUTF8(config));
    }

    //@Test
    public void test_getCategorySearchConfigJson() {
        File file = new File("/Users/<USER>/Documents/category_trial_config.csv");
        if (!file.exists()) {
            return;
        }

        List<TrialClassSearchConfig> configList = new ArrayList<>();

        BufferedReader reader = null;
        StringBuffer sbf = new StringBuffer();
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                if (StringUtils.isEmpty(tempStr)) {
                    continue;
                }
                List<String> values = Lists.newArrayList(tempStr.split(","));
                if (values.size() < 3) {
                    continue;
                }
                TrialClassSearchConfig config = new TrialClassSearchConfig();
                List<String> keyWords = Lists.newArrayList(values.get(0).trim().split("、"));
                config.setSearchKeywords(keyWords);
                config.setSearchType(TrialClassSearchResultEnum.CATEGORY);

                List<String> categoryAndServiceType = Lists.newArrayList(values.get(2).trim().split("-"));
                config.setCategoryName(categoryAndServiceType.get(1));
                if (categoryAndServiceType.size() >2) {
                    config.setServiceType(categoryAndServiceType.get(2));
                }

                String showValue = values.get(1).trim();
                if (showValue.endsWith("类")) {
                    showValue = showValue.substring(0, showValue.length() - 1);
                }
                config.setShowValue(showValue);

                configList.add(config);
            }
            // 添加title
            TrialClassSearchConfig config = new TrialClassSearchConfig();
            config.setSearchType(TrialClassSearchResultEnum.TITLE);
            configList.add(config);

            System.out.println(JsonCodec.encodeWithUTF8(configList));
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    //@Test
    public void test_getClassSearchConfigJson() {
        File file = new File("/Users/<USER>/Documents/class_trial_config.csv");
        if (!file.exists()) {
            return;
        }

        List<TrialClassSearchConfig> configList = new ArrayList<>();

        BufferedReader reader = null;
        StringBuffer sbf = new StringBuffer();
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                if (StringUtils.isEmpty(tempStr)) {
                    continue;
                }
                List<String> values = Lists.newArrayList(tempStr.split(","));
                if (values.size() < 2) {
                    continue;
                }
                TrialClassSearchConfig config = new TrialClassSearchConfig();
                List<String> keyWords = Lists.newArrayList(values.get(0).trim().split("、"));
                config.setSearchKeywords(keyWords);
                config.setSearchType(TrialClassSearchResultEnum.CLASS);
                config.setShowValue(values.get(1).trim());

                configList.add(config);
            }
            System.out.println(JsonCodec.encodeWithUTF8(configList));
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    //@Test
    public void test_getSearchConfigs() {
        TrialClassSearchConfig config1 = new TrialClassSearchConfig();
        List<String> searchKeyword = Lists.newArrayList("画画", "绘画", "画室", "画", "画画培训班", "深夜画室", "学画画", "画画体验", "自助画室", "画画课", "绘画培训", "绘画兴趣班", "手工diy画画", "画画班", "培训班画画", "绘画班", "画画体验馆", "画画培训", "书画培训", "绘画培训班", "成人绘画", "成人画室", "画画成人", "成人画画", "绘画成人", "画室成人", "儿童画画", "儿童画画培训", "儿童绘画", "画画儿童", "绘画儿童", "幼儿画画", "儿童画", "少儿绘画", "幼儿绘画", "儿童绘画培训");
        config1.setSearchKeywords(searchKeyword);
        config1.setSearchType(TrialClassSearchResultEnum.CATEGORY);
        config1.setCategoryName("美术培训");
        config1.setShowValue("画画");

        TrialClassSearchConfig config2 = new TrialClassSearchConfig();
        List<String> searchKeyword2 = Lists.newArrayList("运动培训", "儿童运动培训", "儿童运动培训推荐", "运动培训儿童", "锻炼", "运动", "健身");
        config2.setSearchKeywords(searchKeyword2);
        config2.setSearchType(TrialClassSearchResultEnum.CATEGORY);
        config2.setCategoryName("运动培训");
        config2.setShowValue("运动培训");

        System.out.println(JsonCodec.encodeWithUTF8(Lists.newArrayList(config1, config2)));
    }

    //@Test
    public void test_getId() {
        List<Integer> idList = Lists.newArrayList(2104267, 2104268, 2104331, 2104332, 2104328, 2104329, 2104325, 2104326, 2104322, 2104323, 2104319, 2104320, 2104316, 2104317, 2104343, 2104344, 2104313, 2104314, 2104346, 2104347, 2104307, 2104308, 2104310, 2104311, 2104304, 2104305, 2104301, 2104302, 2104298, 2104299, 2104340, 2104341, 2104337, 2104338, 2104334, 2104335, 2104295, 2104296, 2104292, 2104293, 2104289, 2104290, 2104286, 2104287, 2104283, 2104284, 2104280, 2104281, 2104277, 2104278, 2104274, 2104275, 2104271, 2104272, 2104750, 2104752, 2104744, 2104746, 2104747, 2104749, 2104742, 2104743, 2104738, 2104740, 2104736, 2104737, 2104757, 2104758, 2104753, 2104755, 2105024, 2105095, 2105096, 2105097, 2105146, 2105148, 2105150, 2105152, 2105207, 2105208, 2105209, 2105210, 2105221, 2105222, 2105223, 2105224, 2105231, 2105232, 2105233, 2105234, 2105235, 2105236, 2105237, 2105238, 2105239, 2105240, 2105241, 2105242, 2105243, 2105244, 2105245, 2105246, 2105247, 2105248, 2105249, 2105250, 2105252, 2105253, 2105254, 2105255, 2105256, 2105257, 2105258, 2105259, 2105263, 2105265, 2105266, 2105267, 2105268, 2105269, 2105270, 2105271, 2105272, 2105273, 2105274, 2105025, 2105054, 2105057, 2105058, 2105059, 2105060, 2105061, 2105062, 2105063, 2105064, 2105065, 2105066, 2105067, 2105087, 2105088, 2105089, 2105102, 2105105, 2105106, 2105120, 2105124, 2105125, 2105126, 2105142, 2105143, 2105144, 2105145, 2105147, 2105149, 2105151, 2105153, 2105154, 2105155, 2105156, 2105157, 2105158, 2105159, 2105160, 2105161, 2105162, 2105163, 2105164, 2105165, 2105166, 2105167, 2105168, 2105169, 2105170, 2105171, 2105172, 2105173, 2105174, 2105175, 2105176, 2105177, 2105178, 2105179, 2105180, 2105181, 2105182, 2105183, 2105184, 2105185, 2105186, 2105187, 2105188, 2105189, 2105190, 2105191, 2105192, 2105193, 2105194, 2105195, 2105196, 2105197, 2105198, 2105199, 2105201, 2105202, 2105203, 2105204, 2105205, 2105206, 2105211, 2105212, 2105213, 2105214, 2105215, 2105216, 2105264, 2105275, 2105290, 2105291, 2105292, 2105294, 2105295, 2105296, 2105297, 2105298, 2105299, 2105300, 2105301, 2105302, 2105303, 2105304, 2105305, 2105306, 2105307, 2105308, 2105309, 2105310, 2105311, 2105312, 2105313, 2105314, 2105315, 2105316, 2105317, 2105318, 2105319, 2105320, 2105321, 2105322, 2105323, 2105324, 2105325, 2105326, 2105327, 2105328, 2105329, 2105332, 2105333, 2105334, 2105335, 2105336, 2105337, 2105338, 2105339, 2105340, 2105217, 2105218, 2105219, 2105220);
        List<Integer> addList = Lists.newArrayList(2104267, 2104268, 2104271, 2104272, 2104274, 2104275, 2104277, 2104278, 2104280, 2104281, 2104283, 2104284, 2104286, 2104287, 2104289, 2104290, 2104292, 2104293, 2104295, 2104296, 2104298, 2104299, 2104301, 2104302, 2104304, 2104305, 2104307, 2104308, 2104310, 2104311, 2104313, 2104314, 2104316, 2104317, 2104319, 2104320, 2104322, 2104323, 2104325, 2104326, 2104328, 2104329, 2104331, 2104332, 2104334, 2104335, 2104337, 2104338, 2104340, 2104341, 2104343, 2104344, 2104346, 2104347, 2104736, 2104737, 2104738, 2104740, 2104742, 2104743, 2104744, 2104746, 2104747, 2104749, 2104750, 2104752, 2104753, 2104755, 2104757, 2104758, 2105024, 2105025, 2105054, 2105057, 2105058, 2105059, 2105060, 2105061, 2105062, 2105063, 2105064, 2105065, 2105066, 2105067, 2105087, 2105088, 2105089, 2105095, 2105096, 2105097, 2105102, 2105105, 2105106, 2105120, 2105124, 2105125, 2105126, 2105142, 2105143, 2105144, 2105145, 2105146, 2105147, 2105148, 2105149, 2105150, 2105151, 2105152, 2105153, 2105154, 2105155, 2105156, 2105157, 2105158, 2105159, 2105160, 2105161, 2105162, 2105163, 2105164, 2105165, 2105166, 2105167, 2105168, 2105169, 2105170, 2105171, 2105172, 2105173, 2105174, 2105175, 2105176, 2105177, 2105178, 2105179, 2105180, 2105181, 2105182, 2105183, 2105184, 2105185, 2105186, 2105187, 2105188, 2105189, 2105190, 2105191, 2105192, 2105193, 2105194, 2105195, 2105196, 2105197, 2105198, 2105199, 2105201, 2105202, 2105203, 2105204, 2105205, 2105206, 2105207, 2105208, 2105209, 2105210, 2105211, 2105212, 2105213, 2105214, 2105215, 2105216, 2105221, 2105222, 2105223, 2105224, 2105231, 2105232, 2105233, 2105234, 2105235, 2105236, 2105237, 2105238, 2105239, 2105240, 2105241, 2105242, 2105243, 2105244, 2105245, 2105246, 2105247, 2105248, 2105249, 2105250, 2105252, 2105253, 2105254, 2105255, 2105256, 2105257, 2105258, 2105259, 2105263, 2105264, 2105265, 2105266, 2105267, 2105268, 2105269, 2105270, 2105271, 2105272, 2105273, 2105274, 2105275, 2105290, 2105291, 2105292, 2105294, 2105295, 2105296, 2105297, 2105298, 2105299, 2105300, 2105301, 2105302, 2105303, 2105304, 2105305, 2105306, 2105307, 2105308, 2105309, 2105310, 2105311, 2105312, 2105313, 2105314, 2105315, 2105316, 2105317, 2105318, 2105319, 2105320, 2105321, 2105322, 2105323, 2105324, 2105325, 2105326, 2105327, 2105328, 2105329, 2105332, 2105333, 2105334, 2105335, 2105336, 2105337, 2105338, 2105339, 2105340);
        List<Integer> result = new ArrayList<>();
        for (Integer id : addList) {
            if (idList.contains(id) || result.contains(id)) {
                continue;
            }
            result.add(id);
        }
        System.out.println(JsonCodec.encodeWithUTF8(result));

    }
}
