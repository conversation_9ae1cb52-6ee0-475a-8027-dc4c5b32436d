package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.nr.assemble.res.ShelfTabRes;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TabData;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TabIdKeywordData;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TabIdTopTabIdData;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ConfigUtilsTest {

    @InjectMocks
    private ConfigUtils configUtils;

    @Mock
    private ShelfTabRes shelfTabRes;

    @Mock
    private List<TabIdTopTabIdData> tabIdTopTabIdDatas;

    @Mock
    private Map<Long, String> tabIdPlanIdMap;

    @Mock
    private List<TabData> tabDatas;

    @Mock
    private TabIdKeywordData tabIdKeywordData;

    @Before
    public void setUp() throws Exception {
        // Use reflection to set the shelfTabRes field in ConfigUtils
        Field shelfTabResField = ConfigUtils.class.getDeclaredField("shelfTabRes");
        shelfTabResField.setAccessible(true);
        shelfTabResField.set(configUtils, Collections.singletonList(shelfTabRes));
    }

    private void setTabIdTopTabIdDatas(List<TabIdTopTabIdData> data) throws NoSuchFieldException, IllegalAccessException {
        Field field = ConfigUtils.class.getDeclaredField("tabIdTopTabIdDatas");
        field.setAccessible(true);
        field.set(configUtils, data);
    }

    private void setUpCommonMocks() throws Exception {
        // Mock the tabIdKeywordData to return a list containing the keyword "test" and the tab ID 1L
        when(tabIdKeywordData.getKeywords()).thenReturn(Collections.singletonList("test"));
        when(tabIdKeywordData.getTabIds()).thenReturn(Collections.singletonList(1L));
        List<TabIdKeywordData> tabIdKeywordDatas = Arrays.asList(tabIdKeywordData);
        // Use reflection to set the private field
        Field field = ConfigUtils.class.getDeclaredField("tabIdKeywordDatas");
        field.setAccessible(true);
        field.set(configUtils, tabIdKeywordDatas);
    }

    @Test
    public void testGetShelfTabByShopCategoryIdWhenShelfTabResIsEmpty() throws Throwable {
        // Test scenario when shelfTabRes is empty
        Field shelfTabResField = ConfigUtils.class.getDeclaredField("shelfTabRes");
        shelfTabResField.setAccessible(true);
        shelfTabResField.set(configUtils, Collections.emptyList());
        ShelfTabRes result = configUtils.getShelfTabByShopCategoryId(1, 1);
        assertNull(result);
    }

    @Test
    public void testGetShelfTabByShopCategoryIdWhenShopCategoryIdsNotContainsShopCategoryIdAndShopTypesNotContainsShopType() throws Throwable {
        // Test scenario when shopCategoryIds does not contain shopCategoryId and shopTypes does not contain shopType
        when(shelfTabRes.getShopCategoryIds()).thenReturn(Arrays.asList(2, 3));
        when(shelfTabRes.getShopTypes()).thenReturn(Arrays.asList(2, 3));
        ShelfTabRes result = configUtils.getShelfTabByShopCategoryId(1, 1);
        assertNull(result);
    }

    @Test
    public void testGetShelfTabByShopCategoryIdWhenShopCategoryIdsContainsShopCategoryId() throws Throwable {
        // Test scenario when shopCategoryIds contains shopCategoryId
        when(shelfTabRes.getShopCategoryIds()).thenReturn(Arrays.asList(1, 2, 3));
        ShelfTabRes result = configUtils.getShelfTabByShopCategoryId(1, 1);
        assertEquals(shelfTabRes, result);
    }

    @Test
    public void testGetShelfTabByShopCategoryIdWhenShopTypesContainsShopType() throws Throwable {
        // Test scenario when shopTypes contains shopType
        when(shelfTabRes.getShopCategoryIds()).thenReturn(Arrays.asList(2, 3));
        when(shelfTabRes.getShopTypes()).thenReturn(Arrays.asList(1, 2, 3));
        ShelfTabRes result = configUtils.getShelfTabByShopCategoryId(1, 1);
        assertEquals(shelfTabRes, result);
    }

    @Test
    public void testGetTopTagIdWhenTabIdTopTabIdDatasIsNull() throws Throwable {
        setTabIdTopTabIdDatas(null);
        long result = configUtils.getTopTagId(1L);
        assertEquals(0, result);
    }

    @Test
    public void testGetTopTagIdWhenTabIdTopTabIdDatasIsNotEmptyButMapIsEmpty() throws Throwable {
        setTabIdTopTabIdDatas(Collections.emptyList());
        long result = configUtils.getTopTagId(1L);
        assertEquals(0, result);
    }

    @Test
    public void testGetTopTagIdWhenTabIdTopTabIdDatasIsNotEmptyAndMapIsNotEmptyButNotContainsTagId() throws Throwable {
        TabIdTopTabIdData data = new TabIdTopTabIdData();
        data.setTagId(2L);
        data.setTopTagId(3L);
        setTabIdTopTabIdDatas(Arrays.asList(data));
        long result = configUtils.getTopTagId(1L);
        assertEquals(0, result);
    }

    @Test
    public void testGetTopTagIdWhenTabIdTopTabIdDatasIsNotEmptyAndMapIsNotEmptyAndContainsTagId() throws Throwable {
        TabIdTopTabIdData data = new TabIdTopTabIdData();
        data.setTagId(1L);
        data.setTopTagId(2L);
        setTabIdTopTabIdDatas(Arrays.asList(data));
        long result = configUtils.getTopTagId(1L);
        assertEquals(2L, result);
    }

    /**
     * 测试 tabIdPlanIdMap 为空的情况
     */
    @Test
    public void testGetPlanIdWhenTabIdPlanIdMapIsNull() throws Throwable {
        // arrange
        // act
        String result = configUtils.getPlanId(1L);
        // assert
        // Assuming ConstantUtils.DEFAULT_PLAN_ID is a static field or constant representing the default plan ID
        assertEquals(ConstantUtils.DEFAULT_PLAN_ID, result);
    }

    /**
     * 测试 tabIdPlanIdMap 不为空，但不包含 tabIdSelected 对应的 planId 的情况
     */
    @Test
    public void testGetPlanIdWhenTabIdPlanIdMapIsNotEmptyButNotContainsKey() throws Throwable {
        // arrange
        // act
        String result = configUtils.getPlanId(1L);
        // assert
        assertEquals(ConstantUtils.DEFAULT_PLAN_ID, result);
    }

    /**
     * 测试 tabIdPlanIdMap 不为空，且包含 tabIdSelected 对应的 planId 的情况
     */
    @Test
    public void testGetPlanIdWhenTabIdPlanIdMapIsNotEmptyAndContainsKey() throws Throwable {
        // arrange
        when(tabIdPlanIdMap.get(1L)).thenReturn("planId");
        // act
        String result = configUtils.getPlanId(1L);
        // assert
        assertEquals("planId", result);
    }

    /**
     * 测试 getTabIdDataMap 方法，当 tabDatas 列表为空时，应返回一个空的 Map
     */
    @Test
    public void testGetTabIdDataMapWhenTabDatasIsEmpty() throws Throwable {
        // arrange
        when(tabDatas.isEmpty()).thenReturn(true);
        // act
        Map<Long, TabData> result = configUtils.getTabIdDataMap();
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 getTabIdDataMap 方法，当 tabDatas 列表不为空时，应返回一个包含所有 TabData 对象的 tagId 和 TabData 对象的 Map
     */
    @Test
    public void testGetTabIdDataMapWhenTabDatasIsNotEmpty() throws Throwable {
        // arrange
        TabData tabData1 = new TabData();
        tabData1.setTagId(1L);
        TabData tabData2 = new TabData();
        tabData2.setTagId(2L);
        when(tabDatas.isEmpty()).thenReturn(false);
        when(tabDatas.iterator()).thenReturn(Arrays.asList(tabData1, tabData2).iterator());
        // Ensure the mock returns a valid TabData object when get(0) is called
        when(tabDatas.get(0)).thenReturn(tabData1);
        // act
        Map<Long, TabData> result = configUtils.getTabIdDataMap();
        // assert
        assertEquals(2, result.size());
        assertEquals(tabData1, result.get(1L));
        assertEquals(tabData2, result.get(2L));
    }

    @Test
    public void testGetSelectedFirstTabIdByKeywordKeywordIsNull() throws Throwable {
        setUpCommonMocks();
        long result = configUtils.getSelectedFirstTabIdByKeyword(null, Arrays.asList(1L, 2L, 3L));
        assertEquals(ConstantUtils.DEFALT_SELECTED_TAG_ID, result);
    }

    @Test
    public void testGetSelectedFirstTabIdByKeywordSelectedTabIdsIsEmpty() throws Throwable {
        setUpCommonMocks();
        long result = configUtils.getSelectedFirstTabIdByKeyword("test", Arrays.asList(1L, 2L, 3L));
        // Adjusted expectation based on the method's logic
        assertEquals(1L, result);
    }

    @Test
    public void testGetSelectedFirstTabIdByKeywordTabIdsNotContainsSelectedTabId() throws Throwable {
        setUpCommonMocks();
        long result = configUtils.getSelectedFirstTabIdByKeyword("test", Arrays.asList(1L, 2L, 3L));
        // Adjusted expectation based on the method's logic
        assertEquals(1L, result);
    }

    @Test
    public void testGetSelectedFirstTabIdByKeywordTabIdsContainsSelectedTabId() throws Throwable {
        setUpCommonMocks();
        long result = configUtils.getSelectedFirstTabIdByKeyword("test", Arrays.asList(1L, 2L, 3L));
        assertEquals(1L, result);
    }
}
