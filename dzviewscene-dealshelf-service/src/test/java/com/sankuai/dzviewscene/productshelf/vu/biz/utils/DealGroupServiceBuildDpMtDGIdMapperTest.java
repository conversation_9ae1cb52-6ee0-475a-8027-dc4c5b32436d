package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupServiceBuildDpMtDGIdMapperTest {

    private DealGroupService dealGroupService = new DealGroupService();

    @Test
    public void testBuildDpMtDGIdMapperWhenIdMappersListIsEmpty() throws Throwable {
        List<List<IdMapper>> idMappersList = Arrays.asList();
        CompletableFuture<Map<Integer, Integer>> result = dealGroupService.buildDpMtDGIdMapper(idMappersList);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildDpMtDGIdMapperWhenIdMappersListIsNotEmptyButIdMappersIsEmpty() throws Throwable {
        List<IdMapper> idMappers = Arrays.asList();
        List<List<IdMapper>> idMappersList = Arrays.asList(idMappers);
        CompletableFuture<Map<Integer, Integer>> result = dealGroupService.buildDpMtDGIdMapper(idMappersList);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildDpMtDGIdMapperWhenIdMappersListAndIdMappersAreNotEmpty() throws Throwable {
        // Create IdMapper with mtDealGroupID=2, dpDealGroupID=1, dpDealID=3
        // This should result in a map with key=1 (dpDealGroupID) and value=2 (mtDealGroupID)
        IdMapper idMapper = new IdMapper(2, 1, 3);
        List<IdMapper> idMappers = Arrays.asList(idMapper);
        List<List<IdMapper>> idMappersList = Arrays.asList(idMappers);
        CompletableFuture<Map<Integer, Integer>> result = dealGroupService.buildDpMtDGIdMapper(idMappersList);
        Map<Integer, Integer> resultMap = result.get();
        assertEquals(1, resultMap.size());
        assertTrue(resultMap.containsKey(1));
        assertEquals(Integer.valueOf(2), resultMap.get(1));
    }
}
