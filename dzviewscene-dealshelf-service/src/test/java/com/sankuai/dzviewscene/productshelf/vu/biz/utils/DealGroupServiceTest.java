package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.req.DealThemePlanRequest;
import com.sankuai.dztheme.deal.res.DealThemeDTO;
import com.sankuai.dztheme.deal.res.DealThemePlanResponse;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealGroupThemeHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupServiceTest {

    @InjectMocks
    private DealGroupService dealGroupService;

    @Mock
    private AtomFacadeService atomFacadeService;

    @Mock
    private ConfigUtils configUtils;

    @Mock
    private DealThemeSortService dealThemeSortService;

    @Mock
    private DealGroupThemeHandler dealGroupThemeHandler;

    // The actual constant value from ConstantUtils
    private static final String ATTR_STRUCT_DETAIL = "dealStructDetail";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock ConfigUtils
        when(configUtils.isPriceOptimize()).thenReturn(false);
    }

    private void mockConfigUtils() {
        when(configUtils.getPlanId(anyLong())).thenReturn("mockPlanId");
    }

    @Test
    public void testGetDealThemesWhenDealThemePlanResponseIsNull() throws Throwable {
        mockConfigUtils();
        List<Integer> dgIds = Arrays.asList(1, 2, 3);
        long tabIdSelected = 1L;
        Map<String, Object> extParams = new HashMap<>();
        when(atomFacadeService.getDealThemePlanResponse(any(DealThemePlanRequest.class))).thenReturn(CompletableFuture.completedFuture(null));
        CompletableFuture<List<DealThemeDTO>> result = dealGroupService.getDealThemes(dgIds, tabIdSelected, extParams);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testGetDealThemesWhenDealsIsEmpty() throws Throwable {
        mockConfigUtils();
        List<Integer> dgIds = Arrays.asList(1, 2, 3);
        long tabIdSelected = 1L;
        Map<String, Object> extParams = new HashMap<>();
        DealThemePlanResponse dealThemePlanResponse = new DealThemePlanResponse();
        dealThemePlanResponse.setDeals(Collections.emptyList());
        when(atomFacadeService.getDealThemePlanResponse(any(DealThemePlanRequest.class))).thenReturn(CompletableFuture.completedFuture(dealThemePlanResponse));
        CompletableFuture<List<DealThemeDTO>> result = dealGroupService.getDealThemes(dgIds, tabIdSelected, extParams);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testGetDealThemesWhenDealsIsNotEmpty() throws Throwable {
        mockConfigUtils();
        List<Integer> dgIds = Arrays.asList(1, 2, 3);
        long tabIdSelected = 1L;
        Map<String, Object> extParams = new HashMap<>();
        DealThemePlanResponse dealThemePlanResponse = new DealThemePlanResponse();
        List<DealThemeDTO> deals = Arrays.asList(new DealThemeDTO(), new DealThemeDTO());
        dealThemePlanResponse.setDeals(deals);
        when(atomFacadeService.getDealThemePlanResponse(any(DealThemePlanRequest.class))).thenReturn(CompletableFuture.completedFuture(dealThemePlanResponse));
        CompletableFuture<List<DealThemeDTO>> result = dealGroupService.getDealThemes(dgIds, tabIdSelected, extParams);
        assertEquals(deals, result.get());
    }

    /**
     * 测试 buildDzItems 方法，当 dealThemes 为空时，返回空列表
     */
    @Test
    public void testBuildDzItems_EmptyDealThemes() throws Throwable {
        // arrange
        List<DealThemeDTO> dealThemes = new ArrayList<>();
        Map<Integer, DealProductDTO> dealProductDTOMap = new HashMap<>();
        DouHuResponse subTitleDouHuResponse = new DouHuResponse();
        DouHuResponse noPicDouHu = new DouHuResponse();
        List<Integer> summaryDealIds = new ArrayList<>();
        // act
        List<DzItemVO> result = dealGroupService.buildDzItems(1, 1L, dealThemes, dealProductDTOMap, 1, new CardHoldStatusDTO(), 1L, false, false, false, "", false, subTitleDouHuResponse, noPicDouHu, summaryDealIds);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 buildDzItems 方法，当 dealThemes 不为空时，返回非空列表
     */
    @Test
    public void testBuildDzItems_NonEmptyDealThemes() throws Throwable {
        // arrange
        List<DealThemeDTO> dealThemes = new ArrayList<>();
        DealThemeDTO dealTheme = new DealThemeDTO();
        dealTheme.setDealId(1);
        dealThemes.add(dealTheme);
        Map<Integer, DealProductDTO> dealProductDTOMap = new HashMap<>();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setPromoPrices(new ArrayList<>());
        dealProductDTOMap.put(1, dealProductDTO);
        ProductM mockProductM = new ProductM();
        mockProductM.setBasePriceTag("100");
        when(dealGroupThemeHandler.buildDealProductM(dealProductDTO)).thenReturn(mockProductM);
        DouHuResponse subTitleDouHuResponse = new DouHuResponse();
        DouHuResponse noPicDouHu = new DouHuResponse();
        List<Integer> summaryDealIds = new ArrayList<>();
        List<Integer> shopRecommendThemeIds = new ArrayList<>();
        when(dealThemeSortService.buildShopRecommendThemeIds(anyList(), anyLong(), anyInt())).thenReturn(shopRecommendThemeIds);
        // act
        List<DzItemVO> result = dealGroupService.buildDzItems(1, 1L, dealThemes, dealProductDTOMap, 1, new CardHoldStatusDTO(), 1L, false, false, false, "", false, subTitleDouHuResponse, noPicDouHu, summaryDealIds);
        // assert
        assertEquals(1, result.size());
    }

    /**
     * 测试 buildDzItems 方法，当 dealThemeSortService.buildShopRecommendThemeIds 返回空列表时，返回空列表
     */
    @Test
    public void testBuildDzItems_EmptyShopRecommendThemeIds() throws Throwable {
        // arrange
        List<DealThemeDTO> dealThemes = new ArrayList<>();
        DealThemeDTO dealTheme = new DealThemeDTO();
        dealTheme.setDealId(1);
        dealThemes.add(dealTheme);
        Map<Integer, DealProductDTO> dealProductDTOMap = new HashMap<>();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setPromoPrices(new ArrayList<>());
        dealProductDTOMap.put(1, dealProductDTO);
        ProductM mockProductM = new ProductM();
        mockProductM.setBasePriceTag("100");
        when(dealGroupThemeHandler.buildDealProductM(dealProductDTO)).thenReturn(mockProductM);
        DouHuResponse subTitleDouHuResponse = new DouHuResponse();
        DouHuResponse noPicDouHu = new DouHuResponse();
        List<Integer> summaryDealIds = new ArrayList<>();
        List<Integer> shopRecommendThemeIds = new ArrayList<>();
        when(dealThemeSortService.buildShopRecommendThemeIds(anyList(), anyLong(), anyInt())).thenReturn(shopRecommendThemeIds);
        // act
        List<DzItemVO> result = dealGroupService.buildDzItems(1, 1L, dealThemes, dealProductDTOMap, 1, new CardHoldStatusDTO(), 1L, false, false, false, "", false, subTitleDouHuResponse, noPicDouHu, summaryDealIds);
        // assert
        assertEquals(1, result.size());
    }

    /**
     * 测试 buildDzItems 方法，当 dealThemeSortService.buildShopRecommendThemeIds 返回非空列表时，返回非空列表
     */
    @Test
    public void testBuildDzItems_NonEmptyShopRecommendThemeIds() throws Throwable {
        // arrange
        List<DealThemeDTO> dealThemes = new ArrayList<>();
        DealThemeDTO dealTheme = new DealThemeDTO();
        dealTheme.setDealId(1);
        dealThemes.add(dealTheme);
        Map<Integer, DealProductDTO> dealProductDTOMap = new HashMap<>();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setPromoPrices(new ArrayList<>());
        dealProductDTOMap.put(1, dealProductDTO);
        ProductM mockProductM = new ProductM();
        mockProductM.setBasePriceTag("100");
        when(dealGroupThemeHandler.buildDealProductM(dealProductDTO)).thenReturn(mockProductM);
        DouHuResponse subTitleDouHuResponse = new DouHuResponse();
        DouHuResponse noPicDouHu = new DouHuResponse();
        List<Integer> summaryDealIds = new ArrayList<>();
        List<Integer> shopRecommendThemeIds = new ArrayList<>();
        shopRecommendThemeIds.add(1);
        when(dealThemeSortService.buildShopRecommendThemeIds(anyList(), anyLong(), anyInt())).thenReturn(shopRecommendThemeIds);
        // act
        List<DzItemVO> result = dealGroupService.buildDzItems(1, 1L, dealThemes, dealProductDTOMap, 1, new CardHoldStatusDTO(), 1L, false, false, false, "", false, subTitleDouHuResponse, noPicDouHu, summaryDealIds);
        // assert
        assertEquals(1, result.size());
    }

    // Test case for null input
    @Test
    public void testGetDealStructDetailNullDealProductDTO() throws Throwable {
        DealDetailDto result = DealGroupService.getDealStructDetail(null);
        assertNull(result);
    }

    // Test case for empty attributes
    @Test
    public void testGetDealStructDetailEmptyAttrs() throws Throwable {
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setAttrs(Collections.emptyList());
        DealDetailDto result = DealGroupService.getDealStructDetail(dealProductDTO);
        assertNull(result);
    }

    // Test case for non-matching attribute
    @Test
    public void testGetDealStructDetailNoMatchingAttr() throws Throwable {
        DealProductDTO dealProductDTO = new DealProductDTO();
        DealProductAttrDTO attrDTO = new DealProductAttrDTO();
        attrDTO.setName("WrongName");
        dealProductDTO.setAttrs(Arrays.asList(attrDTO));
        DealDetailDto result = DealGroupService.getDealStructDetail(dealProductDTO);
        assertNull(result);
    }

    // Test case for invalid JSON input
    @Test
    public void testGetDealStructDetailInvalidJson() throws Throwable {
        DealProductDTO dealProductDTO = new DealProductDTO();
        DealProductAttrDTO attrDTO = new DealProductAttrDTO();
        attrDTO.setName("dealStructDetail");
        attrDTO.setValue("invalid json");
        dealProductDTO.setAttrs(Arrays.asList(attrDTO));
        DealDetailDto result = DealGroupService.getDealStructDetail(dealProductDTO);
        assertNull(result);
    }
}
