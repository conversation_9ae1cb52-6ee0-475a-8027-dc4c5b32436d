package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

@RunWith(MockitoJUnitRunner.class)
public class DealThemeRequestUtilsTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ApplicationContext applicationContext;

    private ApplicationContext originalContext;

    @Before
    public void setUp() throws Exception {
        // Save original applicationContext
        Field contextField = AthenaBeanFactory.class.getDeclaredField("applicationContext");
        contextField.setAccessible(true);
        originalContext = (ApplicationContext) contextField.get(null);
        // Set mock applicationContext
        when(applicationContext.getBean(CompositeAtomService.class)).thenReturn(compositeAtomService);
        contextField.set(null, applicationContext);
    }

    @After
    public void tearDown() throws Exception {
        // Restore original applicationContext
        Field contextField = AthenaBeanFactory.class.getDeclaredField("applicationContext");
        contextField.setAccessible(true);
        contextField.set(null, originalContext);
    }

    @Test
    public void testBuildExtParams_PlatformIsDPAndFindShopsByDpShopIdsReturnsEmptyList() throws Throwable {
        // arrange
        int platform = VCPlatformEnum.DP.getType();
        long userId = 1L;
        Long shopId = 1L;
        Integer cityId = 1;
        String appVersion = "1.0.0";
        String deviceId = "deviceId";
        List<String> attributeKeys = Arrays.asList("key1", "key2");
        Map<Integer, Integer> dgIdShopIdMap = new HashMap<>();
        Map<Integer, Long> dgIdShopIdMapForLong = new HashMap<>();
        Integer promoTemplateId = 1;
        Integer tcMergePromoTemplateId = 1;
        Integer noMergePromoTemplateId = 1;
        String unionId = "unionId";
        int scene = 1;
        when(compositeAtomService.findShopsByDpShopIds(any(DpPoiRequest.class))).thenReturn(CompletableFuture.completedFuture(Arrays.asList()));
        // act
        Map<String, Object> result = DealThemeRequestUtils.buildExtParams(platform, userId, shopId, cityId, appVersion, deviceId, attributeKeys, dgIdShopIdMap, dgIdShopIdMapForLong, promoTemplateId, tcMergePromoTemplateId, noMergePromoTemplateId, unionId, scene);
        // assert
        assertNotNull(result);
        assertTrue(result.containsKey("shopId2UuidMap"));
        assertTrue(result.containsKey("shopIdLong2UuidMap"));
    }

    @Test
    public void testBuildExtParams_PlatformIsDPAndFindShopsByDpShopIdsReturnsNonEmptyListButDpPoiDTOShopIdIsNull() throws Throwable {
        // arrange
        int platform = VCPlatformEnum.DP.getType();
        long userId = 1L;
        Long shopId = 1L;
        Integer cityId = 1;
        String appVersion = "1.0.0";
        String deviceId = "deviceId";
        List<String> attributeKeys = Arrays.asList("key1", "key2");
        Map<Integer, Integer> dgIdShopIdMap = new HashMap<>();
        Map<Integer, Long> dgIdShopIdMapForLong = new HashMap<>();
        Integer promoTemplateId = 1;
        Integer tcMergePromoTemplateId = 1;
        Integer noMergePromoTemplateId = 1;
        String unionId = "unionId";
        int scene = 1;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopId(null);
        when(compositeAtomService.findShopsByDpShopIds(any(DpPoiRequest.class))).thenReturn(CompletableFuture.completedFuture(Arrays.asList(dpPoiDTO)));
        // act
        Map<String, Object> result = DealThemeRequestUtils.buildExtParams(platform, userId, shopId, cityId, appVersion, deviceId, attributeKeys, dgIdShopIdMap, dgIdShopIdMapForLong, promoTemplateId, tcMergePromoTemplateId, noMergePromoTemplateId, unionId, scene);
        // assert
        assertNotNull(result);
        assertTrue(result.containsKey("shopId2UuidMap"));
        assertTrue(result.containsKey("shopIdLong2UuidMap"));
    }

    @Test
    public void testBuildExtParams_PlatformIsNotDPAndShopIdIsNull() throws Throwable {
        // arrange
        int platform = VCPlatformEnum.MT.getType();
        long userId = 1L;
        Long shopId = null;
        Integer cityId = 1;
        String appVersion = "1.0.0";
        String deviceId = "deviceId";
        List<String> attributeKeys = Arrays.asList("key1", "key2");
        Map<Integer, Integer> dgIdShopIdMap = new HashMap<>();
        Map<Integer, Long> dgIdShopIdMapForLong = new HashMap<>();
        Integer promoTemplateId = 1;
        Integer tcMergePromoTemplateId = 1;
        Integer noMergePromoTemplateId = 1;
        String unionId = "unionId";
        int scene = 1;
        // act
        Map<String, Object> result = DealThemeRequestUtils.buildExtParams(platform, userId, shopId, cityId, appVersion, deviceId, attributeKeys, dgIdShopIdMap, dgIdShopIdMapForLong, promoTemplateId, tcMergePromoTemplateId, noMergePromoTemplateId, unionId, scene);
        // assert
        assertNotNull(result);
        assertTrue(result.containsKey("shopId2UuidMap"));
        assertTrue(result.containsKey("shopIdLong2UuidMap"));
    }
}
