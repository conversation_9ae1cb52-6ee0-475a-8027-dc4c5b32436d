package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.util.StringUtils;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.dztheme.deal.dto.DealProductPromoDTO;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DzPromoUtilsTest {

    @Mock
    private ProductM productM;

    @Mock
    private DzPromoVO dzPromoVO;

    private static final String MT_TEXT_COLOR = "#000000";

    private static final String MT_BORDER_COLOR = "#000000";

    private static final String MT_BG_COLOR = "#FFFFFF";

    private static final String DP_TEXT_COLOR = "#111111";

    private static final String DP_BG_COLOR = "#EEEEEE";

    private static final String PROMO_TAG = "Test Promo";

    /**
     * 测试促销标签为空的情况
     */
    @Test
    public void testBuildBasicDzTagVOWithColorAndBorderPromoTagIsNull() {
        // arrange
        int platform = 1;
        String promoTag = null;
        String mtTextColor = "mtTextColor";
        String mtBorderColor = "mtBorderColor";
        String mtBackgroundColor = "mtBackgroundColor";
        String dpTextColor = "dpTextColor";
        String dpBackgroundColor = "dpBackgroundColor";
        boolean dpHasBorder = true;
        String dpBorderColor = "dpBorderColor";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColorAndBorder(platform, promoTag, mtTextColor, mtBorderColor, mtBackgroundColor, dpTextColor, dpBackgroundColor, dpHasBorder, dpBorderColor);
        // assert
        assertNull(result);
    }

    /**
     * 测试平台类型为MT的情况
     */
    @Test
    public void testBuildBasicDzTagVOWithColorAndBorderPlatformIsMT() {
        // arrange
        int platform = 2;
        String promoTag = "promoTag";
        String mtTextColor = "mtTextColor";
        String mtBorderColor = "mtBorderColor";
        String mtBackgroundColor = "mtBackgroundColor";
        String dpTextColor = "dpTextColor";
        String dpBackgroundColor = "dpBackgroundColor";
        boolean dpHasBorder = true;
        String dpBorderColor = "dpBorderColor";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColorAndBorder(platform, promoTag, mtTextColor, mtBorderColor, mtBackgroundColor, dpTextColor, dpBackgroundColor, dpHasBorder, dpBorderColor);
        // assert
        assertNotNull(result);
        assertEquals(mtTextColor, result.getTextColor());
        assertEquals(mtBorderColor, result.getBorderColor());
        assertEquals(mtBackgroundColor, result.getBackground());
    }

    /**
     * 测试平台类型为DP的情况
     */
    @Test
    public void testBuildBasicDzTagVOWithColorAndBorderPlatformIsDP() {
        // arrange
        int platform = 1;
        String promoTag = "promoTag";
        String mtTextColor = "mtTextColor";
        String mtBorderColor = "mtBorderColor";
        String mtBackgroundColor = "mtBackgroundColor";
        String dpTextColor = "dpTextColor";
        String dpBackgroundColor = "dpBackgroundColor";
        boolean dpHasBorder = true;
        String dpBorderColor = "dpBorderColor";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColorAndBorder(platform, promoTag, mtTextColor, mtBorderColor, mtBackgroundColor, dpTextColor, dpBackgroundColor, dpHasBorder, dpBorderColor);
        // assert
        assertNotNull(result);
        assertEquals(dpTextColor, result.getTextColor());
        assertEquals(dpBorderColor, result.getBorderColor());
        assertEquals(dpBackgroundColor, result.getBackground());
    }

    /**
     * Test when promoTag is null
     * Should return null
     */
    @Test
    public void testBuildBasicDzTagVO_WhenPromoTagIsNull() {
        // arrange
        int platform = 2;
        String promoTag = null;
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVO(platform, promoTag);
        // assert
        assertNull(result);
    }

    /**
     * Test when platform is MT type (platform=2)
     * Should return DzTagVO with MT specific colors
     */
    @Test
    public void testBuildBasicDzTagVO_WhenPlatformIsMT() {
        // arrange
        // MT platform type
        int platform = 2;
        String promoTag = "Test Promo";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVO(platform, promoTag);
        // assert
        assertNotNull(result);
        assertEquals(ColorUtils.colorFF4A4A, result.getTextColor());
        assertTrue(result.isHasBorder());
        assertEquals(ColorUtils.colorFED1CE, result.getBorderColor());
        assertEquals(promoTag, result.getName());
        assertEquals(ColorUtils.colorFFF5F4, result.getBackground());
    }

    /**
     * Test when platform is MT client type (MT_APP=200)
     * Should return DzTagVO with MT specific colors
     */
    @Test
    public void testBuildBasicDzTagVO_WhenPlatformIsMTClientType() {
        // arrange
        // 200
        int platform = VCClientTypeEnum.MT_APP.getCode();
        String promoTag = "Test Promo";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVO(platform, promoTag);
        // assert
        assertNotNull(result);
        assertEquals(ColorUtils.colorFF4A4A, result.getTextColor());
        assertTrue(result.isHasBorder());
        assertEquals(ColorUtils.colorFED1CE, result.getBorderColor());
        assertEquals(promoTag, result.getName());
        assertEquals(ColorUtils.colorFFF5F4, result.getBackground());
    }

    /**
     * Test when platform is DP type
     * Should return DzTagVO with DP specific colors
     */
    @Test
    public void testBuildBasicDzTagVO_WhenPlatformIsDP() {
        // arrange
        // DP platform type
        int platform = 1;
        String promoTag = "Test Promo";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVO(platform, promoTag);
        // assert
        assertNotNull(result);
        assertEquals(ColorUtils.colorFF6633, result.getTextColor());
        assertFalse(result.isHasBorder());
        assertEquals(promoTag, result.getName());
        assertEquals(ColorUtils.colorFF663314, result.getBackground());
    }

    /**
     * Test when platform is DP client type (DP_APP=100)
     * Should return DzTagVO with DP specific colors
     */
    @Test
    public void testBuildBasicDzTagVO_WhenPlatformIsDPClientType() {
        // arrange
        // 100
        int platform = VCClientTypeEnum.DP_APP.getCode();
        String promoTag = "Test Promo";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVO(platform, promoTag);
        // assert
        assertNotNull(result);
        assertEquals(ColorUtils.colorFF6633, result.getTextColor());
        assertFalse(result.isHasBorder());
        assertEquals(promoTag, result.getName());
        assertEquals(ColorUtils.colorFF663314, result.getBackground());
    }

    @Test
    public void testBuildDzTagVoWithEmptyDealProductPromoDTOs() throws Throwable {
        int platform = 1;
        List<DealProductPromoDTO> dealProductPromoDTOs = new ArrayList<>();
        DzTagVO result = DzPromoUtils.buildDzTagVo(platform, dealProductPromoDTOs, 0);
        assertNull(result);
    }

    @Test
    public void testBuildDzTagVoWithNullElementInDealProductPromoDTOs() throws Throwable {
        int platform = 1;
        List<DealProductPromoDTO> dealProductPromoDTOs = new ArrayList<>();
        dealProductPromoDTOs.add(null);
        DzTagVO result = DzPromoUtils.buildDzTagVo(platform, dealProductPromoDTOs, 0);
        assertNull(result);
    }

    @Test
    public void testBuildDzTagVoWithEmptyProductPromoPriceMS() throws Throwable {
        int platform = 1;
        List<DealProductPromoDTO> dealProductPromoDTOs = new ArrayList<>();
        dealProductPromoDTOs.add(mock(DealProductPromoDTO.class));
        DzTagVO result = DzPromoUtils.buildDzTagVo(platform, dealProductPromoDTOs, 0);
        assertNull(result);
    }

    @Test
    public void testBuildDzTagVoWithNullPromo() throws Throwable {
        int platform = 1;
        List<DealProductPromoDTO> dealProductPromoDTOs = new ArrayList<>();
        DealProductPromoDTO dealProductPromoDTO = mock(DealProductPromoDTO.class);
        when(dealProductPromoDTO.getPromoType()).thenReturn(0);
        dealProductPromoDTOs.add(dealProductPromoDTO);
        DzTagVO result = DzPromoUtils.buildDzTagVo(platform, dealProductPromoDTOs, 0);
        assertNull(result);
    }

    @Test
    public void testBuildDzTagVoWithNonNullPromoAndEmptyPromoTag() throws Throwable {
        int platform = 1;
        List<DealProductPromoDTO> dealProductPromoDTOs = new ArrayList<>();
        DealProductPromoDTO dealProductPromoDTO = mock(DealProductPromoDTO.class);
        when(dealProductPromoDTO.getPromoType()).thenReturn(0);
        when(dealProductPromoDTO.getPromoTag()).thenReturn("");
        dealProductPromoDTOs.add(dealProductPromoDTO);
        DzTagVO result = DzPromoUtils.buildDzTagVo(platform, dealProductPromoDTOs, 0);
        assertNull(result);
    }

    @Test
    public void testBuildDzTagVoWithNonNullPromoAndNonEmptyPromoTag() throws Throwable {
        int platform = 1;
        List<DealProductPromoDTO> dealProductPromoDTOs = new ArrayList<>();
        DealProductPromoDTO dealProductPromoDTO = mock(DealProductPromoDTO.class);
        when(dealProductPromoDTO.getPromoType()).thenReturn(0);
        when(dealProductPromoDTO.getPromoTag()).thenReturn("promoTag");
        dealProductPromoDTOs.add(dealProductPromoDTO);
        DzTagVO result = DzPromoUtils.buildDzTagVo(platform, dealProductPromoDTOs, 0);
        assertNotNull(result);
    }

    /**
     * 测试 productM 为 null 的情况
     */
    @Test
    public void testFillPreSaleInfoProductMIsNull() throws Throwable {
        // Since the method under test does not handle null productM gracefully,
        // we should not call it with null productM to avoid NullPointerException.
        // Instead, we ensure the method does not throw an exception.
        DzPromoUtils.fillPreSaleInfo(null, dzPromoVO);
        verify(dzPromoVO, never()).setPrePic(any());
    }

    /**
     * 测试 productM 不是预售商品的情况
     */
    @Test
    public void testFillPreSaleInfoNotPreSaleDeal() throws Throwable {
        when(productM.getAttr(anyString())).thenReturn("false");
        DzPromoUtils.fillPreSaleInfo(productM, dzPromoVO);
        verify(dzPromoVO, never()).setPrePic(any());
    }

    /**
     * 测试 productM 是预售商品的情况
     */
    @Test
    public void testFillPreSaleInfoPreSaleDeal() throws Throwable {
        when(productM.getAttr(anyString())).thenReturn("true");
        DzPromoUtils.fillPreSaleInfo(productM, dzPromoVO);
        verify(dzPromoVO).setPrePic(any());
    }

    /**
     * 测试促销标签为空的情况
     */
    @Test
    public void testBuildBasicDzTagVOWithColorForBar_PromoTagIsNull() {
        // arrange
        int platform = 1;
        String promoTag = null;
        String mtTextColor = "#FFCC7788";
        String mtBorderColor = "#FFCFB2";
        String mtBackgroundColor = "#FFCC7788";
        String dpTextColor = "#FFCC7788";
        String dpBackgroundColor = "#FFCC7788";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColorForBar(platform, promoTag, mtTextColor, mtBorderColor, mtBackgroundColor, dpTextColor, dpBackgroundColor);
        // assert
        assertNull(result);
    }

    /**
     * 测试平台类型为MT的情况
     */
    @Test
    public void testBuildBasicDzTagVOWithColorForBar_PlatformIsMT() {
        // arrange
        int platform = 200;
        String promoTag = "促销标签";
        String mtTextColor = "#FFCC7788";
        String mtBorderColor = "#FFCFB2";
        String mtBackgroundColor = "#FFCC7788";
        String dpTextColor = "#FFCC7788";
        String dpBackgroundColor = "#FFCC7788";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColorForBar(platform, promoTag, mtTextColor, mtBorderColor, mtBackgroundColor, dpTextColor, dpBackgroundColor);
        // assert
        assertNotNull(result);
        assertEquals(mtTextColor, result.getTextColor());
        assertEquals(mtBorderColor, result.getBorderColor());
        assertEquals(mtBackgroundColor, result.getBackground());
    }

    /**
     * 测试平台类型为DP的情况
     */
    @Test
    public void testBuildBasicDzTagVOWithColorForBar_PlatformIsDP() {
        // arrange
        int platform = 100;
        String promoTag = "促销标签";
        String mtTextColor = "#FFCC7788";
        String mtBorderColor = "#FFCFB2";
        String mtBackgroundColor = "#FFCC7788";
        String dpTextColor = "#FFCC7788";
        String dpBackgroundColor = "#FFCC7788";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColorForBar(platform, promoTag, mtTextColor, mtBorderColor, mtBackgroundColor, dpTextColor, dpBackgroundColor);
        // assert
        assertNotNull(result);
        assertEquals(dpTextColor, result.getTextColor());
        assertEquals(dpBackgroundColor, result.getBackground());
    }

    /**
     * Test buildBasicDzTagVOWithColor when promoTag is null
     */
    @Test
    public void testBuildBasicDzTagVOWithColor_WhenPromoTagNull() {
        // arrange
        int platform = 1;
        String promoTag = null;
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColor(platform, promoTag, MT_TEXT_COLOR, MT_BORDER_COLOR, MT_BG_COLOR, DP_TEXT_COLOR, DP_BG_COLOR);
        // assert
        assertNull("Result should be null when promoTag is null", result);
    }

    /**
     * Test buildBasicDzTagVOWithColor when promoTag is empty string
     */
    @Test
    public void testBuildBasicDzTagVOWithColor_WhenPromoTagEmpty() {
        // arrange
        int platform = 1;
        String promoTag = "";
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColor(platform, promoTag, MT_TEXT_COLOR, MT_BORDER_COLOR, MT_BG_COLOR, DP_TEXT_COLOR, DP_BG_COLOR);
        // assert
        assertNull("Result should be null when promoTag is empty", result);
    }

    /**
     * Test buildBasicDzTagVOWithColor for MT platform
     */
    @Test
    public void testBuildBasicDzTagVOWithColor_ForMTPlatform() {
        // arrange
        // MT platform
        int platform = 2;
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColor(platform, PROMO_TAG, MT_TEXT_COLOR, MT_BORDER_COLOR, MT_BG_COLOR, DP_TEXT_COLOR, DP_BG_COLOR);
        // assert
        assertNotNull("Result should not be null for valid MT platform", result);
        assertEquals("Text color should match MT color", MT_TEXT_COLOR, result.getTextColor());
        assertEquals("Border color should match MT border color", MT_BORDER_COLOR, result.getBorderColor());
        assertEquals("Background color should match MT background color", MT_BG_COLOR, result.getBackground());
        assertEquals("Name should match promo tag", PROMO_TAG, result.getName());
        assertTrue("Should have border for MT platform", result.isHasBorder());
    }

    /**
     * Test buildBasicDzTagVOWithColor for DP platform
     */
    @Test
    public void testBuildBasicDzTagVOWithColor_ForDPPlatform() {
        // arrange
        // DP platform
        int platform = 1;
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColor(platform, PROMO_TAG, MT_TEXT_COLOR, MT_BORDER_COLOR, MT_BG_COLOR, DP_TEXT_COLOR, DP_BG_COLOR);
        // assert
        assertNotNull("Result should not be null for valid DP platform", result);
        assertEquals("Text color should match DP color", DP_TEXT_COLOR, result.getTextColor());
        assertEquals("Background color should match DP background color", DP_BG_COLOR, result.getBackground());
        assertEquals("Name should match promo tag", PROMO_TAG, result.getName());
        assertFalse("Should not have border for DP platform", result.isHasBorder());
    }

    /**
     * Test buildBasicDzTagVOWithColor for MT client type
     */
    @Test
    public void testBuildBasicDzTagVOWithColor_ForMTClientType() {
        // arrange
        // MT_APP client type
        int platform = 200;
        // act
        DzTagVO result = DzPromoUtils.buildBasicDzTagVOWithColor(platform, PROMO_TAG, MT_TEXT_COLOR, MT_BORDER_COLOR, MT_BG_COLOR, DP_TEXT_COLOR, DP_BG_COLOR);
        // assert
        assertNotNull("Result should not be null for valid MT client type", result);
        assertEquals("Text color should match MT color", MT_TEXT_COLOR, result.getTextColor());
        assertEquals("Border color should match MT border color", MT_BORDER_COLOR, result.getBorderColor());
        assertEquals("Background color should match MT background color", MT_BG_COLOR, result.getBackground());
        assertEquals("Name should match promo tag", PROMO_TAG, result.getName());
        assertTrue("Should have border for MT client type", result.isHasBorder());
    }
}
