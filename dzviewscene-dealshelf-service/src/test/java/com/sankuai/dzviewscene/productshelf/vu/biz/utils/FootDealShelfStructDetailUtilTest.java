package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.*;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FootDealShelfStructDetailUtilTest {

    private static final long ATTR_SERVICE_PROCESS_ID = 2597;

    @Mock
    private DealDetailDto dealDetailDto;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private SkuAttrItemDto skuAttrItemDto;

    /**
     * Creates a list of SkuAttrItemDto with the specified service duration
     */
    private List<SkuAttrItemDto> createAttrItems(int serviceDuration) {
        List<SkuAttrItemDto> attrItems = new ArrayList<>();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        // Create JSON string for ProjectProcessDTO
        String processJson = String.format("[{\"serviceDuration\":%d}]", serviceDuration);
        attrItems.add(attrItem);
        return attrItems;
    }

    /**
     * Test case for null DealDetailDto input
     */
    @Test
    public void testGetServiceDurationTagDealDetailDtoIsNull() throws Throwable {
        String result = FootDealShelfStructDetailUtil.getServiceDurationTag(null);
        assertNull("Expected null result when dealDetailDto is null", result);
    }

    /**
     * Test case for empty must SKU list
     */
    @Test
    public void testGetServiceDurationTagMustSkuListIsEmpty() throws Throwable {
        DealDetailDto dealDetailDto = mock(DealDetailDto.class);
        DealDetailSkuUniStructuredDto skuUniStructuredDto = mock(DealDetailSkuUniStructuredDto.class);
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(new ArrayList<>());
        when(skuUniStructuredDto.getOptionalGroups()).thenReturn(new ArrayList<>());
        String result = FootDealShelfStructDetailUtil.getServiceDurationTag(dealDetailDto);
        assertNull("Expected null result when mustSkuList is empty", result);
    }

    @Test
    public void testGetCorePartTagAttrValueNotContainsQuanShen() throws Throwable {
        DealDetailDto dealDetailDto = mock(DealDetailDto.class);
        DealDetailSkuUniStructuredDto skuUniStructuredDto = mock(DealDetailSkuUniStructuredDto.class);
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        MustSkuItemsGroupDto mustSkuItemsGroupDto = mock(MustSkuItemsGroupDto.class);
        List<SkuItemDto> mustSkuList = new ArrayList<>();
        SkuItemDto skuItemDto = mock(SkuItemDto.class);
        mustSkuList.add(skuItemDto);
        when(mustSkuItemsGroupDto.getSkuItems()).thenReturn(mustSkuList);
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        mustGroups.add(mustSkuItemsGroupDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(mustGroups);
        SkuAttrItemDto skuAttrItemDto = mock(SkuAttrItemDto.class);
        when(skuItemDto.getAttrItems()).thenReturn(java.util.Collections.singletonList(skuAttrItemDto));
        when(skuAttrItemDto.getAttrValue()).thenReturn("手臂");
        // Corrected the mocked method to return a long instead of SkuAttrItemDto
        when(skuAttrItemDto.getMetaAttrId()).thenReturn(2596L);
        String result = FootDealShelfStructDetailUtil.getCorePartTag(dealDetailDto);
        assertEquals("手臂", result);
    }

    @Test
    public void testGetCorePartTagAttrValueContainsQuanShen() throws Throwable {
        DealDetailDto dealDetailDto = mock(DealDetailDto.class);
        DealDetailSkuUniStructuredDto skuUniStructuredDto = mock(DealDetailSkuUniStructuredDto.class);
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        MustSkuItemsGroupDto mustSkuItemsGroupDto = mock(MustSkuItemsGroupDto.class);
        List<SkuItemDto> mustSkuList = new ArrayList<>();
        SkuItemDto skuItemDto = mock(SkuItemDto.class);
        mustSkuList.add(skuItemDto);
        when(mustSkuItemsGroupDto.getSkuItems()).thenReturn(mustSkuList);
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        mustGroups.add(mustSkuItemsGroupDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(mustGroups);
        SkuAttrItemDto skuAttrItemDto = mock(SkuAttrItemDto.class);
        when(skuItemDto.getAttrItems()).thenReturn(java.util.Collections.singletonList(skuAttrItemDto));
        when(skuAttrItemDto.getAttrValue()).thenReturn("全身");
        // Corrected the mocked method to return a long instead of SkuAttrItemDto
        when(skuAttrItemDto.getMetaAttrId()).thenReturn(2596L);
        String result = FootDealShelfStructDetailUtil.getCorePartTag(dealDetailDto);
        assertEquals("全身", result);
    }

    // Other test methods remain unchanged
    @Test
    public void testGetCorePartTagDealDetailDtoIsNull() throws Throwable {
        DealDetailDto dealDetailDto = null;
        String result = FootDealShelfStructDetailUtil.getCorePartTag(dealDetailDto);
        assertNull(result);
    }

    @Test
    public void testGetCorePartTagMustSkuListIsEmpty() throws Throwable {
        DealDetailDto dealDetailDto = mock(DealDetailDto.class);
        DealDetailSkuUniStructuredDto skuUniStructuredDto = mock(DealDetailSkuUniStructuredDto.class);
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(new ArrayList<>());
        String result = FootDealShelfStructDetailUtil.getCorePartTag(dealDetailDto);
        assertNull(result);
    }

    @Test
    public void testGetCorePartTagNoCorePartSkuItem() throws Throwable {
        DealDetailDto dealDetailDto = mock(DealDetailDto.class);
        DealDetailSkuUniStructuredDto skuUniStructuredDto = mock(DealDetailSkuUniStructuredDto.class);
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        MustSkuItemsGroupDto mustSkuItemsGroupDto = mock(MustSkuItemsGroupDto.class);
        List<SkuItemDto> mustSkuList = new ArrayList<>();
        mustSkuList.add(mock(SkuItemDto.class));
        when(mustSkuItemsGroupDto.getSkuItems()).thenReturn(mustSkuList);
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        mustGroups.add(mustSkuItemsGroupDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(mustGroups);
        String result = FootDealShelfStructDetailUtil.getCorePartTag(dealDetailDto);
        assertNull(result);
    }

    @Test
    public void testGetCorePartTagAttrValueIsNull() throws Throwable {
        DealDetailDto dealDetailDto = mock(DealDetailDto.class);
        DealDetailSkuUniStructuredDto skuUniStructuredDto = mock(DealDetailSkuUniStructuredDto.class);
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(skuUniStructuredDto);
        MustSkuItemsGroupDto mustSkuItemsGroupDto = mock(MustSkuItemsGroupDto.class);
        List<SkuItemDto> mustSkuList = new ArrayList<>();
        SkuItemDto skuItemDto = mock(SkuItemDto.class);
        mustSkuList.add(skuItemDto);
        when(mustSkuItemsGroupDto.getSkuItems()).thenReturn(mustSkuList);
        List<MustSkuItemsGroupDto> mustGroups = new ArrayList<>();
        mustGroups.add(mustSkuItemsGroupDto);
        when(skuUniStructuredDto.getMustGroups()).thenReturn(mustGroups);
        String result = FootDealShelfStructDetailUtil.getCorePartTag(dealDetailDto);
        assertNull(result);
    }

    @Test
    public void testGetSubCategoriesWhenDealDetailDtoIsNull() throws Throwable {
        List<String> result = FootDealShelfStructDetailUtil.getSubCategories(null);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetSubCategoriesWhenTotalSkuItemsIsEmpty() throws Throwable {
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(null);
        List<String> result = FootDealShelfStructDetailUtil.getSubCategories(dealDetailDto);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetSubCategoriesWhenProjectProcessDTOListIsEmpty() throws Throwable {
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(null);
        List<String> result = FootDealShelfStructDetailUtil.getSubCategories(dealDetailDto);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetSubCategoriesWhenAllProcessesIsEmpty() throws Throwable {
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(null);
        List<String> result = FootDealShelfStructDetailUtil.getSubCategories(dealDetailDto);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetSubCategoriesWhenAllProcessesIsNotEmptyButFilteredListIsEmpty() throws Throwable {
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(null);
        List<String> result = FootDealShelfStructDetailUtil.getSubCategories(dealDetailDto);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetSubCategoriesNormal() throws Throwable {
        when(dealDetailDto.getSkuUniStructuredDto()).thenReturn(null);
        List<String> result = FootDealShelfStructDetailUtil.getSubCategories(dealDetailDto);
        assertEquals(new ArrayList<>(), result);
    }
}
