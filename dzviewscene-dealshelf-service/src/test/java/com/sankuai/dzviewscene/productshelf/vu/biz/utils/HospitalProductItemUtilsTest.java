package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class HospitalProductItemUtilsTest {

    /**
     * 测试正常场景：hospitalDealProductThemeResList 不为空，且每个 ProductM 对象都能成功生成 DzItemVO。
     */
    @Test
    public void testBuildDzItemVOsNormalCase() throws Throwable {
        // arrange
        List<ProductM> hospitalDealProductThemeResList = new ArrayList<>();
        ProductM product1 = new ProductM();
        ProductM product2 = new ProductM();
        hospitalDealProductThemeResList.add(product1);
        hospitalDealProductThemeResList.add(product2);
        List<DouHuM> douHuList = new ArrayList<>();
        int platform = 1;
        String searchKeyword = "keyword";
        long filterId = 123L;
        // act
        List<DzItemVO> result = HospitalProductItemUtils.buildDzItemVOs(hospitalDealProductThemeResList, platform, searchKeyword, filterId, douHuList);
        // assert
        assertEquals(2, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(1));
    }

    /**
     * 测试边界场景：hospitalDealProductThemeResList 为空。
     */
    @Test
    public void testBuildDzItemVOsEmptyListCase() throws Throwable {
        // arrange
        List<ProductM> hospitalDealProductThemeResList = new ArrayList<>();
        List<DouHuM> douHuList = new ArrayList<>();
        int platform = 1;
        String searchKeyword = "keyword";
        long filterId = 123L;
        // act
        List<DzItemVO> result = HospitalProductItemUtils.buildDzItemVOs(hospitalDealProductThemeResList, platform, searchKeyword, filterId, douHuList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试异常场景：hospitalDealProductThemeResList 包含 null 元素。
     */
    @Test
    public void testBuildDzItemVOsNullElementCase() throws Throwable {
        // arrange
        List<ProductM> hospitalDealProductThemeResList = new ArrayList<>();
        hospitalDealProductThemeResList.add(null);
        List<DouHuM> douHuList = new ArrayList<>();
        int platform = 1;
        String searchKeyword = "keyword";
        long filterId = 123L;
        // act
        List<DzItemVO> result = HospitalProductItemUtils.buildDzItemVOs(hospitalDealProductThemeResList, platform, searchKeyword, filterId, douHuList);
        // assert
        assertEquals(1, result.size());
        assertNull(result.get(0));
    }

    /**
     * 测试异常场景：buildDzItem 方法抛出异常。
     */
    @Test
    public void testBuildDzItemVOsExceptionCase() throws Throwable {
        // arrange
        List<ProductM> hospitalDealProductThemeResList = new ArrayList<>();
        ProductM product1 = new ProductM();
        hospitalDealProductThemeResList.add(product1);
        List<DouHuM> douHuList = new ArrayList<>();
        int platform = 1;
        String searchKeyword = "keyword";
        long filterId = 123L;
        // act
        List<DzItemVO> result = HospitalProductItemUtils.buildDzItemVOs(hospitalDealProductThemeResList, platform, searchKeyword, filterId, douHuList);
        // assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
    }
}
