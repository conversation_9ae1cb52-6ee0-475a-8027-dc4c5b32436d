package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.sankuai.dzviewscene.productshelf.nr.assemble.res.FilterTabsRes;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/17 6:00 下午
 */
public class HospitalShelfItemUtilUnitTest {

    //@Test
    @DisplayName("测试HospitalShelfItemUtil中函数getProductNumUnderTabs()")
    public void test_getProductNumUnderTabs_given_filterTabsResList_should_returnNumberOfTabSeleted() {
        List<FilterTabsRes> filterTabsResList = buildFilterTabsResList();
        int a = HospitalShelfItemUtil.getSelectedTabTotalCountWithDefault(filterTabsResList);
        Assert.assertEquals(1,a);
    }

    private static List<FilterTabsRes> buildFilterTabsResList(){
        FilterTabsRes child1 = buildFilterTabsRes(1);
        FilterTabsRes child2 = buildFilterTabsRes(2);
        //child2.setSelected(true);
        FilterTabsRes child3 = buildFilterTabsRes(3);
        FilterTabsRes child4 = buildFilterTabsRes(4);
        FilterTabsRes f1 = buildFilterTabsRes(5);
        //f1.setSelected(true);
        f1.setChildTabs(new ArrayList<FilterTabsRes>(){{
            add(child1);
            add(child2);
            add(child3);
            add(child4);
        }});
        FilterTabsRes child7 = buildFilterTabsRes(7);
        FilterTabsRes child8 = buildFilterTabsRes(8);
        //child8.setSelected(true);
        FilterTabsRes f2 = buildFilterTabsRes(9);
        //f2.setSelected(true);
        f2.setChildTabs(new ArrayList<FilterTabsRes>(){{
            add(child7);
            add(child8);
        }});
        return new ArrayList<FilterTabsRes>(){{
            //FilterTabsRes f0 = buildFilterTabsRes(0);
            //f0.setSelected(true);
            //add(f0);
            add(f1);
            add(f2);
        }};
    }

    private static FilterTabsRes buildFilterTabsRes(int id) {
        FilterTabsRes filterTabsRes = new FilterTabsRes();
        filterTabsRes.setId(id);
        filterTabsRes.setTotalCount(id);
        return filterTabsRes;
    }

}
