package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.nr.assemble.res.ShelfTabRes;
import com.sankuai.dzviewscene.productshelf.vu.vo.TitleComponentVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class JoyTitleComponentUtilsTest {

    /**
     * Tests the buildTitleComponent method when platform is not VCClientTypeEnum.isMtClientTypeByCode(platform)'s return value.
     */
    @Test
    public void testBuildTitleComponentWhenPlatformIsNotMtClientType() throws Throwable {
        // Arrange
        int platform = 100;
        ShelfTabRes shelfTabRes = mock(ShelfTabRes.class);
        when(shelfTabRes.getHeadName()).thenReturn("headName");
        when(shelfTabRes.getDpHeadPic()).thenReturn("dpHeadPic");
        // Mocking the list to return an empty list
        when(shelfTabRes.getDpHeadTags()).thenReturn(java.util.Collections.emptyList());
        // Act
        TitleComponentVO result = JoyTitleComponentUtils.buildTitleComponent(platform, shelfTabRes);
        // Assert
        assertNotNull(result);
        assertEquals("headName", result.getTitle());
        assertEquals("dpHeadPic", result.getIcon());
    }
}
