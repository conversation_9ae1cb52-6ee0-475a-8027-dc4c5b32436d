package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RunWith(MockitoJUnitRunner.class)
public class LogsUtilsTest {

    @Mock
    private ActivityContext ctx;

    private static Logger originalLog;

    @Mock
    private Logger LOG;

    /**
     * Helper method to set dpIdThreadLocal using reflection.
     */
    private void setDpIdThreadLocal(String dpId) throws NoSuchFieldException, IllegalAccessException {
        Field dpIdThreadLocalField = LogsUtils.class.getDeclaredField("dpIdThreadLocal");
        dpIdThreadLocalField.setAccessible(true);
        ThreadLocal<String> dpIdThreadLocal = (ThreadLocal<String>) dpIdThreadLocalField.get(null);
        dpIdThreadLocal.set(dpId);
    }

    /**
     * Helper method to remove dpIdThreadLocal using reflection.
     */
    private void removeDpIdThreadLocal() throws NoSuchFieldException, IllegalAccessException {
        Field dpIdThreadLocalField = LogsUtils.class.getDeclaredField("dpIdThreadLocal");
        dpIdThreadLocalField.setAccessible(true);
        ThreadLocal<String> dpIdThreadLocal = (ThreadLocal<String>) dpIdThreadLocalField.get(null);
        dpIdThreadLocal.remove();
    }

    /**
     * 测试用户ID获取失败的情况
     */
    @Test
    public void testWriteUserLogUserIdGetFailed() throws Throwable {
        // arrange
        when(ctx.getParam(anyString())).thenThrow(new RuntimeException());
        // act
        LogsUtils.writeUserLog(ctx, "title", "obj");
        // assert
        verify(ctx, times(1)).getParam(anyString());
    }

    /**
     * 测试用户ID不在白名单中的情况
     */
    @Test
    public void testWriteUserLogUserIdNotInWhiteList() throws Throwable {
        // arrange
        when(ctx.getParam(anyString())).thenReturn("userId");
        // act
        LogsUtils.writeUserLog(ctx, "title", "obj");
        // assert
        verify(ctx, times(1)).getParam(anyString());
    }

    /**
     * Test successful logging when userId is in whitelist
     */
    @Test
    public void testWriteUserLogWhenUserInWhitelist() throws Throwable {
        // arrange
        String title = "test_title";
        long userId = 123L;
        Object[] testObj = new Object[] { "test" };
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<JsonCodec> jsonCodecMock = Mockito.mockStatic(JsonCodec.class)) {
            lionMock.when(() -> Lion.getStringValue("com.sankuai.dzviewscene.productshelf.log.userid.whitelist")).thenReturn("[123]");
            jsonCodecMock.when(() -> JsonCodec.encode(any())).thenReturn("encoded_content");
            // act
            LogsUtils.writeUserLog(title, userId, testObj);
            // assert
            // Verify that JsonCodec.encode was called with the testObj
            jsonCodecMock.verify(() -> JsonCodec.encode(testObj), times(1));
        }
    }

    /**
     * Test exception handling when Lion throws exception
     */
    @Test
    public void testWriteUserLogWhenLionThrowsException() throws Throwable {
        // arrange
        String title = "test_title";
        long userId = 123L;
        Object[] testObj = new Object[] { "test" };
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getStringValue(any())).thenThrow(new RuntimeException("Lion error"));
            // act
            LogsUtils.writeUserLog(title, userId, testObj);
            // assert
            // Verify that JsonCodec.encode was not called
            try (MockedStatic<JsonCodec> jsonCodecMock = Mockito.mockStatic(JsonCodec.class)) {
                jsonCodecMock.verify(() -> JsonCodec.encode(any()), never());
            }
        }
    }

    /**
     * Test exception handling when JSON encoding fails
     */
    @Test
    public void testWriteUserLogWhenJsonEncodingFails() throws Throwable {
        // arrange
        String title = "test_title";
        long userId = 123L;
        Object[] testObj = new Object[] { "test" };
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<JsonCodec> jsonCodecMock = Mockito.mockStatic(JsonCodec.class)) {
            lionMock.when(() -> Lion.getStringValue("com.sankuai.dzviewscene.productshelf.log.userid.whitelist")).thenReturn("[123]");
            jsonCodecMock.when(() -> JsonCodec.encode(any())).thenThrow(new RuntimeException("JSON encoding error"));
            // act
            LogsUtils.writeUserLog(title, userId, testObj);
            // assert
            // Verify that JsonCodec.encode was called with the testObj
            jsonCodecMock.verify(() -> JsonCodec.encode(testObj), times(1));
        }
    }

    /**
     * 测试 dpId 为空的情况
     */
    @Test
    public void testWriteDpIdIsNull() throws Throwable {
        // arrange
        String title = "title";
        Object obj = new Object();
        // act
        LogsUtils.write(title, obj);
        // assert
        verify(LOG, never()).info(anyString());
    }
}
