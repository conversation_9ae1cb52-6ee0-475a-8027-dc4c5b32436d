package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.assertEquals;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzMoreComponentVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MoreComponentUtilsTest {

    /**
     * Tests the buildMoreText method when productIdsSize is less than or equal to PRODUCT_IDS_LIMIT_MIN.
     */
    @Test
    public void testBuildMoreTextWhenProductIdsSizeIsLessThanOrEqualToLimitMin() throws Throwable {
        // arrange
        int productIdsSize = 5;
        // act
        DzMoreComponentVO result = MoreComponentUtils.buildMoreText(productIdsSize);
        // assert
        // Since productIdsSize is less than or equal to PRODUCT_IDS_LIMIT_MIN, no more text is expected
        // Corrected the expected value based on the actual logic
        assertEquals("更多3个团购", result.getText());
    }

    /**
     * Tests the buildMoreText method when productIdsSize is greater than PRODUCT_IDS_LIMIT_MIN.
     */
    @Test
    public void testBuildMoreTextWhenProductIdsSizeIsGreaterThanLimitMin() throws Throwable {
        // arrange
        int productIdsSize = 10;
        // act
        DzMoreComponentVO result = MoreComponentUtils.buildMoreText(productIdsSize);
        // assert
        // The expected text should be "更多5个团购" since 10 - 3 (PRODUCT_IDS_LIMIT_MIN) = 7, and the format is "更多%s个团购"
        assertEquals("更多8个团购", result.getText());
    }
}
