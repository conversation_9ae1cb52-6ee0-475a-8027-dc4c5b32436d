package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for ShopUserProfileUtils
 */
public class ShopUserProfileUtilsTest {

    /**
     * Test when both discount card and joy card are present
     * Should return DISCOUNT__JOY_CARD status
     */
    @Test
    public void testBuildProfile_BothDiscountAndJoyCards() {
        // arrange
        // DISCOUNT_CARD(1) and JOY_CARD(2)
        List<Integer> cardTypeList = Lists.newArrayList(1, 2);
        // act
        int result = ShopUserProfileUtils.buildProfile(cardTypeList);
        // assert
        // DISCOUNT__JOY_CARD status is 3
        assertEquals(3, result);
    }

    /**
     * Test when only discount card is present
     * Should return DISCOUNT_CARD status
     */
    @Test
    public void testBuildProfile_OnlyDiscountCard() {
        // arrange
        // DISCOUNT_CARD(1)
        List<Integer> cardTypeList = Lists.newArrayList(1);
        // act
        int result = ShopUserProfileUtils.buildProfile(cardTypeList);
        // assert
        // DISCOUNT_CARD status is 1
        assertEquals(1, result);
    }

    /**
     * Test when only joy card is present
     * Should return JOY_CARD status
     */
    @Test
    public void testBuildProfile_OnlyJoyCard() {
        // arrange
        // JOY_CARD(2)
        List<Integer> cardTypeList = Lists.newArrayList(2);
        // act
        int result = ShopUserProfileUtils.buildProfile(cardTypeList);
        // assert
        // JOY_CARD status is 2
        assertEquals(2, result);
    }

    /**
     * Test when no matching cards are present
     * Should return OFFLINE status
     */
    @Test
    public void testBuildProfile_NoMatchingCards() {
        // arrange
        // Other card types
        List<Integer> cardTypeList = Lists.newArrayList(3, 4);
        // act
        int result = ShopUserProfileUtils.buildProfile(cardTypeList);
        // assert
        // OFFLINE status is 4
        assertEquals(4, result);
    }
}
