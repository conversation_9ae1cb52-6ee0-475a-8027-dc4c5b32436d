package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import org.junit.Test;

public class VersionUtilsTest {

    /**
     * 测试 isUpVersionDp10_46_0 方法，当 isMt 为 true 时
     */
    @Test
    public void testIsUpVersionDp10_46_0_IsMtTrue() throws Throwable {
        // arrange
        boolean isMt = true;
        String version = "10.46.0";
        // act
        boolean result = VersionUtils.isUpVersionDp10_46_0(isMt, version);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isUpVersionDp10_46_0 方法，当 isMt 为 false，且 version 大于或等于 DP_VERSION_10_46_0 时
     */
    @Test
    public void testIsUpVersionDp10_46_0_IsMtFalseAndVersionGreaterOrEqual() throws Throwable {
        // arrange
        boolean isMt = false;
        String version = "10.46.1";
        // act
        boolean result = VersionUtils.isUpVersionDp10_46_0(isMt, version);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isUpVersionDp10_46_0 方法，当 isMt 为 false，且 version 小于 DP_VERSION_10_46_0 时
     */
    @Test
    public void testIsUpVersionDp10_46_0_IsMtFalseAndVersionLess() throws Throwable {
        // arrange
        boolean isMt = false;
        String version = "10.45.9";
        // act
        boolean result = VersionUtils.isUpVersionDp10_46_0(isMt, version);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 version 为空的情况
     */
    @Test
    public void testIsUpVersionMt_10_2_200OrDp10_17_0_VersionIsNull() {
        assertFalse(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(true, null));
        assertFalse(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(false, null));
    }

    /**
     * 测试 isMt 为 true，version 大于或等于 MT_YELLOW_VERSION 的情况
     */
    @Test
    public void testIsUpVersionMt_10_2_200OrDp10_17_0_IsMtTrueAndVersionIsUp() {
        assertTrue(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(true, "10.2.201"));
        assertTrue(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(true, "10.2.200"));
    }

    /**
     * 测试 isMt 为 true，version 小于 MT_YELLOW_VERSION 的情况
     */
    @Test
    public void testIsUpVersionMt_10_2_200OrDp10_17_0_IsMtTrueAndVersionIsNotUp() {
        assertFalse(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(true, "10.2.199"));
    }

    /**
     * 测试 isMt 为 false，version 大于或等于 DP_VERSION_10_17_0 的情况
     */
    @Test
    public void testIsUpVersionMt_10_2_200OrDp10_17_0_IsMtFalseAndVersionIsUp() {
        assertTrue(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(false, "10.17.1"));
        assertTrue(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(false, "10.17.0"));
    }

    /**
     * 测试 isMt 为 false，version 小于 DP_VERSION_10_17_0 的情况
     */
    @Test
    public void testIsUpVersionMt_10_2_200OrDp10_17_0_IsMtFalseAndVersionIsNotUp() {
        assertFalse(VersionUtils.isUpVersionMt_10_2_200OrDp10_17_0(false, "10.16.9"));
    }
}
