package com.sankuai.dzviewscene.productshelf.vu.biz.utils.medicalbeauty;

import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

@Ignore("没有可执行的方法")
@RunWith(MockitoJUnitRunner.class)
public class MBConfigUtilsTest {

    @Mock
    private Map<String, String> BToCFilterNameMapper;

    // Define the setUpCommonMocks method to setup common mocks for multiple tests
    private void setUpCommonMocks() {
        Map<String, String> mockMap = new HashMap<>();
        mockMap.put("tab1", "tab1");
        when(BToCFilterNameMapper.get("tab1")).thenReturn("tab1");
    }

}
