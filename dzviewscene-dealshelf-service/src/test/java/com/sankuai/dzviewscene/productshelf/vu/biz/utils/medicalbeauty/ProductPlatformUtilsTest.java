package com.sankuai.dzviewscene.productshelf.vu.biz.utils.medicalbeauty;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.vc.enums.VCClientTypeEnum;
import org.junit.Assert;
import org.junit.Test;

public class ProductPlatformUtilsTest {

    /**
     * 测试getClientType方法，客户端类型为"ios"，平台类型为美团
     */
    @Test
    public void testGetClientTypeIosMt() {
        // arrange
        int platform = VCClientTypeEnum.MT_APP.getCode();
        String client = "ios";
        // act
        int result = ProductPlatformUtils.getClientType(platform, client);
        // assert
        Assert.assertEquals(ClientTypeEnum.mt_mainApp_ios.getType(), result);
    }

    /**
     * 测试getClientType方法，客户端类型为"ios"，平台类型为大众点评
     */
    @Test
    public void testGetClientTypeIosDp() {
        // arrange
        int platform = VCClientTypeEnum.DP_APP.getCode();
        String client = "ios";
        // act
        int result = ProductPlatformUtils.getClientType(platform, client);
        // assert
        Assert.assertEquals(ClientTypeEnum.dp_mainApp_ios.getType(), result);
    }

    /**
     * 测试getClientType方法，客户端类型不为"ios"，平台类型为美团
     */
    @Test
    public void testGetClientTypeNotIosMt() {
        // arrange
        int platform = VCClientTypeEnum.MT_APP.getCode();
        String client = "android";
        // act
        int result = ProductPlatformUtils.getClientType(platform, client);
        // assert
        Assert.assertEquals(ClientTypeEnum.mt_mainApp_android.getType(), result);
    }

    /**
     * 测试getClientType方法，客户端类型不为"ios"，平台类型为大众点评
     */
    @Test
    public void testGetClientTypeNotIosDp() {
        // arrange
        int platform = VCClientTypeEnum.DP_APP.getCode();
        String client = "android";
        // act
        int result = ProductPlatformUtils.getClientType(platform, client);
        // assert
        Assert.assertEquals(ClientTypeEnum.dp_mainApp_android.getType(), result);
    }
}
