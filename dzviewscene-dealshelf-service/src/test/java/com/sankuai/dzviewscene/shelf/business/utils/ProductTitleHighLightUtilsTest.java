package com.sankuai.dzviewscene.shelf.business.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemRichLabelsTitleVP;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.vo.RichLabel;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.junit.runners.JUnit4;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

/**
 * 测试 ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductTitleHighLightUtilsTest {

    /**
     * 测试标题和关键词都不为空时的情况
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_Normal() {
        // arrange
        String title = "测试标题";
        List<String> keyWords = Arrays.asList("测试", "标题");
        // act
        List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWords);
        // assert
        assertNotNull("结果不应该为null", result);
        assertFalse("结果列表不应该为空", result.isEmpty());
    }

    /**
     * 测试标题为空时的情况
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_TitleNull() {
        // arrange
        String title = null;
        List<String> keyWords = Arrays.asList("测试", "标题");
        // act
        List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWords);
        // assert
        assertNull("当标题为空时，应返回null", result);
    }

    /**
     * 测试关键词列表为null时的情况
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_KeyWordsNull() {
        // arrange
        String title = "测试标题";
        List<String> keyWords = null;
        // act
        List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWords);
        // assert
        assertNull("当关键词列表为null时，应返回null", result);
    }

    /**
     * 测试场景：keyword，title 和 lightType 的值使得 readKeyWord 方法返回 null
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightReturnNull() throws Throwable {
        // arrange
        int platform = 1;
        String keyword = "keyword";
        String title = "title";
        String lightType = ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD;
        // act
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(platform, keyword, title, lightType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试场景：keyword，title 和 lightType 的值使得 readKeyWord 方法返回非 null 值
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightReturnNonNull() throws Throwable {
        // arrange
        int platform = 1;
        String keyword = "keyword";
        // Ensure the title contains the keyword to meet the method's criteria for a non-null return
        String title = "keyword is in the title";
        String lightType = ProductTitleHighLightUtils.LIGHT_TYPE_TOTAL_TITLE;
        // act
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(platform, keyword, title, lightType);
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试场景：DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight 方法返回 null
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightReturnNull2() throws Throwable {
        // arrange
        int platform = 1;
        String keyword = "keyword";
        String title = "title";
        String lightType = ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD;
        // act
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(platform, keyword, title, lightType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试场景：DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight 方法返回非 null 值
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightReturnNonNull2() throws Throwable {
        // arrange
        int platform = 1;
        String keyword = "keyword";
        // Ensure the title contains the keyword to meet the method's criteria for a non-null return
        String title = "keyword is in the title";
        String lightType = ProductTitleHighLightUtils.LIGHT_TYPE_TOTAL_TITLE;
        // act
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(platform, keyword, title, lightType);
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试场景：在执行过程中发生异常
     */
    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightException() throws Throwable {
        // arrange
        int platform = 1;
        String keyword = "keyword";
        String title = "title";
        String lightType = ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD;
        // act
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(platform, keyword, title, lightType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * Test case to cover the catch block and return null when exception occurs
     * Scenario: DzItemVOBuildUtils throws exception during processing
     */
    @Test
    public void testBuildStyleTitleWithKeyWordHighlight_WhenExceptionOccurs() throws Throwable {
        // arrange
        String title = "test title";
        List<String> keyWords = Arrays.asList("test");
        RuntimeException expectedException = new RuntimeException("test exception");
        try (MockedStatic<DzItemVOBuildUtils> dzItemVOBuildUtilsMock = Mockito.mockStatic(DzItemVOBuildUtils.class);
            MockedStatic<Cat> catMock = Mockito.mockStatic(Cat.class)) {
            // Mock DzItemVOBuildUtils to throw exception
            dzItemVOBuildUtilsMock.when(() -> DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight2(eq(title), eq(keyWords))).thenThrow(expectedException);
            // act
            List<StyleTextModel> result = ProductTitleHighLightUtils.buildStyleTitleWithKeyWordHighlight(title, keyWords);
            // assert
            assertNull("Should return null when exception occurs", result);
            // verify Cat.logError was called with correct parameters
            catMock.verify(() -> Cat.logError(eq("处理高亮失败"), eq(expectedException)));
        }
    }

    @Test
    public void test_buildRichLabelsTitleWithKeyWordHighlight_successfulEncoding() throws Throwable {
        try (MockedStatic<DzItemVOBuildUtils> mockedDzUtils = mockStatic(DzItemVOBuildUtils.class)) {
            ActivityContext activityContext = new ActivityContext();
            activityContext.setSceneCode("test_scene");
            activityContext.addParam(ShelfActivityConstants.Params.platform, 1);
            activityContext.addParam(ShelfActivityConstants.Params.searchKeyword, "test");
            List<RichLabel> mockLabels = Arrays.asList(new RichLabel("test", "#FF6633", 14, "Bold"));
            // Corrected the expected JSON string to match the actual output
            String expectedJson = "[{\"text\":\"test\",\"textcolor\":\"#FF6633\",\"textsize\":14,\"textstyle\":\"Bold\"}]";
            mockedDzUtils.when(() -> DzItemVOBuildUtils.buildRichLabelsTitleWithKeyWordHighlight(anyString(), anyString(), anyInt(), anyBoolean())).thenReturn(mockLabels);
            String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(activityContext, "test title", "keyWord");
            assertNotNull("Result should not be null", result);
            assertEquals("Should return expected JSON string", expectedJson, result);
        }
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightParamIsNull() throws Throwable {
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(null, "title", 16, "NONE");
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightNoHighlight() throws Throwable {
        ItemRichLabelsTitleVP.Param param = ItemRichLabelsTitleVP.Param.builder().sceneCode("").build();
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(param, "title", 16, "NONE");
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightEmptyWhiteCodes() throws Throwable {
        ItemRichLabelsTitleVP.Param param = ItemRichLabelsTitleVP.Param.builder().sceneCode("sceneCode").build();
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(param, "title", 16, "NONE");
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightNotContainsSceneCode() throws Throwable {
        ItemRichLabelsTitleVP.Param param = ItemRichLabelsTitleVP.Param.builder().sceneCode("sceneCode").build();
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(param, "title", 16, "NONE");
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightContainsSceneCode() throws Throwable {
        // Assuming "validSceneCode" is in CONFIG_WHITE_CODES
        ItemRichLabelsTitleVP.Param param = ItemRichLabelsTitleVP.Param.builder().sceneCode("validSceneCode").searchKeyword("keyword").title("Title containing keyword").build();
        // Use reflection to modify CONFIG_WHITE_CODES
        Field whiteCodesField = ProductTitleHighLightUtils.class.getDeclaredField("CONFIG_WHITE_CODES");
        whiteCodesField.setAccessible(true);
        List<String> originalWhiteCodes = (List<String>) whiteCodesField.get(null);
        whiteCodesField.set(null, Arrays.asList("validSceneCode"));
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(param, "title", 16, "NONE");
        assertNotNull(result);
        // Reset CONFIG_WHITE_CODES to its original state
        whiteCodesField.set(null, originalWhiteCodes);
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightReadKeywordIsNull() throws Throwable {
        ItemRichLabelsTitleVP.Param param = ItemRichLabelsTitleVP.Param.builder().sceneCode("sceneCode").build();
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(param, "title", 16, "NONE");
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightTitleLabelsIsEmpty() throws Throwable {
        ItemRichLabelsTitleVP.Param param = ItemRichLabelsTitleVP.Param.builder().sceneCode("sceneCode").build();
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(param, "title", 16, "NONE");
        assertNull(result);
    }

    @Test
    public void testBuildRichLabelsTitleWithKeyWordHighlightTitleLabelsIsNotEmpty() throws Throwable {
        // Assuming "validSceneCode" is in CONFIG_WHITE_CODES and "keyword" is present in "title"
        ItemRichLabelsTitleVP.Param param = ItemRichLabelsTitleVP.Param.builder().sceneCode("validSceneCode").searchKeyword("keyword").title("Title containing keyword").build();
        // Use reflection to modify CONFIG_WHITE_CODES
        Field whiteCodesField = ProductTitleHighLightUtils.class.getDeclaredField("CONFIG_WHITE_CODES");
        whiteCodesField.setAccessible(true);
        List<String> originalWhiteCodes = (List<String>) whiteCodesField.get(null);
        whiteCodesField.set(null, Arrays.asList("validSceneCode"));
        String result = ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(param, "title", 16, "NONE");
        assertNotNull(result);
        // Reset CONFIG_WHITE_CODES to its original state
        whiteCodesField.set(null, originalWhiteCodes);
    }
}
