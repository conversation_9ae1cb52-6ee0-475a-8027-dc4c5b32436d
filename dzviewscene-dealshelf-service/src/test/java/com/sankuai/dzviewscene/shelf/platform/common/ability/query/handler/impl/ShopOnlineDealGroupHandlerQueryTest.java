package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShopOnlineDealGroupHandlerQueryTest {

    @InjectMocks
    private ShopOnlineDealGroupHandler shopOnlineDealGroupHandler;

    @Mock
    private ActivityContext activityContext;

    @Mock
    private CompositeAtomService compositeAtomService;

    private MockedStatic<PoiMigrateUtils> mockedPoiMigrateUtils;

    @Before
    public void setUp() {
        mockedPoiMigrateUtils = Mockito.mockStatic(PoiMigrateUtils.class);
        // Default mock for compositeAtomService
        when(compositeAtomService.batchGetLongShopOnlineDealGroups(any())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        when(compositeAtomService.batchGetShopOnlineDealGroups(any())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
    }

    @After
    public void tearDown() {
        mockedPoiMigrateUtils.close();
    }

    @Test
    public void testQueryLongPoiProcessAndShopIdsNotEmpty() throws Throwable {
        // Mock PoiMigrateUtils to return true for long POI process
        mockedPoiMigrateUtils.when(() -> PoiMigrateUtils.needLongPoiProcess(any())).thenReturn(true);
        String groupName = "testGroupName";
        Map<String, Object> params = new HashMap<>();
        Map<String, List<Long>> groupName2ShopIds = new HashMap<>();
        groupName2ShopIds.put(groupName, Arrays.asList(1L, 2L, 3L));
        when(activityContext.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(activityContext.getParam(QueryFetcher.Params.groupName2ShopIds)).thenReturn(groupName2ShopIds);
        CompletableFuture<ProductGroupM> result = shopOnlineDealGroupHandler.query(activityContext, groupName, params);
        verify(activityContext, times(2)).getParam(anyString());
        assertNotNull(result);
    }

    @Test
    public void testQueryNotLongPoiProcessAndShopIdsNotEmpty() throws Throwable {
        // Mock PoiMigrateUtils to return false for long POI process
        mockedPoiMigrateUtils.when(() -> PoiMigrateUtils.needLongPoiProcess(any())).thenReturn(false);
        String groupName = "testGroupName";
        Map<String, Object> params = new HashMap<>();
        Map<String, List<Integer>> groupName2ShopIds = new HashMap<>();
        groupName2ShopIds.put(groupName, Arrays.asList(1, 2, 3));
        when(activityContext.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(activityContext.getParam(QueryFetcher.Params.groupName2ShopIds)).thenReturn(groupName2ShopIds);
        CompletableFuture<ProductGroupM> result = shopOnlineDealGroupHandler.query(activityContext, groupName, params);
        verify(activityContext, times(2)).getParam(anyString());
        assertNotNull(result);
    }
}
