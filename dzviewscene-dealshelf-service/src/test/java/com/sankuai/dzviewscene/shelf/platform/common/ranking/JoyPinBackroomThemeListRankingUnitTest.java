package com.sankuai.dzviewscene.shelf.platform.common.ranking;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans.AviatorCompareTransformer;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans.RankingContextMock;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class JoyPinBackroomThemeListRankingUnitTest {

    private RankingEngine rankingEngine = new DefaultRankingEngine();

    //@Test
    public void test_simplex_rank() throws Exception {
        RankingRequest rankingRequest = buildRankingRequest("10400003");
        // 指定排序ID
        List<ProductM> productMs = new ArrayList<>();
        productMs.add(mockProductM1());
        productMs.add(mockProductM2());
        productMs.add(mockProductM3());
        //List<ProductM> productMs = rankingEngine.getRanked(rankingRequest);

        productMs = productMs.stream().sorted(new Comparator<ProductM>() {
            @Override
            public int compare(ProductM o1, ProductM o2) {
                return new AviatorCompareTransformer(Lists.newArrayList("pre(\"attr('attr_single_shop_recommend_order')!= ''\")", "exp_str_asc(\"attr('attr_single_shop_recommend_order')\")")).compare(RankingContextMock.buildRankContext(), o1, o2);
            }
        }).collect(Collectors.toList());
        Assert.assertThat(productMs, Matchers.hasSize(0)); // 校验阶段3个
    }

    private RankingRequest buildRankingRequest(String rankId) {
        List<ProductM> productMs = new ArrayList<>();
        productMs.add(mockProductM1());
        productMs.add(mockProductM2());
        productMs.add(mockProductM3());
        productMs.add(mockProductM4());

        Map<String, Object> params = Maps.newHashMap();
        params.put("productIdList", Lists.newArrayList(1, 2, 3, 4));

        RankingRequest rankingRequest = new RankingRequest();
        rankingRequest.setItems(productMs);
        rankingRequest.setRankId(rankId);
        rankingRequest.setParams(params);
        return rankingRequest;
    }

    private ProductM mockProductM1() {
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setAttr("attr_backroom_pinIconUrl_optimized", "111");
        productM.setAttr(GeneralProductAttrEnum.ATTR_SINGLE_SHOP_RECOMMEND_ORDER.getKey(), "2");
        productM.setSale(buildProductSaleM(1));
        return productM;
    }

    private ProductM mockProductM2() {
        ProductM productM = new ProductM();
        productM.setProductId(2);
        productM.setAttr("attr_backroom_pinIconUrl_optimized", "111");
        productM.setAttr(GeneralProductAttrEnum.ATTR_SINGLE_SHOP_RECOMMEND_ORDER.getKey(), "1");
        return productM;
    }

    private ProductM mockProductM3() {
        ProductM productM = new ProductM();
        productM.setProductId(3);
        productM.setSale(buildProductSaleM(4));
        return productM;
    }

    private ProductM mockProductM4() {
        ProductM productM = new ProductM();
        productM.setProductId(4);
        productM.setSale(buildProductSaleM(0));
        return productM;
    }

    private ProductSaleM buildProductSaleM(int sale) {
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSale(sale);
        return productSaleM;
    }
}
