package com.sankuai.dzviewscene.shelf.platform.other.ability.builder.popup;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.business.other.popup.vo.DzPopupOceanVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PopupOceanBuilderExtAdaptorTest {

    /**
     * 测试 oceanVO 方法是否返回 null
     */
    @Test
    public void testOceanVO() throws Throwable {
        // arrange
        PopupOceanBuilderExtAdaptor adaptor = new PopupOceanBuilderExtAdaptor();
        ActivityContext ctx = new ActivityContext();
        // act
        DzPopupOceanVO result = adaptor.oceanVO(ctx);
        // assert
        assertNull(result);
    }
}
