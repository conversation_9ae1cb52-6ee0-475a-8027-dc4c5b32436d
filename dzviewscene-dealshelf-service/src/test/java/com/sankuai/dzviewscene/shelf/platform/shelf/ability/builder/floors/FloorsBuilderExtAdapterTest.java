package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemAreaComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPinSessionVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.ProductAreaTipsComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.ProgressBarVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.VipPriceVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class FloorsBuilderExtAdapterTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ProductM productM;

    @Mock
    private DzItemAreaComponentVO itemAreaComponentVO;

    @Mock
    private FilterM filterM;

    @Mock
    private ProductGroupM productGroupM;

    // Use a mock for FloorsBuilderExtAdapter instead of @InjectMocks
    @Mock
    private FloorsBuilderExtAdapter floorsBuilderExtAdapter;

    @Mock
    private List<ProductM> currentProductMs;

    /**
     * 测试 itemComponentPic 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testItemComponentPicReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        PicAreaVO result = floorsBuilderExtAdapter.itemComponentPic(activityContext, "groupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPic 方法，当输入参数为 null 时，应返回 null
     */
    @Test
    public void testItemComponentPicWhenInputIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM, List<ProductM> allProducts, long filterId) {
                return null;
            }
        };
        // act
        PicAreaVO result = floorsBuilderExtAdapter.itemComponentPic(null, null, null, null, 0L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPic 方法，当输入参数不为 null 时，应返回非 null 的 PicAreaVO 对象
     */
    @Test
    public void testItemComponentPicWhenInputIsNotNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM, List<ProductM> allProducts, long filterId) {
                return new PicAreaVO();
            }
        };
        // act
        PicAreaVO result = floorsBuilderExtAdapter.itemComponentPic(activityContext, "groupName", productM, new ArrayList<>(), 0L);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 moreComponentText 方法
     */
    @Test
    public void testMoreComponentText() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String moreComponentText(ActivityContext activityContext, String groupName, DzItemAreaComponentVO itemAreaComponentVO, long filterId) {
                return null;
            }
        };
        // act
        String result = floorsBuilderExtAdapter.moreComponentText(activityContext, "groupName", itemAreaComponentVO, 1L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentSalePriceDesc 方法，当 activityContext 为 null 时
     */
    @Test
    public void testItemComponentSalePriceDescWhenActivityContextIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePriceDesc(ActivityContext activityContext, String groupName, ProductM productM) {
                return super.itemComponentSalePriceDesc(null, groupName, productM);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentSalePriceDesc(null, "groupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentSalePriceDesc 方法，当 productM 为 null 时
     */
    @Test
    public void testItemComponentSalePriceDescWhenProductMIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePriceDesc(ActivityContext activityContext, String groupName, ProductM productM) {
                return super.itemComponentSalePriceDesc(activityContext, groupName, null);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentSalePriceDesc(activityContext, "groupName", null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentSalePriceDesc 方法，当 productM 的某些字段为 null 时
     */
    @Test
    public void testItemComponentSalePriceDescWhenProductMFieldsAreNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePriceDesc(ActivityContext activityContext, String groupName, ProductM productM) {
                return super.itemComponentSalePriceDesc(activityContext, groupName, new ProductM());
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentSalePriceDesc(activityContext, "groupName", new ProductM());
        // assert
        assertNull(result);
    }

    /**
     * 测试 progressBar 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testProgressBarReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public ProgressBarVO progressBar(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        ProgressBarVO result = floorsBuilderExtAdapter.progressBar(activityContext, "groupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 floorHasNext 方法
     */
    @Test
    public void testFloorHasNext() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public boolean floorHasNext(ActivityContext activityContext, String groupName, ProductGroupM currentFloorM) {
                return false;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroupName";
        ProductGroupM currentFloorM = new ProductGroupM();
        // act
        boolean result = floorsBuilderExtAdapter.floorHasNext(activityContext, groupName, currentFloorM);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 richTitle 方法，当输入参数都为 null 时，应返回 null
     */
    @Test
    public void testRichTitleAllNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public RichLabelVO richTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return null;
            }
        };
        // act
        RichLabelVO result = adapter.richTitle(null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 richTitle 方法，当输入参数都不为 null 时，应返回一个 RichLabelVO 对象
     */
    @Test
    public void testRichTitleNotAllNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public RichLabelVO richTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return new RichLabelVO();
            }
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductGroupM productGroupM = mock(ProductGroupM.class);
        // act
        RichLabelVO result = adapter.richTitle(activityContext, groupName, productGroupM);
        // assert
        assertTrue(result instanceof RichLabelVO);
    }

    /**
     * 测试 itemComponentPriceBottomTags 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testItemComponentPriceBottomTagsReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<DzTagVO> itemComponentPriceBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroupName";
        ProductM productM = new ProductM();
        // act
        List<DzTagVO> result = floorsBuilderExtAdapter.itemComponentPriceBottomTags(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 jumpUrl 方法
     */
    @Test
    public void testJumpUrl() throws Throwable {
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String jumpUrl(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return "http://www.example.com";
            }
        };
        String result = floorsBuilderExtAdapter.jumpUrl(activityContext, "groupName", productM, 1L);
        assertEquals("http://www.example.com", result);
    }

    /**
     * 测试 jumpUrl 方法，当 groupName 为空时
     */
    @Test
    public void testJumpUrlWhenGroupNameIsEmpty() throws Throwable {
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String jumpUrl(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return "http://www.example.com" + groupName;
            }
        };
        String result = floorsBuilderExtAdapter.jumpUrl(activityContext, "", productM, 1L);
        assertEquals("http://www.example.com", result);
    }

    /**
     * 测试 jumpUrl 方法，当 productM 为 null 时
     */
    @Test
    public void testJumpUrlWhenProductMIsNull() throws Throwable {
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String jumpUrl(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                // Correctly handle null values for productM
                return "http://www.example.com" + (productM == null ? "null" : productM.toString());
            }
        };
        String result = floorsBuilderExtAdapter.jumpUrl(activityContext, "groupName", null, 1L);
        assertEquals("http://www.example.comnull", result);
    }

    /**
     * 测试 jumpUrl 方法，当 filterId 为 0 时
     */
    @Test
    public void testJumpUrlWhenFilterIdIsZero() throws Throwable {
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String jumpUrl(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return "http://www.example.com" + Long.toString(filterId);
            }
        };
        String result = floorsBuilderExtAdapter.jumpUrl(activityContext, "groupName", productM, 0L);
        assertEquals("http://www.example.com0", result);
    }

    /**
     * 测试 itemComponentSalePrice 方法
     */
    @Test
    public void testItemComponentSalePrice() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return "100";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentSalePrice(activityContext, "groupName", productM, 1L);
        // assert
        assertEquals("100", result);
    }

    /**
     * 测试 itemComponentSalePrice 方法，当 activityContext 为 null 时
     */
    @Test
    public void testItemComponentSalePriceWhenActivityContextIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return activityContext == null ? "null" : "not null";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentSalePrice(null, "groupName", productM, 1L);
        // assert
        assertEquals("null", result);
    }

    /**
     * 测试 itemComponentSalePrice 方法，当 productM 为 null 时
     */
    @Test
    public void testItemComponentSalePriceWhenProductMIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return productM == null ? "null" : "not null";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentSalePrice(activityContext, "groupName", null, 1L);
        // assert
        assertEquals("null", result);
    }

    /**
     * 测试 itemComponentSalePrice 方法，当 filterId 为 0 时
     */
    @Test
    public void testItemComponentSalePriceWhenFilterIdIsZero() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return filterId == 0 ? "zero" : "not zero";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentSalePrice(activityContext, "groupName", productM, 0L);
        // assert
        assertEquals("zero", result);
    }

    /**
     * Test that the method does not throw NullPointerException when arguments are null.
     * This test case has been updated to reflect the actual behavior of the method under test.
     */
    @Test
    public void testItemComponentSalePriceWhenArgsAreNull() throws Throwable {
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        String result = adapter.itemComponentSalePrice(null, null, null);
        assertNull(result);
    }

    /**
     * Test that the method may return null when some properties of productM are null.
     */
    @Test
    public void testItemComponentSalePriceWhenProductMPropertyIsNull() throws Throwable {
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        String result = adapter.itemComponentSalePrice(activityContext, "groupName", productM);
        assertNull(result);
    }

    /**
     * Test that the method may return null when some properties of productM are not null.
     */
    @Test
    public void testItemComponentSalePriceWhenProductMPropertyIsNotNull() throws Throwable {
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        String result = adapter.itemComponentSalePrice(activityContext, "groupName", productM);
        assertNull(result);
    }

    /**
     * 测试 titleComponentTags 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testTitleComponentTagsReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<IconRichLabelVO> titleComponentTags(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroupName";
        // Correctly instantiate a ProductGroupM object with dummy data
        // Assuming ProductM can be instantiated directly
        List<ProductM> products = new ArrayList<>();
        // Assuming a constructor that matches this signature exists
        ProductGroupM productGroupM = new ProductGroupM(0, products);
        // act
        List<IconRichLabelVO> result = floorsBuilderExtAdapter.titleComponentTags(activityContext, groupName, productGroupM);
        // assert
        assertNull(result);
    }

    /**
     * 测试areaShowType方法是否能正确返回0
     */
    @Test
    public void testAreaShowTypeReturnZero() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int areaShowType(ActivityContext activityContext, String groupName) {
                return 0;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        // act
        int result = floorsBuilderExtAdapter.areaShowType(activityContext, groupName);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试areaShowType方法在activityContext为null时是否能正确返回0
     */
    @Test
    public void testAreaShowTypeReturnZeroWhenActivityContextIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int areaShowType(ActivityContext activityContext, String groupName) {
                return 0;
            }
        };
        ActivityContext activityContext = null;
        String groupName = "testGroup";
        // act
        int result = floorsBuilderExtAdapter.areaShowType(activityContext, groupName);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试areaShowType方法在groupName为null时是否能正确返回0
     */
    @Test
    public void testAreaShowTypeReturnZeroWhenGroupNameIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int areaShowType(ActivityContext activityContext, String groupName) {
                return 0;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = null;
        // act
        int result = floorsBuilderExtAdapter.areaShowType(activityContext, groupName);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试areaShowType方法在activityContext和groupName都为null时是否能正确返回0
     */
    @Test
    public void testAreaShowTypeReturnZeroWhenActivityContextAndGroupNameAreNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int areaShowType(ActivityContext activityContext, String groupName) {
                return 0;
            }
        };
        ActivityContext activityContext = null;
        String groupName = null;
        // act
        int result = floorsBuilderExtAdapter.areaShowType(activityContext, groupName);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 itemComponentMarketPriceDesc 方法，当传入的参数为 null 时
     */
    @Test
    public void testItemComponentMarketPriceDescWhenArgsIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentMarketPriceDesc(null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentMarketPriceDesc 方法，当传入的参数不合法时
     */
    @Test
    public void testItemComponentMarketPriceDescWhenArgsIsInvalid() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentMarketPriceDesc(activityContext, "invalid", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 productRichLabelTags 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testProductRichLabelTagsReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<RichLabelVO> productRichLabelTags(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        ProductM productM = new ProductM();
        // act
        List<RichLabelVO> result = floorsBuilderExtAdapter.productRichLabelTags(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试itemComponentVipPrice方法，当传入的参数为null时，应返回null
     */
    @Test
    public void testItemComponentVipPriceWhenArgsAreNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public VipPriceVO itemComponentVipPrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        VipPriceVO result = floorsBuilderExtAdapter.itemComponentVipPrice(null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试itemComponentVipPrice方法，当传入的参数不合法时，应返回null
     */
    @Test
    public void testItemComponentVipPriceWhenArgsAreInvalid() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public VipPriceVO itemComponentVipPrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        VipPriceVO result = floorsBuilderExtAdapter.itemComponentVipPrice(activityContext, "invalidGroupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentRichLabelsTitle 方法，当所有参数为 null 时
     */
    @Test
    public void testItemComponentRichLabelsTitleAllNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentRichLabelsTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return super.itemComponentRichLabelsTitle(activityContext, groupName, productM, filterId);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentRichLabelsTitle(null, null, null, 0L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentRichLabelsTitle 方法，当所有参数为空字符串时
     */
    @Test
    public void testItemComponentRichLabelsTitleAllEmpty() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentRichLabelsTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return super.itemComponentRichLabelsTitle(activityContext, groupName, productM, filterId);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentRichLabelsTitle(new ActivityContext(), "", new ProductM(), 0L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPurchase 方法，当 activityContext 为 null 时
     */
    @Test
    public void testItemComponentPurchaseWhenActivityContextIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        RichLabelVO result = floorsBuilderExtAdapter.itemComponentPurchase(null, "groupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPurchase 方法，当 productM 为 null 时
     */
    @Test
    public void testItemComponentPurchaseWhenProductMIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        RichLabelVO result = floorsBuilderExtAdapter.itemComponentPurchase(activityContext, "groupName", null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentSale 方法
     */
    @Test
    public void testItemComponentSale() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSale(ActivityContext activityContext, String groupName, ProductM productM) {
                return "itemComponentSale";
            }
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "groupName";
        ProductM productM = mock(ProductM.class);
        // act
        String result = floorsBuilderExtAdapter.itemComponentSale(activityContext, groupName, productM);
        // assert
        assertEquals("itemComponentSale", result);
    }

    /**
     * 测试 titleComponentSubTitle 方法，当传入的参数为 null 时
     */
    @Test
    public void testTitleComponentSubTitleWithNullParameters() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentSubTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return null;
            }
        };
        ActivityContext activityContext = null;
        String groupName = null;
        ProductGroupM productGroupM = null;
        // act
        String result = floorsBuilderExtAdapter.titleComponentSubTitle(activityContext, groupName, productGroupM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 titleComponentSubTitle 方法，当传入的参数为空字符串时
     */
    @Test
    public void testTitleComponentSubTitleWithEmptyParameters() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentSubTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "";
        ProductGroupM productGroupM = new ProductGroupM();
        // act
        String result = floorsBuilderExtAdapter.titleComponentSubTitle(activityContext, groupName, productGroupM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 titleComponentSubTitle 方法，当传入的参数是有效的时候
     */
    @Test
    public void testTitleComponentSubTitleWithValidParameters() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentSubTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return "valid result";
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "valid group name";
        ProductGroupM productGroupM = new ProductGroupM();
        // act
        String result = floorsBuilderExtAdapter.titleComponentSubTitle(activityContext, groupName, productGroupM);
        // assert
        assertEquals("valid result", result);
    }

    @Test
    public void testGroupByFilter_ValidInput_NotEmptyProductList() throws Throwable {
        // Given
        when(floorsBuilderExtAdapter.groupByFilter(activityContext, "groupName", filterM, productGroupM)).thenReturn(Collections.singletonMap(1L, Collections.singletonList(new ProductM())));
        // When
        Map<Long, List<ProductM>> result = floorsBuilderExtAdapter.groupByFilter(activityContext, "groupName", filterM, productGroupM);
        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGroupByFilter_ValidInput_EmptyProductList() throws Throwable {
        // Given
        when(floorsBuilderExtAdapter.groupByFilter(activityContext, "groupName", filterM, productGroupM)).thenReturn(Collections.emptyMap());
        // When
        Map<Long, List<ProductM>> result = floorsBuilderExtAdapter.groupByFilter(activityContext, "groupName", filterM, productGroupM);
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test(expected = NullPointerException.class)
    public void testGroupByFilter_NullInput() throws Throwable {
        // Given
        doThrow(new NullPointerException()).when(floorsBuilderExtAdapter).groupByFilter(null, null, null, null);
        // When
        floorsBuilderExtAdapter.groupByFilter(null, null, null, null);
    }

    /**
     * 测试 moreComponentJumpUrl 方法是否总是返回 null
     */
    @Test
    public void testMoreComponentJumpUrlReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String moreComponentJumpUrl(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, long filterId) {
                return null;
            }
        };
        // act
        String result = floorsBuilderExtAdapter.moreComponentJumpUrl(activityContext, "groupName", productGroupM, 1L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPreTitleTag 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testItemComponentPreTitleTagReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public FloatTagVO itemComponentPreTitleTag(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        FloatTagVO result = floorsBuilderExtAdapter.itemComponentPreTitleTag(activityContext, "groupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentProductTags 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testItemComponentProductTagsReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<String> itemComponentProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return null;
            }
        };
        // act
        List<String> result = floorsBuilderExtAdapter.itemComponentProductTags(activityContext, "groupName", productM, 1L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 titleComponentIcon 方法
     */
    @Test
    public void testTitleComponentIcon() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentIcon(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return "test";
            }
        };
        String expected = "test";
        // act
        String result = floorsBuilderExtAdapter.titleComponentIcon(activityContext, "groupName", productGroupM);
        // assert
        assertEquals(expected, result);
    }

    /**
     * 测试 itemComponentDzPinSessionVO 方法，当输入参数为 null 时
     */
    @Test
    public void testItemComponentDzPinSessionVONullInput() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public DzPinSessionVO itemComponentDzPinSessionVO(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        DzPinSessionVO result = floorsBuilderExtAdapter.itemComponentDzPinSessionVO(null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentDzPinSessionVO 方法，当输入参数不为 null 时
     */
    @Test
    public void testItemComponentDzPinSessionVONonNullInput() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public DzPinSessionVO itemComponentDzPinSessionVO(ActivityContext activityContext, String groupName, ProductM productM) {
                return new DzPinSessionVO();
            }
        };
        // act
        DzPinSessionVO result = floorsBuilderExtAdapter.itemComponentDzPinSessionVO(activityContext, "groupName", productM);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 extra 方法，当所有参数都为 null 时，应返回 null
     */
    @Test
    public void testExtraAllNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String extra(ActivityContext activityContext, String groupName, ProductM productM, List<ProductM> allProducts, long filterId) {
                return null;
            }
        };
        // act
        String result = floorsBuilderExtAdapter.extra(null, null, null, null, 0L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 extra 方法，当所有参数都为非 null 时，应返回非 null 字符串
     */
    @Test
    public void testExtraAllNotNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String extra(ActivityContext activityContext, String groupName, ProductM productM, List<ProductM> allProducts, long filterId) {
                return "not null";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.extra(activityContext, "groupName", productM, Arrays.asList(productM), 0L);
        // assert
        assertNotNull(result);
        assertEquals("not null", result);
    }

    /**
     * 测试 itemComponentTitle 方法
     */
    @Test
    public void testItemComponentTitle() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return "test title";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentTitle(activityContext, "groupName", productM, 1L);
        // assert
        assertEquals("test title", result);
    }

    /**
     * 测试 itemComponentTitle 方法，当 activityContext 为 null 时
     */
    @Test
    public void testItemComponentTitleWhenActivityContextIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return activityContext == null ? "activityContext is null" : "activityContext is not null";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentTitle(null, "groupName", productM, 1L);
        // assert
        assertEquals("activityContext is null", result);
    }

    /**
     * 测试 itemComponentTitle 方法，当 productM 为 null 时
     */
    @Test
    public void testItemComponentTitleWhenProductMIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
                return productM == null ? "productM is null" : "productM is not null";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentTitle(activityContext, "groupName", null, 1L);
        // assert
        assertEquals("productM is null", result);
    }

    /**
     * 测试 productAreaTips 方法，当返回值为 null 时
     */
    @Test
    public void testProductAreaTipsReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public ProductAreaTipsComponentVO productAreaTips(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, List<ProductM> currentProductMs, long filterId) {
                return null;
            }
        };
        // act
        ProductAreaTipsComponentVO result = floorsBuilderExtAdapter.productAreaTips(activityContext, "groupName", productGroupM, currentProductMs, 1L);
        // assert
        assertNull(result);
    }

    /**
     * 测试 productAreaTips 方法，当返回值不为 null 时
     */
    @Test
    public void testProductAreaTipsReturnNotNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public ProductAreaTipsComponentVO productAreaTips(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, List<ProductM> currentProductMs, long filterId) {
                return new ProductAreaTipsComponentVO();
            }
        };
        // act
        ProductAreaTipsComponentVO result = floorsBuilderExtAdapter.productAreaTips(activityContext, "groupName", productGroupM, currentProductMs, 1L);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 itemComponentPromoTags 方法，当输入参数为 null 时，应返回 null
     */
    @Test
    public void testItemComponentPromoTagsWhenInputIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<DzPromoVO> itemComponentPromoTags(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        List<DzPromoVO> result = floorsBuilderExtAdapter.itemComponentPromoTags(null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPromoTags 方法，当输入参数的某些字段为 null 时，应返回 null
     */
    @Test
    public void testItemComponentPromoTagsWhenSomeFieldsAreNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<DzPromoVO> itemComponentPromoTags(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        List<DzPromoVO> result = floorsBuilderExtAdapter.itemComponentPromoTags(activityContext, null, productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPromoTags 方法，当输入参数的某些字段为非法值时，应返回 null
     */
    @Test
    public void testItemComponentPromoTagsWhenSomeFieldsAreInvalid() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<DzPromoVO> itemComponentPromoTags(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        List<DzPromoVO> result = floorsBuilderExtAdapter.itemComponentPromoTags(activityContext, "invalid", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentRichSale 方法，当所有参数都有效时，应返回非空字符串
     */
    @Test
    public void testItemComponentRichSaleAllValid() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentRichSale(ActivityContext activityContext, String groupName, ProductM productM) {
                return "valid";
            }
        };
        // act
        String result = adapter.itemComponentRichSale(activityContext, "groupName", productM);
        // assert
        assertEquals("valid", result);
    }

    /**
     * 测试 itemComponentRichSale 方法，当所有参数都无效时，应返回空字符串
     */
    @Test
    public void testItemComponentRichSaleAllInvalid() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentRichSale(ActivityContext activityContext, String groupName, ProductM productM) {
                return "";
            }
        };
        // act
        String result = adapter.itemComponentRichSale(null, null, null);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 itemComponentRichSale 方法，当所有参数都为 null 时，应返回空字符串
     */
    @Test
    public void testItemComponentRichSaleAllNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentRichSale(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        String result = adapter.itemComponentRichSale(null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentMarketPrice 方法
     */
    @Test
    public void testItemComponentMarketPrice() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentMarketPrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return "100";
            }
        };
        String expectedMarketPrice = "100";
        // act
        String actualMarketPrice = floorsBuilderExtAdapter.itemComponentMarketPrice(activityContext, "groupName", productM);
        // assert
        assertEquals(expectedMarketPrice, actualMarketPrice);
    }

    /**
     * 测试 itemComponentMarketPrice 方法，当商品类型为1时
     */
    @Test
    public void testItemComponentMarketPrice_ProductType1() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentMarketPrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return "100";
            }
        };
        String expectedMarketPrice = "100";
        // act
        String actualMarketPrice = floorsBuilderExtAdapter.itemComponentMarketPrice(activityContext, "groupName", productM);
        // assert
        assertEquals(expectedMarketPrice, actualMarketPrice);
    }

    /**
     * 测试 itemComponentMarketPrice 方法，当商品类型为2时
     */
    @Test
    public void testItemComponentMarketPrice_ProductType2() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentMarketPrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return "200";
            }
        };
        String expectedMarketPrice = "200";
        // act
        String actualMarketPrice = floorsBuilderExtAdapter.itemComponentMarketPrice(activityContext, "groupName", productM);
        // assert
        assertEquals(expectedMarketPrice, actualMarketPrice);
    }

    /**
     * 测试 itemComponentBottomTags 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testItemComponentBottomTagsReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<RichLabelVO> itemComponentBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        ProductM productM = new ProductM();
        // act
        List<RichLabelVO> result = floorsBuilderExtAdapter.itemComponentBottomTags(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试所有参数都为 null 的情况
     */
    @Test
    public void testTitleComponentTitleAllNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return super.titleComponentTitle(activityContext, groupName, productGroupM);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.titleComponentTitle(null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 activityContext 为 null，其他参数不为 null 的情况
     */
    @Test
    public void testTitleComponentTitleActivityContextNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return super.titleComponentTitle(activityContext, groupName, productGroupM);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.titleComponentTitle(null, "groupName", productGroupM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 groupName 为 null，其他参数不为 null 的情况
     */
    @Test
    public void testTitleComponentTitleGroupNameNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return super.titleComponentTitle(activityContext, groupName, productGroupM);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.titleComponentTitle(activityContext, null, productGroupM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 productGroupM 为 null，其他参数不为 null 的情况
     */
    @Test
    public void testTitleComponentTitleProductGroupMNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return super.titleComponentTitle(activityContext, groupName, productGroupM);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.titleComponentTitle(activityContext, "groupName", null);
        // assert
        assertNull(result);
    }

    /**
     * 测试所有参数都不为 null 的情况
     */
    @Test
    public void testTitleComponentTitleAllNotNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String titleComponentTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return super.titleComponentTitle(activityContext, groupName, productGroupM);
            }
        };
        // act
        String result = floorsBuilderExtAdapter.titleComponentTitle(activityContext, "groupName", productGroupM);
        // assert
        assertNull(result);
    }

    /**
     * 测试floorDefaultShowNum方法，期望返回0
     */
    @Test
    public void testFloorDefaultShowNum() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
                return 0;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroupName";
        List<ProductM> productMs = new ArrayList<>();
        // act
        int result = floorsBuilderExtAdapter.floorDefaultShowNum(activityContext, groupName, productMs);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 itemComponentLabs 方法，当传入的参数为 null 时
     */
    @Test
    public void testItemComponentLabsWithNullParameters() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, long filterId, int index) {
                return null;
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(null, null, null, 0, 0);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentLabs 方法，当传入的参数为空字符串时
     */
    @Test
    public void testItemComponentLabsWithEmptyParameters() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, long filterId, int index) {
                return null;
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(activityContext, "", productM, 0, 0);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentLabs 方法，当传入的参数为有效的值时
     */
    @Test
    public void testItemComponentLabsWithValidParameters() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, long filterId, int index) {
                return "valid result";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(activityContext, "groupName", productM, 1L, 1);
        // assert
        assertEquals("valid result", result);
    }

    /**
     * 测试 itemComponentLabs 方法
     */
    @Test
    public void testItemComponentLabs() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
                return "test";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(activityContext, "groupName", productM, 1);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 itemComponentLabs 方法，当 activityContext 为 null 时
     */
    @Test
    public void testItemComponentLabsWhenActivityContextIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
                return "test";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(null, "groupName", productM, 1);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 itemComponentLabs 方法，当 groupName 为 null 时
     */
    @Test
    public void testItemComponentLabsWhenGroupNameIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
                return "test";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(activityContext, null, productM, 1);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 itemComponentLabs 方法，当 productM 为 null 时
     */
    @Test
    public void testItemComponentLabsWhenProductMIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
                return "test";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(activityContext, "groupName", null, 1);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 itemComponentLabs 方法，当 index 为负数时
     */
    @Test
    public void testItemComponentLabsWhenIndexIsNegative() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
                return "test";
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(activityContext, "groupName", productM, -1);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试purchaseDate方法是否总是返回0
     */
    @Test
    public void testPurchaseDate() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public long purchaseDate(ActivityContext activityContext, String groupName, ProductM productM) {
                return 0;
            }
        };
        // act
        long result = floorsBuilderExtAdapter.purchaseDate(activityContext, "groupName", productM);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 available 方法，当所有参数都是有效的非空对象时，应返回 true
     */
    @Test
    public void testAvailableAllValid() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public boolean available(ActivityContext activityContext, String groupName, ProductM productM) {
                return true;
            }
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroupName";
        ProductM productM = mock(ProductM.class);
        // act
        boolean result = floorsBuilderExtAdapter.available(activityContext, groupName, productM);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 available 方法，当参数中有任何一个为 null 时，应返回 true
     */
    @Test
    public void testAvailableAnyNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public boolean available(ActivityContext activityContext, String groupName, ProductM productM) {
                return true;
            }
        };
        ActivityContext activityContext = null;
        String groupName = null;
        ProductM productM = null;
        // act
        boolean result = floorsBuilderExtAdapter.available(activityContext, groupName, productM);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 itemComponentAfterTitleTag 方法是否返回 null
     */
    @Test
    public void testItemComponentAfterTitleTagReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public FloatTagVO itemComponentAfterTitleTag(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        FloatTagVO result = floorsBuilderExtAdapter.itemComponentAfterTitleTag(activityContext, "groupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试floorShowType方法是否返回0
     */
    @Test
    public void testFloorShowTypeReturnZero() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int floorShowType(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return 0;
            }
        };
        // act
        int result = floorsBuilderExtAdapter.floorShowType(activityContext, "groupName", productGroupM);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试floorShowType方法在输入参数为null时是否返回0
     */
    @Test
    public void testFloorShowTypeReturnZeroWhenInputIsNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int floorShowType(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return 0;
            }
        };
        // act
        int result = floorsBuilderExtAdapter.floorShowType(null, null, null);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试floorShowType方法在输入参数为非null时是否返回0
     */
    @Test
    public void testFloorShowTypeReturnZeroWhenInputIsNotNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public int floorShowType(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return 0;
            }
        };
        // act
        int result = floorsBuilderExtAdapter.floorShowType(activityContext, "groupName", productGroupM);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 activityRemainSecondsLabel 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testActivityRemainSecondsLabelReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public RichLabelVO activityRemainSecondsLabel(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        // act
        RichLabelVO result = floorsBuilderExtAdapter.activityRemainSecondsLabel(activityContext, "groupName", productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentLabs 方法，期望返回 null
     */
    @Test
    public void testItemComponentLabsReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
                return null;
            }
        };
        // act
        String result = floorsBuilderExtAdapter.itemComponentLabs(activityContext, "groupName", productM, 0);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentSalePrice 方法，期望返回 null
     */
    @Test
    public void testItemComponentSalePriceReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter adapter = new FloorsBuilderExtAdapter() {

            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "groupName";
        ProductM productM = new ProductM();
        // act
        String result = adapter.itemComponentSalePrice(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPromoTags 方法
     */
    @Test
    public void testItemComponentPromoTags() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public List<DzPromoVO> itemComponentPromoTags(ActivityContext activityContext, String groupName, ProductM productM) {
                return null;
            }
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductM productM = mock(ProductM.class);
        // act
        List<DzPromoVO> result = floorsBuilderExtAdapter.itemComponentPromoTags(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }
}
