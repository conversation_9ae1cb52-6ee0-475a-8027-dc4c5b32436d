package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.douhu;

import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.SpringConfiguration;
import com.sankuai.dzviewscene.shelf.platform.MockBeanTest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/8/24.
 */
@Ignore("没有可执行的方法")
@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {SpringConfiguration.class})
public class PlatformDouHuFetcherUnitTest extends MockBeanTest {

    @Resource
    protected ComponentFinder componentFinder;


    @Before
    public void setUp() throws Exception {
        Mockito.when(atomFacadeService.getPoiABTest(mockDouHuRequest("test_unionId", "test_exp_id"))).thenReturn(CompletableFuture.completedFuture(mockDouHuResponse()));
    }

    private DouHuRequest mockDouHuRequest(String unionId, String expId) {
        DouHuRequest douHuRequest = new DouHuRequest();
        douHuRequest.setExpId(expId);
        douHuRequest.setUnionId(unionId);
        return douHuRequest;
    }

    private DouHuResponse mockDouHuResponse() {
        DouHuResponse douHuResponse = new DouHuResponse();
        douHuResponse.setCode(ErrorCode.SUCCESS.getCode());
        douHuResponse.setSk("test_sk");
        douHuResponse.setAbQueryId("test_query_id");
        return douHuResponse;
    }

    //@Test
    @DisplayName("测试默认斗斛数据查询能力")
    public void test_dou_hu_fetcher() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        activityContext.setParameters(new HashMap<String, Object>(){{
            put(ShelfActivityConstants.Params.unionId, "test_unionId");
        }});
        CompletableFuture<DouHuM> douHuCompletableFuture = componentFinder.findAbility(activityContext, DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE).build(activityContext);
        Assert.assertTrue(douHuCompletableFuture.join().getAbtest().equals("[{\"query_id\":\"test_query_id\",\"ab_id\":\"test_sk\"}]"));
    }
}
