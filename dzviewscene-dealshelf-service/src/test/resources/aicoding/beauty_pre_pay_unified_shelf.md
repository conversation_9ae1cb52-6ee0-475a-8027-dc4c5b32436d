# 医美货架迭代-POI一致性需求和设计

## 需求背景
需求文档详见[医美货架迭代-POI一致性需求](https://km.sankuai.com/collabpage/2702887488)
### 货架首页-一级筛选器

#### 展示位置：
- 导航条下方，收敛为一行筛选器

#### 展示逻辑：
- 货架首页展示一级筛选器分为3个坑位区域，①基础筛选，支持展示4个筛选器坑位；②商品CPV“关键属性”筛选，为了一级筛选器的稳定性，该属性限定特定的n个https://km.sankuai.com/collabpage/2604501819（注意：本需求中所有“商品属性”筛选器仅抓取的CPV中“关键属性”，且剔除【指定医生服务】，其他销售、营销属性不考虑）；③营销活动筛选，支持展示1个筛选器坑位。各区域坑位独立，展示逻辑均为“锚定无商品召回则不展示”，不展示时，仅支持本区域筛选器按照排序补足，其他区域筛选器无法补足
- 基础筛选区域：【全部xx】【神券】【门店主打】【项目分类】（“神券”作为平台统一的心智放在基础坑位内）
- CPV商品关键属性筛选区域：抓取医美行业CPV配置的关键属性字段【药品/仪器】
- 营销活动坑位筛选：运营单独提需配置，如【百亿补贴】等（运营提需配置，B端不可见）

#### 交互逻辑：
- 【神券】、【门店主打】、【项目分类】、【药品/仪器】（【种植方法】、【毛囊数量】）筛选器之间支持交叉选择（商品召回逻辑取交集），在命中一个筛选项后，用户在选择其他筛选器时，需要实时判断其他筛选器的筛选项交叉选择后是否有商品召回（注意药品/仪器虽然在C端合成一个筛选器，但是是切换tab的形式，交互逻辑上可当作两个筛选器，以下判断召回商品个数时，分开计算）
    - 若该筛选器下部分筛选项无商品召回，则无召回的筛选项置灰，不支持选择，其他筛选项正常展示。筛选下拉页顶部，展示“您选择的【前置筛选项目名称】”（加粗），可选【项目/药品/仪器】【x】个（药品/仪器分开计算）
        - 【项目/药品/仪器】根据当下点击的筛选器展示对应的筛选按钮文案
        - 【x】=当下复选后有商品召回的筛选项个数
    - 若该筛选器下全部筛选项无商品召回，则无召回的筛选项置灰，不支持选择。筛选下拉页顶部，展示“您选择的【前置筛选项名称】（加粗）”，暂无可选【项目/药品/仪器】（药品/仪器分开计算）
- 同一筛选器下的筛选项支持多选（商品召回逻辑取并集），需要实时判断多选后商品召回个数，确认按钮【查看（xxx）】上的xxx=商品召回个数

### 货架首页-一级筛选器下拉页

#### 展示逻辑：
- 一级筛选器：项目分类
    - 一个项目分层下最多展示三行筛选项，超过的按照折叠，点击右侧「下拉箭头」后展示全部
    - 一行固定最多展示三个筛选项，筛选项框长度固定，字数按照左右边距展示，超过字数展示“...”
- 一级筛选器：药品
    - 一个项目分层下最多展示三行筛选项，超过的按照折叠，点击右侧「下拉箭头」后展示全部
    - 一行固定最多展示两个筛选项（因仪器的字数普遍比较多），筛选项框长度固定，字数按照左右边距展示，超过字数展示“...”
- 一级筛选器：仪器
    - 一个项目分层下最多展示三行筛选项，超过的按照折叠，点击右侧「下拉箭头」后展示全部
    - 一行固定最多展示两个筛选项（因仪器的字数普遍比较多），筛选项框长度固定，字数按照左右边距展示，超过字数展示“...”

#### 交互逻辑：
- 用户搜索命中某个筛选项时，点击对应筛选器的下拉页，该筛选项对应的项目分层置顶展示
- 【项目分类】筛选下拉页支持滑动，右边的列表随着用户滑动的区域进行锚定，如果用户勾选某个筛选项，则右边对应的分类前需要加“红点”表述命中。【药品/仪器】筛选下拉页内右边药品、仪器分类不支持随滑动锚定，必须要用户主动点击右边tab才可以看对应分类
- 点击【查看（xxx）】筛选下拉页消失，该筛选器为命中态，底色为红色，并且筛选器文字内容变为用户选择的筛选项内容，内容按照视觉稿边距展示最长字数，字数超过展示“...”
- 点击【重置】该筛选下拉页的所有已勾选的筛选项取消勾选

### 货架首页-搜索
#### 展示位置：
- 展示在筛选器最右边，作为独立区域，筛选器滑动不影响

#### 展示/交互逻辑：
- 逻辑不变，仅改变样式

### 货架首页-查看更多
#### 展示逻辑：
- 根据召回商品数展示不同的文案
    - >4个：文案展示：更多【x】个商品。【x】当下筛选项锚定后，所召回的商品总数-已展示商品数
    - <=4个：文案展示：更多【x】个【扩展筛选项】商品
        - 【扩展筛选项】当下筛选项选中的商品数<=4个，将拓展其他相关筛选项能选中的商品所谓“相似推荐商品”展示，拓展逻辑是：判断当下所召回商品是否能归属的其他一级筛选器下的筛选项
        - 能归属，且归属一个，直接用该一级筛选项做扩展。比如当下锚定四代热玛吉，一级能归属“热玛吉”。文案应是“更多x个热玛吉商品”，对应二级页推荐“本店其他热玛吉商品”
        - 能归属，归属多个，则选择能召回商品数最多一级筛选项的当作扩展。比如当下锚定伊婉C，一级能归属“苹果肌”、“玻尿酸”，且玻尿酸能召回的商品更多，文案应是“查看更多玻尿酸商品”，对应二级页推荐“本店其他玻尿酸商品”
        - 不能归属，则展示“查看全部商品”，对应二级页推荐“本店其他商品”（按照全部tab召回）
        - 【x】拓展筛选项召回商品数-已展示商品数
#### 交互逻辑：
- 点击查看更多>后进入货架二级页

### 货架二级页-二级筛选器
#### 展示逻辑：
- 货架二级页展示一级页全部一级筛选器
- 新增二级筛选器，二级筛选器逻辑：
    - 默认二级筛选器【排序】，筛选项为：低价优先、好评优先、销量优先（必出）
    - 新增“特殊配置”二级筛选器
    - 仅选一个一级筛选项：在仅命中某一个一级筛选项下，需要出特定的二级筛选器，且该特定筛选器下的有商品召回筛选项>=1（特定的二级筛选器见筛选器枚举表）
    - 复选多个一级筛选项：不展示（注意：多个筛选项同时命中不出特殊的默认筛选器）
    - 新增“商品关键属性”二级筛选器，非一级筛选器的商品关键属性，按以下逻辑判断后作为二级筛选器展示（注意：CPV中的​【选择标品信息】内更细粒度的相关信息是单独储存的，筛选器需要按照最细粒度的展示，如【药品品牌】、【型号】、【剂量】等）：  
        - 仅选一个一级筛选项：按照提供的二级筛选器对应的关键属性字段（见下方二级筛选器枚举表），判断>=80%的召回商品中上单有该字段，该属性字段上单的赋值非null、非单一值，则该关键属性作为二级筛选器展示。
        - 复选多个一级筛选项：取二级筛选器对应关键属性的并集，判断>=80%的召回商品中上单有该字段，以及该属性字段上单的赋值非null、非单一值，则作为二级筛选器展示。筛选器内的筛选项取该关键属性字段下的上单赋值。如果出现重复的属性筛选器，则去重整合为一个，展示筛选项也合并
    - 二级筛选器的特殊处理逻辑：
        - 命中一级【项目分类】-【面部塑形】中任何筛选项，二级筛选器不展示【适用部位】筛选器（两者信息重合）
        - 命中一级【项目分类】-【玻尿酸】中的筛选项，二级筛选器不展示【药品品牌】、【型号】筛选器
#### 交互逻辑：
- 同上，同首页货架筛选器的交互逻辑
- 用户在货架首屏选择的筛选，要带到2级页命中锚定 

### 货架二级页-相关推荐
#### 展示位置：
- 精准召回商品下方新增模块
#### 展示条件：
- 命中某筛选后，当召回商品数<=5个时，精准的召回商品下方新增相关商品推荐模块

## 方案设计

### 系统交互
#### 首次请求货架
```PlantUML
@startuml
actor 客户端
participant "货架服务"
participant "商品BP货架"
participant "团购主题"

客户端 -> 货架服务: 访问货架
activate 货架服务

货架服务 -> 货架服务:场景路由
activate 货架服务 
deactivate 货架服务

货架服务 -> 商品BP货架: 请求货架导航
activate 商品BP货架
商品BP货架--> 货架服务: 
deactivate 商品BP货架

货架服务 -> 货架服务:导航锚定计算
activate 货架服务 
deactivate 货架服务

货架服务 ->商品BP货架: 根据锚定筛选请求商品列表
activate 商品BP货架
商品BP货架 --> 货架服务: 
deactivate 商品BP货架

货架服务 -> 团购主题: 查询预付商品信息
activate 团购主题
团购主题  --> 货架服务
deactivate 团购主题

货架服务 -> 货架服务:货架展示信息构造
activate 货架服务 
deactivate 货架服务

货架服务 --> 客户端: 返回货架导航及商品列表
deactivate 货架服务
@enduml
```
#### 复选筛选项后请求货架
```PlantUML
@startuml
actor 客户端
participant "货架服务"
participant "商品BP货架"
participant "团购主题"

客户端 -> 货架服务: 选择多个筛选项点击
activate 货架服务

货架服务 -> 货架服务:场景路由
activate 货架服务 
deactivate 货架服务

货架服务 -> 商品BP货架: 多筛选项组合请求导航
activate 商品BP货架
商品BP货架--> 货架服务: 返回包含筛选项是否可选的导航信息
deactivate 商品BP货架

货架服务 ->商品BP货架: 多筛选项组合召回商品列表
activate 商品BP货架
商品BP货架 --> 货架服务: 
deactivate 商品BP货架

货架服务 -> 团购主题: 查询预付商品信息
activate 团购主题
团购主题  --> 货架服务
deactivate 团购主题

货架服务 -> 货架服务:货架展示信息构造
activate 货架服务 
deactivate 货架服务

货架服务 --> 客户端: 返回货架导航及商品列表
deactivate 货架服务
@enduml
```
### 详细设计
#### 导航模型设计
医美导航为多级导航，一级筛选子项为F型筛选器，除此之外，条件筛选器还有列表型，网格型，聚合型（眼镜货架），故可以将导航路径分为以下几种
1. 筛选项->子项普通筛选器
    - 常规导航多级筛选，现有模型支持
2. 筛选项->子项F型筛选器
    - 子项为F型筛选器，左侧为分类导航，右侧为多层网格状筛选
    - F型筛选器有两种情况
        1.两级结构，左侧导航为一级，右侧筛选为所有一、二级平铺展开，分组标题为一级标题，左侧导航支持点击和滑动锚定
        2.三级结构，左侧导航为一级，右侧筛选为所有二、三级平铺展开，分组标题为二级标题，左侧导航只支持切换
3. 筛选器->列表筛选器
    - 子项为下拉列表型选项
4. 筛选器->网格筛选器
    - 子项为网格型选项，需要后端控制网格列数
5. 聚合筛选器->聚合网格筛选器
    - 筛选节点特殊样式：在最右侧显示聚合图标
    - 子项为多层网格状，需要后端控制网格列数
根据上述分析，为满足多种样式筛选，ShelfFilterNodeVO新增如下字段：
- nodeType
    - 类型：String
    - 可选值：
        - TAB("tab", "导航tab"),
        - OPTION("option", "普通条件筛选器"),
        - AGGREGATION("aggregation", "聚合条件筛选器");
- showType
    - 类型：int
    - 枚举值：
        - FILTER_TAB_NORMAL(0, "导航筛选"),
        - FILTER_TAB_NORMAL(1, "无框导航筛选"),
        - FILTER_TAB_F_(11, "F型导航筛选，滑动分类选项"),
        - FILTER_TAB_F_(12, "F型导航筛选，切换分类选项"),
        - FILTER_OPTION_LIST(21, "列表筛选器"),
        - FILTER_OPTION_GRID(22, "网格筛选器（包含重制、查看交互）"),
        - FILTER_OPTION_GROUP(23, "聚合网格筛选器");
- disabled
    - 类型：boolean
    - 描述：是否禁用
- column
    - 类型：int
    - 描述：子项网格列数，为网格筛选器，且大于0生效
- defaultRow
    - 类型：int
    - 描述：子项网格默认展示行数，超过行折叠，为网格筛选器，且大于0生效
- tips
    - 类型：FilterTips
        - preTitle
            - 类型：RichLabelModel
            - 描述：前置标签
        - suffixTitle
            - 类型：RichLabelModel
            - 描述：后置标签
        - tags
            - 类型：List<IconRichLabelModel>
            - 描述：标签列表
    - 描述：筛选提示
#### 货架导航
商品bp返回的为树状导航结构，货架模型映射为树状，子项筛选项样式通过场景层配置控制。节点字段信息定义为变化点VP，通过opt扩展
- nodeType
    - 类型：String
    - 实现方式
        -筛选节点类型，代码处理，导航tab的类型为tab，条件筛选为option
        -可配置aggregation的节点
- showType
    - 类型：int
    - 实现方式：变化点`UnifiedShelfFilterShowTypeVP`
- column
    - 类型：int
    - 实现方式：
        - 子项网格筛选列数，当showType为F型筛选器和网格筛选器时需配置该值
        - 变化点`UnifiedShelfFilterColumnVP`
- defaultRow
    - 类型：int
    - 实现方式：
        - 子项网格筛选默认列数，当showType为F型筛选器和网格筛选器时配置该值
        - 变化点`UnifiedShelfFilterDefaultRowVP`
- minShowNum
    - 类型：int
- multipleSelect
    - 类型：boolean
- echoSelectedLeafNode
    - 类型：boolean
- defaultSelectedLeafNode
    - 类型：List<String>
#### 选购引导区
1. 选购引导区的内容需要跟随选择的导航筛选项变化，同时选项标签可交互，点击后展示对应文案。选项文案分两种：一种常规文案，需要计算选项召回的商品个数，一种是复杂定制文案，需要根据多个数据源信息处理
2. 从交互和功能定义上来看，选购引导区应归属于导航部分，故将选购引导区模型放在导航模型中，导航刷新返回的数据包含更新的选购引导区信息
3. 导航模型ShelfFilterVO新增筛选引导信息
- filterRoot
    - 类型：ShelfFilterNodeVO
    - 描述：筛选根节点
- filterGuideInfo
    - 类型：FilterGuideInfo
    - 描述：筛选引导信息
4. FilterGuidInfo模型
- background
    - 类型：String
    - 描述：背景色
- icon
    - 类型：PictureModel
    - 描述：图标
- title
    - 类型：String
    - 描述：标题
- guidTags
    - 类型：List<GuidTag> 
    - 描述：标签列表
5. GuidTag模型
- tagName
    - 类型：String
    - 描述：标签名
- tagDesc
    - 类型：String
    - 描述：标签介绍
6. 选购引导区信息构造，在实现上可在UnifiedShelfFilterBuilder中定义一个VP，通过opt方式实现。
7. 常规文案可根据商品bp返回的二级筛选项下商品列表计算，复杂定制文案这块需要再讨论下。。。。。。涉及到分页情况，